/*! This file is auto-generated */
!function(){"use strict";var e={d:function(n,t){for(var o in t)e.o(t,o)&&!e.o(n,o)&&Object.defineProperty(n,o,{enumerable:!0,get:t[o]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}},n={};e.d(n,{default:function(){return i}});var t=window.wp.hooks;const o=Object.create(null);function i(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{since:i,version:r,alternative:c,plugin:d,link:a,hint:l}=n,s=d?` from ${d}`:"",u=i?` since version ${i}`:"",$=r?` and will be removed${s} in version ${r}`:"",w=c?` Please use ${c} instead.`:"",p=a?` See: ${a}`:"",f=l?` Note: ${l}`:"",v=`${e} is deprecated${u}${$}.${w}${p}${f}`;v in o||((0,t.doAction)("deprecated",e,n,v),console.warn(v),o[v]=!0)}(window.wp=window.wp||{}).deprecated=n.default}();