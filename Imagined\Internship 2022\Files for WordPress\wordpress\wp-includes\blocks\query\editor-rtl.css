/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.block-library-query-toolbar__popover .components-popover__content {
  min-width: 230px;
}

.wp-block-query__create-new-link {
  padding: 0 56px 16px 16px;
}

.block-library-query__pattern-selection-content .block-editor-block-patterns-list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 8px;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item {
  margin-bottom: 0;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item .block-editor-block-preview__container {
  max-height: 250px;
}

.block-editor-query-pattern__selection-modal .components-modal__content {
  overflow: hidden;
  padding: 0;
}
.block-editor-query-pattern__selection-modal .components-modal__content::before {
  margin-bottom: 0;
}

@media (min-width: 600px) {
  .block-editor-query-pattern__selection-modal {
    width: calc(100% - 32px);
    height: calc(100% - 120px);
  }
}
@media (min-width: 782px) {
  .block-editor-query-pattern__selection-modal {
    width: 750px;
  }
}
@media (min-width: 960px) {
  .block-editor-query-pattern__selection-modal {
    height: 80%;
    width: 80%;
    max-height: none;
  }
}