{# Invisible characters will make javascript crash #}
<script type="text/javascript">
var designerConfig = {{ designer_config|raw }};
</script>

{# side menu #}
{% if not has_query %}
    <div id="name-panel">
        <span id="page_name">
            {{ selected_page == null ? 'Untitled'|trans : selected_page }}
        </span>
        <span id="saved_state">
            {{ selected_page == null ? '*' : '' }}
        </span>
    </div>
{% endif %}
<div class="designer_header side-menu" id="side_menu">
    <a class="M_butt" id="key_Show_left_menu" href="#">
        <img title="{% trans 'Show/Hide tables list' %}"
             alt="v"
             src="{{ image('designer/downarrow2_m.png') }}"
             data-down="{{ image('designer/downarrow2_m.png') }}"
             data-up="{{ image('designer/uparrow2_m.png') }}">
        <span class="hide hidable">
            {% trans 'Show/Hide tables list' %}
        </span>
    </a>
    <a href="#" id="toggleFullscreen" class="M_butt">
        <img title="{% trans 'View in fullscreen' %}"
             src="{{ image('designer/viewInFullscreen.png') }}"
             data-enter="{{ image('designer/viewInFullscreen.png') }}"
             data-exit="{{ image('designer/exitFullscreen.png') }}">
        <span class="hide hidable"
              data-exit="{% trans 'Exit fullscreen' %}"
              data-enter="{% trans 'View in fullscreen' %}">
            {% trans 'View in fullscreen' %}
        </span>
    </a>
    <a href="#" id="addOtherDbTables" class="M_butt">
        <img title="{% trans 'Add tables from other databases' %}"
             src="{{ image('designer/other_table.png') }}">
        <span class="hide hidable">
            {% trans 'Add tables from other databases' %}
        </span>
    </a>
    {% if not has_query %}
        <a id="newPage" href="#" class="M_butt">
            <img title="{% trans 'New page' %}"
                 alt=""
                 src="{{ image('designer/page_add.png') }}">
            <span class="hide hidable">
                {% trans 'New page' %}
            </span>
        </a>
        <a href="#" id="editPage" class="M_butt ajax">
            <img title="{% trans 'Open page' %}"
                 src="{{ image('designer/page_edit.png') }}">
            <span class="hide hidable">
                {% trans 'Open page' %}
            </span>
        </a>
        <a href="#" id="savePos" class="M_butt">
            <img title="{% trans 'Save page' %}"
                 src="{{ image('designer/save.png') }}">
            <span class="hide hidable">
                {% trans 'Save page' %}
            </span>
        </a>
        <a href="#" id="SaveAs" class="M_butt ajax">
            <img title="{% trans 'Save page as' %}"
                 src="{{ image('designer/save_as.png') }}">
            <span class="hide hidable">
                {% trans 'Save page as' %}
            </span>
        </a>
        <a href="#" id="delPages" class="M_butt ajax">
            <img title="{% trans 'Delete pages' %}"
                 src="{{ image('designer/page_delete.png') }}">
            <span class="hide hidable">
                {% trans 'Delete pages' %}
            </span>
        </a>
        <a href="#" id="StartTableNew" class="M_butt">
            <img title="{% trans 'Create table' %}"
                 src="{{ image('designer/table.png') }}">
            <span class="hide hidable">
                {% trans 'Create table' %}
            </span>
        </a>
        <a href="#" class="M_butt" id="rel_button">
            <img title="{% trans 'Create relationship' %}"
                 src="{{ image('designer/relation.png') }}">
            <span class="hide hidable">
                {% trans 'Create relationship' %}
            </span>
        </a>
        <a href="#" class="M_butt" id="display_field_button">
            <img title="{% trans 'Choose column to display' %}"
                 src="{{ image('designer/display_field.png') }}">
            <span class="hide hidable">
                {% trans 'Choose column to display' %}
            </span>
        </a>
        <a href="#" id="reloadPage" class="M_butt">
            <img title="{% trans 'Reload' %}"
                 src="{{ image('designer/reload.png') }}">
            <span class="hide hidable">
                {% trans 'Reload' %}
            </span>
        </a>
        <a href="{{ get_docu_link('faq', 'faq6-31') }}" target="_blank" rel="noopener noreferrer" class="M_butt">
            <img title="{% trans 'Help' %}"
                 src="{{ image('designer/help.png') }}">
            <span class="hide hidable">
                {% trans 'Help' %}
            </span>
        </a>
    {% endif %}
    <a href="#" class="{{ params_array['angular_direct'] }}" id="angular_direct_button">
        <img title="{% trans 'Angular links' %} / {% trans 'Direct links' %}"
             src="{{ image('designer/ang_direct.png') }}">
        <span class="hide hidable">
            {% trans 'Angular links' %} / {% trans 'Direct links' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['snap_to_grid'] }}" id="grid_button">
        <img title="{% trans 'Snap to grid' %}" src="{{ image('designer/grid.png') }}">
        <span class="hide hidable">
            {% trans 'Snap to grid' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['small_big_all'] }}" id="key_SB_all">
        <img title="{% trans 'Small/Big All' %}"
             alt="v"
             src="{{ image('designer/downarrow1.png') }}"
             data-down="{{ image('designer/downarrow1.png') }}"
             data-right="{{ image('designer/rightarrow1.png') }}">
        <span class="hide hidable">
            {% trans 'Small/Big All' %}
        </span>
    </a>
    <a href="#" id="SmallTabInvert" class="M_butt">
        <img title="{% trans 'Toggle small/big' %}"
             src="{{ image('designer/bottom.png') }}">
        <span class="hide hidable">
            {% trans 'Toggle small/big' %}
        </span>
    </a>
    <a href="#" id="relLineInvert" class="{{ params_array['relation_lines'] }}" >
        <img title="{% trans 'Toggle relationship lines' %}"
             src="{{ image('designer/toggle_lines.png') }}">
        <span class="hide hidable">
            {% trans 'Toggle relationship lines' %}
        </span>
    </a>
    {% if not visual_builder %}
        <a href="#" id="exportPages" class="M_butt" >
            <img title="{% trans 'Export schema' %}"
                 src="{{ image('designer/export.png') }}">
            <span class="hide hidable">
                {% trans 'Export schema' %}
            </span>
        </a>
    {% else %}
        <a id="build_query_button"
           class="M_butt"
           href="#"
           class="M_butt">
            <img title="{% trans 'Build Query' %}"
                 src="{{ image('designer/query_builder.png') }}">
            <span class="hide hidable">
                {% trans 'Build Query' %}
            </span>
        </a>
    {% endif %}
    <a href="#" class="{{ params_array['side_menu'] }}" id="key_Left_Right">
        <img title="{% trans 'Move Menu' %}" alt=">"
             data-right="{{ image('designer/2leftarrow_m.png') }}"
             src="{{ image('designer/2rightarrow_m.png') }}">
        <span class="hide hidable">
            {% trans 'Move Menu' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['pin_text'] }}" id="pin_Text">
        <img title="{% trans 'Pin text' %}"
             alt=">"
             data-right="{{ image('designer/anchor.png') }}"
             src="{{ image('designer/anchor.png') }}">
        <span class="hide hidable">
            {% trans 'Pin text' %}
        </span>
    </a>
</div>
<div id="canvas_outer">
    <form action="" id="container-form" method="post" name="form1">
        <div id="osn_tab">
            <canvas class="designer" id="canvas" width="100" height="100"></canvas>
        </div>
        <div id="layer_menu" class="hide">
            <div class="text-center">
                <a href="#" class="M_butt" target="_self" >
                    <img title="{% trans 'Hide/Show all' %}"
                        alt="v"
                        id="key_HS_all"
                        src="{{ image('designer/downarrow1.png') }}"
                        data-down="{{ image('designer/downarrow1.png') }}"
                        data-right="{{ image('designer/rightarrow1.png') }}">
                </a>
                <a href="#" class="M_butt" target="_self" >
                    <img alt="v"
                        id="key_HS"
                        title="{% trans 'Hide/Show tables with no relationship' %}"
                        src="{{ image('designer/downarrow2.png') }}"
                        data-down="{{ image('designer/downarrow2.png') }}"
                        data-right="{{ image('designer/rightarrow2.png') }}">
                </a>
            </div>
            <div id="id_scroll_tab" class="scroll_tab">
                <table class="table table-sm ps-1"></table>
            </div>
            {# end id_scroll_tab #}
            <div class="text-center">
                {% trans 'Number of tables:' %} <span id="tables_counter">0</span>
            </div>
            <div id="layer_menu_sizer">
                  <img class="icon float-start"
                      id="layer_menu_sizer_btn"
                      data-right="{{ image('designer/resizeright.png') }}"
                      src="{{ image('designer/resize.png') }}">
            </div>
        </div>
        {# end layer_menu #}
        {% include 'database/designer/database_tables.twig' with {
            'db': db,
            'text_dir': text_dir,
            'get_db': get_db,
            'has_query': has_query,
            'tab_pos': tab_pos,
            'display_page': display_page,
            'tab_column': tab_column,
            'tables_all_keys': tables_all_keys,
            'tables_pk_or_unique_keys': tables_pk_or_unique_keys,
            'columns_type': columns_type,
            'tables': designerTables,
        } only %}
    </form>
</div>
<div id="designer_hint"></div>
{# create relation pane #}
<table id="layer_new_relation" class="table table-borderless w-auto hide">
    <tbody>
        <tr>
            <td class="frams1" width="10px">
            </td>
            <td class="frams5" width="99%" >
            </td>
            <td class="frams2" width="10px">
                <div class="bor">
                </div>
            </td>
        </tr>
        <tr>
            <td class="frams8">
            </td>
            <td class="input_tab p-0">
                <table class="table table-borderless bg-white mb-0 text-center">
                    <thead>
                        <tr>
                            <td colspan="2" class="text-center text-nowrap">
                                <strong>
                                    {% trans 'Create relationship' %}
                                </strong>
                            </td>
                        </tr>
                    </thead>
                    <tbody id="foreign_relation">
                        <tr>
                            <td colspan="2" class="text-center text-nowrap">
                                <strong>
                                    FOREIGN KEY
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td width="58" class="text-nowrap">
                                on delete
                            </td>
                            <td width="102">
                                <select name="on_delete" id="on_delete">
                                    <option value="nix" selected="selected">
                                        --
                                    </option>
                                    <option value="CASCADE">
                                        CASCADE
                                    </option>
                                    <option value="SET NULL">
                                        SET NULL
                                    </option>
                                    <option value="NO ACTION">
                                        NO ACTION
                                    </option>
                                    <option value="RESTRICT">
                                        RESTRICT
                                    </option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-nowrap">
                                on update
                            </td>
                            <td>
                                <select name="on_update" id="on_update">
                                    <option value="nix" selected="selected">
                                        --
                                    </option>
                                    <option value="CASCADE">
                                        CASCADE
                                    </option>
                                    <option value="SET NULL">
                                        SET NULL
                                    </option>
                                    <option value="NO ACTION">
                                        NO ACTION
                                    </option>
                                    <option value="RESTRICT">
                                        RESTRICT
                                    </option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                    <tbody>
                        <tr>
                            <td colspan="2" class="text-center text-nowrap">
                                <input type="button" id="ok_new_rel_panel" class="btn btn-secondary butt"
                                    name="Button" value="{% trans 'OK' %}">
                                <input type="button" id="cancel_new_rel_panel"
                                    class="btn btn-secondary butt" name="Button" value="{% trans 'Cancel' %}">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td class="frams6">
            </td>
        </tr>
        <tr>
            <td class="frams4">
                <div class="bor">
                </div>
            </td>
            <td class="frams7">
            </td>
            <td class="frams3">
            </td>
        </tr>
    </tbody>
</table>
{# delete relation pane #}
<table id="layer_upd_relation" class="table table-borderless w-auto hide">
    <tbody>
        <tr>
            <td class="frams1" width="10px">
            </td>
            <td class="frams5" width="99%">
            </td>
            <td class="frams2" width="10px">
                <div class="bor">
                </div>
            </td>
        </tr>
        <tr>
            <td class="frams8">
            </td>
            <td class="input_tab p-0">
                <table class="table table-borderless bg-white mb-0 text-center">
                    <tr>
                        <td colspan="3" class="text-center text-nowrap">
                            <strong>
                                {% trans 'Delete relationship' %}
                            </strong>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="text-center text-nowrap">
                            <input id="del_button" name="Button" type="button"
                                class="btn btn-secondary butt" value="{% trans 'Delete' %}">
                            <input id="cancel_button" type="button" class="btn btn-secondary butt"
                                name="Button" value="{% trans 'Cancel' %}">
                        </td>
                    </tr>
                </table>
            </td>
            <td class="frams6">
            </td>
        </tr>
        <tr>
            <td class="frams4">
                <div class="bor">
                </div>
            </td>
            <td class="frams7">
            </td>
            <td class="frams3">
            </td>
        </tr>
    </tbody>
</table>
{% if has_query %}
    {# options panel #}
    <table id="designer_optionse" class="table table-borderless w-auto hide">
        <tbody>
            <tr>
                <td class="frams1" width="10px">
                </td>
                <td class="frams5" width="99%" >
                </td>
                <td class="frams2" width="10px">
                    <div class="bor">
                    </div>
                </td>
            </tr>
            <tr>
                <td class="frams8">
                </td>
                <td class="input_tab p-0">
                    <table class="table table-borderless bg-white mb-0 text-center">
                        <thead>
                            <tr>
                                <td colspan="2" rowspan="2" id="option_col_name" class="text-center text-nowrap">
                                </td>
                            </tr>
                        </thead>
                        <tbody id="where">
                            <tr>
                                <td class="text-center text-nowrap">
                                    <b>
                                        WHERE
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Relationship operator' %}
                                </td>
                                <td width="102">
                                    <select name="rel_opt" id="rel_opt">
                                        <option value="--" selected="selected">
                                            --
                                        </option>
                                        <option value="=">
                                            =
                                        </option>
                                        <option value="&gt;">
                                            &gt;
                                        </option>
                                        <option value="&lt;">
                                            &lt;
                                        </option>
                                        <option value="&gt;=">
                                            &gt;=
                                        </option>
                                        <option value="&lt;=">
                                            &lt;=
                                        </option>
                                        <option value="NOT">
                                            NOT
                                        </option>
                                        <option value="IN">
                                            IN
                                        </option>
                                        <option value="EXCEPT">
                                            {% trans 'Except' %}
                                        </option>
                                        <option value="NOT IN">
                                            NOT IN
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-nowrap">
                                    {% trans 'Value' %}
                                    <br>
                                    {% trans 'subquery' %}
                                </td>
                                <td>
                                    <textarea id="Query" cols="18"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-center text-nowrap">
                                    <b>
                                        {% trans 'Rename to' %}
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'New name' %}
                                </td>
                                <td width="102">
                                    <input type="text" id="new_name">
                                </td>
                            </tr>
                            <tr>
                                <td class="text-center text-nowrap">
                                    <b>
                                        {% trans 'Aggregate' %}
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="operator" id="operator">
                                        <option value="---" selected="selected">
                                            ---
                                        </option>
                                        <option value="sum" >
                                            SUM
                                        </option>
                                        <option value="min">
                                            MIN
                                        </option>
                                        <option value="max">
                                            MAX
                                        </option>
                                        <option value="avg">
                                            AVG
                                        </option>
                                        <option value="count">
                                            COUNT
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-center text-nowrap">
                                    <b>
                                        GROUP BY
                                    </b>
                                </td>
                                <td>
                                    <input type="checkbox" value="groupby" id="groupby">
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-center text-nowrap">
                                    <b>
                                        ORDER BY
                                    </b>
                                </td>
                                <td>
                                    <select name="orderby" id="orderby">
                                        <option value="---" selected="selected">
                                            ---
                                        </option>
                                        <option value="ASC" >
                                            ASC
                                        </option>
                                        <option value="DESC">
                                            DESC
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-center text-nowrap">
                                    <b>
                                        HAVING
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="h_operator" id="h_operator">
                                        <option value="---" selected="selected">
                                            ---
                                        </option>
                                        <option value="None" >
                                            {% trans 'None' %}
                                        </option>
                                        <option value="sum" >
                                            SUM
                                        </option>
                                        <option value="min">
                                            MIN
                                        </option>
                                        <option value="max">
                                            MAX
                                        </option>
                                        <option value="avg">
                                            AVG
                                        </option>
                                        <option value="count">
                                            COUNT
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Relationship operator' %}
                                </td>
                                <td width="102">
                                    <select name="h_rel_opt" id="h_rel_opt">
                                        <option value="--" selected="selected">
                                            --
                                        </option>
                                        <option value="=">
                                            =
                                        </option>
                                        <option value="&gt;">
                                            &gt;
                                        </option>
                                        <option value="&lt;">
                                            &lt;
                                        </option>
                                        <option value="&gt;=">
                                            &gt;=
                                        </option>
                                        <option value="&lt;=">
                                            &lt;=
                                        </option>
                                        <option value="NOT">
                                            NOT
                                        </option>
                                        <option value="IN">
                                            IN
                                        </option>
                                        <option value="EXCEPT">
                                            {% trans 'Except' %}
                                        </option>
                                        <option value="NOT IN">
                                            NOT IN
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Value' %}
                                    <br>
                                    {% trans 'subquery' %}
                                </td>
                                <td width="102">
                                    <textarea id="having" cols="18"></textarea>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <input type="hidden" id="ok_add_object_db_and_table_name_url" />
                                    <input type="hidden" id="ok_add_object_db_name" />
                                    <input type="hidden" id="ok_add_object_table_name" />
                                    <input type="hidden" id="ok_add_object_col_name" />
                                    <input type="button" id="ok_add_object" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'OK' %}">
                                    <input type="button" id="cancel_close_option" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'Cancel' %}">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td class="frams6">
                </td>
            </tr>
            <tr>
                <td class="frams4">
                    <div class="bor">
                    </div>
                </td>
                <td class="frams7">
                </td>
                <td class="frams3">
                </td>
            </tr>
        </tbody>
    </table>
    {# rename to pane #}
    <table id="query_rename_to" class="table table-borderless w-auto hide">
        <tbody>
            <tr>
                <td class="frams1" width="10px">
                </td>
                <td class="frams5" width="99%" >
                </td>
                <td class="frams2" width="10px">
                    <div class="bor">
                    </div>
                </td>
            </tr>
            <tr>
                <td class="frams8">
                </td>
                <td class="input_tab p-0">
                    <table class="table table-borderless bg-white mb-0 text-center">
                        <thead>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <strong>
                                        {% trans 'Rename to' %}
                                    </strong>
                                </td>
                            </tr>
                        </thead>
                        <tbody id="rename_to">
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'New name' %}
                                </td>
                                <td width="102">
                                    <input type="text" id="e_rename">
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <input type="button" id="ok_edit_rename" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'OK' %}">
                                    <input id="query_rename_to_button" type="button"
                                        class="btn btn-secondary butt"
                                        name="Button"
                                        value="{% trans 'Cancel' %}">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td class="frams6">
                </td>
            </tr>
            <tr>
                <td class="frams4">
                    <div class="bor">
                    </div>
                </td>
                <td class="frams7">
                </td>
                <td class="frams3">
                </td>
            </tr>
        </tbody>
    </table>
    {# having query panel #}
    <table id="query_having" class="table table-borderless w-auto hide">
        <tbody>
            <tr>
                <td class="frams1" width="10px">
                </td>
                <td class="frams5" width="99%" >
                </td>
                <td class="frams2" width="10px">
                    <div class="bor">
                    </div>
                </td>
            </tr>
            <tr>
                <td class="frams8">
                </td>
                <td class="input_tab p-0">
                    <table class="table table-borderless bg-white mb-0 text-center">
                        <thead>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <strong>
                                        HAVING
                                    </strong>
                                </td>
                            </tr>
                        </thead>
                        <tbody id="rename_to">
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="hoperator" id="hoperator">
                                        <option value="---" selected="selected">
                                            ---
                                        </option>
                                        <option value="None" >
                                            None
                                        </option>
                                        <option value="sum" >
                                            SUM
                                        </option>
                                        <option value="min">
                                            MIN
                                        </option>
                                        <option value="max">
                                            MAX
                                        </option>
                                        <option value="avg">
                                            AVG
                                        </option>
                                        <option value="count">
                                            COUNT
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <tr>
                                    <td width="58" class="text-nowrap">
                                        {% trans 'Operator' %}
                                    </td>
                                    <td width="102">
                                        <select name="hrel_opt" id="hrel_opt">
                                            <option value="--" selected="selected">
                                                --
                                            </option>
                                            <option value="=">
                                                =
                                            </option>
                                            <option value="&gt;">
                                                &gt;
                                            </option>
                                            <option value="&lt;">
                                                &lt;
                                            </option>
                                            <option value="&gt;=">
                                                &gt;=
                                            </option>
                                            <option value="&lt;=">
                                                &lt;=
                                            </option>
                                            <option value="NOT">
                                                NOT
                                            </option>
                                            <option value="IN">
                                                IN
                                            </option>
                                            <option value="EXCEPT">
                                                {% trans 'Except' %}
                                            </option>
                                            <option value="NOT IN">
                                                NOT IN
                                            </option>
                                        </select>
                                    </td>
                            </tr>
                            <tr>
                                <td class="text-nowrap">
                                    {% trans 'Value' %}
                                    <br>
                                    {% trans 'subquery' %}
                                </td>
                                <td>
                                    <textarea id="hQuery" cols="18">
                                    </textarea>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <input type="button" id="ok_edit_having" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'OK' %}">
                                    <input id="query_having_button" type="button"
                                        class="btn btn-secondary butt"
                                        name="Button"
                                        value="{% trans 'Cancel' %}">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td class="frams6">
                </td>
            </tr>
            <tr>
                <td class="frams4">
                    <div class="bor">
                    </div>
                </td>
                <td class="frams7">
                </td>
                <td class="frams3">
                </td>
            </tr>
        </tbody>
    </table>
    {# aggregate query pane #}
    <table id="query_Aggregate" class="table table-borderless w-auto hide">
        <tbody>
            <tr>
                <td class="frams1" width="10px">
                </td>
                <td class="frams5" width="99%" >
                </td>
                <td class="frams2" width="10px">
                    <div class="bor">
                    </div>
                </td>
            </tr>
            <tr>
                <td class="frams8">
                </td>
                <td class="input_tab p-0">
                    <table class="table table-borderless bg-white mb-0 text-center">
                        <thead>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <strong>
                                        {% trans 'Aggregate' %}
                                    </strong>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="operator" id="e_operator">
                                        <option value="---" selected="selected">
                                            ---
                                        </option>
                                        <option value="sum" >
                                            SUM
                                        </option>
                                        <option value="min">
                                            MIN
                                        </option>
                                        <option value="max">
                                            MAX
                                        </option>
                                        <option value="avg">
                                            AVG
                                        </option>
                                        <option value="count">
                                            COUNT
                                        </option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <input type="button" id="ok_edit_Aggr" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'OK' %}">
                                    <input id="query_Aggregate_Button" type="button"
                                        class="btn btn-secondary butt"
                                        name="Button"
                                        value="{% trans 'Cancel' %}">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td class="frams6">
                </td>
            </tr>
            <tr>
                <td class="frams4">
                    <div class="bor">
                    </div>
                </td>
                <td class="frams7">
                </td>
                <td class="frams3">
                </td>
            </tr>
        </tbody>
    </table>
    {# where query pane #}
    <table id="query_where" class="table table-borderless w-auto hide">
        <tbody>
            <tr>
                <td class="frams1" width="10px">
                </td>
                <td class="frams5" width="99%" >
                </td>
                <td class="frams2" width="10px">
                    <div class="bor">
                    </div>
                </td>
            </tr>
            <tr>
                <td class="frams8">
                </td>
                <td class="input_tab p-0">
                    <table class="table table-borderless bg-white mb-0 text-center">
                        <thead>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <strong>
                                        WHERE
                                    </strong>
                                </td>
                            </tr>
                        </thead>
                        <tbody id="rename_to">
                            <tr>
                                <td width="58" class="text-nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="erel_opt" id="erel_opt">
                                        <option value="--" selected="selected">
                                            --
                                        </option>
                                        <option value="=" >
                                            =
                                        </option>
                                        <option value="&gt;">
                                            &gt;
                                        </option>
                                        <option value="&lt;">
                                            &lt;
                                        </option>
                                        <option value="&gt;=">
                                            &gt;=
                                        </option>
                                        <option value="&lt;=">
                                            &lt;=
                                        </option>
                                        <option value="NOT">
                                            NOT
                                        </option>
                                        <option value="IN">
                                            IN
                                        </option>
                                        <option value="EXCEPT">
                                            {% trans 'Except' %}
                                        </option>
                                        <option value="NOT IN">
                                            NOT IN
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-nowrap">
                                    {% trans 'Value' %}
                                    <br>
                                    {% trans 'subquery' %}
                                </td>
                                <td>
                                    <textarea id="eQuery" cols="18"></textarea>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text-center text-nowrap">
                                    <input type="button" id="ok_edit_where" class="btn btn-secondary butt"
                                        name="Button" value="{% trans 'OK' %}">
                                    <input id="query_where_button" type="button" class="btn btn-secondary butt" name="Button"
                                           value="{% trans 'Cancel' %}">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td class="frams6">
                </td>
            </tr>
            <tr>
                <td class="frams4">
                    <div class="bor">
                    </div>
                </td>
                <td class="frams7">
                </td>
                <td class="frams3">
                </td>
            </tr>
        </tbody>
    </table>
    {# query details #}
    <div class="panel">
        <div class="clearfloat"></div>
        <div id="ab"></div>
        <div class="clearfloat"></div>
    </div>
    <a class="trigger" href="#">{% trans 'Active options' %}</a>
{% endif %}
<div id="PMA_disable_floating_menubar"></div>
<div class="modal fade" id="designerGoModal" tabindex="-1" aria-labelledby="designerGoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="designerGoModalLabel">{% trans 'Loading' %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans 'Cancel' %}"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="designerModalGoButton">{% trans 'Go' %}</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans 'Cancel' %}</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="designerPromptModal" tabindex="-1" aria-labelledby="designerPromptModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="designerPromptModalLabel">{% trans 'Loading' %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans 'Cancel' %}"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="designerModalYesButton">{% trans 'Yes' %}</button>
        <button type="button" class="btn btn-secondary" id="designerModalNoButton">{% trans 'No' %}</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans 'Cancel' %}</button>
      </div>
    </div>
  </div>
</div>
{% if visual_builder %}
  {{ include('modals/build_query.twig', {'get_db': get_db}) }}
{% endif %}
