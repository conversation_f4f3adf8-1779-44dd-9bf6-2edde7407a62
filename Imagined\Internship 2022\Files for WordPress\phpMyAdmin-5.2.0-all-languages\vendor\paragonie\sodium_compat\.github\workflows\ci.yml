name: CI

on: [push]

jobs:
  old:
    name: PHP ${{ matrix.php-versions }} Test on ${{ matrix.operating-system }}
    runs-on: ${{ matrix.operating-system }}
    strategy:
      fail-fast: false
      matrix:
        operating-system: ['ubuntu-18.04']
        php-versions: ['5.3', '5.4', '5.5', '5.6', '7.0']
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, intl
          ini-values: max_execution_time=600, memory_limit=256M, error_reporting=-1, display_errors=On
          coverage: none

      - name: Use Composer 1.x
        run: composer self-update --1

      - name: Install Composer dependencies
        uses: "ramsey/composer-install@v2"

      - name: PHPUnit tests
        run: vendor/bin/phpunit

  moderate-modern:
    name: PHP ${{ matrix.php-versions }} Test on ${{ matrix.operating-system }}
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-latest']
        php-versions: ['7.1', '7.2', '7.3', '7.4', '8.0', '8.1']

    continue-on-error: ${{ matrix.php-versions == '8.1' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, intl, sodium
          ini-values: error_reporting=-1, display_errors=On
          coverage: none

      - name: Install Composer dependencies (PHP < 8.1)
        if: ${{ matrix.php-versions != '8.1' }}
        uses: "ramsey/composer-install@v2"

      - name: Install Composer dependencies - ignore-platform-reqs (PHP 8.1)
        if: ${{ matrix.php-versions == '8.1' }}
        uses: "ramsey/composer-install@v2"
        with:
          composer-options: --ignore-platform-reqs

      - name: PHPUnit tests
        run: vendor/bin/phpunit
