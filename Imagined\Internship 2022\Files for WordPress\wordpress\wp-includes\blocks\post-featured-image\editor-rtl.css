/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder,
.wp-block-post-featured-image.wp-block-post-featured-image .components-resizable-box__container {
  border-radius: inherit;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder {
  justify-content: center;
  align-items: center;
  box-shadow: none;
  padding: 0;
  color: currentColor;
  background: transparent;
  min-height: 200px;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-form-file-upload,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-form-file-upload {
  display: none;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__preview,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__preview {
  position: absolute;
  top: 4px;
  left: 4px;
  bottom: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder::before,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border: 1px dashed currentColor;
  opacity: 0.4;
  pointer-events: none;
  border-radius: inherit;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__fieldset,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__fieldset {
  width: auto;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button {
  color: inherit;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  visibility: hidden;
  background: transparent;
  transition: all 0.1s linear;
}
@media (prefers-reduced-motion: reduce) {
  .wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button > svg,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button > svg {
  color: #fff;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__illustration,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__illustration {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  stroke: currentColor;
  stroke-dasharray: 3;
  opacity: 0.4;
}
.wp-block-post-featured-image.wp-block-post-featured-image[style*=height] .components-placeholder {
  min-height: 48px;
  min-width: 48px;
  height: 100%;
  width: 100%;
}
.wp-block-post-featured-image.wp-block-post-featured-image.is-selected .components-button.components-button {
  background: var(--wp-admin-theme-color);
  border-color: var(--wp-admin-theme-color);
  border-style: solid;
  color: #fff;
  opacity: 1;
  visibility: visible;
}

div[data-type="core/post-featured-image"] img {
  max-width: 100%;
  height: auto;
  display: block;
}