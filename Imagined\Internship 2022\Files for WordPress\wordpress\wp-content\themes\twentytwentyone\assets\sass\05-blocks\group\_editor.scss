.wp-block-group {
	// Start IE clearfix.
	// This hack is only necessary because we want to support IE11.
	// If we don't want to support IE11, then "display: flow-root" would suffice.
	display: block;
	clear: both;

	display: flow-root; // stylelint-disable-line declaration-block-no-duplicate-properties

	&:before,
	&:after {
		content: "";
		display: block;
		clear: both;
	}
	// End IE clearfix.

	&.has-background {
		padding: var(--global--spacing-vertical);

		[data-align="full"] & {
			margin-top: 0;
			margin-bottom: 0;
		}
	}

	// Block Styles
	&.is-style-twentytwentyone-border {
		border: calc(3 * var(--separator--height)) solid var(--global--color-border);
		padding: var(--global--spacing-vertical);

		.wp-block-group__inner-container > [data-align="full"] {
			max-width: calc(var(--responsive--alignfull-width) + (2 * var(--global--spacing-vertical)));
			width: calc(var(--responsive--alignfull-width) + (2 * var(--global--spacing-vertical)));
			margin-left: calc(-1 * var(--global--spacing-vertical));
		}
	}

	@include innerblock-margin-clear(".wp-block-group__inner-container");
}

.wp-block-group .wp-block-group.has-background > .block-editor-block-list__layout > [data-align="full"] {
	margin: 0;
	width: 100%;
}
