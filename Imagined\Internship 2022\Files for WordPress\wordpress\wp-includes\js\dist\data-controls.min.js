/*! This file is auto-generated */
!function(){"use strict";var t={n:function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return t.d(e,{a:e}),e},d:function(n,e){for(var r in e)t.o(e,r)&&!t.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:e[r]})},o:function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},n={};t.r(n),t.d(n,{__unstableAwaitPromise:function(){return d},apiFetch:function(){return u},controls:function(){return p},dispatch:function(){return a},select:function(){return s},syncSelect:function(){return l}});var e=window.wp.apiFetch,r=t.n(e),o=window.wp.data,i=window.wp.deprecated,c=t.n(i);function u(t){return{type:"API_FETCH",request:t}}function s(){return c()("`select` control in `@wordpress/data-controls`",{since:"5.7",alternative:"built-in `resolveSelect` control in `@wordpress/data`"}),o.controls.resolveSelect(...arguments)}function l(){return c()("`syncSelect` control in `@wordpress/data-controls`",{since:"5.7",alternative:"built-in `select` control in `@wordpress/data`"}),o.controls.select(...arguments)}function a(){return c()("`dispatch` control in `@wordpress/data-controls`",{since:"5.7",alternative:"built-in `dispatch` control in `@wordpress/data`"}),o.controls.dispatch(...arguments)}const d=function(t){return{type:"AWAIT_PROMISE",promise:t}},p={AWAIT_PROMISE:t=>{let{promise:n}=t;return n},API_FETCH(t){let{request:n}=t;return r()(n)}};(window.wp=window.wp||{}).dataControls=n}();