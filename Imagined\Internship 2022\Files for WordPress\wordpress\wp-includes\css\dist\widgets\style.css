/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
:root {
  --wp-admin-theme-color: #007cba;
  --wp-admin-theme-color--rgb: 0, 124, 186;
  --wp-admin-theme-color-darker-10: #006ba1;
  --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
  --wp-admin-theme-color-darker-20: #005a87;
  --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  :root {
    --wp-admin-border-width-focus: 1.5px;
  }
}

.wp-block-legacy-widget__edit-form {
  background: #fff;
  border-radius: 2px;
  border: 1px solid #1e1e1e;
  padding: 11px;
}
.wp-block-legacy-widget__edit-form:not([hidden]) {
  display: flow-root;
}
.wp-block-legacy-widget__edit-form .wp-block-legacy-widget__edit-form-title {
  color: #000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside {
  border: none;
  box-shadow: none;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside p {
  margin: 8px 0;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside label {
  font-size: 13px;
  line-height: 2.1;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside label,
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input,
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside a {
  font-family: system-ui;
  font-weight: normal;
  color: #000;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=text],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=password],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=date],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=datetime],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=datetime-local],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=email],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=month],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=number],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=search],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=tel],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=time],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=url],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside input[type=week],
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside select {
  font-family: system-ui;
  background-color: transparent;
  box-sizing: border-box;
  border: 1px solid #757575;
  border-radius: 3px;
  box-shadow: none;
  color: #000;
  display: block;
  margin: 0;
  width: 100%;
  font-size: 13px;
  font-weight: normal;
  line-height: 1;
  min-height: 30px;
  padding-left: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
}
.wp-block-legacy-widget__edit-form .widget-inside.widget-inside select {
  padding-left: 4px;
}
.wp-block-legacy-widget__edit-form .widget.open,
.wp-block-legacy-widget__edit-form .widget.open:focus-within {
  z-index: 0;
}

.wp-block-legacy-widget__edit-form.wp-block-legacy-widget__edit-form,
.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview {
  color: #000;
}

.wp-block-legacy-widget__edit-preview,
.wp-block-legacy-widget__edit-no-preview {
  cursor: pointer;
}
.wp-block-legacy-widget__edit-preview:hover::after,
.wp-block-legacy-widget__edit-no-preview:hover::after {
  border-radius: 2px;
  border: 1px solid #949494;
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.wp-block-legacy-widget__edit-preview.is-offscreen {
  left: -9999px;
  position: absolute;
  top: 0;
  width: 100%;
}

.wp-block-legacy-widget__edit-preview-iframe {
  overflow: hidden;
  width: 100%;
}

.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview {
  background: #f0f0f0;
  padding: 8px 12px;
}
.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview, .wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview h3, .wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview p {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
}
.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview h3 {
  font-size: 14px;
  font-weight: 600;
  margin: 4px 0;
}
.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview.wp-block-legacy-widget__edit-no-preview p {
  margin: 4px 0;
}

.wp-block-legacy-widget-inspector-card {
  padding: 0 16px 16px 52px;
}

.interface-complementary-area .wp-block-legacy-widget-inspector-card__name {
  margin: 0 0 5px;
  font-weight: 500;
}

.is-selected .wp-block-legacy-widget__container {
  padding: 8px 12px;
  min-height: 50px;
}

.components-popover__content .wp-block-legacy-widget__edit-form {
  min-width: 400px;
}

.wp-block-legacy-widget .components-base-control {
  width: 100%;
}
.wp-block-legacy-widget .components-select-control__input {
  padding: 0;
  font-family: system-ui;
}

.wp-block-widget-group.has-child-selected::after {
  border-radius: 2px;
  border: 1px solid var(--wp-admin-theme-color);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.wp-block-widget-group .widget-title {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 18px;
  font-weight: 600;
}

.wp-block-widget-group__placeholder .block-editor-inserter {
  width: 100%;
}

.is-dark-theme .wp-block-widget-group__placeholder .block-editor-button-block-appender {
  box-shadow: inset 0 0 0 1px #1e1e1e;
  color: #1e1e1e;
}