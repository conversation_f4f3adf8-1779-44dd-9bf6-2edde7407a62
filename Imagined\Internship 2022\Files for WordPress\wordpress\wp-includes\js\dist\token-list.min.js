/*! This file is auto-generated */
!function(){"use strict";var r={d:function(t,e){for(var a in e)r.o(e,a)&&!r.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},o:function(r,t){return Object.prototype.hasOwnProperty.call(r,t)}},t={};r.d(t,{default:function(){return a}});var e=window.lodash;class a{constructor(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.value=r,this._currentValue,this._valueAsArray}entries(){return this._valueAsArray.entries(...arguments)}forEach(){return this._valueAsArray.forEach(...arguments)}keys(){return this._valueAsArray.keys(...arguments)}values(){return this._valueAsArray.values(...arguments)}get value(){return this._currentValue}set value(r){r=String(r),this._valueAsArray=(0,e.uniq)((0,e.compact)(r.split(/\s+/g))),this._currentValue=this._valueAsArray.join(" ")}get length(){return this._valueAsArray.length}toString(){return this.value}*[Symbol.iterator](){return yield*this._valueAsArray}item(r){return this._valueAsArray[r]}contains(r){return-1!==this._valueAsArray.indexOf(r)}add(){for(var r=arguments.length,t=new Array(r),e=0;e<r;e++)t[e]=arguments[e];this.value+=" "+t.join(" ")}remove(){for(var r=arguments.length,t=new Array(r),a=0;a<r;a++)t[a]=arguments[a];this.value=(0,e.without)(this._valueAsArray,...t).join(" ")}toggle(r,t){return void 0===t&&(t=!this.contains(r)),t?this.add(r):this.remove(r),t}replace(r,t){return!!this.contains(r)&&(this.remove(r),this.add(t),!0)}supports(){return!0}}(window.wp=window.wp||{}).tokenList=t.default}();