<tr class="noclick">
  <td class="text-center">
    {{ column.Field_title|raw }}
    <input type="hidden" name="fields_name[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ column.Field }}">
  </td>

  {% if show_field_types_in_data_edit_view %}
    <td class="text-center{{ column.wrap }}">
      <span class="column_type" dir="ltr">{{ column.pma_type }}</span>
    </td>
  {% endif %}

  {% if show_function_fields %}
    {% if is_column_binary %}
      <td class="text-center">{% trans 'Binary' %}</td>
    {% elseif 'enum' in column.True_Type or 'set' in column.True_Type %}
      <td class="text-center">--</td>
    {% else %}
      <td>
        <select name="funcs[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')" id="field_{{ id_index }}_1">
          {{ function_options|raw }}
        </select>
      </td>
    {% endif %}
  {% endif %}

  <td>
    {% if column.Null|upper == 'YES' and not read_only %}
      <input type="hidden" name="fields_null_prev[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]"{{ real_null_value and not column.first_timestamp ? ' value="on"' }}>
      <input type="checkbox" class="checkbox_null" name="fields_null[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" id="field_{{ id_index }}_2" aria-label="{% trans 'Use the NULL value for this column.' %}"{{ real_null_value ? ' checked' }}>
      <input type="hidden" class="nullify_code" name="nullify_code[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ nullify_code }}">
      <input type="hidden" class="hashed_field" name="hashed_field[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ column.Field_md5 }}">
      <input type="hidden" class="multi_edit" name="multi_edit[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ ('[multi_edit][' ~ row_id ~ ']')|escape_js_string }}">
    {% endif %}
  </td>

  <td data-type="{{ type }}" data-decimals="{{ decimals }}">
    {# Will be used by table/change.js to set the default value for the "Continue insertion" feature. #}
    <span class="default_value hide">{{ special_chars|raw }}</span>

    {% if transformed_value is not empty %}
      {{ transformed_value|raw }}
    {% else %}
      {% if is_value_foreign_link %}
        {{ backup_field|raw }}
        <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="foreign">
        <input type="text" name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" class="textfield" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')" id="field_{{ id_index }}_3" value="{{ data }}">
        <a class="ajax browse_foreign" href="{{ url('/browse-foreigners') }}" data-post="{{ get_common({'db': db, 'table': table, 'field': column.Field, 'rownumber': row_id, 'data': data}) }}">{{ get_icon('b_browse', 'Browse foreign values'|trans) }}</a>
      {% elseif foreign_dropdown is not empty %}
        {{ backup_field|raw }}
        <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ column.is_binary ? 'hex' : 'foreign' }}">
        <select name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" class="textfield" id="field_{{ id_index }}_3" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')">
          {{ foreign_dropdown|raw }}
        </select>
      {% elseif (longtext_double_textarea and 'longtext' in column.pma_type) or 'json' in column.pma_type or 'text' in column.pma_type %}
        {{ backup_field|raw }}
        <textarea name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" id="field_{{ id_index }}_3" data-type="{{ data_type }}" dir="{{ text_dir }}" rows="{{ textarea_rows }}" cols="{{ textarea_cols }}"
          {{- max_length ? ' data-maxlength="' ~  max_length ~ '"' }}{{ column.is_char ? ' class="char charField"' }} onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')">
          {#- We need to duplicate the first \n or otherwise we will lose the first newline entered in a VARCHAR or TEXT column -#}
          {{- special_chars starts with "\r\n" ? "\n" }}{{ special_chars|raw -}}
        </textarea>
        {% if 'text' in column.pma_type and special_chars|length > 32000 %}
          </td>
          <td>
          {% trans 'Because of its length,<br> this column might not be editable.' %}
        {% endif %}
      {% elseif column.pma_type == 'enum' %}
        {{ backup_field|raw }}
        <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="enum">
        {% if column.Type|length > 20 %}
          <select name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" class="textfield" id="field_{{ id_index }}_3" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')">
            <option value=""></option>
            {% for enum_value in column.values %}
              <option value="{{ enum_value.plain }}"{{ enum_value.plain == enum_selected_value ? ' selected' }}>{{ enum_value.plain }}</option>
            {% endfor %}
          </select>
        {% else %}
          {% for enum_value in column.values %}
            <input type="radio" name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="{{ enum_value.plain }}" class="textfield" id="field_{{ id_index }}_3_{{ loop.index0 }}" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')"{{ enum_value.plain == enum_selected_value ? ' checked' }}>
            <label for="field_{{ id_index }}_3_{{ loop.index0 }}">{{ enum_value.plain }}</label>
          {% endfor %}
        {% endif %}
      {% elseif column.pma_type == 'set' %}
        {{ backup_field|raw }}
        <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="set">
        <select name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" class="textfield" size="{{ set_select_size }}" id="field_{{ id_index }}_3" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')" multiple>
          {% for set_value in set_values %}
            <option value="{{ set_value.plain }}"{{ set_value.plain in data|split(',') ? ' selected' }}>{{ set_value.plain }}</option>
          {% endfor %}
        </select>
      {% elseif column.is_binary or column.is_blob %}
        {% if is_column_protected_blob %}
          {% trans 'Binary - do not edit' %}
          ({{ blob_value }} {{ blob_value_unit }})
          <input type="hidden" name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="">
          <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="protected">
        {% elseif column.is_blob or (column.len > limit_chars) %}
          {{ backup_field|raw }}
          <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="hex">
          <textarea name="fields[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" id="field_{{ id_index }}_3" data-type="HEX" dir="{{ text_dir }}" rows="{{ textarea_rows }}" cols="{{ textarea_cols }}"
            {{- max_length ? ' data-maxlength="' ~  max_length ~ '"' }}{{ column.is_char ? ' class="char charField"' }} onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')">
            {#- We need to duplicate the first \n or otherwise we will lose the first newline entered in a VARCHAR or TEXT column -#}
            {{- special_chars starts with "\r\n" ? "\n" }}{{ special_chars|raw -}}
          </textarea>
        {% else %}
          {{ backup_field|raw }}
          <input type="hidden" name="fields_type[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" value="hex">
          {{ input_field_html|raw }}
        {% endif %}
        {% if is_upload and column.is_blob %}
          <br>
          {# We don't want to prevent users from using browser's default drag-drop feature on some page(s), so we add noDragDrop class to the input #}
          <input type="file" name="fields_upload[multi_edit][{{ row_id }}][{{ column.Field_md5 }}]" class="textfield noDragDrop" id="field_{{ id_index }}_3" size="10" onchange="return verificationsAfterFieldChange('{{ column.Field_md5|escape_js_string }}', '{{ row_id|escape_js_string }}', '{{ column.pma_type }}')">
          {{ max_upload_size }}
        {% endif %}
        {{ select_option_for_upload|raw }}
      {% else %}
        {{ value|raw }}
      {% endif %}

      {% if column.pma_type in gis_data_types %}
        <span class="open_gis_editor" data-row-id="{{ row_id }}">{{ link_or_button('#', null, get_icon('b_edit', 'Edit/Insert'|trans), [], '_blank') }}</span>
      {% endif %}
    {% endif %}
  </td>
</tr>
