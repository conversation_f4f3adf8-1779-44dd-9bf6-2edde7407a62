{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/post-date", "title": "Post Date", "category": "theme", "description": "Add the date of this post.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}, "format": {"type": "string"}, "isLink": {"type": "boolean", "default": false}}, "usesContext": ["postId", "postType", "queryId"], "supports": {"html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}}}