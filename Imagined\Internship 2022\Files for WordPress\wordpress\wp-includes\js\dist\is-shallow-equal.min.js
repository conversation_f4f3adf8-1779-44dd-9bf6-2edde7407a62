/*! This file is auto-generated */
!function(){"use strict";var r={d:function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o:function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},r:function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})}},t={};function e(r,t){if(r===t)return!0;const e=Object.keys(r),n=Object.keys(t);if(e.length!==n.length)return!1;let o=0;for(;o<e.length;){const n=e[o],u=r[n];if(void 0===u&&!t.hasOwnProperty(n)||u!==t[n])return!1;o++}return!0}function n(r,t){if(r===t)return!0;if(r.length!==t.length)return!1;for(let e=0,n=r.length;e<n;e++)if(r[e]!==t[e])return!1;return!0}function o(r,t){if(r&&t){if(r.constructor===Object&&t.constructor===Object)return e(r,t);if(Array.isArray(r)&&Array.isArray(t))return n(r,t)}return r===t}r.r(t),r.d(t,{default:function(){return o},isShallowEqualArrays:function(){return n},isShallowEqualObjects:function(){return e}}),(window.wp=window.wp||{}).isShallowEqual=t}();