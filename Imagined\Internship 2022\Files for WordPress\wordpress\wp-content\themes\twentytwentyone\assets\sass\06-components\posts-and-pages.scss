.sticky {
	// This class is required to pass ThemeCheck.
}

.no-results.not-found > *:first-child {
	margin-bottom: calc(3 * var(--global--spacing-vertical));
}

// Styling for wp_link_pages.
.page-links {
	clear: both;

	.post-page-numbers {
		display: inline-block;
		margin-left: calc(0.66 * var(--global--spacing-unit));
		margin-right: calc(0.66 * var(--global--spacing-unit));
		min-width: 44px;
		min-height: 44px;

		&:first-child {
			margin-left: 0;
		}
	}
}
