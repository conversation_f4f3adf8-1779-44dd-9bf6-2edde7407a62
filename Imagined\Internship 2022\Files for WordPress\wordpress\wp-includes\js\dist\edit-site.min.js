/*! This file is auto-generated */
!function(){var e={6411:function(e,t){var n,r,a;
/*!
	autosize 4.0.4
	license: MIT
	http://www.jacklmoore.com/autosize
*/r=[e,t],n=function(e,t){"use strict";var n,r,a="function"==typeof Map?new Map:(n=[],r=[],{has:function(e){return n.indexOf(e)>-1},get:function(e){return r[n.indexOf(e)]},set:function(e,t){-1===n.indexOf(e)&&(n.push(e),r.push(t))},delete:function(e){var t=n.indexOf(e);t>-1&&(n.splice(t,1),r.splice(t,1))}}),l=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){l=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function o(e){if(e&&e.nodeName&&"TEXTAREA"===e.nodeName&&!a.has(e)){var t=null,n=null,r=null,o=function(){e.clientWidth!==n&&d()},i=function(t){window.removeEventListener("resize",o,!1),e.removeEventListener("input",d,!1),e.removeEventListener("keyup",d,!1),e.removeEventListener("autosize:destroy",i,!1),e.removeEventListener("autosize:update",d,!1),Object.keys(t).forEach((function(n){e.style[n]=t[n]})),a.delete(e)}.bind(e,{height:e.style.height,resize:e.style.resize,overflowY:e.style.overflowY,overflowX:e.style.overflowX,wordWrap:e.style.wordWrap});e.addEventListener("autosize:destroy",i,!1),"onpropertychange"in e&&"oninput"in e&&e.addEventListener("keyup",d,!1),window.addEventListener("resize",o,!1),e.addEventListener("input",d,!1),e.addEventListener("autosize:update",d,!1),e.style.overflowX="hidden",e.style.wordWrap="break-word",a.set(e,{destroy:i,update:d}),s()}function s(){var n=window.getComputedStyle(e,null);"vertical"===n.resize?e.style.resize="none":"both"===n.resize&&(e.style.resize="horizontal"),t="content-box"===n.boxSizing?-(parseFloat(n.paddingTop)+parseFloat(n.paddingBottom)):parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth),isNaN(t)&&(t=0),d()}function c(t){var n=e.style.width;e.style.width="0px",e.offsetWidth,e.style.width=n,e.style.overflowY=t}function u(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}function m(){if(0!==e.scrollHeight){var r=u(e),a=document.documentElement&&document.documentElement.scrollTop;e.style.height="",e.style.height=e.scrollHeight+t+"px",n=e.clientWidth,r.forEach((function(e){e.node.scrollTop=e.scrollTop})),a&&(document.documentElement.scrollTop=a)}}function d(){m();var t=Math.round(parseFloat(e.style.height)),n=window.getComputedStyle(e,null),a="content-box"===n.boxSizing?Math.round(parseFloat(n.height)):e.offsetHeight;if(a<t?"hidden"===n.overflowY&&(c("scroll"),m(),a="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight):"hidden"!==n.overflowY&&(c("hidden"),m(),a="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight),r!==a){r=a;var o=l("autosize:resized");try{e.dispatchEvent(o)}catch(e){}}}}function i(e){var t=a.get(e);t&&t.destroy()}function s(e){var t=a.get(e);t&&t.update()}var c=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((c=function(e){return e}).destroy=function(e){return e},c.update=function(e){return e}):((c=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],(function(e){return o(e,t)})),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],i),e},c.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],s),e}),t.default=c,e.exports=t.default},void 0===(a="function"==typeof n?n.apply(t,r):n)||(e.exports=a)},4403:function(e,t){var n;
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var l=typeof n;if("string"===l||"number"===l)e.push(n);else if(Array.isArray(n)){if(n.length){var o=a.apply(null,n);o&&e.push(o)}}else if("object"===l)if(n.toString===Object.prototype.toString)for(var i in n)r.call(n,i)&&n[i]&&e.push(i);else e.push(n.toString())}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()},4827:function(e){e.exports=function(e,t,n){return((n=window.getComputedStyle)?n(e):e.currentStyle)[t.replace(/-(\w)/gi,(function(e,t){return t.toUpperCase()}))]}},8981:function(e,t){var n,r,a;r=[],void 0===(a="function"==typeof(n=function(){return function e(t,n,r){var a,l,o=window,i="application/octet-stream",s=r||i,c=t,u=!n&&!r&&c,m=document.createElement("a"),d=function(e){return String(e)},p=o.Blob||o.MozBlob||o.WebKitBlob||d,_=n||"download";if(p=p.call?p.bind(o):Blob,"true"===String(this)&&(s=(c=[c,s])[0],c=c[1]),u&&u.length<2048&&(_=u.split("/").pop().split("?")[0],m.href=u,-1!==m.href.indexOf(u))){var h=new XMLHttpRequest;return h.open("GET",u,!0),h.responseType="blob",h.onload=function(t){e(t.target.response,_,i)},setTimeout((function(){h.send()}),0),h}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(c)){if(!(c.length>2096103.424&&p!==d))return navigator.msSaveBlob?navigator.msSaveBlob(v(c),_):y(c);s=(c=v(c)).type||i}else if(/([\x80-\xff])/.test(c)){for(var g=0,f=new Uint8Array(c.length),E=f.length;g<E;++g)f[g]=c.charCodeAt(g);c=new p([f],{type:s})}function v(e){for(var t=e.split(/[:;,]/),n=t[1],r=("base64"==t[2]?atob:decodeURIComponent)(t.pop()),a=r.length,l=0,o=new Uint8Array(a);l<a;++l)o[l]=r.charCodeAt(l);return new p([o],{type:n})}function y(e,t){if("download"in m)return m.href=e,m.setAttribute("download",_),m.className="download-js-link",m.innerHTML="downloading...",m.style.display="none",document.body.appendChild(m),setTimeout((function(){m.click(),document.body.removeChild(m),!0===t&&setTimeout((function(){o.URL.revokeObjectURL(m.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,i)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var n=document.createElement("iframe");document.body.appendChild(n),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,i)),n.src=e,setTimeout((function(){document.body.removeChild(n)}),333)}if(a=c instanceof p?c:new p([c],{type:s}),navigator.msSaveBlob)return navigator.msSaveBlob(a,_);if(o.URL)y(o.URL.createObjectURL(a),!0);else{if("string"==typeof a||a.constructor===d)try{return y("data:"+s+";base64,"+o.btoa(a))}catch(e){return y("data:"+s+","+encodeURIComponent(a))}(l=new FileReader).onload=function(e){y(this.result)},l.readAsDataURL(a)}return!0}})?n.apply(t,r):n)||(e.exports=a)},9894:function(e,t,n){var r=n(4827);e.exports=function(e){var t=r(e,"line-height"),n=parseFloat(t,10);if(t===n+""){var a=e.style.lineHeight;e.style.lineHeight=t+"em",t=r(e,"line-height"),n=parseFloat(t,10),a?e.style.lineHeight=a:delete e.style.lineHeight}if(-1!==t.indexOf("pt")?(n*=4,n/=3):-1!==t.indexOf("mm")?(n*=96,n/=25.4):-1!==t.indexOf("cm")?(n*=96,n/=2.54):-1!==t.indexOf("in")?n*=96:-1!==t.indexOf("pc")&&(n*=16),n=Math.round(n),"normal"===t){var l=e.nodeName,o=document.createElement(l);o.innerHTML="&nbsp;","TEXTAREA"===l.toUpperCase()&&o.setAttribute("rows","1");var i=r(e,"font-size");o.style.fontSize=i,o.style.padding="0px",o.style.border="0px";var s=document.body;s.appendChild(o),n=o.offsetHeight,s.removeChild(o)}return n}},5372:function(e,t,n){"use strict";var r=n(9567);function a(){}function l(){}l.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,l,o){if(o!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:l,resetWarningCache:a};return n.PropTypes=n,n}},2652:function(e,t,n){e.exports=n(5372)()},9567:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5438:function(e,t,n){"use strict";var r,a=this&&this.__extends||(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l=this&&this.__assign||Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},o=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&(n[r[a]]=e[r[a]])}return n};t.__esModule=!0;var i=n(9196),s=n(2652),c=n(6411),u=n(9894),m="autosize:resized",d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={lineHeight:null},t.textarea=null,t.onResize=function(e){t.props.onResize&&t.props.onResize(e)},t.updateLineHeight=function(){t.textarea&&t.setState({lineHeight:u(t.textarea)})},t.onChange=function(e){var n=t.props.onChange;t.currentValue=e.currentTarget.value,n&&n(e)},t}return a(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.maxRows,r=t.async;"number"==typeof n&&this.updateLineHeight(),"number"==typeof n||r?setTimeout((function(){return e.textarea&&c(e.textarea)})):this.textarea&&c(this.textarea),this.textarea&&this.textarea.addEventListener(m,this.onResize)},t.prototype.componentWillUnmount=function(){this.textarea&&(this.textarea.removeEventListener(m,this.onResize),c.destroy(this.textarea))},t.prototype.render=function(){var e=this,t=this.props,n=(t.onResize,t.maxRows),r=(t.onChange,t.style),a=(t.innerRef,t.children),s=o(t,["onResize","maxRows","onChange","style","innerRef","children"]),c=this.state.lineHeight,u=n&&c?c*n:null;return i.createElement("textarea",l({},s,{onChange:this.onChange,style:u?l({},r,{maxHeight:u}):r,ref:function(t){e.textarea=t,"function"==typeof e.props.innerRef?e.props.innerRef(t):e.props.innerRef&&(e.props.innerRef.current=t)}}),a)},t.prototype.componentDidUpdate=function(){this.textarea&&c.update(this.textarea)},t.defaultProps={rows:1,async:!1},t.propTypes={rows:s.number,maxRows:s.number,onResize:s.func,innerRef:s.any,async:s.bool},t}(i.Component);t.TextareaAutosize=i.forwardRef((function(e,t){return i.createElement(d,l({},e,{innerRef:t}))}))},773:function(e,t,n){"use strict";var r=n(5438);t.Z=r.TextareaAutosize},9196:function(e){"use strict";e.exports=window.React}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r].call(l.exports,l,l.exports,n),l.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){"use strict";n.r(r),n.d(r,{PluginMoreMenuItem:function(){return ho},PluginSidebar:function(){return po},PluginSidebarMoreMenuItem:function(){return _o},__experimentalMainDashboardButton:function(){return Aa},__experimentalNavigationToggle:function(){return Da},initializeEditor:function(){return fo},reinitializeEditor:function(){return go}});var e={};n.r(e),n.d(e,{disableComplementaryArea:function(){return j},enableComplementaryArea:function(){return W},pinItem:function(){return Y},setFeatureDefaults:function(){return Z},setFeatureValue:function(){return K},toggleFeature:function(){return X},unpinItem:function(){return q}});var t={};n.r(t),n.d(t,{getActiveComplementaryArea:function(){return Q},isFeatureActive:function(){return ee},isItemPinned:function(){return J}});var a={};n.r(a),n.d(a,{__experimentalSetPreviewDeviceType:function(){return Me},addTemplate:function(){return Re},closeGeneralSidebar:function(){return je},openGeneralSidebar:function(){return We},openNavigationPanelToMenu:function(){return De},removeTemplate:function(){return Ae},revertTemplate:function(){return $e},setHomeTemplateId:function(){return Le},setIsInserterOpened:function(){return He},setIsListViewOpened:function(){return Ue},setIsNavigationPanelOpened:function(){return Fe},setNavigationPanelActiveMenu:function(){return Oe},setPage:function(){return Ve},setTemplate:function(){return Be},setTemplatePart:function(){return ze},switchEditorMode:function(){return Ye},toggleFeature:function(){return Pe},updateSettings:function(){return Ge}});var l={};n.r(l),n.d(l,{__experimentalGetInsertionPoint:function(){return gt},__experimentalGetPreviewDeviceType:function(){return rt},__unstableGetPreference:function(){return tt},getCanUserCreateMedia:function(){return at},getCurrentTemplateNavigationPanelSubMenu:function(){return pt},getCurrentTemplateTemplateParts:function(){return Et},getEditedPostId:function(){return ut},getEditedPostType:function(){return ct},getEditorMode:function(){return vt},getHomeTemplateId:function(){return it},getNavigationPanelActiveMenu:function(){return dt},getPage:function(){return mt},getReusableBlocks:function(){return lt},getSettings:function(){return ot},isFeatureActive:function(){return nt},isInserterOpened:function(){return ht},isListViewOpened:function(){return ft},isNavigationOpened:function(){return _t}});var o=window.wp.element,i=window.wp.blocks,s=window.wp.blockLibrary,c=window.wp.data,u=window.wp.coreData,m=window.wp.editor,d=window.wp.preferences,p=window.wp.i18n,_=window.wp.viewport,h=window.wp.url,g=window.wp.hooks,f=window.wp.mediaUtils;(0,g.addFilter)("editor.MediaUpload","core/edit-site/components/media-upload",(()=>f.MediaUpload));const E="core/edit-site",v="uncategorized",y=["index","singular","archive","single","page","home","404","search","author","category","taxonomy","date","tag","attachment","single-post","front-page"],b=["page-home"],w=["post-","author-","single-post-","tag-"],S=["page-"],x={singular:["single","page"],index:["archive","404","search","singular","home"],home:["front-page"]},k="root",C="templates",T="templates-general",N=[{area:"header",menu:"template-parts-headers",title:(0,p.__)("headers")},{area:"footer",menu:"template-parts-footers",title:(0,p.__)("footers")},{area:"sidebar",menu:"template-parts-sidebars",title:(0,p.__)("sidebars")},{area:v,menu:"template-parts-general",title:(0,p.__)("general")}];var I=(0,c.combineReducers)({deviceType:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Desktop",t=arguments.length>1?arguments[1]:void 0;return"SET_PREVIEW_DEVICE_TYPE"===t.type?t.deviceType:e},settings:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return"UPDATE_SETTINGS"===t.type?{...e,...t.settings}:e},editedPost:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_TEMPLATE":case"SET_PAGE":return{type:"wp_template",id:t.templateId,page:t.page};case"SET_TEMPLATE_PART":return{type:"wp_template_part",id:t.templatePartId}}return e},homeTemplateId:function(e,t){return"SET_HOME_TEMPLATE"===t.type?t.homeTemplateId:e},navigationPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{menu:k,isOpen:!1},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_NAVIGATION_PANEL_ACTIVE_MENU":return{...e,menu:t.menu};case"OPEN_NAVIGATION_PANEL_TO_MENU":return{...e,isOpen:!0,menu:t.menu};case"SET_IS_NAVIGATION_PANEL_OPENED":return{...e,menu:t.isOpen?e.menu:k,isOpen:t.isOpen};case"SET_IS_LIST_VIEW_OPENED":return{...e,menu:e.isOpen&&t.isOpen?k:e.menu,isOpen:!t.isOpen&&e.isOpen};case"SET_IS_INSERTER_OPENED":return{...e,menu:e.isOpen&&t.value?k:e.menu,isOpen:!t.value&&e.isOpen}}return e},blockInserterPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_NAVIGATION_PANEL_TO_MENU":return!1;case"SET_IS_NAVIGATION_PANEL_OPENED":case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},listViewPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_NAVIGATION_PANEL_TO_MENU":return!1;case"SET_IS_NAVIGATION_PANEL_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e}}),P=window.wp.apiFetch,M=n.n(P),B=window.wp.deprecated,R=n.n(B),A=window.wp.notices;function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},z.apply(this,arguments)}var L=n(4403),V=n.n(L),O=window.wp.components,D=window.wp.primitives;var F=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"}));var H=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"}));var G=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"}));var U=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),$=window.lodash;const W=(e,t)=>n=>{let{registry:r}=n;t&&r.dispatch(d.store).set(e,"complementaryArea",t)},j=e=>t=>{let{registry:n}=t;n.dispatch(d.store).set(e,"complementaryArea",null)},Y=(e,t)=>n=>{let{registry:r}=n;if(!t)return;const a=r.select(d.store).get(e,"pinnedItems");!0!==(null==a?void 0:a[t])&&r.dispatch(d.store).set(e,"pinnedItems",{...a,[t]:!0})},q=(e,t)=>n=>{let{registry:r}=n;if(!t)return;const a=r.select(d.store).get(e,"pinnedItems");r.dispatch(d.store).set(e,"pinnedItems",{...a,[t]:!1})};function X(e,t){return function(n){let{registry:r}=n;R()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),r.dispatch(d.store).toggle(e,t)}}function K(e,t,n){return function(r){let{registry:a}=r;R()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),a.dispatch(d.store).set(e,t,!!n)}}function Z(e,t){return function(n){let{registry:r}=n;R()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),r.dispatch(d.store).setDefaults(e,t)}}const Q=(0,c.createRegistrySelector)((e=>(t,n)=>e(d.store).get(n,"complementaryArea"))),J=(0,c.createRegistrySelector)((e=>(t,n,r)=>{var a;const l=e(d.store).get(n,"pinnedItems");return null===(a=null==l?void 0:l[r])||void 0===a||a})),ee=(0,c.createRegistrySelector)((e=>(t,n,r)=>(R()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(d.store).get(n,r)))),te=(0,c.createReduxStore)("core/interface",{reducer:()=>{},actions:e,selectors:t});(0,c.register)(te);var ne=window.wp.plugins,re=(0,ne.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`})));var ae=re((function(e){let{as:t=O.Button,scope:n,identifier:r,icon:a,selectedIcon:l,...i}=e;const s=t,u=(0,c.useSelect)((e=>e(te).getActiveComplementaryArea(n)===r),[r]),{enableComplementaryArea:m,disableComplementaryArea:d}=(0,c.useDispatch)(te);return(0,o.createElement)(s,z({icon:l&&u?l:a,onClick:()=>{u?d(n):m(n,r)}},(0,$.omit)(i,["name"])))}));var le=e=>{let{smallScreenTitle:t,children:n,className:r,toggleButtonProps:a}=e;const l=(0,o.createElement)(ae,z({icon:U},a));return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"components-panel__header interface-complementary-area-header__small"},t&&(0,o.createElement)("span",{className:"interface-complementary-area-header__small-title"},t),l),(0,o.createElement)("div",{className:V()("components-panel__header","interface-complementary-area-header",r),tabIndex:-1},n,l))};function oe(e){let{name:t,as:n=O.Button,onClick:r,...a}=e;return(0,o.createElement)(O.Fill,{name:t},(e=>{let{onClick:t}=e;return(0,o.createElement)(n,z({onClick:r||t?function(){(r||$.noop)(...arguments),(t||$.noop)(...arguments)}:void 0},a))}))}oe.Slot=function(e){let{name:t,as:n=O.ButtonGroup,fillProps:r={},bubblesVirtually:a,...l}=e;return(0,o.createElement)(O.Slot,{name:t,bubblesVirtually:a,fillProps:r},(e=>{if((0,$.isEmpty)(o.Children.toArray(e)))return null;const t=[];o.Children.forEach(e,(e=>{let{props:{__unstableExplicitMenuItem:n,__unstableTarget:r}}=e;r&&n&&t.push(r)}));const r=o.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&t.includes(e.props.__unstableTarget)?null:e));return(0,o.createElement)(n,l,r)}))};var ie=oe;const se=e=>(0,o.createElement)(O.MenuItem,(0,$.omit)(e,["__unstableExplicitMenuItem","__unstableTarget"]));function ce(e){let{scope:t,target:n,__unstableExplicitMenuItem:r,...a}=e;return(0,o.createElement)(ae,z({as:e=>(0,o.createElement)(ie,z({__unstableExplicitMenuItem:r,__unstableTarget:`${t}/${n}`,as:se,name:`${t}/plugin-more-menu`},e)),role:"menuitemcheckbox",selectedIcon:F,name:n,scope:t},a))}function ue(e){let{scope:t,...n}=e;return(0,o.createElement)(O.Fill,z({name:`PinnedItems/${t}`},n))}ue.Slot=function(e){let{scope:t,className:n,...r}=e;return(0,o.createElement)(O.Slot,z({name:`PinnedItems/${t}`},r),(e=>!(0,$.isEmpty)(e)&&(0,o.createElement)("div",{className:V()(n,"interface-pinned-items")},e)))};var me=ue;function de(e){let{scope:t,children:n,className:r}=e;return(0,o.createElement)(O.Fill,{name:`ComplementaryArea/${t}`},(0,o.createElement)("div",{className:r},n))}const pe=re((function(e){let{children:t,className:n,closeLabel:r=(0,p.__)("Close plugin"),identifier:a,header:l,headerClassName:i,icon:s,isPinnable:u=!0,panelClassName:m,scope:d,name:h,smallScreenTitle:g,title:f,toggleShortcut:E,isActiveByDefault:v,showIconLabels:y=!1}=e;const{isActive:b,isPinned:w,activeArea:S,isSmall:x,isLarge:k}=(0,c.useSelect)((e=>{const{getActiveComplementaryArea:t,isItemPinned:n}=e(te),r=t(d);return{isActive:r===a,isPinned:n(d,a),activeArea:r,isSmall:e(_.store).isViewportMatch("< medium"),isLarge:e(_.store).isViewportMatch("large")}}),[a,d]);!function(e,t,n,r,a){const l=(0,o.useRef)(!1),i=(0,o.useRef)(!1),{enableComplementaryArea:s,disableComplementaryArea:u}=(0,c.useDispatch)(te);(0,o.useEffect)((()=>{r&&a&&!l.current?(u(e),i.current=!0):i.current&&!a&&l.current?(i.current=!1,s(e,t)):i.current&&n&&n!==t&&(i.current=!1),a!==l.current&&(l.current=a)}),[r,a,e,t,n])}(d,a,S,b,x);const{enableComplementaryArea:C,disableComplementaryArea:T,pinItem:N,unpinItem:I}=(0,c.useDispatch)(te);return(0,o.useEffect)((()=>{v&&void 0===S&&!x&&C(d,a)}),[S,v,d,a,x]),(0,o.createElement)(o.Fragment,null,u&&(0,o.createElement)(me,{scope:d},w&&(0,o.createElement)(ae,{scope:d,identifier:a,isPressed:b&&(!y||k),"aria-expanded":b,label:f,icon:y?F:s,showTooltip:!y,variant:y?"tertiary":void 0})),h&&u&&(0,o.createElement)(ce,{target:h,scope:d,icon:s},f),b&&(0,o.createElement)(de,{className:V()("interface-complementary-area",n),scope:d},(0,o.createElement)(le,{className:i,closeLabel:r,onClose:()=>T(d),smallScreenTitle:g,toggleButtonProps:{label:r,shortcut:E,scope:d,identifier:a}},l||(0,o.createElement)(o.Fragment,null,(0,o.createElement)("strong",null,f),u&&(0,o.createElement)(O.Button,{className:"interface-complementary-area__pin-unpin-item",icon:w?H:G,label:w?(0,p.__)("Unpin from toolbar"):(0,p.__)("Pin to toolbar"),onClick:()=>(w?I:N)(d,a),isPressed:w,"aria-expanded":w}))),(0,o.createElement)(O.Panel,{className:m},t)))}));pe.Slot=function(e){let{scope:t,...n}=e;return(0,o.createElement)(O.Slot,z({name:`ComplementaryArea/${t}`},n))};var _e=pe,he=window.wp.compose;var ge=(0,o.forwardRef)((function(e,t){let{footer:n,header:r,sidebar:a,secondarySidebar:l,notices:i,content:s,drawer:c,actions:u,labels:m,className:d,shortcuts:_}=e;const h=(0,O.__unstableUseNavigateRegions)(_);!function(e){(0,o.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const g={...{drawer:(0,p.__)("Drawer"),header:(0,p.__)("Header"),body:(0,p.__)("Content"),secondarySidebar:(0,p.__)("Block Library"),sidebar:(0,p.__)("Settings"),actions:(0,p.__)("Publish"),footer:(0,p.__)("Footer")},...m};return(0,o.createElement)("div",z({},h,{ref:(0,he.useMergeRefs)([t,h.ref]),className:V()(d,"interface-interface-skeleton",h.className,!!n&&"has-footer")}),!!c&&(0,o.createElement)("div",{className:"interface-interface-skeleton__drawer",role:"region","aria-label":g.drawer,tabIndex:"-1"},c),(0,o.createElement)("div",{className:"interface-interface-skeleton__editor"},!!r&&(0,o.createElement)("div",{className:"interface-interface-skeleton__header",role:"region","aria-label":g.header,tabIndex:"-1"},r),(0,o.createElement)("div",{className:"interface-interface-skeleton__body"},!!l&&(0,o.createElement)("div",{className:"interface-interface-skeleton__secondary-sidebar",role:"region","aria-label":g.secondarySidebar,tabIndex:"-1"},l),!!i&&(0,o.createElement)("div",{className:"interface-interface-skeleton__notices"},i),(0,o.createElement)("div",{className:"interface-interface-skeleton__content",role:"region","aria-label":g.body,tabIndex:"-1"},s),!!a&&(0,o.createElement)("div",{className:"interface-interface-skeleton__sidebar",role:"region","aria-label":g.sidebar,tabIndex:"-1"},a),!!u&&(0,o.createElement)("div",{className:"interface-interface-skeleton__actions",role:"region","aria-label":g.actions,tabIndex:"-1"},u))),!!n&&(0,o.createElement)("div",{className:"interface-interface-skeleton__footer",role:"region","aria-label":g.footer,tabIndex:"-1"},n))}));var fe=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function Ee(e){let{as:t=O.DropdownMenu,className:n,label:r=(0,p.__)("Options"),popoverProps:a,toggleProps:l,children:i}=e;return(0,o.createElement)(t,{className:V()("interface-more-menu-dropdown",n),icon:fe,label:r,popoverProps:{position:"bottom left",...a,className:V()("interface-more-menu-dropdown__content",null==a?void 0:a.className)},toggleProps:{tooltipPosition:"bottom",...l}},(e=>i(e)))}function ve(e){let{closeModal:t,children:n}=e;return(0,o.createElement)(O.Modal,{className:"interface-preferences-modal",title:(0,p.__)("Preferences"),closeLabel:(0,p.__)("Close"),onRequestClose:t},n)}var ye=function(e){let{icon:t,size:n=24,...r}=e;return(0,o.cloneElement)(t,{width:n,height:n,...r})};var be=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"}));var we=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"}));const Se="preferences-menu";function xe(e){let{sections:t}=e;const n=(0,he.useViewportMatch)("medium"),[r,a]=(0,o.useState)(Se),{tabs:l,sectionsContentMap:i}=(0,o.useMemo)((()=>{let e={tabs:[],sectionsContentMap:{}};return t.length&&(e=t.reduce(((e,t)=>{let{name:n,tabLabel:r,content:a}=t;return e.tabs.push({name:n,title:r}),e.sectionsContentMap[n]=a,e}),{tabs:[],sectionsContentMap:{}})),e}),[t]),s=(0,o.useCallback)((e=>i[e.name]||null),[i]);let c;return c=n?(0,o.createElement)(O.TabPanel,{className:"interface-preferences__tabs",tabs:l,initialTabName:r!==Se?r:void 0,onSelect:a,orientation:"vertical"},s):(0,o.createElement)(O.__experimentalNavigatorProvider,{initialPath:"/",className:"interface-preferences__provider"},(0,o.createElement)(O.__experimentalNavigatorScreen,{path:"/"},(0,o.createElement)(O.Card,{isBorderless:!0,size:"small"},(0,o.createElement)(O.CardBody,null,(0,o.createElement)(O.__experimentalItemGroup,null,l.map((e=>(0,o.createElement)(O.__experimentalNavigatorButton,{key:e.name,path:e.name,as:O.__experimentalItem,isAction:!0},(0,o.createElement)(O.__experimentalHStack,{justify:"space-between"},(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.__experimentalTruncate,null,e.title)),(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(ye,{icon:(0,p.isRTL)()?be:we})))))))))),t.length&&t.map((e=>(0,o.createElement)(O.__experimentalNavigatorScreen,{key:`${e.name}-menu`,path:e.name},(0,o.createElement)(O.Card,{isBorderless:!0,size:"large"},(0,o.createElement)(O.CardHeader,{isBorderless:!1,justify:"left",size:"small",gap:"6"},(0,o.createElement)(O.__experimentalNavigatorBackButton,{icon:(0,p.isRTL)()?we:be,"aria-label":(0,p.__)("Navigate to the previous view")}),(0,o.createElement)(O.__experimentalText,{size:"16"},e.tabLabel)),(0,o.createElement)(O.CardBody,null,e.content)))))),c}var ke=e=>{let{description:t,title:n,children:r}=e;return(0,o.createElement)("fieldset",{className:"interface-preferences-modal__section"},(0,o.createElement)("legend",null,(0,o.createElement)("h2",{className:"interface-preferences-modal__section-title"},n),t&&(0,o.createElement)("p",{className:"interface-preferences-modal__section-description"},t)),r)};var Ce=function(e){let{help:t,label:n,isChecked:r,onChange:a,children:l}=e;return(0,o.createElement)("div",{className:"interface-preferences-modal__option"},(0,o.createElement)(O.ToggleControl,{help:t,label:n,checked:r,onChange:a}),l)},Te=window.wp.blockEditor,Ne=window.wp.a11y;function Ie(e){return!!e&&("custom"===(null==e?void 0:e.source)&&(null==e?void 0:e.has_theme_file))}function Pe(e){return function(t){let{registry:n}=t;R()("select( 'core/edit-site' ).toggleFeature( featureName )",{since:"6.0",alternative:"select( 'core/preferences').toggle( 'core/edit-site', featureName )"}),n.dispatch(d.store).toggle("core/edit-site",e)}}function Me(e){return{type:"SET_PREVIEW_DEVICE_TYPE",deviceType:e}}const Be=(e,t)=>async n=>{let{dispatch:r,registry:a}=n;if(!t){const n=await a.resolveSelect(u.store).getEntityRecord("postType","wp_template",e);t=null==n?void 0:n.slug}r({type:"SET_TEMPLATE",templateId:e,page:{context:{templateSlug:t}}})},Re=e=>async t=>{let{dispatch:n,registry:r}=t;const a=await r.dispatch(u.store).saveEntityRecord("postType","wp_template",e);e.content&&r.dispatch(u.store).editEntityRecord("postType","wp_template",a.id,{blocks:(0,i.parse)(e.content)},{undoIgnore:!0}),n({type:"SET_TEMPLATE",templateId:a.id,page:{context:{templateSlug:a.slug}}})},Ae=e=>async t=>{let{registry:n}=t;try{await n.dispatch(u.store).deleteEntityRecord("postType",e.type,e.id,{force:!0});const t=n.select(u.store).getLastEntityDeleteError("postType",e.type,e.id);if(t)throw t;n.dispatch(A.store).createSuccessNotice((0,p.sprintf)((0,p.__)('"%s" deleted.'),e.title.rendered),{type:"snackbar"})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("An error occurred while deleting the template.");n.dispatch(A.store).createErrorNotice(t,{type:"snackbar"})}};function ze(e){return{type:"SET_TEMPLATE_PART",templatePartId:e}}function Le(e){return{type:"SET_HOME_TEMPLATE",homeTemplateId:e}}const Ve=e=>async t=>{var n;let{dispatch:r,registry:a}=t;if(!e.path&&null!==(n=e.context)&&void 0!==n&&n.postId){const t=await a.resolveSelect(u.store).getEntityRecord("postType",e.context.postType||"post",e.context.postId);e.path=(0,h.getPathAndQueryString)(null==t?void 0:t.link)}const l=await a.resolveSelect(u.store).__experimentalGetTemplateForLink(e.path);if(l)return r({type:"SET_PAGE",page:l.slug?{...e,context:{...e.context,templateSlug:l.slug}}:e,templateId:l.id}),l.id};function Oe(e){return{type:"SET_NAVIGATION_PANEL_ACTIVE_MENU",menu:e}}function De(e){return{type:"OPEN_NAVIGATION_PANEL_TO_MENU",menu:e}}function Fe(e){return{type:"SET_IS_NAVIGATION_PANEL_OPENED",isOpen:e}}function He(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function Ge(e){return{type:"UPDATE_SETTINGS",settings:e}}function Ue(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const $e=function(e){let{allowUndo:t=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return async n=>{let{registry:r}=n;if(Ie(e))try{var a;const n=r.select(u.store).getEntityConfig("postType",e.type);if(!n)return void r.dispatch(A.store).createErrorNotice((0,p.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const l=(0,h.addQueryArgs)(`${n.baseURL}/${e.id}`,{context:"edit",source:"theme"}),o=await M()({path:l});if(!o)return void r.dispatch(A.store).createErrorNotice((0,p.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const s=e=>{let{blocks:t=[]}=e;return(0,i.__unstableSerializeAndClean)(t)},c=r.select(u.store).getEditedEntityRecord("postType",e.type,e.id);r.dispatch(u.store).editEntityRecord("postType",e.type,e.id,{content:s,blocks:c.blocks,source:"custom"},{undoIgnore:!0});const m=(0,i.parse)(null==o||null===(a=o.content)||void 0===a?void 0:a.raw);if(r.dispatch(u.store).editEntityRecord("postType",e.type,o.id,{content:s,blocks:m,source:"theme"}),t){const t=()=>{r.dispatch(u.store).editEntityRecord("postType",e.type,c.id,{content:s,blocks:c.blocks,source:"custom"})};r.dispatch(A.store).createSuccessNotice((0,p.__)("Template reverted."),{type:"snackbar",actions:[{label:(0,p.__)("Undo"),onClick:t}]})}else r.dispatch(A.store).createSuccessNotice((0,p.__)("Template reverted."))}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("Template revert failed. Please reload.");r.dispatch(A.store).createErrorNotice(t,{type:"snackbar"})}else r.dispatch(A.store).createErrorNotice((0,p.__)("This template is not revertable."),{type:"snackbar"})}},We=e=>t=>{let{registry:n}=t;n.dispatch(te).enableComplementaryArea(E,e)},je=()=>e=>{let{registry:t}=e;t.dispatch(te).disableComplementaryArea(E)},Ye=e=>t=>{let{registry:n}=t;n.dispatch("core/preferences").set("core/edit-site","editorMode",e),"visual"!==e&&n.dispatch(Te.store).clearSelectedBlock(),"visual"===e?(0,Ne.speak)((0,p.__)("Visual editor selected"),"assertive"):"mosaic"===e&&(0,Ne.speak)((0,p.__)("Mosaic view selected"),"assertive")};var qe,Xe;function Ke(e){return[e]}function Ze(){var e={clear:function(){e.head=null}};return e}function Qe(e,t,n){var r;if(e.length!==t.length)return!1;for(r=n;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function Je(e,t,n){return!!x[e]&&("home"===e&&"posts"!==n||x[e].every((e=>t.includes(e)||Je(e,t,n))))}function et(e){if(y.includes(e))return C;if(b.includes(e))return T;if(w.some((t=>e.startsWith(t))))return"templates-posts";return S.some((t=>e.startsWith(t)))?"templates-pages":T}qe={},Xe="undefined"!=typeof WeakMap;const tt=(0,c.createRegistrySelector)((e=>(t,n)=>e(d.store).get("core/edit-site",n)));function nt(e,t){return R()("select( 'core/interface' ).isFeatureActive",{since:"6.0",alternative:"select( 'core/preferences' ).get"}),!!tt(e,t)}function rt(e){return e.deviceType}const at=(0,c.createRegistrySelector)((e=>()=>e(u.store).canUser("create","media"))),lt=(0,c.createRegistrySelector)((e=>()=>"web"===o.Platform.OS?e(u.store).getEntityRecords("postType","wp_block",{per_page:-1}):[])),ot=function(e,t){var n,r;function a(){n=Xe?new WeakMap:Ze()}function l(){var n,a,l,o,i,s=arguments.length;for(o=new Array(s),l=0;l<s;l++)o[l]=arguments[l];for(i=t.apply(null,o),(n=r(i)).isUniqueByDependants||(n.lastDependants&&!Qe(i,n.lastDependants,0)&&n.clear(),n.lastDependants=i),a=n.head;a;){if(Qe(a.args,o,1))return a!==n.head&&(a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=n.head,a.prev=null,n.head.prev=a,n.head=a),a.val;a=a.next}return a={val:e.apply(null,o)},o[0]=null,a.args=o,n.head&&(n.head.prev=a,a.next=n.head),n.head=a,a.val}return t||(t=Ke),r=Xe?function(e){var t,r,a,l,o,i=n,s=!0;for(t=0;t<e.length;t++){if(r=e[t],!(o=r)||"object"!=typeof o){s=!1;break}i.has(r)?i=i.get(r):(a=new WeakMap,i.set(r,a),i=a)}return i.has(qe)||((l=Ze()).isUniqueByDependants=s,i.set(qe,l)),i.get(qe)}:function(){return n},l.getDependants=t,l.clear=a,a(),l}(((e,t)=>{const n={...e.settings,outlineMode:!0,focusMode:!!tt(e,"focusMode"),hasFixedToolbar:!!tt(e,"fixedToolbar"),keepCaretInsideBlock:!!tt(e,"keepCaretInsideBlock"),showIconLabels:!!tt(e,"showIconLabels"),__experimentalSetIsInserterOpened:t,__experimentalReusableBlocks:lt(e),__experimentalPreferPatternsOnRoot:"wp_template"===ct(e)};return at(e)?(n.mediaUpload=t=>{let{onError:n,...r}=t;(0,f.uploadMedia)({wpAllowedMimeTypes:e.settings.allowedMimeTypes,onError:e=>{let{message:t}=e;return n(t)},...r})},n):n}),(e=>[at(e),e.settings,tt(e,"focusMode"),tt(e,"fixedToolbar"),tt(e,"keepCaretInsideBlock"),tt(e,"showIconLabels"),lt(e),ct(e)]));function it(e){return e.homeTemplateId}function st(e){return e.editedPost}function ct(e){return st(e).type}function ut(e){return st(e).id}function mt(e){return st(e).page}function dt(e){return e.navigationPanel.menu}const pt=(0,c.createRegistrySelector)((e=>t=>{const n=ct(t),r=ut(t),a=r?e(u.store).getEntityRecord("postType",n,r):null;if(!a)return k;var l;if("wp_template_part"===n)return(null===(l=N.find((e=>e.area===(null==a?void 0:a.area))))||void 0===l?void 0:l.menu)||"template-parts";const o=e(u.store).getEntityRecords("postType","wp_template"),i=e(u.store).getEditedEntityRecord("root","site").show_on_front;return Je(a.slug,(0,$.map)(o,"slug"),i)?"templates-unused":et(a.slug)}));function _t(e){return e.navigationPanel.isOpen}function ht(e){return!!e.blockInserterPanel}function gt(e){const{rootClientId:t,insertionIndex:n,filterValue:r}=e.blockInserterPanel;return{rootClientId:t,insertionIndex:n,filterValue:r}}function ft(e){return e.listViewPanel}const Et=(0,c.createRegistrySelector)((e=>t=>{var n;const r=ct(t),a=ut(t),l=e(u.store).getEditedEntityRecord("postType",r,a),o=e(u.store).getEntityRecords("postType","wp_template_part",{per_page:-1}),s=(0,$.keyBy)(o,(e=>e.id));return(null!==(n=l.blocks)&&void 0!==n?n:[]).filter((e=>(0,i.isTemplatePart)(e))).map((e=>{const{attributes:{theme:t,slug:n}}=e;return{templatePart:s[`${t}//${n}`],block:e}})).filter((e=>{let{templatePart:t}=e;return!!t}))}));function vt(e){return tt(e,"editorMode")}const yt={reducer:I,actions:a,selectors:l},bt=(0,c.createReduxStore)(E,yt);var wt;(0,c.register)(bt),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(wt||(wt={}));var St=function(e){return e};var xt="beforeunload",kt="popstate";function Ct(e){e.preventDefault(),e.returnValue=""}function Tt(){var e=[];return{get length(){return e.length},push:function(t){return e.push(t),function(){e=e.filter((function(e){return e!==t}))}},call:function(t){e.forEach((function(e){return e&&e(t)}))}}}function Nt(){return Math.random().toString(36).substr(2,8)}function It(e){var t=e.pathname,n=void 0===t?"/":t,r=e.search,a=void 0===r?"":r,l=e.hash,o=void 0===l?"":l;return a&&"?"!==a&&(n+="?"===a.charAt(0)?a:"?"+a),o&&"#"!==o&&(n+="#"===o.charAt(0)?o:"#"+o),n}function Pt(e){var t={};if(e){var n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));var r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}const Mt=function(e){void 0===e&&(e={});var t=e.window,n=void 0===t?document.defaultView:t,r=n.history;function a(){var e=n.location,t=e.pathname,a=e.search,l=e.hash,o=r.state||{};return[o.idx,St({pathname:t,search:a,hash:l,state:o.usr||null,key:o.key||"default"})]}var l=null;n.addEventListener(kt,(function(){if(l)m.call(l),l=null;else{var e=wt.Pop,t=a(),n=t[0],r=t[1];if(m.length){if(null!=n){var o=s-n;o&&(l={action:e,location:r,retry:function(){f(-1*o)}},f(o))}}else g(e)}}));var o=wt.Pop,i=a(),s=i[0],c=i[1],u=Tt(),m=Tt();function d(e){return"string"==typeof e?e:It(e)}function p(e,t){return void 0===t&&(t=null),St(z({pathname:c.pathname,hash:"",search:""},"string"==typeof e?Pt(e):e,{state:t,key:Nt()}))}function _(e,t){return[{usr:e.state,key:e.key,idx:t},d(e)]}function h(e,t,n){return!m.length||(m.call({action:e,location:t,retry:n}),!1)}function g(e){o=e;var t=a();s=t[0],c=t[1],u.call({action:o,location:c})}function f(e){r.go(e)}null==s&&(s=0,r.replaceState(z({},r.state,{idx:s}),""));var E={get action(){return o},get location(){return c},createHref:d,push:function e(t,a){var l=wt.Push,o=p(t,a);if(h(l,o,(function(){e(t,a)}))){var i=_(o,s+1),c=i[0],u=i[1];try{r.pushState(c,"",u)}catch(e){n.location.assign(u)}g(l)}},replace:function e(t,n){var a=wt.Replace,l=p(t,n);if(h(a,l,(function(){e(t,n)}))){var o=_(l,s),i=o[0],c=o[1];r.replaceState(i,"",c),g(a)}},go:f,back:function(){f(-1)},forward:function(){f(1)},listen:function(e){return u.push(e)},block:function(e){var t=m.push(e);return 1===m.length&&n.addEventListener(xt,Ct),function(){t(),m.length||n.removeEventListener(xt,Ct)}}};return E}(),Bt=Mt.push,Rt=Mt.replace;Mt.push=function(e,t){return Bt.call(Mt,(0,h.addQueryArgs)(window.location.href,e),t)},Mt.replace=function(e,t){return Rt.call(Mt,(0,h.addQueryArgs)(window.location.href,e),t)};var At=Mt;const zt=(0,o.createContext)(),Lt=(0,o.createContext)();function Vt(){return(0,o.useContext)(zt)}function Ot(){return(0,o.useContext)(Lt)}function Dt(e){const t=new URLSearchParams(e.search);return{...e,params:Object.fromEntries(t.entries())}}function Ft(e){let{children:t}=e;const[n,r]=(0,o.useState)((()=>Dt(At.location)));return(0,o.useEffect)((()=>At.listen((e=>{let{location:t}=e;r(Dt(t))}))),[]),(0,o.createElement)(Lt.Provider,{value:At},(0,o.createElement)(zt.Provider,{value:n},t(n)))}var Ht=window.wp.keyboardShortcuts;var Gt=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));var Ut=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M13.8 5.2H3v1.5h10.8V5.2zm-3.6 12v1.5H21v-1.5H10.2zm7.2-6H6.6v1.5h10.8v-1.5z"})),$t=window.wp.keycodes;var Wt=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z"}));const jt=[{keyCombination:{modifier:"primary",character:"b"},description:(0,p.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,p.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,p.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,p.__)("Remove a link.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,p.__)("Underline the selected text.")}];function Yt(e){let{keyCombination:t,forceAriaLabel:n}=e;const r=t.modifier?$t.displayShortcutList[t.modifier](t.character):t.character,a=t.modifier?$t.shortcutAriaLabel[t.modifier](t.character):t.character;return(0,o.createElement)("kbd",{className:"edit-site-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":n||a},(0,$.castArray)(r).map(((e,t)=>"+"===e?(0,o.createElement)(o.Fragment,{key:t},e):(0,o.createElement)("kbd",{key:t,className:"edit-site-keyboard-shortcut-help-modal__shortcut-key"},e))))}function qt(e){let{description:t,keyCombination:n,aliases:r=[],ariaLabel:a}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"edit-site-keyboard-shortcut-help-modal__shortcut-description"},t),(0,o.createElement)("div",{className:"edit-site-keyboard-shortcut-help-modal__shortcut-term"},(0,o.createElement)(Yt,{keyCombination:n,forceAriaLabel:a}),r.map(((e,t)=>(0,o.createElement)(Yt,{keyCombination:e,forceAriaLabel:a,key:t})))))}function Xt(e){let{name:t}=e;const{keyCombination:n,description:r,aliases:a}=(0,c.useSelect)((e=>{const{getShortcutKeyCombination:n,getShortcutDescription:r,getShortcutAliases:a}=e(Ht.store);return{keyCombination:n(t),aliases:a(t),description:r(t)}}),[t]);return n?(0,o.createElement)(qt,{keyCombination:n,description:r,aliases:a}):null}const Kt=e=>{let{shortcuts:t}=e;return(0,o.createElement)("ul",{className:"edit-site-keyboard-shortcut-help-modal__shortcut-list",role:"list"},t.map(((e,t)=>(0,o.createElement)("li",{className:"edit-site-keyboard-shortcut-help-modal__shortcut",key:t},(0,$.isString)(e)?(0,o.createElement)(Xt,{name:e}):(0,o.createElement)(qt,e)))))},Zt=e=>{let{title:t,shortcuts:n,className:r}=e;return(0,o.createElement)("section",{className:V()("edit-site-keyboard-shortcut-help-modal__section",r)},!!t&&(0,o.createElement)("h2",{className:"edit-site-keyboard-shortcut-help-modal__section-title"},t),(0,o.createElement)(Kt,{shortcuts:n}))},Qt=e=>{let{title:t,categoryName:n,additionalShortcuts:r=[]}=e;const a=(0,c.useSelect)((e=>e(Ht.store).getCategoryShortcuts(n)),[n]);return(0,o.createElement)(Zt,{title:t,shortcuts:a.concat(r)})};function Jt(e){let{isModalActive:t,toggleModal:n}=e;return t?(0,o.createElement)(O.Modal,{className:"edit-site-keyboard-shortcut-help-modal",title:(0,p.__)("Keyboard shortcuts"),closeLabel:(0,p.__)("Close"),onRequestClose:n},(0,o.createElement)(Zt,{className:"edit-site-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/edit-site/keyboard-shortcuts"]}),(0,o.createElement)(Qt,{title:(0,p.__)("Global shortcuts"),categoryName:"global"}),(0,o.createElement)(Qt,{title:(0,p.__)("Selection shortcuts"),categoryName:"selection"}),(0,o.createElement)(Qt,{title:(0,p.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,p.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,p.__)("Forward-slash")}]}),(0,o.createElement)(Zt,{title:(0,p.__)("Text formatting"),shortcuts:jt})):null}function en(e){const{featureName:t,...n}=e,r=(0,c.useSelect)((e=>!!e(d.store).get("core/edit-site",t)),[t]),{toggle:a}=(0,c.useDispatch)(d.store);return(0,o.createElement)(Ce,z({onChange:()=>a("core/edit-site",t),isChecked:r},n))}function tn(e){let{isModalActive:t,toggleModal:n}=e;const r=(0,o.useMemo)((()=>[{name:"general",tabLabel:(0,p.__)("General"),content:(0,o.createElement)(ke,{title:(0,p.__)("Appearance"),description:(0,p.__)("Customize options related to the block editor interface and editing flow.")},(0,o.createElement)(en,{featureName:"focusMode",help:(0,p.__)("Highlights the current block and fades other content."),label:(0,p.__)("Spotlight mode")}),(0,o.createElement)(en,{featureName:"showIconLabels",label:(0,p.__)("Show button text labels"),help:(0,p.__)("Show text instead of icons on buttons")}))},{name:"blocks",tabLabel:(0,p.__)("Blocks"),content:(0,o.createElement)(ke,{title:(0,p.__)("Block interactions"),description:(0,p.__)("Customize how you interact with blocks in the block library and editing canvas.")},(0,o.createElement)(en,{featureName:"keepCaretInsideBlock",help:(0,p.__)("Aids screen readers by stopping text caret from leaving blocks."),label:(0,p.__)("Contain text cursor inside block")}))}]));return t?(0,o.createElement)(ve,{closeModal:n},(0,o.createElement)(xe,{sections:r})):null}const{Fill:nn,Slot:rn}=(0,O.createSlotFill)("EditSiteToolsMoreMenuGroup");nn.Slot=e=>{let{fillProps:t}=e;return(0,o.createElement)(rn,{fillProps:t},(e=>!(0,$.isEmpty)(e)&&e))};var an=nn,ln=n(8981),on=n.n(ln);var sn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"}));function cn(){const{createErrorNotice:e}=(0,c.useDispatch)(A.store);return(0,o.createElement)(O.MenuItem,{role:"menuitem",icon:sn,onClick:async function(){try{const e=await M()({path:"/wp-block-editor/v1/export",parse:!1}),t=await e.blob(),n=e.headers.get("content-disposition").match(/=(.+)\.zip/),r=n[1]?n[1]:"edit-site-export";on()(t,r+".zip","application/zip")}catch(t){let n={};try{n=await t.json()}catch(e){}const r=n.message&&"unknown_error"!==n.code?n.message:(0,p.__)("An error occurred while creating the site export.");e(r,{type:"snackbar"})}},info:(0,p.__)("Download your theme with updated templates and styles.")},(0,p._x)("Export","site exporter menu item"))}function un(){const{toggle:e}=(0,c.useDispatch)(d.store);return(0,o.createElement)(O.MenuItem,{onClick:()=>e("core/edit-site","welcomeGuide")},(0,p.__)("Welcome Guide"))}function mn(){const{createNotice:e}=(0,c.useDispatch)(A.store),t=(0,c.useSelect)((e=>()=>{const{getEditedPostId:t,getEditedPostType:n}=e(bt),{getEditedEntityRecord:r}=e(u.store),a=r("postType",n(),t());if(a){if("function"==typeof a.content)return a.content(a);if(a.blocks)return(0,i.__unstableSerializeAndClean)(a.blocks);if(a.content)return a.content}return""}),[]);const n=(0,he.useCopyToClipboard)(t,(function(){e("info",(0,p.__)("All content copied."),{isDismissible:!0,type:"snackbar"})}));return(0,o.createElement)(O.MenuItem,{ref:n},(0,p.__)("Copy all content"))}const dn=[{value:"visual",label:(0,p.__)("Visual editor")},{value:"text",label:(0,p.__)("Code editor")}];var pn=function(){const{shortcut:e,mode:t}=(0,c.useSelect)((e=>({shortcut:e(Ht.store).getShortcutRepresentation("core/edit-site/toggle-mode"),isRichEditingEnabled:e(bt).getSettings().richEditingEnabled,isCodeEditingEnabled:e(bt).getSettings().codeEditingEnabled,mode:e(bt).getEditorMode()})),[]),{switchEditorMode:n}=(0,c.useDispatch)(bt),r=dn.map((n=>n.value!==t?{...n,shortcut:e}:n));return(0,o.createElement)(O.MenuGroup,{label:(0,p.__)("Editor")},(0,o.createElement)(O.MenuItemsChoice,{choices:r,value:t,onSelect:n}))};function _n(){const[e,t]=(0,o.useReducer)((e=>!e),!1),[n,r]=(0,o.useReducer)((e=>!e),!1);return(0,Ht.useShortcut)("core/edit-site/keyboard-shortcuts",t),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Ee,null,(e=>{let{onClose:n}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.MenuGroup,{label:(0,p._x)("View","noun")},(0,o.createElement)(d.PreferenceToggleMenuItem,{scope:"core/edit-site",name:"fixedToolbar",label:(0,p.__)("Top toolbar"),info:(0,p.__)("Access all block and document tools in a single place"),messageActivated:(0,p.__)("Top toolbar activated"),messageDeactivated:(0,p.__)("Top toolbar deactivated")}),(0,o.createElement)(d.PreferenceToggleMenuItem,{scope:"core/edit-site",name:"focusMode",label:(0,p.__)("Spotlight mode"),info:(0,p.__)("Focus on one block at a time"),messageActivated:(0,p.__)("Spotlight mode activated"),messageDeactivated:(0,p.__)("Spotlight mode deactivated")}),(0,o.createElement)(pn,null),(0,o.createElement)(ie.Slot,{name:"core/edit-site/plugin-more-menu",label:(0,p.__)("Plugins"),as:O.MenuGroup,fillProps:{onClick:n}})),(0,o.createElement)(O.MenuGroup,{label:(0,p.__)("Tools")},(0,o.createElement)(cn,null),(0,o.createElement)(O.MenuItem,{onClick:t,shortcut:$t.displayShortcut.access("h")},(0,p.__)("Keyboard shortcuts")),(0,o.createElement)(un,null),(0,o.createElement)(mn,null),(0,o.createElement)(O.MenuItem,{icon:Wt,role:"menuitem",href:(0,p.__)("https://wordpress.org/support/article/site-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,p.__)("Help"),(0,o.createElement)(O.VisuallyHidden,{as:"span"},(0,p.__)("(opens in a new tab)"))),(0,o.createElement)(an.Slot,{fillProps:{onClose:n}})),(0,o.createElement)(O.MenuGroup,null,(0,o.createElement)(O.MenuItem,{onClick:r},(0,p.__)("Preferences"))))})),(0,o.createElement)(Jt,{isModalActive:e,toggleModal:t}),(0,o.createElement)(tn,{isModalActive:n,toggleModal:r}))}function hn(e){let{openEntitiesSavedStates:t,isEntitiesSavedStatesOpen:n}=e;const{isDirty:r,isSaving:a}=(0,c.useSelect)((e=>{const{__experimentalGetDirtyEntityRecords:t,isSavingEntityRecord:n}=e(u.store),r=t();return{isDirty:r.length>0,isSaving:(0,$.some)(r,(e=>n(e.kind,e.name,e.key)))}}),[]),l=!r||a;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.Button,{variant:"primary",className:"edit-site-save-button__button","aria-disabled":l,"aria-expanded":n,disabled:l,isBusy:a,onClick:l?void 0:t},(0,p.__)("Save")))}var gn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"}));var fn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"}));function En(){const e=(0,c.useSelect)((e=>e(u.store).hasUndo()),[]),{undo:t}=(0,c.useDispatch)(u.store);return(0,o.createElement)(O.Button,{icon:(0,p.isRTL)()?fn:gn,label:(0,p.__)("Undo"),shortcut:$t.displayShortcut.primary("z"),"aria-disabled":!e,onClick:e?t:void 0})}function vn(){const e=(0,c.useSelect)((e=>e(u.store).hasRedo()),[]),{redo:t}=(0,c.useDispatch)(u.store);return(0,o.createElement)(O.Button,{icon:(0,p.isRTL)()?gn:fn,label:(0,p.__)("Redo"),shortcut:$t.displayShortcut.primaryShift("z"),"aria-disabled":!e,onClick:e?t:void 0})}var yn=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"}));function bn(e){if(e){const t=(0,i.getBlockType)(e.name);return t?(0,i.__experimentalGetBlockLabel)(t,e.attributes):null}return null}function wn(e){let{entityTitle:t,entityLabel:n,isLoaded:r,children:a,showIconLabels:l}=e;const{label:i}=function(){const{getBlock:e}=(0,c.useSelect)(Te.store),t=(0,c.useSelect)((e=>e(Te.store).__experimentalGetActiveBlockIdByBlockNames(["core/template-part"])),[]);return t?{label:bn(e(t)),isActive:!0}:{}}(),s=(0,o.useRef)();return r?t?(0,o.createElement)("div",{className:V()("edit-site-document-actions",{"has-secondary-label":!!i})},(0,o.createElement)("div",{ref:s,className:"edit-site-document-actions__title-wrapper"},(0,o.createElement)(O.__experimentalText,{size:"body",className:"edit-site-document-actions__title",as:"h1"},(0,o.createElement)(O.VisuallyHidden,{as:"span"},(0,p.sprintf)((0,p.__)("Editing %s: "),n)),t),(0,o.createElement)(O.__experimentalText,{size:"body",className:"edit-site-document-actions__secondary-item"},null!=i?i:""),a&&(0,o.createElement)(O.Dropdown,{popoverProps:{anchorRef:s.current},position:"bottom center",renderToggle:e=>{let{isOpen:t,onToggle:r}=e;return(0,o.createElement)(O.Button,{className:"edit-site-document-actions__get-info",icon:yn,"aria-expanded":t,"aria-haspopup":"true",onClick:r,label:(0,p.sprintf)((0,p.__)("Show %s details"),n)},l&&(0,p.__)("Details"))},contentClassName:"edit-site-document-actions__info-dropdown",renderContent:a}))):(0,o.createElement)("div",{className:"edit-site-document-actions"},(0,p.__)("Template not found")):(0,o.createElement)("div",{className:"edit-site-document-actions"},(0,p.__)("Loading…"))}function Sn(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=Ot();function a(a){a.preventDefault(),n?r.replace(e,t):r.push(e,t)}return{href:(0,h.addQueryArgs)(window.location.href,e),onClick:a}}function xn(e){let{params:t={},state:n,replace:r=!1,children:a,...l}=e;const{href:i,onClick:s}=Sn(t,n,r);return(0,o.createElement)("a",z({href:i,onClick:s},l),a)}function kn(e){var t;let{onClose:n,templatePart:r,closeTemplateDetailsDropdown:a}=e;const{revertTemplate:l}=(0,c.useDispatch)(bt),{params:i}=Vt(),s=Sn({postId:r.id,postType:r.type},{fromTemplateId:i.postId});return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.MenuGroup,null,(0,o.createElement)(O.MenuItem,z({},s,{onClick:function(e){s.onClick(e),n(),a()}}),(0,p.sprintf)((0,p.__)("Edit %s"),null===(t=r.title)||void 0===t?void 0:t.rendered))),Ie(r)&&(0,o.createElement)(O.MenuGroup,null,(0,o.createElement)(O.MenuItem,{info:(0,p.__)("Restore template to default state"),onClick:function(){l(r),n(),a()}},(0,p.__)("Clear customizations"))))}function Cn(e){let{templatePart:t,clientId:n,closeTemplateDetailsDropdown:r}=e;const{selectBlock:a,toggleBlockHighlight:l}=(0,c.useDispatch)(Te.store),i=(0,c.useSelect)((e=>e(m.store).__experimentalGetDefaultTemplatePartAreas().find((e=>e.area===t.area))),[t.area]),s=()=>l(n,!0),u=()=>l(n,!1);return(0,o.createElement)("div",{role:"menuitem",className:"edit-site-template-details__template-areas-item"},(0,o.createElement)(O.MenuItem,{role:"button",icon:null==i?void 0:i.icon,iconPosition:"left",onClick:()=>{a(n)},onMouseOver:s,onMouseLeave:u,onFocus:s,onBlur:u},null==i?void 0:i.label),(0,o.createElement)(O.DropdownMenu,{icon:fe,label:(0,p.__)("More options"),className:"edit-site-template-details__template-areas-item-more"},(e=>{let{onClose:n}=e;return(0,o.createElement)(kn,{onClose:n,templatePart:t,closeTemplateDetailsDropdown:r})})))}function Tn(e){let{closeTemplateDetailsDropdown:t}=e;const n=(0,c.useSelect)((e=>e(bt).getCurrentTemplateTemplateParts()),[]);return n.length?(0,o.createElement)(O.MenuGroup,{label:(0,p.__)("Areas"),className:"edit-site-template-details__group edit-site-template-details__template-areas"},n.map((e=>{let{templatePart:n,block:r}=e;return(0,o.createElement)(Cn,{key:n.slug,clientId:r.clientId,templatePart:n,closeTemplateDetailsDropdown:t})}))):null}function Nn(e){let{template:t}=e;const[n,r]=(0,u.useEntityProp)("postType",t.type,"title",t.id);return(0,o.createElement)(O.TextControl,{label:(0,p.__)("Title"),value:n,help:(0,p.__)('Give the template a title that indicates its purpose, e.g. "Full Width".'),onChange:e=>{r(e||t.slug)}})}function In(e){let{template:t,onClose:n}=e;const{title:r,description:a}=(0,c.useSelect)((e=>e(m.store).__experimentalGetTemplateInfo(t)),[]),{revertTemplate:l}=(0,c.useDispatch)(bt),i=(0,o.useMemo)((()=>"wp_template"===(null==t?void 0:t.type)?{title:(0,p.__)("templates"),menu:C}:N.find((e=>{let{area:n}=e;return n===(null==t?void 0:t.area)}))),[t]),s=Sn({postType:t.type,postId:void 0}),u=t.is_custom&&!t.has_theme_file;if(!t)return null;return(0,o.createElement)("div",{className:"edit-site-template-details"},(0,o.createElement)("div",{className:"edit-site-template-details__group"},u?(0,o.createElement)(Nn,{template:t}):(0,o.createElement)(O.__experimentalHeading,{level:4,weight:600,className:"edit-site-template-details__title"},r),a&&(0,o.createElement)(O.__experimentalText,{size:"body",className:"edit-site-template-details__description",as:"p"},a)),(0,o.createElement)(Tn,{closeTemplateDetailsDropdown:n}),Ie(t)&&(0,o.createElement)(O.MenuGroup,{className:"edit-site-template-details__group edit-site-template-details__revert"},(0,o.createElement)(O.MenuItem,{className:"edit-site-template-details__revert-button",info:(0,p.__)("Restore template to default state"),onClick:()=>{l(t),n()}},(0,p.__)("Clear customizations"))),(0,o.createElement)(O.Button,z({className:"edit-site-template-details__show-all-button"},s),(0,p.sprintf)((0,p.__)("Browse all %s"),i.title)))}const Pn=e=>{e.preventDefault()};function Mn(e){let{openEntitiesSavedStates:t,isEntitiesSavedStatesOpen:n,showIconLabels:r}=e;const a=(0,o.useRef)(),{deviceType:l,entityTitle:i,template:s,templateType:d,isInserterOpen:_,isListViewOpen:h,listViewShortcut:g,isLoaded:f,isVisualMode:E}=(0,c.useSelect)((e=>{const{__experimentalGetPreviewDeviceType:t,getEditedPostType:n,getEditedPostId:r,isInserterOpened:a,isListViewOpened:l,getEditorMode:o}=e(bt),{getEditedEntityRecord:i}=e(u.store),{__experimentalGetTemplateInfo:s}=e(m.store),{getShortcutRepresentation:c}=e(Ht.store),d=n(),p=r(),_=i("postType",d,p),h=!!p;return{deviceType:t(),entityTitle:s(_).title,isLoaded:h,template:_,templateType:d,isInserterOpen:a(),isListViewOpen:l(),listViewShortcut:c("core/edit-site/toggle-list-view"),isVisualMode:"visual"===o()}}),[]),{__experimentalSetPreviewDeviceType:v,setIsInserterOpened:y,setIsListViewOpened:b}=(0,c.useDispatch)(bt),w=(0,he.useViewportMatch)("medium"),S=(0,o.useCallback)((()=>{_?a.current.focus():y(!0)}),[_,y]),x=(0,o.useCallback)((()=>b(!h)),[b,h]),k="wp_template_part"===d;return(0,o.createElement)("div",{className:"edit-site-header"},(0,o.createElement)("div",{className:"edit-site-header_start"},(0,o.createElement)("div",{className:"edit-site-header__toolbar"},(0,o.createElement)(O.Button,{ref:a,variant:"primary",isPressed:_,className:"edit-site-header-toolbar__inserter-toggle",disabled:!E,onMouseDown:Pn,onClick:S,icon:Gt,label:(0,p._x)("Toggle block inserter","Generic label for block inserter button")},r&&(_?(0,p.__)("Close"):(0,p.__)("Add"))),w&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.ToolbarItem,{as:Te.ToolSelector,disabled:!E}),(0,o.createElement)(En,null),(0,o.createElement)(vn,null),(0,o.createElement)(O.Button,{className:"edit-site-header-toolbar__list-view-toggle",disabled:!E,icon:Ut,isPressed:h,label:(0,p.__)("List View"),onClick:x,shortcut:g})))),(0,o.createElement)("div",{className:"edit-site-header_center"},(0,o.createElement)(wn,{entityTitle:i,entityLabel:"wp_template_part"===d?"template part":"template",isLoaded:f,showIconLabels:r},(e=>{let{onClose:t}=e;return(0,o.createElement)(In,{template:s,onClose:t})}))),(0,o.createElement)("div",{className:"edit-site-header_end"},(0,o.createElement)("div",{className:"edit-site-header__actions"},!k&&(0,o.createElement)(Te.__experimentalPreviewOptions,{deviceType:l,setDeviceType:v}),(0,o.createElement)(hn,{openEntitiesSavedStates:t,isEntitiesSavedStatesOpen:n}),(0,o.createElement)(me.Slot,{scope:"core/edit-site"}),(0,o.createElement)(_n,null))))}var Bn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"}));function Rn(e){let{className:t,identifier:n,title:r,icon:a,children:l,closeLabel:i,header:s,headerClassName:c,panelClassName:u}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(_e,{className:t,scope:"core/edit-site",identifier:n,title:r,icon:a,closeLabel:i,header:s,headerClassName:c,panelClassName:u},l),(0,o.createElement)(ce,{scope:"core/edit-site",identifier:n,icon:a},r))}var An=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"}));function zn(e){let{className:t,...n}=e;return(0,o.createElement)(O.Icon,z({className:V()(t,"edit-site-global-styles-icon-with-current-color")},n))}function Ln(e){let{icon:t,children:n,...r}=e;return(0,o.createElement)(O.__experimentalItem,r,t&&(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(zn,{icon:t,size:24}),(0,o.createElement)(O.FlexItem,null,n)),!t&&n)}function Vn(e){return(0,o.createElement)(O.__experimentalNavigatorButton,z({as:Ln},e))}function On(e){return(0,o.createElement)(O.__experimentalNavigatorBackButton,z({as:Ln},e))}var Dn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"}));var Fn=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"}));var Hn=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}));const Gn="body",Un=[{path:["color","palette"],valueKey:"color",cssVarInfix:"color",classes:[{classSuffix:"color",propertyName:"color"},{classSuffix:"background-color",propertyName:"background-color"},{classSuffix:"border-color",propertyName:"border-color"}]},{path:["color","gradients"],valueKey:"gradient",cssVarInfix:"gradient",classes:[{classSuffix:"gradient-background",propertyName:"background"}]},{path:["color","duotone"],cssVarInfix:"duotone",valueFunc:e=>{let{slug:t}=e;return`url( '#wp-duotone-${t}' )`},classes:[]},{path:["typography","fontSizes"],valueKey:"size",cssVarInfix:"font-size",classes:[{classSuffix:"font-size",propertyName:"font-size"}]},{path:["typography","fontFamilies"],valueKey:"fontFamily",cssVarInfix:"font-family",classes:[{classSuffix:"font-family",propertyName:"font-family"}]}],$n={"color.background":"color","color.text":"color","elements.link.color.text":"color","color.gradient":"gradient","typography.fontSize":"font-size","typography.fontFamily":"font-family"};function Wn(e,t,n,r,a){const l=[(0,$.get)(e,["blocks",t,...n]),(0,$.get)(e,n)];for(const o of l)if(o){const l=["custom","theme","default"];for(const i of l){const l=o[i];if(l){const o=(0,$.find)(l,(e=>e[r]===a));if(o){if("slug"===r)return o;return Wn(e,t,n,"slug",o.slug)[r]===o[r]?o:void 0}}}}}function jn(e,t,n,r){if(!r)return r;const a=$n[n],l=(0,$.find)(Un,["cssVarInfix",a]);if(!l)return r;const{valueKey:o,path:i}=l,s=Wn(e,t,i,o,r);return s?`var:preset|${a}|${s.slug}`:r}function Yn(e,t,n){if(!n||!(0,$.isString)(n))return n;const r="var:",a="var(--wp--";let l;if(n.startsWith(r))l=n.slice(r.length).split("|");else{if(!n.startsWith(a)||!n.endsWith(")"))return n;l=n.slice(a.length,-")".length).split("--")}const[o,...i]=l;return"preset"===o?function(e,t,n,r){let[a,l]=r;const o=(0,$.find)(Un,["cssVarInfix",a]);if(!o)return n;const i=Wn(e,t,o.path,"slug",l);if(i){const{valueKey:n}=o;return Yn(e,t,i[n])}return n}(e,t,n,i):"custom"===o?function(e,t,n,r){var a;const l=null!==(a=(0,$.get)(e,["blocks",t,"custom",...r]))&&void 0!==a?a:(0,$.get)(e,["custom",...r]);return l?Yn(e,t,l):n}(e,t,n,i):n}const qn=(0,o.createContext)({user:{},base:{},merged:{},setUserConfig:()=>{}}),Xn={settings:{},styles:{}};function Kn(e,t){var n;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";const{merged:a,base:l,user:s,setUserConfig:c}=(0,o.useContext)(qn),u=t?`settings.blocks.${t}.${e}`:`settings.${e}`,m=t=>{c((n=>{const r=(0,$.cloneDeep)(n),a=i.__EXPERIMENTAL_PATHS_WITH_MERGE[e]?u+".custom":u;return(0,$.set)(r,a,t),r}))},d=t=>{const n=t?`settings.blocks.${t}.${e}`:`settings.${e}`,o=t=>{const r=(0,$.get)(t,n);var a,l;return i.__EXPERIMENTAL_PATHS_WITH_MERGE[e]?null!==(a=null!==(l=null==r?void 0:r.custom)&&void 0!==l?l:null==r?void 0:r.theme)&&void 0!==a?a:null==r?void 0:r.default:r};let c;switch(r){case"all":c=o(a);break;case"user":c=o(s);break;case"base":c=o(l);break;default:throw"Unsupported source"}return c},p=null!==(n=d(t))&&void 0!==n?n:d();return[p,m]}function Zn(e,t){var n;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";const{merged:a,base:l,user:i,setUserConfig:s}=(0,o.useContext)(qn),c=t?`styles.blocks.${t}.${e}`:`styles.${e}`,u=n=>{s((r=>{const l=(0,$.cloneDeep)(r);return(0,$.set)(l,c,jn(a.settings,t,e,n)),l}))};let m;switch(r){case"all":m=Yn(a.settings,t,null!==(n=(0,$.get)(i,c))&&void 0!==n?n:(0,$.get)(l,c));break;case"user":m=Yn(a.settings,t,(0,$.get)(i,c));break;case"base":m=Yn(l.settings,t,(0,$.get)(l,c));break;default:throw"Unsupported source"}return[m,u]}const Qn=["background","backgroundColor","color","linkColor","fontFamily","fontSize","fontStyle","fontWeight","lineHeight","textDecoration","textTransform","padding"];function Jn(e){if(!e)return Qn;const t=(0,i.getBlockType)(e);if(!t)return[];const n=[];return Object.keys(i.__EXPERIMENTAL_STYLE_PROPERTY).forEach((e=>{if(i.__EXPERIMENTAL_STYLE_PROPERTY[e].support)return i.__EXPERIMENTAL_STYLE_PROPERTY[e].requiresOptOut&&(0,$.has)(t.supports,i.__EXPERIMENTAL_STYLE_PROPERTY[e].support[0])&&!1!==(0,$.get)(t.supports,i.__EXPERIMENTAL_STYLE_PROPERTY[e].support)||(0,$.get)(t.supports,i.__EXPERIMENTAL_STYLE_PROPERTY[e].support,!1)?n.push(e):void 0})),n}function er(e){const[t]=Kn("color.palette.custom",e),[n]=Kn("color.palette.theme",e),[r]=Kn("color.palette.default",e),[a]=Kn("color.defaultPalette");return(0,o.useMemo)((()=>{const e=[];return n&&n.length&&e.push({name:(0,p._x)("Theme","Indicates this palette comes from the theme."),colors:n}),a&&r&&r.length&&e.push({name:(0,p._x)("Default","Indicates this palette comes from WordPress."),colors:r}),t&&t.length&&e.push({name:(0,p._x)("Custom","Indicates this palette is created by the user."),colors:t}),e}),[t,n,r])}function tr(e){return[nr(e),rr(e),ar(e),lr(e)].some(Boolean)}function nr(e){const t=Jn(e);return Kn("border.color",e)[0]&&t.includes("borderColor")}function rr(e){const t=Jn(e);return Kn("border.radius",e)[0]&&t.includes("borderRadius")}function ar(e){const t=Jn(e);return Kn("border.style",e)[0]&&t.includes("borderStyle")}function lr(e){const t=Jn(e);return Kn("border.width",e)[0]&&t.includes("borderWidth")}function or(e){let{name:t}=e;const[n]=Zn("border",t,"user"),r=e=>()=>!(null==n||!n[e]),a=e=>()=>e(void 0),l=e=>t=>{e(t||void 0)},i=(0,O.__experimentalUseCustomUnits)({availableUnits:Kn("spacing.units")[0]||["px","em","rem"]}),s=lr(t),[c,u]=Zn("border.width",t),m=ar(t),[d,_]=Zn("border.style",t),h=e=>t=>{t&&!d&&_("solid"),e(t||void 0)},g=nr(t),[f,E]=Zn("border.color",t),v=!Kn("color.custom")[0],y=!Kn("color.customGradient")[0],b=[{label:(0,p.__)("Color"),colors:er(t),colorValue:f,onColorChange:h(E),clearable:!1}],w=rr(t),[S,x]=Zn("border.radius",t);return(0,o.createElement)(O.__experimentalToolsPanel,{label:(0,p.__)("Border"),resetAll:()=>{E(void 0),x(void 0),_(void 0),u(void 0)}},s&&(0,o.createElement)(O.__experimentalToolsPanelItem,{className:"single-column",hasValue:r("width"),label:(0,p.__)("Width"),onDeselect:a(u),isShownByDefault:!0},(0,o.createElement)(O.__experimentalUnitControl,{value:c,label:(0,p.__)("Width"),min:0,onChange:h(u),units:i})),m&&(0,o.createElement)(O.__experimentalToolsPanelItem,{className:"single-column",hasValue:r("style"),label:(0,p.__)("Style"),onDeselect:a(_),isShownByDefault:!0},(0,o.createElement)(Te.__experimentalBorderStyleControl,{value:d,onChange:l(_)})),g&&(0,o.createElement)(O.__experimentalToolsPanelItem,{hasValue:r("color"),label:(0,p.__)("Color"),onDeselect:a(E),isShownByDefault:!0},(0,o.createElement)(Te.__experimentalColorGradientSettingsDropdown,{__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,disableCustomColors:v,disableCustomGradients:y,enableAlpha:!0,settings:b})),w&&(0,o.createElement)(O.__experimentalToolsPanelItem,{hasValue:()=>{const e=null==n?void 0:n.radius;return"object"==typeof e?Object.entries(e).some(Boolean):!!e},label:(0,p.__)("Radius"),onDeselect:a(x),isShownByDefault:!0},(0,o.createElement)(Te.__experimentalBorderRadiusControl,{values:S,onChange:l(x)})))}function ir(e){const t=Jn(e);return t.includes("color")||t.includes("backgroundColor")||t.includes("background")||t.includes("linkColor")}const sr=["horizontal","vertical"];function cr(e){const t=ur(e),n=mr(e),r=dr(e);return t||n||r}function ur(e){const t=Jn(e),[n]=Kn("spacing.padding",e);return n&&t.includes("padding")}function mr(e){const t=Jn(e),[n]=Kn("spacing.margin",e);return n&&t.includes("margin")}function dr(e){const t=Jn(e),[n]=Kn("spacing.blockGap",e);return!e&&(n&&t.includes("--wp--style--block-gap"))}function pr(e,t){if(!t)return e;const n={};return t.forEach((t=>{"vertical"===t&&(n.top=e.top,n.bottom=e.bottom),"horizontal"===t&&(n.left=e.left,n.right=e.right),n[t]=e[t]})),n}function _r(e){return e&&"string"==typeof e?{top:e,right:e,bottom:e,left:e}:e}function hr(e){let{name:t}=e;const n=ur(t),r=mr(t),a=dr(t),l=(0,O.__experimentalUseCustomUnits)({availableUnits:Kn("spacing.units",t)[0]||["%","px","em","rem","vw"]}),[i,s]=Zn("spacing.padding",t),c=_r(i),u=(0,Te.__experimentalUseCustomSides)(t,"padding"),m=u&&u.some((e=>sr.includes(e))),d=e=>{const t=pr(e,u);s(t)},_=()=>d({}),[h,g]=Zn("spacing.margin",t),f=_r(h),E=(0,Te.__experimentalUseCustomSides)(t,"margin"),v=E&&E.some((e=>sr.includes(e))),y=e=>{const t=pr(e,E);g(t)},b=()=>y({}),[w,S]=Zn("spacing.blockGap",t),x=()=>S(void 0);return(0,o.createElement)(O.__experimentalToolsPanel,{label:(0,p.__)("Dimensions"),resetAll:()=>{_(),b(),x()}},n&&(0,o.createElement)(O.__experimentalToolsPanelItem,{hasValue:()=>!!c&&Object.keys(c).length,label:(0,p.__)("Padding"),onDeselect:_,isShownByDefault:!0},(0,o.createElement)(O.__experimentalBoxControl,{values:c,onChange:d,label:(0,p.__)("Padding"),sides:u,units:l,allowReset:!1,splitOnAxis:m})),r&&(0,o.createElement)(O.__experimentalToolsPanelItem,{hasValue:()=>!!f&&Object.keys(f).length,label:(0,p.__)("Margin"),onDeselect:b,isShownByDefault:!0},(0,o.createElement)(O.__experimentalBoxControl,{values:f,onChange:y,label:(0,p.__)("Margin"),sides:E,units:l,allowReset:!1,splitOnAxis:v})),a&&(0,o.createElement)(O.__experimentalToolsPanelItem,{hasValue:()=>!!w,label:(0,p.__)("Block spacing"),onDeselect:x,isShownByDefault:!0},(0,o.createElement)(O.__experimentalUnitControl,{label:(0,p.__)("Block spacing"),__unstableInputWidth:"80px",min:0,onChange:S,units:l,value:w})))}function gr(e){const t=fr(e),n=Er(e),r=vr(e),a=Jn(e);return t||n||r||a.includes("fontSize")}function fr(e){const t=Jn(e);return Kn("typography.lineHeight",e)[0]&&t.includes("lineHeight")}function Er(e){const t=Jn(e),n=Kn("typography.fontStyle",e)[0]&&t.includes("fontStyle"),r=Kn("typography.fontWeight",e)[0]&&t.includes("fontWeight");return n||r}function vr(e){const t=Jn(e);return Kn("typography.letterSpacing",e)[0]&&t.includes("letterSpacing")}function yr(e){let{name:t,element:n}=e;const r=Jn(t),a="text"!==n&&n?`elements.${n}.`:"",[l]=Kn("typography.fontSizes",t),i=!Kn("typography.customFontSize",t)[0],[s]=Kn("typography.fontFamilies",t),c=Kn("typography.fontStyle",t)[0]&&r.includes("fontStyle"),u=Kn("typography.fontWeight",t)[0]&&r.includes("fontWeight"),m=fr(t),d=Er(t),p=vr(t),[_,h]=Zn(a+"typography.fontFamily",t),[g,f]=Zn(a+"typography.fontSize",t),[E,v]=Zn(a+"typography.fontStyle",t),[y,b]=Zn(a+"typography.fontWeight",t),[w,S]=Zn(a+"typography.lineHeight",t),[x,k]=Zn(a+"typography.letterSpacing",t),[C]=Zn(a+"color.background",t),[T]=Zn(a+"color.gradient",t),[N]=Zn(a+"color.text",t),I="link"===n?{textDecoration:"underline"}:{};return(0,o.createElement)(O.PanelBody,{className:"edit-site-typography-panel",initialOpen:!0},(0,o.createElement)("div",{className:"edit-site-typography-panel__preview",style:{fontFamily:null!=_?_:"serif",background:null!=T?T:C,color:N,fontSize:g,fontStyle:E,fontWeight:y,letterSpacing:x,...I}},"Aa"),r.includes("fontFamily")&&(0,o.createElement)(Te.__experimentalFontFamilyControl,{fontFamilies:s,value:_,onChange:h}),r.includes("fontSize")&&(0,o.createElement)(O.FontSizePicker,{value:g,onChange:f,fontSizes:l,disableCustomFontSizes:i}),m&&(0,o.createElement)(O.__experimentalSpacer,{marginBottom:6},(0,o.createElement)(Te.LineHeightControl,{__nextHasNoMarginBottom:!0,value:w,onChange:S})),d&&(0,o.createElement)(Te.__experimentalFontAppearanceControl,{value:{fontStyle:E,fontWeight:y},onChange:e=>{let{fontStyle:t,fontWeight:n}=e;v(t),b(n)},hasFontStyles:c,hasFontWeights:u}),p&&(0,o.createElement)(Te.__experimentalLetterSpacingControl,{value:x,onChange:k}))}var br=function(e){let{name:t,parentMenu:n=""}=e;const r=gr(t),a=ir(t),l=tr(t),i=cr(t),s=l||i;return(0,o.createElement)(O.__experimentalItemGroup,null,r&&(0,o.createElement)(Vn,{icon:Dn,path:n+"/typography"},(0,p.__)("Typography")),a&&(0,o.createElement)(Vn,{icon:Fn,path:n+"/colors"},(0,p.__)("Colors")),s&&(0,o.createElement)(Vn,{icon:Hn,path:n+"/layout"},(0,p.__)("Layout")))};function wr(e,t,n,r){const a=(0,$.get)(e,n);if(!a)return[];const l=[];if("string"==typeof a)l.push({selector:t.selector,key:r,value:a});else{const e=["top","right","bottom","left"].reduce(((e,n)=>{const l=(0,$.get)(a,[n]);return l&&e.push({selector:t.selector,key:`${r}${(0,$.upperFirst)(n)}`,value:l}),e}),[]);l.push(...e)}return l}const Sr=[{name:"margin",generate:(e,t)=>wr(e,t,["spacing","margin"],"margin")},{name:"padding",generate:(e,t)=>wr(e,t,["spacing","padding"],"padding")}];function xr(e,t){const n=[];return Sr.forEach((r=>{n.push(...r.generate(e,t))})),n}function kr(e){const t="var:";if((0,$.startsWith)(e,t)){return`var(--wp--${e.slice(t.length).split("|").join("--")})`}return e}function Cr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,r=[];return Object.keys(e).forEach((a=>{const l=t+(0,$.kebabCase)(a.replace("/","-")),o=e[a];if(o instanceof Object){const e=l+n;r=[...r,...Cr(o,e,n)]}else r.push(`${l}: ${o}`)})),r}function Tr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,$.reduce)(i.__EXPERIMENTAL_STYLE_PROPERTY,((t,n,r)=>{let{value:a,properties:l,useEngine:o}=n;const i=a;if("elements"===(0,$.first)(i)||o)return t;const s=(0,$.get)(e,i);if(l&&!(0,$.isString)(s))Object.entries(l).forEach((e=>{const[n,r]=e;if(!(0,$.get)(s,[r],!1))return;const a=(0,$.kebabCase)(n);t.push(`${a}: ${kr((0,$.get)(s,[r]))}`)}));else if((0,$.get)(e,i,!1)){const n=r.startsWith("--")?r:(0,$.kebabCase)(r);t.push(`${n}: ${kr((0,$.get)(e,i))}`)}return t}),[]),n=xr(e,{selector:"self"});return n.forEach((e=>{if("self"!==e.selector)throw"This style can't be added as inline style";const n=e.key.startsWith("--")?e.key:(0,$.kebabCase)(e.key);t.push(`${n}: ${kr(e.value)}`)})),t}const Nr=(e,t)=>{var n,r;const a=[];if(null==e||!e.settings)return a;const l=e=>{const t={};return Un.forEach((n=>{let{path:r}=n;const a=(0,$.get)(e,r,!1);!1!==a&&(0,$.set)(t,r,a)})),t},o=l(e.settings),i=null===(n=e.settings)||void 0===n?void 0:n.custom;return(0,$.isEmpty)(o)&&!i||a.push({presets:o,custom:i,selector:Gn}),(0,$.forEach)(null===(r=e.settings)||void 0===r?void 0:r.blocks,((e,n)=>{const r=l(e),o=e.custom;(0,$.isEmpty)(r)&&!o||a.push({presets:r,custom:o,selector:t[n].selector})})),a},Ir=(e,t)=>{const n=Nr(e,t);let r="";return n.forEach((e=>{let{presets:t,custom:n,selector:a}=e;const l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,$.reduce)(Un,((t,n)=>{let{path:r,valueKey:a,valueFunc:l,cssVarInfix:o}=n;const i=(0,$.get)(e,r,[]);return["default","theme","custom"].forEach((e=>{i[e]&&i[e].forEach((e=>{a?t.push(`--wp--preset--${o}--${(0,$.kebabCase)(e.slug)}: ${e[a]}`):l&&"function"==typeof l&&t.push(`--wp--preset--${o}--${(0,$.kebabCase)(e.slug)}: ${l(e)}`)}))})),t}),[])}(t),o=Cr(n,"--wp--custom--","--");o.length>0&&l.push(...o),l.length>0&&(r+=`${a}{${l.join(";")};}`)})),r},Pr=(e,t)=>{const n=((e,t)=>{var n,r;const a=[];if(null==e||!e.styles)return a;const l=e=>(0,$.pickBy)(e,((e,t)=>["border","color","spacing","typography","filter"].includes(t))),o=l(e.styles);return o&&a.push({styles:o,selector:Gn}),(0,$.forEach)(null===(n=e.styles)||void 0===n?void 0:n.elements,((e,t)=>{e&&i.__EXPERIMENTAL_ELEMENTS[t]&&a.push({styles:e,selector:i.__EXPERIMENTAL_ELEMENTS[t]})})),(0,$.forEach)(null===(r=e.styles)||void 0===r?void 0:r.blocks,((e,n)=>{var r;const o=l(e);o&&null!=t&&null!==(r=t[n])&&void 0!==r&&r.selector&&a.push({styles:o,selector:t[n].selector,duotoneSelector:t[n].duotoneSelector}),(0,$.forEach)(null==e?void 0:e.elements,((e,r)=>{e&&null!=t&&t[n]&&null!==i.__EXPERIMENTAL_ELEMENTS&&void 0!==i.__EXPERIMENTAL_ELEMENTS&&i.__EXPERIMENTAL_ELEMENTS[r]&&a.push({styles:e,selector:t[n].selector.split(",").map((e=>e+" "+i.__EXPERIMENTAL_ELEMENTS[r])).join(",")})}))})),a})(e,t),r=Nr(e,t);let a="body {margin: 0;}";return n.forEach((e=>{let{selector:t,duotoneSelector:n,styles:r}=e;const l={};if(null!=r&&r.filter&&(l.filter=r.filter,delete r.filter),n){const e=Tr(l);if(0===e.length)return;a+=`${n}{${e.join(";")};}`}const o=Tr(r);0!==o.length&&(a+=`${t}{${o.join(";")};}`)})),r.forEach((e=>{let{selector:t,presets:n}=e;Gn===t&&(t="");const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,$.reduce)(Un,((n,r)=>{let{path:a,cssVarInfix:l,classes:o}=r;if(!o)return n;const i=(0,$.get)(t,a,[]);return["default","theme","custom"].forEach((t=>{i[t]&&i[t].forEach((t=>{let{slug:r}=t;o.forEach((t=>{let{classSuffix:a,propertyName:o}=t;const i=`.has-${(0,$.kebabCase)(r)}-${a}`,s=e.split(",").map((e=>`${e}${i}`)).join(","),c=`var(--wp--preset--${l}--${(0,$.kebabCase)(r)})`;n+=`${s}{${o}: ${c} !important;}`}))}))})),n}),"")}(t,n);(0,$.isEmpty)(r)||(a+=r)})),a};function Mr(e,t){return Nr(e,t).flatMap((e=>{let{presets:t}=e;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Un.filter((e=>"duotone"===e.path.at(-1))).flatMap((t=>{const n=(0,$.get)(e,t.path,{});return["default","theme"].filter((e=>n[e])).flatMap((e=>n[e].map((e=>(0,o.createElement)(Te.__unstablePresetDuotoneFilter,{preset:e,key:e.slug})))))}))}(t)}))}function Br(){const[e,t]=(0,o.useState)([]),[n,r]=(0,o.useState)({}),[a,l]=(0,o.useState)({}),{merged:s}=(0,o.useContext)(qn);return(0,o.useEffect)((()=>{if(null==s||!s.styles||null==s||!s.settings)return;const e=(e=>{const t={};return e.forEach((e=>{var n,r,a,l,o;const i=e.name,s=null!==(n=null==e||null===(r=e.supports)||void 0===r?void 0:r.__experimentalSelector)&&void 0!==n?n:".wp-block-"+i.replace("core/","").replace("/","-"),c=null!==(a=null==e||null===(l=e.supports)||void 0===l||null===(o=l.color)||void 0===o?void 0:o.__experimentalDuotone)&&void 0!==a?a:null;t[i]={name:i,selector:s,duotoneSelector:c}})),t})((0,i.getBlockTypes)()),n=Ir(s,e),a=Pr(s,e),o=Mr(s,e);t([{css:n,isGlobalStyles:!0},{css:a,isGlobalStyles:!0}]),r(s.settings),l(o)}),[s]),[e,n,a]}const Rr={start:{opacity:1,display:"block"},hover:{opacity:0,display:"none"}},Ar={hover:{opacity:1,display:"block"},start:{opacity:0,display:"none"}};var zr=e=>{let{label:t,isFocused:n}=e;const[r]=Zn("typography.fontWeight"),[a="serif"]=Zn("typography.fontFamily"),[l=a]=Zn("elements.h1.typography.fontFamily"),[i=r]=Zn("elements.h1.typography.fontWeight"),[s="black"]=Zn("color.text"),[c=s]=Zn("elements.h1.color.text"),[u="blue"]=Zn("elements.link.color.text"),[m="white"]=Zn("color.background"),[d]=Zn("color.gradient"),[p]=Br(),_=(0,he.useReducedMotion)(),[h]=Kn("color.palette.core"),[g]=Kn("color.palette.theme"),[f]=Kn("color.palette.custom"),[E,v]=(0,o.useState)(!1),[y,{width:b}]=(0,he.useResizeObserver)(),w=b?b/248:1,S=(null!=g?g:[]).concat(null!=f?f:[]).concat(null!=h?h:[]),x=S.filter((e=>{let{color:t}=e;return t!==m&&t!==c})).slice(0,2),k=(0,o.useMemo)((()=>p?[...p,{css:"body{min-width: 0;}",isGlobalStyles:!0}]:p),[p]);return(0,o.createElement)(Te.__unstableIframe,{className:"edit-site-global-styles-preview__iframe",head:(0,o.createElement)(Te.__unstableEditorStyles,{styles:k}),style:{height:152*w,visibility:b?"visible":"hidden"},onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),tabIndex:-1},y,(0,o.createElement)(O.__unstableMotion.div,{style:{height:152*w,width:"100%",background:null!=d?d:m,cursor:"pointer"},initial:"start",animate:!E&&!n||_?"start":"hover"},(0,o.createElement)(O.__unstableMotion.div,{variants:Rr,style:{height:"100%",overflow:"hidden"}},(0,o.createElement)(O.__experimentalHStack,{spacing:10*w,justify:"center",style:{height:"100%",overflow:"hidden"}},(0,o.createElement)("div",{style:{fontFamily:l,fontSize:65*w,color:c,fontWeight:i}},"Aa"),(0,o.createElement)(O.__experimentalVStack,{spacing:4*w},x.map((e=>{let{slug:t,color:n}=e;return(0,o.createElement)("div",{key:t,style:{height:32*w,width:32*w,background:n,borderRadius:32*w/2}})}))))),(0,o.createElement)(O.__unstableMotion.div,{variants:Ar,style:{height:"100%",overflow:"hidden"}},(0,o.createElement)(O.__experimentalVStack,{spacing:3*w,justify:"center",style:{height:"100%",overflow:"hidden",padding:10*w,boxSizing:"border-box"}},t&&(0,o.createElement)("div",{style:{fontSize:35*w,fontFamily:l,color:c,fontWeight:i,lineHeight:"1em"}},t),(0,o.createElement)(O.__experimentalHStack,{spacing:2*w,justify:"flex-start"},(0,o.createElement)("div",{style:{fontFamily:a,fontSize:24*w,color:s}},"Aa"),(0,o.createElement)("div",{style:{fontFamily:a,fontSize:24*w,color:u}},"Aa")),S&&(0,o.createElement)(O.__experimentalHStack,{spacing:0},S.slice(0,4).map(((e,t)=>{let{color:n}=e;return(0,o.createElement)("div",{key:t,style:{height:10*w,width:30*w,background:n,flexGrow:1}})})))))))};var Lr=function(){const{variations:e}=(0,c.useSelect)((e=>({variations:e(u.store).__experimentalGetCurrentThemeGlobalStylesVariations()})),[]);return(0,o.createElement)(O.Card,{size:"small"},(0,o.createElement)(O.CardBody,null,(0,o.createElement)(O.__experimentalVStack,{spacing:2},(0,o.createElement)(O.Card,null,(0,o.createElement)(O.CardMedia,null,(0,o.createElement)(zr,null))),!(null==e||!e.length)&&(0,o.createElement)(Vn,{path:"/variations"},(0,o.createElement)(O.__experimentalHStack,{justify:"space-between"},(0,o.createElement)(O.FlexItem,null,(0,p.__)("Browse styles")),(0,o.createElement)(zn,{icon:(0,p.isRTL)()?be:we}))))),(0,o.createElement)(O.CardBody,null,(0,o.createElement)(br,null)),(0,o.createElement)(O.CardDivider,null),(0,o.createElement)(O.CardBody,null,(0,o.createElement)(O.__experimentalItemGroup,null,(0,o.createElement)(O.__experimentalItem,null,(0,p.__)("Customize the appearance of specific blocks for the whole site.")),(0,o.createElement)(Vn,{path:"/blocks"},(0,o.createElement)(O.__experimentalHStack,{justify:"space-between"},(0,o.createElement)(O.FlexItem,null,(0,p.__)("Blocks")),(0,o.createElement)(zn,{icon:(0,p.isRTL)()?be:we}))))))};var Vr=function(e){let{title:t,description:n}=e;return(0,o.createElement)(O.__experimentalVStack,{spacing:2},(0,o.createElement)(O.__experimentalHStack,{spacing:2},(0,o.createElement)(O.__experimentalView,null,(0,o.createElement)(On,{icon:(0,p.isRTL)()?we:be,size:"small","aria-label":(0,p.__)("Navigate to the previous view")})),(0,o.createElement)(O.__experimentalSpacer,null,(0,o.createElement)(O.__experimentalHeading,{level:5},t))),n&&(0,o.createElement)("p",{className:"edit-site-global-styles-header__description"},n))};function Or(e){let{block:t}=e;const n=gr(t.name),r=ir(t.name),a=tr(t.name),l=cr(t.name);return n||r||(a||l)?(0,o.createElement)(Vn,{path:"/blocks/"+t.name},(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(Te.BlockIcon,{icon:t.icon}),(0,o.createElement)(O.FlexItem,null,t.title))):null}var Dr=function(){const e=function(){const e=(0,c.useSelect)((e=>e(i.store).getBlockTypes()),[]),{core:t,noncore:n}=e.reduce(((e,t)=>{const{core:n,noncore:r}=e;return(t.name.startsWith("core/")?n:r).push(t),e}),{core:[],noncore:[]});return[...t,...n]}(),[t,n]=(0,o.useState)(""),r=(0,he.useDebounce)(Ne.speak,500),a=(0,c.useSelect)((e=>e(i.store).isMatchingSearchTerm),[]),l=(0,o.useMemo)((()=>t?e.filter((e=>a(e,t))):e),[t,e,a]),s=(0,o.useRef)();return(0,o.useEffect)((()=>{if(!t)return;const e=s.current.childElementCount,n=(0,p.sprintf)((0,p._n)("%d result found.","%d results found.",e),e);r(n,e)}),[t,r]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Blocks"),description:(0,p.__)("Customize the appearance of specific blocks and for the whole site.")}),(0,o.createElement)(O.SearchControl,{className:"edit-site-block-types-search",onChange:n,value:t,label:(0,p.__)("Search for blocks"),placeholder:(0,p.__)("Search")}),(0,o.createElement)("div",{ref:s,className:"edit-site-block-types-item-list"},l.map((e=>(0,o.createElement)(Or,{block:e,key:"menu-itemblock-"+e.name})))))};var Fr=function(e){let{name:t}=e;const n=(0,i.getBlockType)(t);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:n.title}),(0,o.createElement)(br,{parentMenu:"/blocks/"+t,name:t}))};var Hr=function(e){let{children:t}=e;return(0,o.createElement)(O.__experimentalHeading,{className:"edit-site-global-styles-subtitle",level:2},t)};function Gr(e){let{name:t,parentMenu:n,element:r,label:a}=e;const l=!t,i="text"!==r&&r?`elements.${r}.`:"",s="link"===r?{textDecoration:"underline"}:{},[c]=Zn(i+"typography.fontFamily",t),[u]=Zn(i+"typography.fontStyle",t),[m]=Zn(i+"typography.fontWeight",t),[d]=Zn(i+"typography.letterSpacing",t),[_]=Zn(i+"color.background",t),[h]=Zn(i+"color.gradient",t),[g]=Zn(i+"color.text",t);return l?(0,o.createElement)(Vn,{path:n+"/typography/"+r},(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(O.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=c?c:"serif",background:null!=h?h:_,color:g,fontStyle:u,fontWeight:m,letterSpacing:d,...s}},(0,p.__)("Aa")),(0,o.createElement)(O.FlexItem,null,a))):null}var Ur=function(e){let{name:t}=e;const n=void 0===t?"":"/blocks/"+t;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Typography"),description:(0,p.__)("Manage the typography settings for different elements.")}),!t&&(0,o.createElement)("div",{className:"edit-site-global-styles-screen-typography"},(0,o.createElement)(O.__experimentalVStack,{spacing:3},(0,o.createElement)(Hr,null,(0,p.__)("Elements")),(0,o.createElement)(O.__experimentalItemGroup,{isBordered:!0,isSeparated:!0},(0,o.createElement)(Gr,{name:t,parentMenu:n,element:"text",label:(0,p.__)("Text")}),(0,o.createElement)(Gr,{name:t,parentMenu:n,element:"link",label:(0,p.__)("Links")})))),!!t&&(0,o.createElement)(yr,{name:t,element:"text"}))};const $r={text:{description:(0,p.__)("Manage the fonts used on the site."),title:(0,p.__)("Text")},link:{description:(0,p.__)("Manage the fonts and typography used on the links."),title:(0,p.__)("Links")}};var Wr=function(e){let{name:t,element:n}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:$r[n].title,description:$r[n].description}),(0,o.createElement)(yr,{name:t,element:n}))};var jr=function(e){let{className:t,...n}=e;return(0,o.createElement)(O.Flex,z({className:V()("edit-site-global-styles__color-indicator-wrapper",t)},n))};const Yr=[];var qr=function(e){let{name:t}=e;const[n]=Kn("color.palette.custom"),[r]=Kn("color.palette.theme"),[a]=Kn("color.palette.default"),[l]=Kn("color.defaultPalette",t),i=(0,o.useMemo)((()=>[...n||Yr,...r||Yr,...a&&l?a:Yr]),[n,r,a,l]),s=t?"/blocks/"+t+"/colors/palette":"/colors/palette",c=i.length>0?(0,p.sprintf)((0,p._n)("%d color","%d colors",i.length),i.length):(0,p.__)("Add custom colors");return(0,o.createElement)(O.__experimentalVStack,{spacing:3},(0,o.createElement)(Hr,null,(0,p.__)("Palette")),(0,o.createElement)(O.__experimentalItemGroup,{isBordered:!0,isSeparated:!0},(0,o.createElement)(Vn,{path:s},(0,o.createElement)(O.__experimentalHStack,{direction:0===i.length?"row-reverse":"row"},(0,o.createElement)(O.__experimentalZStack,{isLayered:!1,offset:-8},i.slice(0,5).map((e=>{let{color:t}=e;return(0,o.createElement)(jr,{key:t},(0,o.createElement)(O.ColorIndicator,{colorValue:t}))}))),(0,o.createElement)(O.FlexItem,null,c)))))};function Xr(e){let{name:t,parentMenu:n}=e;const r=Jn(t),a=r.includes("backgroundColor")||r.includes("background"),[l]=Zn("color.background",t),[i]=Zn("color.gradient",t);return a?(0,o.createElement)(Vn,{path:n+"/colors/background"},(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(jr,{expanded:!1},(0,o.createElement)(O.ColorIndicator,{colorValue:null!=i?i:l})),(0,o.createElement)(O.FlexItem,null,(0,p.__)("Background")))):null}function Kr(e){let{name:t,parentMenu:n}=e;const r=Jn(t).includes("color"),[a]=Zn("color.text",t);return r?(0,o.createElement)(Vn,{path:n+"/colors/text"},(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(jr,{expanded:!1},(0,o.createElement)(O.ColorIndicator,{colorValue:a})),(0,o.createElement)(O.FlexItem,null,(0,p.__)("Text")))):null}function Zr(e){let{name:t,parentMenu:n}=e;const r=Jn(t).includes("linkColor"),[a]=Zn("elements.link.color.text",t);return r?(0,o.createElement)(Vn,{path:n+"/colors/link"},(0,o.createElement)(O.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(jr,{expanded:!1},(0,o.createElement)(O.ColorIndicator,{colorValue:a})),(0,o.createElement)(O.FlexItem,null,(0,p.__)("Links")))):null}var Qr=function(e){let{name:t}=e;const n=void 0===t?"":"/blocks/"+t;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Colors"),description:(0,p.__)("Manage palettes and the default color of different global elements on the site.")}),(0,o.createElement)("div",{className:"edit-site-global-styles-screen-colors"},(0,o.createElement)(O.__experimentalVStack,{spacing:10},(0,o.createElement)(qr,{name:t}),(0,o.createElement)(O.__experimentalVStack,{spacing:3},(0,o.createElement)(Hr,null,(0,p.__)("Elements")),(0,o.createElement)(O.__experimentalItemGroup,{isBordered:!0,isSeparated:!0},(0,o.createElement)(Xr,{name:t,parentMenu:n}),(0,o.createElement)(Kr,{name:t,parentMenu:n}),(0,o.createElement)(Zr,{name:t,parentMenu:n}))))))};function Jr(e){let{name:t}=e;const[n,r]=Kn("color.palette.theme",t),[a]=Kn("color.palette.theme",t,"base"),[l,i]=Kn("color.palette.default",t),[s]=Kn("color.palette.default",t,"base"),[c,u]=Kn("color.palette.custom",t),[m]=Kn("color.defaultPalette",t);return(0,o.createElement)(O.__experimentalVStack,{className:"edit-site-global-styles-color-palette-panel",spacing:10},!!n&&!!n.length&&(0,o.createElement)(O.__experimentalPaletteEdit,{canReset:n!==a,canOnlyChangeValues:!0,colors:n,onChange:r,paletteLabel:(0,p.__)("Theme")}),!!l&&!!l.length&&!!m&&(0,o.createElement)(O.__experimentalPaletteEdit,{canReset:l!==s,canOnlyChangeValues:!0,colors:l,onChange:i,paletteLabel:(0,p.__)("Default")}),(0,o.createElement)(O.__experimentalPaletteEdit,{colors:c,onChange:u,paletteLabel:(0,p.__)("Custom"),emptyMessage:(0,p.__)("Custom colors are empty! Add some colors to create your own color palette."),slugPrefix:"custom-"}))}function ea(e){let{name:t}=e;const[n,r]=Kn("color.gradients.theme",t),[a]=Kn("color.gradients.theme",t,"base"),[l,i]=Kn("color.gradients.default",t),[s]=Kn("color.gradients.default",t,"base"),[c,u]=Kn("color.gradients.custom",t),[m]=Kn("color.defaultGradients",t),[d]=Kn("color.duotone")||[];return(0,o.createElement)(O.__experimentalVStack,{className:"edit-site-global-styles-gradient-palette-panel",spacing:10},!!n&&!!n.length&&(0,o.createElement)(O.__experimentalPaletteEdit,{canReset:n!==a,canOnlyChangeValues:!0,gradients:n,onChange:r,paletteLabel:(0,p.__)("Theme")}),!!l&&!!l.length&&!!m&&(0,o.createElement)(O.__experimentalPaletteEdit,{canReset:l!==s,canOnlyChangeValues:!0,gradients:l,onChange:i,paletteLabel:(0,p.__)("Default")}),(0,o.createElement)(O.__experimentalPaletteEdit,{gradients:c,onChange:u,paletteLabel:(0,p.__)("Custom"),emptyMessage:(0,p.__)("Custom gradients are empty! Add some gradients to create your own palette."),slugPrefix:"custom-"}),(0,o.createElement)("div",null,(0,o.createElement)(Hr,null,(0,p.__)("Duotone")),(0,o.createElement)(O.__experimentalSpacer,{margin:3}),(0,o.createElement)(O.DuotonePicker,{duotonePalette:d,disableCustomDuotone:!0,disableCustomColors:!0,clearable:!1,onChange:$.noop})))}var ta=function(e){let{name:t}=e;const[n,r]=(0,o.useState)("solid");return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Palette"),description:(0,p.__)("Palettes are used to provide default color options for blocks and various design tools. Here you can edit the colors with their labels.")}),(0,o.createElement)(O.__experimentalToggleGroupControl,{className:"edit-site-screen-color-palette-toggle",value:n,onChange:r,label:(0,p.__)("Select palette type"),hideLabelFromVision:!0,isBlock:!0},(0,o.createElement)(O.__experimentalToggleGroupControlOption,{value:"solid",label:(0,p.__)("Solid")}),(0,o.createElement)(O.__experimentalToggleGroupControlOption,{value:"gradient",label:(0,p.__)("Gradient")})),"solid"===n&&(0,o.createElement)(Jr,{name:t}),"gradient"===n&&(0,o.createElement)(ea,{name:t}))};var na=function(e){let{name:t}=e;const n=Jn(t),[r]=Kn("color.palette",t),[a]=Kn("color.gradients",t),[l]=Kn("color.custom",t),[i]=Kn("color.customGradient",t),s=er(t),c=function(e){const[t]=Kn("color.gradients.custom",e),[n]=Kn("color.gradients.theme",e),[r]=Kn("color.gradients.default",e),[a]=Kn("color.defaultGradients");return(0,o.useMemo)((()=>{const e=[];return n&&n.length&&e.push({name:(0,p._x)("Theme","Indicates this palette comes from the theme."),gradients:n}),a&&r&&r.length&&e.push({name:(0,p._x)("Default","Indicates this palette comes from WordPress."),gradients:r}),t&&t.length&&e.push({name:(0,p._x)("Custom","Indicates this palette is created by the user."),gradients:t}),e}),[t,n,r])}(t),[u]=Kn("color.background",t),m=n.includes("backgroundColor")&&u&&(r.length>0||l),d=n.includes("background")&&(a.length>0||i),[_,h]=Zn("color.background",t),[g]=Zn("color.background",t,"user"),[f,E]=Zn("color.gradient",t),[v]=Zn("color.gradient",t,"user");if(!m&&!d)return null;let y={};m&&(y={colorValue:_,onColorChange:h},_&&(y.clearable=_===g));let b={};d&&(b={gradientValue:f,onGradientChange:E},f&&(b.clearable=f===v));const w={...y,...b};return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Background"),description:(0,p.__)("Set a background color or gradient for the whole site.")}),(0,o.createElement)(Te.__experimentalColorGradientControl,z({className:"edit-site-screen-background-color__control",colors:s,gradients:c,disableCustomColors:!l,disableCustomGradients:!i,__experimentalHasMultipleOrigins:!0,showTitle:!1,enableAlpha:!0,__experimentalIsRenderedInSidebar:!0},w)))};var ra=function(e){let{name:t}=e;const n=Jn(t),[r]=Kn("color.palette",t),[a]=Kn("color.custom",t),[l]=Kn("color.text",t),i=er(t),s=n.includes("color")&&l&&(r.length>0||a),[c,u]=Zn("color.text",t),[m]=Zn("color.text",t,"user");return s?(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Text"),description:(0,p.__)("Set the default color used for text across the site.")}),(0,o.createElement)(Te.__experimentalColorGradientControl,{className:"edit-site-screen-text-color__control",colors:i,disableCustomColors:!a,__experimentalHasMultipleOrigins:!0,showTitle:!1,enableAlpha:!0,__experimentalIsRenderedInSidebar:!0,colorValue:c,onColorChange:u,clearable:c===m})):null};var aa=function(e){let{name:t}=e;const n=Jn(t),[r]=Kn("color.palette",t),[a]=Kn("color.custom",t),l=er(t),[i]=Kn("color.link",t),s=n.includes("linkColor")&&i&&(r.length>0||a),[c,u]=Zn("elements.link.color.text",t),[m]=Zn("elements.link.color.text",t,"user");return s?(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Links"),description:(0,p.__)("Set the default color used for links across the site.")}),(0,o.createElement)(Te.__experimentalColorGradientControl,{className:"edit-site-screen-link-color__control",colors:l,disableCustomColors:!a,__experimentalHasMultipleOrigins:!0,showTitle:!1,enableAlpha:!0,__experimentalIsRenderedInSidebar:!0,colorValue:c,onColorChange:u,clearable:c===m})):null};var la=function(e){let{name:t}=e;const n=tr(t),r=cr(t);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{title:(0,p.__)("Layout")}),r&&(0,o.createElement)(hr,{name:t}),n&&(0,o.createElement)(or,{name:t}))};function oa(e,t){if(Array.isArray(t))return t}function ia(e,t){return(0,$.mergeWith)({},e,t,oa)}const sa=e=>{if(!(0,$.isObject)(e)||Array.isArray(e))return e;const t=(0,$.pickBy)((0,$.mapValues)(e,sa),$.identity);return(0,$.isEmpty)(t)?void 0:t};function ca(){const[e,t,n]=function(){const{globalStylesId:e,settings:t,styles:n}=(0,c.useSelect)((e=>{const t=e(u.store).__experimentalGetCurrentGlobalStylesId(),n=t?e(u.store).getEditedEntityRecord("root","globalStyles",t):void 0;return{globalStylesId:t,settings:null==n?void 0:n.settings,styles:null==n?void 0:n.styles}}),[]),{getEditedEntityRecord:r}=(0,c.useSelect)(u.store),{editEntityRecord:a}=(0,c.useDispatch)(u.store),l=(0,o.useMemo)((()=>({settings:null!=t?t:{},styles:null!=n?n:{}})),[t,n]),i=(0,o.useCallback)((t=>{var n,l;const o=r("root","globalStyles",e),i=t({styles:null!==(n=null==o?void 0:o.styles)&&void 0!==n?n:{},settings:null!==(l=null==o?void 0:o.settings)&&void 0!==l?l:{}});a("root","globalStyles",e,{styles:sa(i.styles)||{},settings:sa(i.settings)||{}})}),[e]);return[!!t||!!n,l,i]}(),[r,a]=function(){const e=(0,c.useSelect)((e=>e(u.store).__experimentalGetCurrentThemeBaseGlobalStyles()),[]);return[!!e,e]}(),l=(0,o.useMemo)((()=>a&&t?ia(a,t):{}),[t,a]);return(0,o.useMemo)((()=>({isReady:e&&r,user:t,base:a,merged:l,setUserConfig:n})),[l,t,a,n,e,r])}function ua(e){let{children:t}=e;const n=ca();return n.isReady?(0,o.createElement)(qn.Provider,{value:n},t):null}function ma(e){let{variation:t}=e;const[n,r]=(0,o.useState)(!1),{base:a,user:l,setUserConfig:i}=(0,o.useContext)(qn),s=(0,o.useMemo)((()=>{var e,n;return{user:{settings:null!==(e=t.settings)&&void 0!==e?e:{},styles:null!==(n=t.styles)&&void 0!==n?n:{}},base:a,merged:ia(a,t),setUserConfig:()=>{}}}),[t,a]),c=()=>{i((()=>({settings:t.settings,styles:t.styles})))},u=(0,o.useMemo)((()=>{return e=l,n=t,(0,$.isEqual)(e.styles,n.styles)&&(0,$.isEqual)(e.settings,n.settings);var e,n}),[l,t]);return(0,o.createElement)(qn.Provider,{value:s},(0,o.createElement)("div",{className:V()("edit-site-global-styles-variations_item",{"is-active":u}),role:"button",onClick:c,onKeyDown:e=>{e.keyCode===$t.ENTER&&(e.preventDefault(),c())},tabIndex:"0","aria-label":null==t?void 0:t.title,onFocus:()=>r(!0),onBlur:()=>r(!1)},(0,o.createElement)("div",{className:"edit-site-global-styles-variations_item-preview"},(0,o.createElement)(zr,{label:null==t?void 0:t.title,isFocused:n}))))}var da=function(){const{variations:e}=(0,c.useSelect)((e=>({variations:e(u.store).__experimentalGetCurrentThemeGlobalStylesVariations()})),[]),t=(0,o.useMemo)((()=>[{title:(0,p.__)("Default"),settings:{},styles:{}},...e]),[e]);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Vr,{back:"/",title:(0,p.__)("Browse styles"),description:(0,p.__)("Choose a different style combination for the theme styles")}),(0,o.createElement)(O.Card,{size:"small",isBorderless:!0},(0,o.createElement)(O.CardBody,null,(0,o.createElement)(O.__experimentalGrid,{columns:2},null==t?void 0:t.map(((e,t)=>(0,o.createElement)(ma,{key:t,variation:e})))))))};function pa(e){let{className:t,...n}=e;return(0,o.createElement)(O.__experimentalNavigatorScreen,z({className:["edit-site-global-styles-sidebar__navigator-screen",t].filter(Boolean).join(" ")},n))}function _a(e){let{name:t}=e;const n=void 0===t?"":"/blocks/"+t;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(pa,{path:n+"/typography"},(0,o.createElement)(Ur,{name:t})),(0,o.createElement)(pa,{path:n+"/typography/text"},(0,o.createElement)(Wr,{name:t,element:"text"})),(0,o.createElement)(pa,{path:n+"/typography/link"},(0,o.createElement)(Wr,{name:t,element:"link"})),(0,o.createElement)(pa,{path:n+"/colors"},(0,o.createElement)(Qr,{name:t})),(0,o.createElement)(pa,{path:n+"/colors/palette"},(0,o.createElement)(ta,{name:t})),(0,o.createElement)(pa,{path:n+"/colors/background"},(0,o.createElement)(na,{name:t})),(0,o.createElement)(pa,{path:n+"/colors/text"},(0,o.createElement)(ra,{name:t})),(0,o.createElement)(pa,{path:n+"/colors/link"},(0,o.createElement)(aa,{name:t})),(0,o.createElement)(pa,{path:n+"/layout"},(0,o.createElement)(la,{name:t})))}var ha=function(){const e=(0,i.getBlockTypes)();return(0,o.createElement)(O.__experimentalNavigatorProvider,{className:"edit-site-global-styles-sidebar__navigator-provider",initialPath:"/"},(0,o.createElement)(pa,{path:"/"},(0,o.createElement)(Lr,null)),(0,o.createElement)(pa,{path:"/variations"},(0,o.createElement)(da,null)),(0,o.createElement)(pa,{path:"/blocks"},(0,o.createElement)(Dr,null)),e.map((e=>(0,o.createElement)(pa,{key:"menu-block-"+e.name,path:"/blocks/"+e.name},(0,o.createElement)(Fr,{name:e.name})))),(0,o.createElement)(_a,null),e.map((e=>(0,o.createElement)(_a,{key:"screens-block-"+e.name,name:e.name}))))};function ga(){const[e,t]=(()=>{const{user:e,setUserConfig:t}=(0,o.useContext)(qn);return[!!e&&!(0,$.isEqual)(e,Xn),(0,o.useCallback)((()=>t((()=>Xn))),[t])]})(),{toggle:n}=(0,c.useDispatch)(d.store);return(0,o.createElement)(Rn,{className:"edit-site-global-styles-sidebar",identifier:"edit-site/global-styles",title:(0,p.__)("Styles"),icon:An,closeLabel:(0,p.__)("Close global styles sidebar"),panelClassName:"edit-site-global-styles-sidebar__panel",header:(0,o.createElement)(O.Flex,null,(0,o.createElement)(O.FlexBlock,null,(0,o.createElement)("strong",null,(0,p.__)("Styles")),(0,o.createElement)("span",{className:"edit-site-global-styles-sidebar__beta"},(0,p.__)("Beta"))),(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.DropdownMenu,{icon:fe,label:(0,p.__)("More Global Styles Actions"),controls:[{title:(0,p.__)("Reset to defaults"),onClick:t,isDisabled:!e},{title:(0,p.__)("Welcome Guide"),onClick:()=>n("core/edit-site","welcomeGuideStyles")}]})))},(0,o.createElement)(ha,null))}const fa="edit-site/template",Ea="edit-site/block-inspector";var va=e=>{let{sidebarName:t}=e;const{enableComplementaryArea:n}=(0,c.useDispatch)(te),[r,a]=t===fa?[(0,p.__)("Template (selected)"),"is-active"]:[(0,p.__)("Template"),""],[l,i]=t===Ea?[(0,p.__)("Block (selected)"),"is-active"]:[(0,p.__)("Block"),""];return(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,o.createElement)(O.Button,{onClick:()=>n(E,fa),className:`edit-site-sidebar__panel-tab ${a}`,"aria-label":r,"data-label":(0,p.__)("Template")},(0,p.__)("Template"))),(0,o.createElement)("li",null,(0,o.createElement)(O.Button,{onClick:()=>n(E,Ea),className:`edit-site-sidebar__panel-tab ${i}`,"aria-label":l,"data-label":(0,p.__)("Block")},(0,p.__)("Block"))))};function ya(e){let{area:t,clientId:n}=e;const{selectBlock:r,toggleBlockHighlight:a}=(0,c.useDispatch)(Te.store),l=(0,c.useSelect)((e=>e(m.store).__experimentalGetDefaultTemplatePartAreas().find((e=>e.area===t))),[t]),i=()=>a(n,!0),s=()=>a(n,!1);return(0,o.createElement)(O.Button,{className:"edit-site-template-card__template-areas-item",icon:null==l?void 0:l.icon,onMouseOver:i,onMouseLeave:s,onFocus:i,onBlur:s,onClick:()=>{r(n)}},null==l?void 0:l.label)}function ba(){const e=(0,c.useSelect)((e=>e(bt).getCurrentTemplateTemplateParts()),[]);return e.length?(0,o.createElement)("section",{className:"edit-site-template-card__template-areas"},(0,o.createElement)(O.__experimentalHeading,{level:3,className:"edit-site-template-card__template-areas-title"},(0,p.__)("Areas")),(0,o.createElement)("ul",{className:"edit-site-template-card__template-areas-list"},e.map((e=>{let{templatePart:t,block:n}=e;return(0,o.createElement)("li",{key:t.slug},(0,o.createElement)(ya,{area:t.area,clientId:n.clientId}))})))):null}function wa(){const{title:e,description:t,icon:n}=(0,c.useSelect)((e=>{const{getEditedPostType:t,getEditedPostId:n}=e(bt),{getEntityRecord:r}=e(u.store),{__experimentalGetTemplateInfo:a}=e(m.store),l=r("postType",t(),n());return l?a(l):{}}),[]);return e||t?(0,o.createElement)("div",{className:"edit-site-template-card"},(0,o.createElement)(O.Icon,{className:"edit-site-template-card__icon",icon:n}),(0,o.createElement)("div",{className:"edit-site-template-card__content"},(0,o.createElement)("h2",{className:"edit-site-template-card__title"},e),(0,o.createElement)("div",{className:"edit-site-template-card__description"},t),(0,o.createElement)(ba,null))):null}const{Slot:Sa,Fill:xa}=(0,O.createSlotFill)("EditSiteSidebarInspector"),ka=xa;function Ca(){const{sidebar:e,isEditorSidebarOpened:t,hasBlockSelection:n}=(0,c.useSelect)((e=>{const t=e(te).getActiveComplementaryArea(E);return{sidebar:t,isEditorSidebarOpened:[Ea,fa].includes(t),hasBlockSelection:!!e(Te.store).getBlockSelectionStart()}}),[]),{enableComplementaryArea:r}=(0,c.useDispatch)(te);(0,o.useEffect)((()=>{t&&r(E,n?Ea:fa)}),[n,t]);let a=e;t||(a=n?Ea:fa);let l=o.Fragment;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Rn,{identifier:a,title:(0,p.__)("Settings"),icon:Bn,closeLabel:(0,p.__)("Close settings sidebar"),header:(0,o.createElement)(va,{sidebarName:a}),headerClassName:"edit-site-sidebar__panel-tabs"},a===fa&&(0,o.createElement)(O.PanelBody,null,(0,o.createElement)(wa,null)),a===Ea&&(0,o.createElement)(Sa,{bubblesVirtually:!0})),(0,o.createElement)(ga,null),(0,o.createElement)(l,null))}var Ta=window.wp.htmlEntities;var Na=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M12 4L4 7.9V20h16V7.9L12 4zm6.5 14.5H14V13h-4v5.5H5.5V8.8L12 5.7l6.5 3.1v9.7z"}));var Ia=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"}));const Pa="__experimentalMainDashboardButton",{Fill:Ma,Slot:Ba}=(0,O.createSlotFill)(Pa),Ra=Ma;Ra.Slot=e=>{let{children:t}=e;const n=(0,O.__experimentalUseSlot)(Pa);return Boolean(n.fills&&n.fills.length)?(0,o.createElement)(Ba,{bubblesVirtually:!0}):t};var Aa=Ra;const za="site-editor";function La(e){let{params:t,replace:n,...r}=e;const a=Sn(t,n);return(0,o.createElement)(O.__experimentalNavigationItem,z({},a,r))}var Va=e=>{let{activeItem:t=za}=e;const{homeTemplate:n,isNavigationOpen:r,siteTitle:a}=(0,c.useSelect)((e=>{const{getEntityRecord:t}=e(u.store),{getSettings:n,isNavigationOpened:r}=e(bt);return{siteTitle:(t("root","__unstableBase",void 0)||{}).name,homeTemplate:n().__unstableHomeTemplate,isNavigationOpen:r()}}),[]),{setIsNavigationPanelOpened:l}=(0,c.useDispatch)(bt);return(0,o.createElement)("div",{className:V()("edit-site-navigation-panel",{"is-open":r}),onKeyDown:e=>{e.keyCode!==$t.ESCAPE||e.defaultPrevented||(e.preventDefault(),l(!1))}},(0,o.createElement)("div",{className:"edit-site-navigation-panel__inner"},(0,o.createElement)("div",{className:"edit-site-navigation-panel__site-title-container"},(0,o.createElement)("div",{className:"edit-site-navigation-panel__site-title"},(0,Ta.decodeEntities)(a))),(0,o.createElement)("div",{className:"edit-site-navigation-panel__scroll-container"},(0,o.createElement)(O.__experimentalNavigation,{activeItem:t},(0,o.createElement)(Aa.Slot,null,(0,o.createElement)(O.__experimentalNavigationBackButton,{backButtonLabel:(0,p.__)("Dashboard"),className:"edit-site-navigation-panel__back-to-dashboard",href:"index.php"})),(0,o.createElement)(O.__experimentalNavigationMenu,null,(0,o.createElement)(O.__experimentalNavigationGroup,{title:(0,p.__)("Editor")},(0,o.createElement)(La,{icon:Na,title:(0,p.__)("Site"),item:za,params:{postId:null==n?void 0:n.postId,postType:null==n?void 0:n.postType}}),(0,o.createElement)(La,{icon:Hn,title:(0,p.__)("Templates"),item:"wp_template",params:{postId:void 0,postType:"wp_template"}}),(0,o.createElement)(La,{icon:Ia,title:(0,p.__)("Template Parts"),item:"wp_template_part",params:{postId:void 0,postType:"wp_template_part"}})))))))};var Oa=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,o.createElement)(D.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"}));var Da=function(e){let{icon:t}=e;const{isNavigationOpen:n,isRequestingSiteIcon:r,siteIconUrl:a}=(0,c.useSelect)((e=>{const{getEntityRecord:t,isResolving:n}=e(u.store),r=t("root","__unstableBase",void 0)||{};return{isNavigationOpen:e(bt).isNavigationOpened(),isRequestingSiteIcon:n("core","getEntityRecord",["root","__unstableBase",void 0]),siteIconUrl:r.site_icon_url}}),[]),{setIsNavigationPanelOpened:l}=(0,c.useDispatch)(bt),i=(0,he.useReducedMotion)(),s=(0,o.useRef)();(0,o.useEffect)((()=>{n||s.current.focus()}),[n]);let m=(0,o.createElement)(O.Icon,{size:"36px",icon:Oa});const d={expand:{scale:1.25,transition:{type:"tween",duration:"0.3"}}};a?m=(0,o.createElement)(O.__unstableMotion.img,{variants:!i&&d,alt:(0,p.__)("Site Icon"),className:"edit-site-navigation-toggle__site-icon",src:a}):r?m=null:t&&(m=(0,o.createElement)(O.Icon,{size:"36px",icon:t}));const _=V()({"edit-site-navigation-toggle__button":!0,"has-icon":a});return(0,o.createElement)(O.__unstableMotion.div,{className:"edit-site-navigation-toggle"+(n?" is-open":""),whileHover:"expand"},(0,o.createElement)(O.Button,{className:_,label:(0,p.__)("Toggle navigation"),ref:s,"aria-pressed":n,onClick:()=>l(!n),showTooltip:!0},m))};const{Fill:Fa,Slot:Ha}=(0,O.createSlotFill)("EditSiteNavigationPanelPreview"),{Fill:Ga,Slot:Ua}=(0,O.createSlotFill)("EditSiteNavigationSidebar");function $a(e){let{isDefaultOpen:t=!1,activeTemplateType:n}=e;const r=(0,he.useViewportMatch)("medium"),{setIsNavigationPanelOpened:a}=(0,c.useDispatch)(bt);return(0,o.useEffect)((function(){a(t&&r)}),[t,r,a]),(0,o.createElement)(Ga,null,(0,o.createElement)(Da,null),(0,o.createElement)(Va,{activeItem:n}),(0,o.createElement)(Ha,null))}$a.Slot=Ua;var Wa=$a,ja=window.wp.reusableBlocks;function Ya(e){let{clientId:t}=e;const{getBlocks:n}=(0,c.useSelect)(Te.store),{replaceBlocks:r}=(0,c.useDispatch)(Te.store);return(0,c.useSelect)((e=>e(Te.store).canRemoveBlock(t)),[t])?(0,o.createElement)(Te.BlockSettingsMenuControls,null,(e=>{let{onClose:a}=e;return(0,o.createElement)(O.MenuItem,{onClick:()=>{r(t,n(t)),a()}},(0,p.__)("Detach blocks from template part"))})):null}function qa(e){let{closeModal:t,onCreate:n}=e;const[r,a]=(0,o.useState)(""),[l,i]=(0,o.useState)(v),[s,u]=(0,o.useState)(!1),d=(0,he.useInstanceId)(qa),_=(0,c.useSelect)((e=>e(m.store).__experimentalGetDefaultTemplatePartAreas()),[]);return(0,o.createElement)(O.Modal,{title:(0,p.__)("Create a template part"),closeLabel:(0,p.__)("Close"),onRequestClose:t,overlayClassName:"edit-site-create-template-part-modal"},(0,o.createElement)("form",{onSubmit:async e=>{e.preventDefault(),r&&(u(!0),await n({title:r,area:l}))}},(0,o.createElement)(O.TextControl,{label:(0,p.__)("Name"),value:r,onChange:a,required:!0}),(0,o.createElement)(O.BaseControl,{label:(0,p.__)("Area"),id:`edit-site-create-template-part-modal__area-selection-${d}`,className:"edit-site-create-template-part-modal__area-base-control"},(0,o.createElement)(O.__experimentalRadioGroup,{label:(0,p.__)("Area"),className:"edit-site-create-template-part-modal__area-radio-group",id:`edit-site-create-template-part-modal__area-selection-${d}`,onChange:i,checked:l},_.map((e=>{let{icon:t,label:n,area:r,description:a}=e;return(0,o.createElement)(O.__experimentalRadio,{key:n,value:r,className:"edit-site-create-template-part-modal__area-radio"},(0,o.createElement)(O.Flex,{align:"start",justify:"start"},(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.Icon,{icon:t})),(0,o.createElement)(O.FlexBlock,{className:"edit-site-create-template-part-modal__option-label"},n,(0,o.createElement)("div",null,a)),(0,o.createElement)(O.FlexItem,{className:"edit-site-create-template-part-modal__checkbox"},l===r&&(0,o.createElement)(O.Icon,{icon:F}))))})))),(0,o.createElement)(O.Flex,{className:"edit-site-create-template-part-modal__modal-actions",justify:"flex-end"},(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.Button,{variant:"secondary",onClick:()=>{t()}},(0,p.__)("Cancel"))),(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.Button,{variant:"primary",type:"submit",disabled:!r,isBusy:s},(0,p.__)("Create"))))))}function Xa(e){let{clientIds:t,blocks:n}=e;const[r,a]=(0,o.useState)(!1),{replaceBlocks:l}=(0,c.useDispatch)(Te.store),{saveEntityRecord:s}=(0,c.useDispatch)(u.store),{createSuccessNotice:m}=(0,c.useDispatch)(A.store);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Te.BlockSettingsMenuControls,null,(()=>(0,o.createElement)(O.MenuItem,{onClick:()=>{a(!0)}},(0,p.__)("Make template part")))),r&&(0,o.createElement)(qa,{closeModal:()=>{a(!1)},onCreate:async e=>{let{title:r,area:a}=e;const o=(0,$.kebabCase)(r).replace(/[^\w-]+/g,"")||"wp-custom-part",c=await s("postType","wp_template_part",{slug:o,title:r,content:(0,i.serialize)(n),area:a});l(t,(0,i.createBlock)("core/template-part",{slug:c.slug,theme:c.theme})),m((0,p.__)("Template part created."),{type:"snackbar"})}}))}function Ka(){var e;const{clientIds:t,blocks:n}=(0,c.useSelect)((e=>{const{getSelectedBlockClientIds:t,getBlocksByClientId:n}=e(Te.store),r=t();return{clientIds:r,blocks:n(r)}}),[]);return 1===n.length&&"core/template-part"===(null===(e=n[0])||void 0===e?void 0:e.name)?(0,o.createElement)(Ya,{clientId:t[0]}):(0,o.createElement)(Xa,{clientIds:t,blocks:n})}var Za=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M20.1 5.1L16.9 2 6.2 12.7l-1.3 4.4 4.5-1.3L20.1 5.1zM4 20.8h8v-1.5H4v1.5z"}));function Qa(e){let{type:t,id:n,activePage:r,onActivePageChange:a}=e;const l=(0,c.useSelect)((e=>t&&n&&"URL"!==t&&e(u.store).getEntityRecord("postType",t,n)),[t,n]),i=(0,o.useMemo)((()=>{if(null==l||!l.link)return null;const e=(0,h.getPathAndQueryString)(l.link);return e===(null==r?void 0:r.path)?null:()=>a({type:t,slug:l.slug,path:e,context:{postType:l.type,postId:l.id}})}),[l,null==r?void 0:r.path,a]);return i&&(0,o.createElement)(O.Button,{icon:Za,label:(0,p.__)("Edit Page Template"),onClick:i})}function Ja(e){let{onClick:t=(()=>{})}=e;const{shortcut:n,isBlockInspectorOpen:r}=(0,c.useSelect)((e=>({shortcut:e(Ht.store).getShortcutRepresentation("core/edit-site/toggle-block-settings-sidebar"),isBlockInspectorOpen:e(te).getActiveComplementaryArea(bt.name)===Ea})),[]),{enableComplementaryArea:a,disableComplementaryArea:l}=(0,c.useDispatch)(te),i=r?(0,p.__)("Hide more settings"):(0,p.__)("Show more settings");return(0,o.createElement)(O.MenuItem,{onClick:()=>{r?(l(E),(0,Ne.speak)((0,p.__)("Block settings closed"))):(a(E,Ea),(0,Ne.speak)((0,p.__)("Additional settings are now available in the Editor block settings sidebar"))),t()},shortcut:n},i)}function el(){return(0,o.createElement)(Te.BlockSettingsMenuControls,null,(e=>{let{selectedClientIds:t,onClose:n}=e;return(0,o.createElement)(tl,{selectedClientId:t[0],onClose:n})}))}function tl(e){let{selectedClientId:t,onClose:n}=e;const{params:r}=Vt(),a=(0,c.useSelect)((e=>{const n=e(Te.store).getBlock(t);if(n&&(0,i.isTemplatePart)(n)){const{theme:t,slug:r}=n.attributes;return e(u.store).getEntityRecord("postType","wp_template_part",`${t}//${r}`)}}),[t]),l=Sn({postId:null==a?void 0:a.id,postType:null==a?void 0:a.type},{fromTemplateId:r.postId});return a?(0,o.createElement)(O.MenuItem,z({},l,{onClick:e=>{l.onClick(e),n()}}),(0,p.sprintf)((0,p.__)("Edit %s"),a.slug)):null}var nl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M20 10.8H6.7l4.1-4.5-1.1-1.1-5.8 6.3 5.8 5.8 1.1-1.1-4-3.9H20z"}));var rl=function(){var e;const t=Vt(),n=Ot(),r="wp_template_part"===t.params.postType,a=null===(e=t.state)||void 0===e?void 0:e.fromTemplateId;return r&&a?(0,o.createElement)(O.Button,{className:"edit-site-visual-editor__back-button",icon:nl,onClick:()=>{n.back()}},(0,p.__)("Back")):null};function al(e){let{direction:t,resizeWidthBy:n}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("button",{className:`resizable-editor__drag-handle is-${t}`,"aria-label":(0,p.__)("Drag to resize"),"aria-describedby":`resizable-editor__resize-help-${t}`,onKeyDown:function(e){const{keyCode:r}=e;"left"===t&&r===$t.LEFT||"right"===t&&r===$t.RIGHT?n(20):("left"===t&&r===$t.RIGHT||"right"===t&&r===$t.LEFT)&&n(-20)}}),(0,o.createElement)(O.VisuallyHidden,{id:`resizable-editor__resize-help-${t}`},(0,p.__)("Use left and right arrow keys to resize the canvas.")))}const ll="100%",ol="100%",il={position:void 0,userSelect:void 0,cursor:void 0,width:void 0,height:void 0,top:void 0,right:void 0,bottom:void 0,left:void 0};var sl=function(e){let{enableResizing:t,settings:n,children:r,...a}=e;const l=(0,c.useSelect)((e=>e(bt).__experimentalGetPreviewDeviceType()),[]),i=(0,Te.__experimentalUseResizeCanvas)(l),[s,u]=(0,o.useState)(ll),[m,d]=(0,o.useState)(ol),p=(0,o.useRef)(),_=(0,Te.__unstableUseMouseMoveTypingReset)(),h=(0,he.useMergeRefs)([p,_]);(0,o.useEffect)((function(){const e=p.current;if(!e||!t)return;let n,r=null;function a(){r||(r=e.contentWindow.requestAnimationFrame((()=>{d(e.contentDocument.documentElement.scrollHeight),r=null})))}function l(){var t;null===(t=n)||void 0===t||t.disconnect(),n=new e.contentWindow.ResizeObserver(a),n.observe(e.contentDocument.documentElement),a()}return e.addEventListener("load",l),l(),()=>{var t,a;null===(t=e.contentWindow)||void 0===t||t.cancelAnimationFrame(r),null===(a=n)||void 0===a||a.disconnect(),e.removeEventListener("load",l)}}),[t]);const g=(0,o.useCallback)((e=>{p.current&&u(p.current.offsetWidth+e)}),[]);return(0,o.createElement)(O.ResizableBox,{size:{width:s,height:m},onResizeStop:(e,t,n)=>{u(n.style.width)},minWidth:300,maxWidth:"100%",maxHeight:"100%",enable:{right:t,left:t},showHandle:t,resizeRatio:2,handleComponent:{left:(0,o.createElement)(al,{direction:"left",resizeWidthBy:g}),right:(0,o.createElement)(al,{direction:"right",resizeWidthBy:g})},handleClasses:void 0,handleStyles:{left:il,right:il}},(0,o.createElement)(Te.__unstableIframe,z({style:t?void 0:i,head:(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Te.__unstableEditorStyles,{styles:n.styles}),(0,o.createElement)("style",null,".is-root-container { display: flow-root; }"),t&&(0,o.createElement)("style",null,"html, body { height: -moz-fit-content !important; height: fit-content !important; min-height: 0 !important; }",".is-root-container { min-height: 0 !important; }")),assets:n.__unstableResolvedAssets,ref:h,name:"editor-canvas",className:"edit-site-visual-editor__editor-canvas"},a),n.svgFilters,r))};const cl={type:"default",alignments:[]};function ul(e){var t,n;let{setIsInserterOpen:r}=e;const{storedSettings:a,templateType:l,templateId:i,page:s}=(0,c.useSelect)((e=>{const{getSettings:t,getEditedPostType:n,getEditedPostId:a,getPage:l}=e(bt);return{storedSettings:t(r),templateType:n(),templateId:a(),page:l()}}),[r]),m=null!==(t=a.__experimentalAdditionalBlockPatterns)&&void 0!==t?t:a.__experimentalBlockPatterns,d=null!==(n=a.__experimentalAdditionalBlockPatternCategories)&&void 0!==n?n:a.__experimentalBlockPatternCategories,{restBlockPatterns:p,restBlockPatternCategories:_}=(0,c.useSelect)((e=>({restBlockPatterns:e(u.store).getBlockPatterns(),restBlockPatternCategories:e(u.store).getBlockPatternCategories()})),[]),h=(0,o.useMemo)((()=>(0,$.unionBy)(m,p,"name")),[m,p]),g=(0,o.useMemo)((()=>(0,$.unionBy)(d,_,"name")),[d,_]),f=(0,o.useMemo)((()=>({...(0,$.omit)(a,["__experimentalAdditionalBlockPatterns","__experimentalAdditionalBlockPatternCategories"]),__experimentalBlockPatterns:h,__experimentalBlockPatternCategories:g})),[a,h,g]),[E,v,y]=(0,u.useEntityBlockEditor)("postType",l),{setPage:b}=(0,c.useDispatch)(bt),{enableComplementaryArea:w}=(0,c.useDispatch)(te),S=((0,o.useCallback)((()=>{w("core/edit-site","edit-site/navigation-menu")}),[w]),(0,o.useRef)()),x=(0,he.useMergeRefs)([S,(0,Te.__unstableUseTypingObserver)()]),k=(0,he.useViewportMatch)("small","<"),{clearSelectedBlock:C}=(0,c.useDispatch)(Te.store),T="wp_template_part"===l,N=0!==E.length;let I=o.Fragment;return(0,o.createElement)(Te.BlockEditorProvider,{settings:f,value:E,onInput:v,onChange:y,useSubRegistry:!1},(0,o.createElement)(el,null),(0,o.createElement)(Ka,null),(0,o.createElement)(Te.__experimentalLinkControl.ViewerFill,null,(0,o.useCallback)((e=>(0,o.createElement)(Qa,z({},e,{activePage:s,onActivePageChange:b}))),[s])),(0,o.createElement)(ka,null,(0,o.createElement)(Te.BlockInspector,null)),(0,o.createElement)(Te.BlockTools,{className:V()("edit-site-visual-editor",{"is-focus-mode":T}),__unstableContentRef:S,onClick:e=>{e.target===e.currentTarget&&C()}},(0,o.createElement)(Te.BlockEditorKeyboardShortcuts.Register,null),(0,o.createElement)(rl,null),(0,o.createElement)(sl,{key:i,enableResizing:T&&!k,settings:f,contentRef:x},(0,o.createElement)(Te.BlockList,{className:"edit-site-block-editor__block-list wp-site-blocks",__experimentalLayout:cl,renderAppender:(!T||!N)&&void 0})),(0,o.createElement)(Te.__unstableBlockSettingsMenuFirstItem,null,(e=>{let{onClose:t}=e;return(0,o.createElement)(Ja,{onClick:t})})),(0,o.createElement)(Te.__unstableBlockToolbarLastItem,null,(0,o.createElement)(Te.__unstableBlockNameContext.Consumer,null,(e=>"core/navigation"===e&&(0,o.createElement)(I,null))))),(0,o.createElement)(ja.ReusableBlocksMenuItems,null))}var ml=n(773);function dl(e){let{value:t,onChange:n,onInput:r}=e;const[a,l]=(0,o.useState)(t),[i,s]=(0,o.useState)(!1),c=(0,he.useInstanceId)(dl);i||a===t||l(t);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.VisuallyHidden,{as:"label",htmlFor:`code-editor-text-area-${c}`},(0,p.__)("Type text or HTML")),(0,o.createElement)(ml.Z,{autoComplete:"off",dir:"auto",value:a,onChange:e=>{const t=e.target.value;r(t),l(t),s(!0)},onBlur:()=>{i&&(n(a),s(!1))},className:"edit-site-code-editor-text-area",id:`code-editor-text-area-${c}`,placeholder:(0,p.__)("Start writing with text or HTML")}))}function pl(){const{templateType:e,shortcut:t}=(0,c.useSelect)((e=>{const{getEditedPostType:t}=e(bt),{getShortcutRepresentation:n}=e(Ht.store);return{templateType:t(),shortcut:n("core/edit-site/toggle-mode")}}),[]),[n,r]=(0,u.useEntityProp)("postType",e,"content"),[a,,l]=(0,u.useEntityBlockEditor)("postType",e),s=n instanceof Function?n({blocks:a}):n,{switchEditorMode:m}=(0,c.useDispatch)(bt);return(0,o.createElement)("div",{className:"edit-site-code-editor"},(0,o.createElement)("div",{className:"edit-site-code-editor__toolbar"},(0,o.createElement)("h2",null,(0,p.__)("Editing code")),(0,o.createElement)(O.Button,{variant:"tertiary",onClick:()=>m("visual"),shortcut:t},(0,p.__)("Exit code editor"))),(0,o.createElement)("div",{className:"edit-site-code-editor__body"},(0,o.createElement)(dl,{value:s,onChange:e=>{l((0,i.parse)(e),{selection:void 0})},onInput:r})))}function _l(e){let{openEntitiesSavedStates:t}=e;const{__experimentalGetDirtyEntityRecords:n,isSavingEntityRecord:r}=(0,c.useSelect)(u.store),{getEditorMode:a}=(0,c.useSelect)(bt),l=(0,c.useSelect)((e=>e(bt).isListViewOpened()),[]),o=(0,c.useSelect)((e=>e(te).getActiveComplementaryArea(bt.name)===Ea),[]),{redo:i,undo:s}=(0,c.useDispatch)(u.store),{setIsListViewOpened:m,switchEditorMode:d}=(0,c.useDispatch)(bt),{enableComplementaryArea:p,disableComplementaryArea:_}=(0,c.useDispatch)(te);return(0,Ht.useShortcut)("core/edit-site/save",(e=>{e.preventDefault();const a=n(),l=!!a.length;!a.some((e=>r(e.kind,e.name,e.key)))&&l&&t()})),(0,Ht.useShortcut)("core/edit-site/undo",(e=>{s(),e.preventDefault()})),(0,Ht.useShortcut)("core/edit-site/redo",(e=>{i(),e.preventDefault()})),(0,Ht.useShortcut)("core/edit-site/toggle-list-view",(()=>{m(!l)})),(0,Ht.useShortcut)("core/edit-site/toggle-block-settings-sidebar",(e=>{e.preventDefault(),o?_(E):p(E,Ea)})),(0,Ht.useShortcut)("core/edit-site/toggle-mode",(()=>{d("visual"===a()?"text":"visual")})),null}_l.Register=function(){const{registerShortcut:e}=(0,c.useDispatch)(Ht.store);return(0,o.useEffect)((()=>{e({name:"core/edit-site/save",category:"global",description:(0,p.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/edit-site/undo",category:"global",description:(0,p.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/edit-site/redo",category:"global",description:(0,p.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}}),e({name:"core/edit-site/toggle-list-view",category:"global",description:(0,p.__)("Open the block list view."),keyCombination:{modifier:"access",character:"o"}}),e({name:"core/edit-site/toggle-block-settings-sidebar",category:"global",description:(0,p.__)("Show or hide the block settings sidebar."),keyCombination:{modifier:"primaryShift",character:","}}),e({name:"core/edit-site/keyboard-shortcuts",category:"main",description:(0,p.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/edit-site/next-region",category:"global",description:(0,p.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/edit-site/previous-region",category:"global",description:(0,p.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"}]}),e({name:"core/edit-site/toggle-mode",category:"global",description:(0,p.__)("Switch between visual editor and code editor."),keyCombination:{modifier:"secondary",character:"m"}})}),[e]),null};var hl=_l;function gl(){const{setTemplate:e,setTemplatePart:t,setPage:n}=(0,c.useDispatch)(bt),{params:{postId:r,postType:a}}=Vt();return(0,o.useEffect)((()=>{"page"===a||"post"===a?n({context:{postType:a,postId:r}}):"wp_template"===a?e(r):"wp_template_part"===a&&t(r)}),[r,a]),null}var fl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));function El(){const{setIsInserterOpened:e}=(0,c.useDispatch)(bt),t=(0,c.useSelect)((e=>e(bt).__experimentalGetInsertionPoint()),[]),n=(0,he.useViewportMatch)("medium","<"),r=n?"div":O.VisuallyHidden,[a,l]=(0,he.__experimentalUseDialog)({onClose:()=>e(!1),focusOnMount:null}),i=(0,o.useRef)();return(0,o.useEffect)((()=>{i.current.focusSearch()}),[]),(0,o.createElement)("div",z({ref:a},l,{className:"edit-site-editor__inserter-panel"}),(0,o.createElement)(r,{className:"edit-site-editor__inserter-panel-header"},(0,o.createElement)(O.Button,{icon:fl,label:(0,p.__)("Close block inserter"),onClick:()=>e(!1)})),(0,o.createElement)("div",{className:"edit-site-editor__inserter-panel-content"},(0,o.createElement)(Te.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:n,rootClientId:t.rootClientId,__experimentalInsertionIndex:t.insertionIndex,__experimentalFilterValue:t.filterValue,ref:i})))}function vl(){const{setIsListViewOpened:e}=(0,c.useDispatch)(bt),t=(0,he.useFocusOnMount)("firstElement"),n=(0,he.useFocusReturn)(),r=(0,he.useFocusReturn)();const a=`edit-site-editor__list-view-panel-label-${(0,he.useInstanceId)(vl)}`;return(0,o.createElement)("div",{"aria-labelledby":a,className:"edit-site-editor__list-view-panel",onKeyDown:function(t){t.keyCode!==$t.ESCAPE||t.defaultPrevented||e(!1)}},(0,o.createElement)("div",{className:"edit-site-editor__list-view-panel-header",ref:n},(0,o.createElement)("strong",{id:a},(0,p.__)("List View")),(0,o.createElement)(O.Button,{icon:U,label:(0,p.__)("Close List View Sidebar"),onClick:()=>e(!1)})),(0,o.createElement)("div",{className:"edit-site-editor__list-view-panel-content",ref:(0,he.useMergeRefs)([r,t])},(0,o.createElement)(Te.__experimentalListView,{showNestedBlocks:!0,__experimentalFeatures:!0,__experimentalPersistentListViewFeatures:!0})))}function yl(e){let{text:t,children:n}=e;const r=(0,he.useCopyToClipboard)(t);return(0,o.createElement)(O.Button,{variant:"secondary",ref:r},n)}function bl(e){let{message:t,error:n,reboot:r,dashboardLink:a}=e;const l=[];return r&&l.push((0,o.createElement)(O.Button,{key:"recovery",onClick:r,variant:"secondary"},(0,p.__)("Attempt Recovery"))),n&&l.push((0,o.createElement)(yl,{key:"copy-error",text:n.stack},(0,p.__)("Copy Error"))),a&&l.push((0,o.createElement)(O.Button,{key:"back-to-dashboard",variant:"secondary",href:a},(0,p.__)("Back to dashboard"))),(0,o.createElement)(Te.Warning,{className:"editor-error-boundary",actions:l},t)}class wl extends o.Component{constructor(){super(...arguments),this.reboot=this.reboot.bind(this),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}reboot(){this.props.onError()}render(){const{error:e}=this.state;return e?(0,o.createElement)(bl,{message:(0,p.__)("The editor has encountered an unexpected error."),error:e,reboot:this.reboot}):this.props.children}}function Sl(e){let{nonAnimatedSrc:t,animatedSrc:n}=e;return(0,o.createElement)("picture",{className:"edit-site-welcome-guide__image"},(0,o.createElement)("source",{srcSet:t,media:"(prefers-reduced-motion: reduce)"}),(0,o.createElement)("img",{src:n,width:"312",height:"240",alt:""}))}function xl(){const{toggle:e}=(0,c.useDispatch)(d.store);return(0,c.useSelect)((e=>!!e(d.store).get("core/edit-site","welcomeGuide")),[])?(0,o.createElement)(O.Guide,{className:"edit-site-welcome-guide",contentLabel:(0,p.__)("Welcome to the site editor"),finishButtonText:(0,p.__)("Get Started"),onFinish:()=>e("core/edit-site","welcomeGuide"),pages:[{image:(0,o.createElement)(Sl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/edit-your-site.svg?1",animatedSrc:"https://s.w.org/images/block-editor/edit-your-site.gif?1"}),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h1",{className:"edit-site-welcome-guide__heading"},(0,p.__)("Edit your site")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,p.__)("Design everything on your site — from the header right down to the footer — using blocks.")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,o.createInterpolateElement)((0,p.__)("Click <StylesIconImage /> to start designing your blocks, and choose your typography, layout, and colors."),{StylesIconImage:(0,o.createElement)("img",{alt:(0,p.__)("styles"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z' fill='%231E1E1E'/%3E%3C/svg%3E%0A"})})))}]}):null}function kl(){const{toggle:e}=(0,c.useDispatch)(d.store),{isActive:t,isStylesOpen:n}=(0,c.useSelect)((e=>{const t=e(te).getActiveComplementaryArea(bt.name);return{isActive:!!e(d.store).get("core/edit-site","welcomeGuideStyles"),isStylesOpen:"edit-site/global-styles"===t}}),[]);return t&&n?(0,o.createElement)(O.Guide,{className:"edit-site-welcome-guide",contentLabel:(0,p.__)("Welcome to styles"),finishButtonText:(0,p.__)("Get Started"),onFinish:()=>e("core/edit-site","welcomeGuideStyles"),pages:[{image:(0,o.createElement)(Sl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-to-styles.svg?1",animatedSrc:"https://s.w.org/images/block-editor/welcome-to-styles.gif?1"}),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h1",{className:"edit-site-welcome-guide__heading"},(0,p.__)("Welcome to Styles")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,p.__)("Tweak your site, or give it a whole new look! Get creative — how about a new color palette for your buttons, or choosing a new font? Take a look at what you can do here.")))},{image:(0,o.createElement)(Sl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/set-the-design.svg?1",animatedSrc:"https://s.w.org/images/block-editor/set-the-design.gif?1"}),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h1",{className:"edit-site-welcome-guide__heading"},(0,p.__)("Set the design")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,p.__)("You can customize your site as much as you like with different colors, typography, and layouts. Or if you prefer, just leave it up to your theme to handle! ")))},{image:(0,o.createElement)(Sl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/personalize-blocks.svg?1",animatedSrc:"https://s.w.org/images/block-editor/personalize-blocks.gif?1"}),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h1",{className:"edit-site-welcome-guide__heading"},(0,p.__)("Personalize blocks")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,p.__)("You can adjust your blocks to ensure a cohesive experience across your site — add your unique colors to a branded Button block, or adjust the Heading block to your preferred size.")))},{image:(0,o.createElement)(Sl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h1",{className:"edit-site-welcome-guide__heading"},(0,p.__)("Learn more")),(0,o.createElement)("p",{className:"edit-site-welcome-guide__text"},(0,p.__)("New to block themes and styling your site? "),(0,o.createElement)(O.ExternalLink,{href:(0,p.__)("https://wordpress.org/support/article/styles-overview/")},(0,p.__)("Here’s a detailed guide to learn how to make the most of it."))))}]}):null}function Cl(){return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(xl,null),(0,o.createElement)(kl,null))}function Tl(){return function(){const[e,t,n]=Br(),{getSettings:r}=(0,c.useSelect)(bt),{updateSettings:a}=(0,c.useDispatch)(bt);(0,o.useEffect)((()=>{if(!e||!t)return;const l=r(),o=(0,$.filter)(l.styles,(e=>!e.isGlobalStyles));a({...l,styles:[...o,...e],svgFilters:n,__experimentalFeatures:t})}),[e,t])}(),null}function Nl(e){const t=Vt(),n=(0,c.useSelect)((e=>{var t;return null===(t=e(u.store).getEntityRecord("root","site"))||void 0===t?void 0:t.title}),[]),r=(0,o.useRef)(!0);(0,o.useEffect)((()=>{r.current=!1}),[t]),(0,o.useEffect)((()=>{if(!r.current&&e&&n){const t=(0,p.sprintf)((0,p.__)("%1$s ‹ %2$s — WordPress"),e,n);document.title=t,(0,Ne.speak)((0,p.sprintf)((0,p.__)("Now displaying: %s"),document.title),"assertive")}}),[e,n,t])}const Il={drawer:(0,p.__)("Navigation Sidebar")};var Pl=function(e){let{onError:t}=e;const{isInserterOpen:n,isListViewOpen:r,sidebarIsOpened:a,settings:l,entityId:i,templateType:s,page:_,template:h,templateResolved:g,isNavigationOpen:f,previousShortcut:E,nextShortcut:v,editorMode:y,showIconLabels:b}=(0,c.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:n,getSettings:r,getEditedPostType:a,getEditedPostId:l,getPage:o,isNavigationOpened:i,getEditorMode:s}=e(bt),{hasFinishedResolution:c,getEntityRecord:m}=e(u.store),p=a(),_=l();return{isInserterOpen:t(),isListViewOpen:n(),sidebarIsOpened:!!e(te).getActiveComplementaryArea(bt.name),settings:r(),templateType:p,page:o(),template:_?m("postType",p,_):null,templateResolved:!!_&&c("getEntityRecord",["postType",p,_]),entityId:_,isNavigationOpen:i(),previousShortcut:e(Ht.store).getAllShortcutKeyCombinations("core/edit-site/previous-region"),nextShortcut:e(Ht.store).getAllShortcutKeyCombinations("core/edit-site/next-region"),editorMode:s(),showIconLabels:e(d.store).get("core/edit-site","showIconLabels")}}),[]),{setPage:w,setIsInserterOpened:S}=(0,c.useDispatch)(bt),{enableComplementaryArea:x}=(0,c.useDispatch)(te),[k,C]=(0,o.useState)(!1),T=(0,o.useCallback)((()=>C(!0)),[]),N=(0,o.useCallback)((()=>{C(!1)}),[]),I=(0,o.useMemo)((()=>({...null==_?void 0:_.context,queryContext:[(null==_?void 0:_.context.queryContext)||{page:1},e=>w({..._,context:{...null==_?void 0:_.context,queryContext:{...null==_?void 0:_.context.queryContext,...e}}})]})),[null==_?void 0:_.context]);(0,o.useEffect)((()=>{f?document.body.classList.add("is-navigation-sidebar-open"):document.body.classList.remove("is-navigation-sidebar-open")}),[f]),(0,o.useEffect)((function(){"open"===new URLSearchParams(window.location.search).get("styles")&&x("core/edit-site","edit-site/global-styles")}),[x]);const P=(null==l?void 0:l.siteUrl)&&void 0!==s&&void 0!==i,M=r?(0,p.__)("List View"):(0,p.__)("Block Library");return Nl(P&&(0,p.__)("Editor (beta)")),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(gl,null),P&&(0,o.createElement)(Ht.ShortcutProvider,null,(0,o.createElement)(u.EntityProvider,{kind:"root",type:"site"},(0,o.createElement)(u.EntityProvider,{kind:"postType",type:s,id:i},(0,o.createElement)(ua,null,(0,o.createElement)(Te.BlockContextProvider,{value:I},(0,o.createElement)(Tl,null),(0,o.createElement)(wl,{onError:t},(0,o.createElement)(hl.Register,null),(0,o.createElement)(Ca,null),(0,o.createElement)(ge,{labels:{...Il,secondarySidebar:M},className:b&&"show-icon-labels",secondarySidebar:"visual"===y&&n?(0,o.createElement)(El,null):"visual"===y&&r?(0,o.createElement)(vl,null):null,sidebar:a&&(0,o.createElement)(_e.Slot,{scope:"core/edit-site"}),drawer:(0,o.createElement)(Wa.Slot,null),header:(0,o.createElement)(Mn,{openEntitiesSavedStates:T,showIconLabels:b}),notices:(0,o.createElement)(m.EditorSnackbars,null),content:(0,o.createElement)(o.Fragment,null,(0,o.createElement)(m.EditorNotices,null),(0,o.createElement)(Te.BlockStyles.Slot,{scope:"core/block-inspector"}),"visual"===y&&h&&(0,o.createElement)(ul,{setIsInserterOpen:S}),"text"===y&&h&&(0,o.createElement)(pl,null),g&&!h&&(null==l?void 0:l.siteUrl)&&i&&(0,o.createElement)(O.Notice,{status:"warning",isDismissible:!1},(0,p.__)("You attempted to edit an item that doesn't exist. Perhaps it was deleted?")),(0,o.createElement)(hl,{openEntitiesSavedStates:T})),actions:(0,o.createElement)(o.Fragment,null,k?(0,o.createElement)(m.EntitiesSavedStates,{close:N}):(0,o.createElement)("div",{className:"edit-site-editor__toggle-save-panel"},(0,o.createElement)(O.Button,{variant:"secondary",className:"edit-site-editor__toggle-save-panel-button",onClick:T,"aria-expanded":!1},(0,p.__)("Open save panel")))),footer:(0,o.createElement)(Te.BlockBreadcrumb,{rootLabelText:(0,p.__)("Template")}),shortcuts:{previous:E,next:v}}),(0,o.createElement)(Cl,null),(0,o.createElement)(O.Popover.Slot,null))))))))};var Ml=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"m7.3 9.7 1.4 1.4c.2-.2.3-.3.4-.5 0 0 0-.1.1-.1.3-.5.4-1.1.3-1.6L12 7 9 4 7.2 6.5c-.6-.1-1.1 0-1.6.3 0 0-.1 0-.1.1-.3.1-.4.2-.6.4l1.4 1.4L4 11v1h1l2.3-2.3zM4 20h9v-1.5H4V20zm0-5.5V16h16v-1.5H4z"}));var Bl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M7 5.5h10a.5.5 0 01.5.5v12a.5.5 0 01-.5.5H7a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM17 4H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2zm-1 3.75H8v1.5h8v-1.5zM8 11h8v1.5H8V11zm6 3.25H8v1.5h6v-1.5z"}));var Rl=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M19 6.2h-5.9l-.6-1.1c-.3-.7-1-1.1-1.8-1.1H5c-1.1 0-2 .9-2 2v11.8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8.2c0-1.1-.9-2-2-2zm.5 11.6c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h5.8c.2 0 .4.1.4.3l1 2H19c.3 0 .5.2.5.5v9.5zM8 12.8h8v-1.5H8v1.5zm0 3h8v-1.5H8v1.5z"}));var Al=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M13.5 6C10.5 6 8 8.5 8 11.5c0 1.1.3 2.1.9 3l-3.4 3 1 1.1 3.4-2.9c1 .9 2.2 1.4 3.6 1.4 3 0 5.5-2.5 5.5-5.5C19 8.5 16.5 6 13.5 6zm0 9.5c-2.2 0-4-1.8-4-4s1.8-4 4-4 4 1.8 4 4-1.8 4-4 4z"}));var zl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M19 5H5c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm.5 12c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V7c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v10zm-11-7.6h-.7l-3.1 4.3h2.8V15h1v-1.3h.7v-.8h-.7V9.4zm-.9 3.5H6.3l1.2-1.7v1.7zm5.6-3.2c-.4-.2-.8-.4-1.2-.4-.5 0-.9.1-1.2.4-.4.2-.6.6-.8 1-.2.4-.3.9-.3 1.5s.1 1.1.3 1.6c.2.4.5.8.8 1 .4.2.8.4 1.2.4.5 0 .9-.1 1.2-.4.4-.2.6-.6.8-1 .2-.4.3-1 .3-1.6 0-.6-.1-1.1-.3-1.5-.1-.5-.4-.8-.8-1zm0 3.6c-.1.3-.3.5-.5.7-.2.1-.4.2-.7.2-.3 0-.5-.1-.7-.2-.2-.1-.4-.4-.5-.7-.1-.3-.2-.7-.2-1.2 0-.7.1-1.2.4-1.5.3-.3.6-.5 1-.5s.7.2 1 .5c.3.3.4.8.4 1.5-.1.5-.1.9-.2 1.2zm5-3.9h-.7l-3.1 4.3h2.8V15h1v-1.3h.7v-.8h-.7V9.4zm-1 3.5H16l1.2-1.7v1.7z"}));var Ll=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M4 4v1.5h16V4H4zm8 8.5h8V11h-8v1.5zM4 20h16v-1.5H4V20zm4-8c0-1.1-.9-2-2-2s-2 .9-2 2 .9 2 2 2 2-.9 2-2z"}));var Vl=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M6 5.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm11-.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM13 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2h-3a2 2 0 01-2-2V6zm5 8.5h-3a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5zM15 13a2 2 0 00-2 2v3a2 2 0 002 2h3a2 2 0 002-2v-3a2 2 0 00-2-2h-3zm-9 1.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5zM4 15a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2v-3z",fillRule:"evenodd",clipRule:"evenodd"}));var Ol=(0,o.createElement)(D.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(D.Path,{d:"M10 4.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm2.25 7.5v-1A2.75 2.75 0 0011 8.25H7A2.75 2.75 0 004.25 11v1h1.5v-1c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v1h1.5zM4 20h9v-1.5H4V20zm16-4H4v-1.5h16V16z",fillRule:"evenodd",clipRule:"evenodd"}));var Dl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{fillRule:"evenodd",d:"M8.95 11.25H4v1.5h4.95v4.5H13V18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-3c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v.75h-2.55v-7.5H13V9c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v.75H8.95v4.5ZM14.5 15v3c0 .*******.5h3c.3 0 .5-.2.5-.5v-3c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5Zm0-6V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v3c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5Z",clipRule:"evenodd"}));var Fl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M11.696 13.972c.356-.546.599-.958.728-1.235a1.79 1.79 0 00.203-.783c0-.264-.077-.47-.23-.618-.148-.153-.354-.23-.618-.23-.295 0-.569.07-.82.212a3.413 3.413 0 00-.738.571l-.147-1.188c.289-.234.59-.41.903-.526.313-.117.66-.175 1.041-.175.375 0 .695.08.959.24.264.153.46.362.59.626.135.265.203.556.203.876 0 .362-.08.734-.24 1.115-.154.381-.427.87-.82 1.466l-.756 1.152H14v1.106h-4l1.696-2.609z"}),(0,o.createElement)(D.Path,{d:"M19.5 7h-15v12a.5.5 0 00.5.5h14a.5.5 0 00.5-.5V7zM3 7V5a2 2 0 012-2h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"}));var Hl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M20.1 11.2l-6.7-6.7c-.1-.1-.3-.2-.5-.2H5c-.4-.1-.8.3-.8.7v7.8c0 .*******.5l6.7 6.7c.*******.7.5s.6.2.9.2c.3 0 .6-.1.9-.2.3-.1.5-.3.8-.5l5.6-5.6c.4-.4.7-1 .7-1.6.1-.6-.2-1.2-.6-1.6zM19 13.4L13.4 19c-.1.1-.2.1-.3.2-.2.1-.4.1-.6 0-.1 0-.2-.1-.3-.2l-6.5-6.5V5.8h6.8l6.5 6.5c.2.2.2.4.2.6 0 .1 0 .3-.2.5zM9 8c-.6 0-1 .4-1 1s.4 1 1 1 1-.4 1-1-.4-1-1-1z"}));const Gl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M18.7 3H5.3C4 3 3 4 3 5.3v13.4C3 20 4 21 5.3 21h13.4c1.3 0 2.3-1 2.3-2.3V5.3C21 4 20 3 18.7 3zm.8 15.7c0 .4-.4.8-.8.8H5.3c-.4 0-.8-.4-.8-.8V5.3c0-.4.4-.8.8-.8h13.4c.4 0 .8.4.8.8v13.4zM10 15l5-3-5-3v6z"}));const Ul=["front-page","single-post","page","index","archive","author","category","date","tag","taxonomy","search","404"],$l={"front-page":Na,"single-post":Ml,page:Bl,archive:Rl,search:Al,404:zl,index:Ll,category:Vl,author:Ol,taxonomy:Dl,date:Fl,tag:Hl,attachment:Gl};function Wl(e){let{postType:t}=e;const n=Ot(),{templates:r,defaultTemplateTypes:a}=(0,c.useSelect)((e=>({templates:e(u.store).getEntityRecords("postType","wp_template",{per_page:-1}),defaultTemplateTypes:e(m.store).__experimentalGetDefaultTemplateTypes()})),[]),{saveEntityRecord:l}=(0,c.useDispatch)(u.store),{createErrorNotice:i}=(0,c.useDispatch)(A.store),{setTemplate:s}=(0,c.useDispatch)(bt);const d=(0,$.map)(r,"slug"),_=(0,$.filter)(a,(e=>(0,$.includes)(Ul,e.slug)&&!(0,$.includes)(d,e.slug)));return _.length?(_.sort(((e,t)=>Ul.indexOf(e.slug)-Ul.indexOf(t.slug))),(0,o.createElement)(O.DropdownMenu,{className:"edit-site-new-template-dropdown",icon:null,text:t.labels.add_new,label:t.labels.add_new_item,popoverProps:{noArrow:!1},toggleProps:{variant:"primary"}},(()=>(0,o.createElement)(O.NavigableMenu,{className:"edit-site-new-template-dropdown__popover"},(0,o.createElement)(O.MenuGroup,{label:t.labels.add_new_item},(0,$.map)(_,(e=>{let{title:t,description:r,slug:c}=e;return(0,o.createElement)(O.MenuItem,{icon:$l[c],iconPosition:"left",info:r,key:c,onClick:()=>{!async function(e){let{slug:t}=e;try{const{title:e,description:r}=(0,$.find)(a,{slug:t}),o=await l("postType","wp_template",{excerpt:r,slug:t.toString(),status:"publish",title:e},{throwOnError:!0});s(o.id,o.slug),n.push({postId:o.id,postType:o.type})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("An error occurred while creating the template.");i(t,{type:"snackbar"})}}({slug:c})}},t)}))))))):null}function jl(e){let{postType:t}=e;const n=Ot(),[r,a]=(0,o.useState)(!1),{createErrorNotice:l}=(0,c.useDispatch)(A.store),{saveEntityRecord:i}=(0,c.useDispatch)(u.store);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.Button,{variant:"primary",onClick:()=>{a(!0)}},t.labels.add_new),r&&(0,o.createElement)(qa,{closeModal:()=>a(!1),onCreate:async function(e){let{title:t,area:r}=e;if(t)try{const e=(0,$.kebabCase)(t).replace(/[^\w-]+/g,"")||"wp-custom-part",l=await i("postType","wp_template_part",{slug:e,title:t,content:"",area:r},{throwOnError:!0});a(!1),n.push({postId:l.id,postType:l.type})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("An error occurred while creating the template part.");l(t,{type:"snackbar"}),a(!1)}else l((0,p.__)("Title is not defined."),{type:"snackbar"})}}))}function Yl(e){let{templateType:t="wp_template"}=e;const n=(0,c.useSelect)((e=>e(u.store).getPostType(t)),[t]);return n?"wp_template"===t?(0,o.createElement)(Wl,{postType:n}):"wp_template_part"===t?(0,o.createElement)(jl,{postType:n}):null:null}function ql(e){var t;let{templateType:n}=e;const r=(0,c.useSelect)((e=>e(u.store).getPostType(n)),[n]);return r?(0,o.createElement)("header",{className:"edit-site-list-header"},(0,o.createElement)(O.__experimentalHeading,{level:1,className:"edit-site-list-header__title"},null===(t=r.labels)||void 0===t?void 0:t.name),(0,o.createElement)("div",{className:"edit-site-list-header__right"},(0,o.createElement)(Yl,{templateType:n}))):null}function Xl(e){let{template:t,onClose:n}=e;const[r,a]=(0,o.useState)((()=>t.title.rendered)),[l,i]=(0,o.useState)(!1),{editEntityRecord:s,saveEditedEntityRecord:m}=(0,c.useDispatch)(u.store),{createSuccessNotice:d,createErrorNotice:_}=(0,c.useDispatch)(A.store);if(!t.is_custom)return null;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(O.MenuItem,{onClick:()=>{i(!0),a(t.title.rendered)}},(0,p.__)("Rename")),l&&(0,o.createElement)(O.Modal,{title:(0,p.__)("Rename"),closeLabel:(0,p.__)("Close"),onRequestClose:()=>{i(!1)},overlayClassName:"edit-site-list__rename-modal"},(0,o.createElement)("form",{onSubmit:async function(e){e.preventDefault();try{await s("postType",t.type,t.id,{title:r}),a(""),i(!1),n(),await m("postType",t.type,t.id,{throwOnError:!0}),d((0,p.__)("Entity renamed."),{type:"snackbar"})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("An error occurred while renaming the entity.");_(t,{type:"snackbar"})}}},(0,o.createElement)(O.Flex,{align:"flex-start",gap:8},(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.TextControl,{label:(0,p.__)("Name"),value:r,onChange:a,required:!0}))),(0,o.createElement)(O.Flex,{className:"edit-site-list__rename-modal-actions",justify:"flex-end",expanded:!1},(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.Button,{variant:"tertiary",onClick:()=>{i(!1)}},(0,p.__)("Cancel"))),(0,o.createElement)(O.FlexItem,null,(0,o.createElement)(O.Button,{variant:"primary",type:"submit"},(0,p.__)("Save")))))))}function Kl(e){let{template:t}=e;const{removeTemplate:n,revertTemplate:r}=(0,c.useDispatch)(bt),{saveEditedEntityRecord:a}=(0,c.useDispatch)(u.store),{createSuccessNotice:l,createErrorNotice:i}=(0,c.useDispatch)(A.store),s=function(e){return!!e&&"custom"===e.source&&!e.has_theme_file}(t),m=Ie(t);if(!s&&!m)return null;return(0,o.createElement)(O.DropdownMenu,{icon:fe,label:(0,p.__)("Actions"),className:"edit-site-list-table__actions"},(e=>{let{onClose:c}=e;return(0,o.createElement)(O.MenuGroup,null,s&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Xl,{template:t,onClose:c}),(0,o.createElement)(O.MenuItem,{isDestructive:!0,isTertiary:!0,onClick:()=>{n(t),c()}},(0,p.__)("Delete"))),m&&(0,o.createElement)(O.MenuItem,{info:(0,p.__)("Restore to default state"),onClick:()=>{!async function(){try{await r(t,{allowUndo:!1}),await a("postType",t.type,t.id),l((0,p.__)("Entity reverted."),{type:"snackbar"})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,p.__)("An error occurred while reverting the entity.");i(t,{type:"snackbar"})}}(),c()}},(0,p.__)("Clear customizations")))}))}var Zl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M10.5 4v4h3V4H15v4h1.5a1 1 0 011 1v4l-3 4v2a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2l-3-4V9a1 1 0 011-1H9V4h1.5zm.5 12.5v2h2v-2l3-4v-3H8v3l3 4z"}));var Ql=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"}));var Jl=(0,o.createElement)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(D.Path,{d:"M12 3.3c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8s-4-8.8-8.8-8.8zm6.5 5.5h-2.6C15.4 7.3 14.8 6 14 5c2 .6 3.6 2 4.5 3.8zm.7 3.2c0 .6-.1 1.2-.2 1.8h-2.9c.1-.6.1-1.2.1-1.8s-.1-1.2-.1-1.8H19c.2.6.2 1.2.2 1.8zM12 18.7c-1-.7-1.8-1.9-2.3-3.5h4.6c-.5 1.6-1.3 2.9-2.3 3.5zm-2.6-4.9c-.1-.6-.1-1.1-.1-1.8 0-.6.1-1.2.1-1.8h5.2c.1.6.1 1.1.1 1.8s-.1 1.2-.1 1.8H9.4zM4.8 12c0-.6.1-1.2.2-1.8h2.9c-.1.6-.1 1.2-.1 1.8 0 .6.1 1.2.1 1.8H5c-.2-.6-.2-1.2-.2-1.8zM12 5.3c1 .7 1.8 1.9 2.3 3.5H9.7c.5-1.6 1.3-2.9 2.3-3.5zM10 5c-.8 1-1.4 2.3-1.8 3.8H5.5C6.4 7 8 5.6 10 5zM5.5 15.3h2.6c.4 1.5 1 2.8 1.8 3.7-1.8-.6-3.5-2-4.4-3.7zM14 19c.8-1 1.4-2.2 1.8-3.7h2.6C17.6 17 16 18.4 14 19z"}));const eo=["wp_template","wp_template_part"];function to(e){let{isCustomized:t,children:n}=e;return t?(0,o.createElement)(O.Tooltip,{text:(0,p.__)("This template has been customized")},n):n}function no(e){let{text:t,icon:n,imageUrl:r,isCustomized:a}=e;const[l,i]=(0,o.useState)(!1);return(0,o.createElement)(O.__experimentalHStack,{alignment:"left"},(0,o.createElement)(to,{isCustomized:a},r?(0,o.createElement)("div",{className:V()("edit-site-list-added-by__avatar",{"is-loaded":l})},(0,o.createElement)("img",{onLoad:()=>i(!0),alt:"",src:r})):(0,o.createElement)("div",{className:V()("edit-site-list-added-by__icon",{"is-customized":a})},(0,o.createElement)(O.Icon,{icon:n}))),(0,o.createElement)("span",null,t))}function ro(e){var t;let{slug:n,isCustomized:r}=e;const a=(0,c.useSelect)((e=>e(u.store).getTheme(n)),[n]);return(0,o.createElement)(no,{icon:Hn,text:(null==a||null===(t=a.name)||void 0===t?void 0:t.rendered)||n,isCustomized:r})}function ao(e){let{slug:t,isCustomized:n}=e;const r=(0,c.useSelect)((e=>e(u.store).getPlugin(t)),[t]);return(0,o.createElement)(no,{icon:Zl,text:(null==r?void 0:r.name)||t,isCustomized:n})}function lo(e){var t;let{id:n}=e;const r=(0,c.useSelect)((e=>e(u.store).getUser(n)),[n]);return(0,o.createElement)(no,{icon:Ql,imageUrl:null==r||null===(t=r.avatar_urls)||void 0===t?void 0:t[48],text:null==r?void 0:r.nickname})}function oo(){const{name:e,logoURL:t}=(0,c.useSelect)((e=>{var t;const{getEntityRecord:n,getMedia:r}=e(u.store),a=n("root","__unstableBase");return{name:null==a?void 0:a.name,logoURL:null!=a&&a.site_logo?null===(t=r(a.site_logo))||void 0===t?void 0:t.source_url:void 0}}),[]);return(0,o.createElement)(no,{icon:Jl,imageUrl:t,text:e})}function io(e){let{templateType:t,template:n}=e;if(n){if(eo.includes(t)){if(n.has_theme_file&&("theme"===n.origin||!n.origin&&["theme","custom"].includes(n.source)))return(0,o.createElement)(ro,{slug:n.theme,isCustomized:"custom"===n.source});if(n.has_theme_file&&"plugin"===n.origin)return(0,o.createElement)(ao,{slug:n.theme,isCustomized:"custom"===n.source});if(!n.has_theme_file&&"custom"===n.source&&!n.author)return(0,o.createElement)(oo,null)}return(0,o.createElement)(lo,{id:n.author})}}function so(e){let{templateType:t}=e;const{records:n,isResolving:r}=(0,u.__experimentalUseEntityRecords)("postType",t,{per_page:-1}),a=(0,c.useSelect)((e=>e(u.store).getPostType(t)),[t]);return!n||r?null:n.length?(0,o.createElement)("table",{className:"edit-site-list-table",role:"table"},(0,o.createElement)("thead",null,(0,o.createElement)("tr",{className:"edit-site-list-table-head",role:"row"},(0,o.createElement)("th",{className:"edit-site-list-table-column",role:"columnheader"},(0,p.__)("Template")),(0,o.createElement)("th",{className:"edit-site-list-table-column",role:"columnheader"},(0,p.__)("Added by")),(0,o.createElement)("th",{className:"edit-site-list-table-column",role:"columnheader"},(0,o.createElement)(O.VisuallyHidden,null,(0,p.__)("Actions"))))),(0,o.createElement)("tbody",null,n.map((e=>{var n;return(0,o.createElement)("tr",{key:e.id,className:"edit-site-list-table-row",role:"row"},(0,o.createElement)("td",{className:"edit-site-list-table-column",role:"cell"},(0,o.createElement)(O.__experimentalHeading,{level:4},(0,o.createElement)(xn,{params:{postId:e.id,postType:e.type}},(0,Ta.decodeEntities)((null===(n=e.title)||void 0===n?void 0:n.rendered)||e.slug))),e.description),(0,o.createElement)("td",{className:"edit-site-list-table-column",role:"cell"},(0,o.createElement)(io,{templateType:t,template:e})),(0,o.createElement)("td",{className:"edit-site-list-table-column",role:"cell"},(0,o.createElement)(Kl,{template:e})))})))):(0,o.createElement)("div",null,(0,p.sprintf)((0,p.__)("No %s found."),null==a||null===(l=a.labels)||void 0===l||null===(i=l.name)||void 0===i?void 0:i.toLowerCase()));var l,i}function co(){var e,t;const{params:{postType:n}}=Vt();!function(){const{registerShortcut:e}=(0,c.useDispatch)(Ht.store);(0,o.useEffect)((()=>{e({name:"core/edit-site/next-region",category:"global",description:(0,p.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/edit-site/previous-region",category:"global",description:(0,p.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"}]})}),[])}();const{previousShortcut:r,nextShortcut:a,isNavigationOpen:l}=(0,c.useSelect)((e=>({previousShortcut:e(Ht.store).getAllShortcutKeyCombinations("core/edit-site/previous-region"),nextShortcut:e(Ht.store).getAllShortcutKeyCombinations("core/edit-site/next-region"),isNavigationOpen:e(bt).isNavigationOpened()})),[]),i=(0,c.useSelect)((e=>e(u.store).getPostType(n)),[n]);Nl(null==i||null===(e=i.labels)||void 0===e?void 0:e.name);const s=null==i||null===(t=i.labels)||void 0===t?void 0:t.items_list,d=i?{header:(0,p.sprintf)((0,p.__)("%s - Header"),s),body:(0,p.sprintf)((0,p.__)("%s - Content"),s)}:void 0;return(0,o.createElement)(ge,{className:V()("edit-site-list",{"is-navigation-open":l}),labels:{drawer:(0,p.__)("Navigation Sidebar"),...d},header:(0,o.createElement)(ql,{templateType:n}),drawer:(0,o.createElement)(Wa.Slot,null),notices:(0,o.createElement)(m.EditorSnackbars,null),content:(0,o.createElement)(so,{templateType:n}),shortcuts:{previous:r,next:a}})}function uo(e){let{postId:t,postType:n}=e;return!(t||!n)}function mo(e){let{reboot:t}=e;const{createErrorNotice:n}=(0,c.useDispatch)(A.store);function r(e){n((0,p.sprintf)((0,p.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}return(0,o.createElement)(O.SlotFillProvider,null,(0,o.createElement)(m.UnsavedChangesWarning,null),(0,o.createElement)(Ft,null,(e=>{let{params:n}=e;const a=uo(n);return(0,o.createElement)(o.Fragment,null,a?(0,o.createElement)(co,null):(0,o.createElement)(Pl,{onError:t}),(0,o.createElement)(ne.PluginArea,{onError:r}),(0,o.createElement)(Wa,{isDefaultOpen:!!a,activeTemplateType:a?n.postType:void 0}))})))}function po(e){let{className:t,...n}=e;return(0,o.createElement)(_e,z({panelClassName:t,className:"edit-site-sidebar",scope:"core/edit-site"},n))}function _o(e){return(0,o.createElement)(ce,z({__unstableExplicitMenuItem:!0,scope:"core/edit-site"},e))}var ho=(0,he.compose)((0,ne.withPluginContext)(((e,t)=>{var n;return{as:null!==(n=t.as)&&void 0!==n?n:O.MenuItem,icon:t.icon||e.icon,name:"core/edit-site/plugin-more-menu"}})))(ie);function go(e,t){if(!t.__unstableHomeTemplate)return void(0,o.render)((0,o.createElement)(bl,{message:(0,p.__)("The editor is unable to find a block template for the homepage."),dashboardLink:"index.php"}),e);(0,o.unmountComponentAtNode)(e);const n=go.bind(null,e,t);(0,c.dispatch)(d.store).setDefaults("core/edit-site",{editorMode:"visual",fixedToolbar:!1,focusMode:!1,keepCaretInsideBlock:!1,welcomeGuide:!0,welcomeGuideStyles:!0}),(0,c.dispatch)(bt).updateSettings(t),(0,c.dispatch)(m.store).updateEditorSettings({defaultTemplateTypes:t.defaultTemplateTypes,defaultTemplatePartAreas:t.defaultTemplatePartAreas});uo((0,h.getQueryArgs)(window.location.href))&&(0,c.dispatch)(bt).setIsNavigationPanelOpened((0,c.select)(_.store).isViewportMatch("medium"));(0,o.render)((0,o.createElement)(mo,{reboot:n}),e)}function fo(e,t){t.__experimentalFetchLinkSuggestions=(e,n)=>(0,u.__experimentalFetchLinkSuggestions)(e,n,t),t.__experimentalFetchRichUrlData=u.__experimentalFetchUrlData,t.__experimentalSpotlightEntityBlocks=["core/template-part"];const n=document.getElementById(e);(0,c.dispatch)(i.store).__experimentalReapplyBlockTypeFilters(),(0,s.registerCoreBlocks)(),go(n,t)}}(),(window.wp=window.wp||{}).editSite=r}();