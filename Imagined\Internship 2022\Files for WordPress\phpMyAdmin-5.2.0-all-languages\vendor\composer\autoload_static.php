<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit30dc56dbcd95b1f5db28729d0c4615c4
{
    public static $files = array (
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        '23c18046f52bef3eea034657bafda50f' => __DIR__ . '/..' . '/symfony/polyfill-php81/bootstrap.php',
        '0d59ee240a4cd96ddbb4ff164fccea4d' => __DIR__ . '/..' . '/symfony/polyfill-php73/bootstrap.php',
        '7b11c4dc42b3b3023073cb14e519683c' => __DIR__ . '/..' . '/ralouphie/getallheaders/src/getallheaders.php',
        '253c157292f75eb38082b5acb06f3f01' => __DIR__ . '/..' . '/nikic/fast-route/src/functions.php',
        '3109cb1a231dcd04bee1f9f620d46975' => __DIR__ . '/..' . '/paragonie/sodium_compat/autoload.php',
        '5011c20a8c04dcdea9f79098d4afe025' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/functions.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Williamdes\\MariaDBMySQLKBS\\' => 27,
            'Webmozart\\Assert\\' => 17,
        ),
        'T' => 
        array (
            'Twig\\' => 5,
        ),
        'S' => 
        array (
            'Symfony\\Polyfill\\Php81\\' => 23,
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Polyfill\\Php73\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Ctype\\' => 23,
            'Symfony\\Contracts\\Service\\' => 26,
            'Symfony\\Contracts\\Cache\\' => 24,
            'Symfony\\Component\\VarExporter\\' => 30,
            'Symfony\\Component\\Filesystem\\' => 29,
            'Symfony\\Component\\ExpressionLanguage\\' => 37,
            'Symfony\\Component\\DependencyInjection\\' => 38,
            'Symfony\\Component\\Config\\' => 25,
            'Symfony\\Component\\Cache\\' => 24,
            'Slim\\Psr7\\' => 10,
        ),
        'R' => 
        array (
            'ReCaptcha\\' => 10,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Container\\' => 14,
            'Psr\\Cache\\' => 10,
            'PragmaRX\\Google2FA\\' => 19,
            'PragmaRX\\Google2FAQRCode\\Tests\\' => 31,
            'PragmaRX\\Google2FAQRCode\\' => 25,
            'PhpMyAdmin\\Twig\\Extensions\\' => 27,
            'PhpMyAdmin\\SqlParser\\' => 21,
            'PhpMyAdmin\\ShapeFile\\' => 21,
            'PhpMyAdmin\\MoTranslator\\' => 24,
            'PhpMyAdmin\\' => 11,
            'ParagonIE\\ConstantTime\\' => 23,
        ),
        'F' => 
        array (
            'Fig\\Http\\Message\\' => 17,
            'FastRoute\\' => 10,
        ),
        'D' => 
        array (
            'DASPRiD\\Enum\\' => 13,
        ),
        'C' => 
        array (
            'Composer\\CaBundle\\' => 18,
            'CodeLts\\U2F\\U2FServer\\' => 22,
        ),
        'B' => 
        array (
            'BaconQrCode\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Williamdes\\MariaDBMySQLKBS\\' => 
        array (
            0 => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src',
        ),
        'Webmozart\\Assert\\' => 
        array (
            0 => __DIR__ . '/..' . '/webmozart/assert/src',
        ),
        'Twig\\' => 
        array (
            0 => __DIR__ . '/..' . '/twig/twig/src',
        ),
        'Symfony\\Polyfill\\Php81\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php81',
        ),
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Polyfill\\Php73\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php73',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'Symfony\\Contracts\\Service\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/service-contracts',
        ),
        'Symfony\\Contracts\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/cache-contracts',
        ),
        'Symfony\\Component\\VarExporter\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/var-exporter',
        ),
        'Symfony\\Component\\Filesystem\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/filesystem',
        ),
        'Symfony\\Component\\ExpressionLanguage\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/expression-language',
        ),
        'Symfony\\Component\\DependencyInjection\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/dependency-injection',
        ),
        'Symfony\\Component\\Config\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/config',
        ),
        'Symfony\\Component\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/cache',
        ),
        'Slim\\Psr7\\' => 
        array (
            0 => __DIR__ . '/..' . '/slim/psr7/src',
        ),
        'ReCaptcha\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Psr\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/cache/src',
        ),
        'PragmaRX\\Google2FA\\' => 
        array (
            0 => __DIR__ . '/..' . '/pragmarx/google2fa/src',
        ),
        'PragmaRX\\Google2FAQRCode\\Tests\\' => 
        array (
            0 => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/tests',
        ),
        'PragmaRX\\Google2FAQRCode\\' => 
        array (
            0 => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src',
        ),
        'PhpMyAdmin\\Twig\\Extensions\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmyadmin/twig-i18n-extension/src',
        ),
        'PhpMyAdmin\\SqlParser\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src',
        ),
        'PhpMyAdmin\\ShapeFile\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmyadmin/shapefile/src',
        ),
        'PhpMyAdmin\\MoTranslator\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmyadmin/motranslator/src',
        ),
        'PhpMyAdmin\\' => 
        array (
            0 => __DIR__ . '/../..' . '/libraries/classes',
        ),
        'ParagonIE\\ConstantTime\\' => 
        array (
            0 => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src',
        ),
        'Fig\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/fig/http-message-util/src',
        ),
        'FastRoute\\' => 
        array (
            0 => __DIR__ . '/..' . '/nikic/fast-route/src',
        ),
        'DASPRiD\\Enum\\' => 
        array (
            0 => __DIR__ . '/..' . '/dasprid/enum/src',
        ),
        'Composer\\CaBundle\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/ca-bundle/src',
        ),
        'CodeLts\\U2F\\U2FServer\\' => 
        array (
            0 => __DIR__ . '/..' . '/code-lts/u2f-php-server/src',
        ),
        'BaconQrCode\\' => 
        array (
            0 => __DIR__ . '/..' . '/bacon/bacon-qr-code/src',
        ),
    );

    public static $classMap = array (
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'BaconQrCode\\Common\\BitArray' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/BitArray.php',
        'BaconQrCode\\Common\\BitMatrix' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/BitMatrix.php',
        'BaconQrCode\\Common\\BitUtils' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/BitUtils.php',
        'BaconQrCode\\Common\\CharacterSetEci' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/CharacterSetEci.php',
        'BaconQrCode\\Common\\EcBlock' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/EcBlock.php',
        'BaconQrCode\\Common\\EcBlocks' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/EcBlocks.php',
        'BaconQrCode\\Common\\ErrorCorrectionLevel' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/ErrorCorrectionLevel.php',
        'BaconQrCode\\Common\\FormatInformation' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/FormatInformation.php',
        'BaconQrCode\\Common\\Mode' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/Mode.php',
        'BaconQrCode\\Common\\ReedSolomonCodec' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/ReedSolomonCodec.php',
        'BaconQrCode\\Common\\Version' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Common/Version.php',
        'BaconQrCode\\Encoder\\BlockPair' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/BlockPair.php',
        'BaconQrCode\\Encoder\\ByteMatrix' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/ByteMatrix.php',
        'BaconQrCode\\Encoder\\Encoder' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/Encoder.php',
        'BaconQrCode\\Encoder\\MaskUtil' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/MaskUtil.php',
        'BaconQrCode\\Encoder\\MatrixUtil' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/MatrixUtil.php',
        'BaconQrCode\\Encoder\\QrCode' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Encoder/QrCode.php',
        'BaconQrCode\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/ExceptionInterface.php',
        'BaconQrCode\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/InvalidArgumentException.php',
        'BaconQrCode\\Exception\\OutOfBoundsException' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/OutOfBoundsException.php',
        'BaconQrCode\\Exception\\RuntimeException' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/RuntimeException.php',
        'BaconQrCode\\Exception\\UnexpectedValueException' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/UnexpectedValueException.php',
        'BaconQrCode\\Exception\\WriterException' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Exception/WriterException.php',
        'BaconQrCode\\Renderer\\Color\\Alpha' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Color/Alpha.php',
        'BaconQrCode\\Renderer\\Color\\Cmyk' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Color/Cmyk.php',
        'BaconQrCode\\Renderer\\Color\\ColorInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Color/ColorInterface.php',
        'BaconQrCode\\Renderer\\Color\\Gray' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Color/Gray.php',
        'BaconQrCode\\Renderer\\Color\\Rgb' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Color/Rgb.php',
        'BaconQrCode\\Renderer\\Eye\\CompositeEye' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Eye/CompositeEye.php',
        'BaconQrCode\\Renderer\\Eye\\EyeInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Eye/EyeInterface.php',
        'BaconQrCode\\Renderer\\Eye\\ModuleEye' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Eye/ModuleEye.php',
        'BaconQrCode\\Renderer\\Eye\\SimpleCircleEye' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Eye/SimpleCircleEye.php',
        'BaconQrCode\\Renderer\\Eye\\SquareEye' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Eye/SquareEye.php',
        'BaconQrCode\\Renderer\\ImageRenderer' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/ImageRenderer.php',
        'BaconQrCode\\Renderer\\Image\\EpsImageBackEnd' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Image/EpsImageBackEnd.php',
        'BaconQrCode\\Renderer\\Image\\ImageBackEndInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Image/ImageBackEndInterface.php',
        'BaconQrCode\\Renderer\\Image\\ImagickImageBackEnd' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Image/ImagickImageBackEnd.php',
        'BaconQrCode\\Renderer\\Image\\SvgImageBackEnd' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Image/SvgImageBackEnd.php',
        'BaconQrCode\\Renderer\\Image\\TransformationMatrix' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Image/TransformationMatrix.php',
        'BaconQrCode\\Renderer\\Module\\DotsModule' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/DotsModule.php',
        'BaconQrCode\\Renderer\\Module\\EdgeIterator\\Edge' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/Edge.php',
        'BaconQrCode\\Renderer\\Module\\EdgeIterator\\EdgeIterator' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/EdgeIterator.php',
        'BaconQrCode\\Renderer\\Module\\ModuleInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/ModuleInterface.php',
        'BaconQrCode\\Renderer\\Module\\RoundnessModule' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/RoundnessModule.php',
        'BaconQrCode\\Renderer\\Module\\SquareModule' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Module/SquareModule.php',
        'BaconQrCode\\Renderer\\Path\\Close' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/Close.php',
        'BaconQrCode\\Renderer\\Path\\Curve' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/Curve.php',
        'BaconQrCode\\Renderer\\Path\\EllipticArc' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/EllipticArc.php',
        'BaconQrCode\\Renderer\\Path\\Line' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/Line.php',
        'BaconQrCode\\Renderer\\Path\\Move' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/Move.php',
        'BaconQrCode\\Renderer\\Path\\OperationInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/OperationInterface.php',
        'BaconQrCode\\Renderer\\Path\\Path' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/Path/Path.php',
        'BaconQrCode\\Renderer\\PlainTextRenderer' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/PlainTextRenderer.php',
        'BaconQrCode\\Renderer\\RendererInterface' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererInterface.php',
        'BaconQrCode\\Renderer\\RendererStyle\\EyeFill' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/EyeFill.php',
        'BaconQrCode\\Renderer\\RendererStyle\\Fill' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Fill.php',
        'BaconQrCode\\Renderer\\RendererStyle\\Gradient' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Gradient.php',
        'BaconQrCode\\Renderer\\RendererStyle\\GradientType' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/GradientType.php',
        'BaconQrCode\\Renderer\\RendererStyle\\RendererStyle' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/RendererStyle.php',
        'BaconQrCode\\Writer' => __DIR__ . '/..' . '/bacon/bacon-qr-code/src/Writer.php',
        'CodeLts\\U2F\\U2FServer\\Registration' => __DIR__ . '/..' . '/code-lts/u2f-php-server/src/Registration.php',
        'CodeLts\\U2F\\U2FServer\\RegistrationRequest' => __DIR__ . '/..' . '/code-lts/u2f-php-server/src/RegistrationRequest.php',
        'CodeLts\\U2F\\U2FServer\\SignRequest' => __DIR__ . '/..' . '/code-lts/u2f-php-server/src/SignRequest.php',
        'CodeLts\\U2F\\U2FServer\\U2FException' => __DIR__ . '/..' . '/code-lts/u2f-php-server/src/U2FException.php',
        'CodeLts\\U2F\\U2FServer\\U2FServer' => __DIR__ . '/..' . '/code-lts/u2f-php-server/src/U2FServer.php',
        'Composer\\CaBundle\\CaBundle' => __DIR__ . '/..' . '/composer/ca-bundle/src/CaBundle.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'DASPRiD\\Enum\\AbstractEnum' => __DIR__ . '/..' . '/dasprid/enum/src/AbstractEnum.php',
        'DASPRiD\\Enum\\EnumMap' => __DIR__ . '/..' . '/dasprid/enum/src/EnumMap.php',
        'DASPRiD\\Enum\\Exception\\CloneNotSupportedException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/CloneNotSupportedException.php',
        'DASPRiD\\Enum\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/ExceptionInterface.php',
        'DASPRiD\\Enum\\Exception\\ExpectationException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/ExpectationException.php',
        'DASPRiD\\Enum\\Exception\\IllegalArgumentException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/IllegalArgumentException.php',
        'DASPRiD\\Enum\\Exception\\MismatchException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/MismatchException.php',
        'DASPRiD\\Enum\\Exception\\SerializeNotSupportedException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/SerializeNotSupportedException.php',
        'DASPRiD\\Enum\\Exception\\UnserializeNotSupportedException' => __DIR__ . '/..' . '/dasprid/enum/src/Exception/UnserializeNotSupportedException.php',
        'DASPRiD\\Enum\\NullValue' => __DIR__ . '/..' . '/dasprid/enum/src/NullValue.php',
        'Datamatrix' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/barcodes/datamatrix.php',
        'FastRoute\\BadRouteException' => __DIR__ . '/..' . '/nikic/fast-route/src/BadRouteException.php',
        'FastRoute\\DataGenerator' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator.php',
        'FastRoute\\DataGenerator\\CharCountBased' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator/CharCountBased.php',
        'FastRoute\\DataGenerator\\GroupCountBased' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator/GroupCountBased.php',
        'FastRoute\\DataGenerator\\GroupPosBased' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator/GroupPosBased.php',
        'FastRoute\\DataGenerator\\MarkBased' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator/MarkBased.php',
        'FastRoute\\DataGenerator\\RegexBasedAbstract' => __DIR__ . '/..' . '/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php',
        'FastRoute\\Dispatcher' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher.php',
        'FastRoute\\Dispatcher\\CharCountBased' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher/CharCountBased.php',
        'FastRoute\\Dispatcher\\GroupCountBased' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher/GroupCountBased.php',
        'FastRoute\\Dispatcher\\GroupPosBased' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher/GroupPosBased.php',
        'FastRoute\\Dispatcher\\MarkBased' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher/MarkBased.php',
        'FastRoute\\Dispatcher\\RegexBasedAbstract' => __DIR__ . '/..' . '/nikic/fast-route/src/Dispatcher/RegexBasedAbstract.php',
        'FastRoute\\Route' => __DIR__ . '/..' . '/nikic/fast-route/src/Route.php',
        'FastRoute\\RouteCollector' => __DIR__ . '/..' . '/nikic/fast-route/src/RouteCollector.php',
        'FastRoute\\RouteParser' => __DIR__ . '/..' . '/nikic/fast-route/src/RouteParser.php',
        'FastRoute\\RouteParser\\Std' => __DIR__ . '/..' . '/nikic/fast-route/src/RouteParser/Std.php',
        'Fig\\Http\\Message\\RequestMethodInterface' => __DIR__ . '/..' . '/fig/http-message-util/src/RequestMethodInterface.php',
        'Fig\\Http\\Message\\StatusCodeInterface' => __DIR__ . '/..' . '/fig/http-message-util/src/StatusCodeInterface.php',
        'JsonException' => __DIR__ . '/..' . '/symfony/polyfill-php73/Resources/stubs/JsonException.php',
        'PDF417' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/barcodes/pdf417.php',
        'ParagonIE\\ConstantTime\\Base32' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base32.php',
        'ParagonIE\\ConstantTime\\Base32Hex' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base32Hex.php',
        'ParagonIE\\ConstantTime\\Base64' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base64.php',
        'ParagonIE\\ConstantTime\\Base64DotSlash' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base64DotSlash.php',
        'ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
        'ParagonIE\\ConstantTime\\Base64UrlSafe' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
        'ParagonIE\\ConstantTime\\Binary' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Binary.php',
        'ParagonIE\\ConstantTime\\EncoderInterface' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/EncoderInterface.php',
        'ParagonIE\\ConstantTime\\Encoding' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Encoding.php',
        'ParagonIE\\ConstantTime\\Hex' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/Hex.php',
        'ParagonIE\\ConstantTime\\RFC4648' => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src/RFC4648.php',
        'PhpMyAdmin\\Advisor' => __DIR__ . '/../..' . '/libraries/classes/Advisor.php',
        'PhpMyAdmin\\Bookmark' => __DIR__ . '/../..' . '/libraries/classes/Bookmark.php',
        'PhpMyAdmin\\BrowseForeigners' => __DIR__ . '/../..' . '/libraries/classes/BrowseForeigners.php',
        'PhpMyAdmin\\Cache' => __DIR__ . '/../..' . '/libraries/classes/Cache.php',
        'PhpMyAdmin\\Charsets' => __DIR__ . '/../..' . '/libraries/classes/Charsets.php',
        'PhpMyAdmin\\Charsets\\Charset' => __DIR__ . '/../..' . '/libraries/classes/Charsets/Charset.php',
        'PhpMyAdmin\\Charsets\\Collation' => __DIR__ . '/../..' . '/libraries/classes/Charsets/Collation.php',
        'PhpMyAdmin\\CheckUserPrivileges' => __DIR__ . '/../..' . '/libraries/classes/CheckUserPrivileges.php',
        'PhpMyAdmin\\Command\\CacheWarmupCommand' => __DIR__ . '/../..' . '/libraries/classes/Command/CacheWarmupCommand.php',
        'PhpMyAdmin\\Command\\FixPoTwigCommand' => __DIR__ . '/../..' . '/libraries/classes/Command/FixPoTwigCommand.php',
        'PhpMyAdmin\\Command\\SetVersionCommand' => __DIR__ . '/../..' . '/libraries/classes/Command/SetVersionCommand.php',
        'PhpMyAdmin\\Command\\TwigLintCommand' => __DIR__ . '/../..' . '/libraries/classes/Command/TwigLintCommand.php',
        'PhpMyAdmin\\Command\\WriteGitRevisionCommand' => __DIR__ . '/../..' . '/libraries/classes/Command/WriteGitRevisionCommand.php',
        'PhpMyAdmin\\Common' => __DIR__ . '/../..' . '/libraries/classes/Common.php',
        'PhpMyAdmin\\Config' => __DIR__ . '/../..' . '/libraries/classes/Config.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\BookmarkFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/BookmarkFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\BrowserTransformationFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/BrowserTransformationFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\CentralColumnsFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/CentralColumnsFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\ColumnCommentsFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/ColumnCommentsFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\ConfigurableMenusFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/ConfigurableMenusFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\DatabaseDesignerSettingsFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/DatabaseDesignerSettingsFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\DisplayFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/DisplayFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\ExportTemplatesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/ExportTemplatesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\FavoriteTablesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/FavoriteTablesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\NavigationItemsHidingFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/NavigationItemsHidingFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\PdfFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/PdfFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\RecentlyUsedTablesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/RecentlyUsedTablesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\RelationFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/RelationFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\SavedQueryByExampleSearchesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/SavedQueryByExampleSearchesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\SqlHistoryFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/SqlHistoryFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\TrackingFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/TrackingFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\UiPreferencesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/UiPreferencesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Features\\UserPreferencesFeature' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Features/UserPreferencesFeature.php',
        'PhpMyAdmin\\ConfigStorage\\Relation' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/Relation.php',
        'PhpMyAdmin\\ConfigStorage\\RelationCleanup' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/RelationCleanup.php',
        'PhpMyAdmin\\ConfigStorage\\RelationParameters' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/RelationParameters.php',
        'PhpMyAdmin\\ConfigStorage\\UserGroups' => __DIR__ . '/../..' . '/libraries/classes/ConfigStorage/UserGroups.php',
        'PhpMyAdmin\\Config\\ConfigFile' => __DIR__ . '/../..' . '/libraries/classes/Config/ConfigFile.php',
        'PhpMyAdmin\\Config\\Descriptions' => __DIR__ . '/../..' . '/libraries/classes/Config/Descriptions.php',
        'PhpMyAdmin\\Config\\Form' => __DIR__ . '/../..' . '/libraries/classes/Config/Form.php',
        'PhpMyAdmin\\Config\\FormDisplay' => __DIR__ . '/../..' . '/libraries/classes/Config/FormDisplay.php',
        'PhpMyAdmin\\Config\\FormDisplayTemplate' => __DIR__ . '/../..' . '/libraries/classes/Config/FormDisplayTemplate.php',
        'PhpMyAdmin\\Config\\Forms\\BaseForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/BaseForm.php',
        'PhpMyAdmin\\Config\\Forms\\BaseFormList' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/BaseFormList.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\BrowseForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/BrowseForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\DbStructureForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/DbStructureForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\EditForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/EditForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\ExportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/ExportForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\ImportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/ImportForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\NaviForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/NaviForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\PageFormList' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/PageFormList.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\SqlForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/SqlForm.php',
        'PhpMyAdmin\\Config\\Forms\\Page\\TableStructureForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Page/TableStructureForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\ConfigForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/ConfigForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\ExportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/ExportForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\FeaturesForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/FeaturesForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\ImportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/ImportForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\MainForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/MainForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\NaviForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/NaviForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\ServersForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/ServersForm.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\SetupFormList' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/SetupFormList.php',
        'PhpMyAdmin\\Config\\Forms\\Setup\\SqlForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/Setup/SqlForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\ExportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/ExportForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\FeaturesForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/FeaturesForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\ImportForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/ImportForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\MainForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/MainForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\NaviForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/NaviForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\SqlForm' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/SqlForm.php',
        'PhpMyAdmin\\Config\\Forms\\User\\UserFormList' => __DIR__ . '/../..' . '/libraries/classes/Config/Forms/User/UserFormList.php',
        'PhpMyAdmin\\Config\\PageSettings' => __DIR__ . '/../..' . '/libraries/classes/Config/PageSettings.php',
        'PhpMyAdmin\\Config\\ServerConfigChecks' => __DIR__ . '/../..' . '/libraries/classes/Config/ServerConfigChecks.php',
        'PhpMyAdmin\\Config\\Settings' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings.php',
        'PhpMyAdmin\\Config\\Settings\\Console' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Console.php',
        'PhpMyAdmin\\Config\\Settings\\Debug' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Debug.php',
        'PhpMyAdmin\\Config\\Settings\\Export' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Export.php',
        'PhpMyAdmin\\Config\\Settings\\Import' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Import.php',
        'PhpMyAdmin\\Config\\Settings\\Schema' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Schema.php',
        'PhpMyAdmin\\Config\\Settings\\Server' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Server.php',
        'PhpMyAdmin\\Config\\Settings\\SqlQueryBox' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/SqlQueryBox.php',
        'PhpMyAdmin\\Config\\Settings\\Transformations' => __DIR__ . '/../..' . '/libraries/classes/Config/Settings/Transformations.php',
        'PhpMyAdmin\\Config\\SpecialSchemaLinks' => __DIR__ . '/../..' . '/libraries/classes/Config/SpecialSchemaLinks.php',
        'PhpMyAdmin\\Config\\Validator' => __DIR__ . '/../..' . '/libraries/classes/Config/Validator.php',
        'PhpMyAdmin\\Console' => __DIR__ . '/../..' . '/libraries/classes/Console.php',
        'PhpMyAdmin\\Controllers\\AbstractController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/AbstractController.php',
        'PhpMyAdmin\\Controllers\\BrowseForeignersController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/BrowseForeignersController.php',
        'PhpMyAdmin\\Controllers\\ChangeLogController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/ChangeLogController.php',
        'PhpMyAdmin\\Controllers\\CheckRelationsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/CheckRelationsController.php',
        'PhpMyAdmin\\Controllers\\CollationConnectionController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/CollationConnectionController.php',
        'PhpMyAdmin\\Controllers\\ColumnController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/ColumnController.php',
        'PhpMyAdmin\\Controllers\\Config\\GetConfigController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Config/GetConfigController.php',
        'PhpMyAdmin\\Controllers\\Config\\SetConfigController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Config/SetConfigController.php',
        'PhpMyAdmin\\Controllers\\DatabaseController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/DatabaseController.php',
        'PhpMyAdmin\\Controllers\\Database\\AbstractController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/AbstractController.php',
        'PhpMyAdmin\\Controllers\\Database\\CentralColumnsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/CentralColumnsController.php',
        'PhpMyAdmin\\Controllers\\Database\\CentralColumns\\PopulateColumnsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/CentralColumns/PopulateColumnsController.php',
        'PhpMyAdmin\\Controllers\\Database\\DataDictionaryController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/DataDictionaryController.php',
        'PhpMyAdmin\\Controllers\\Database\\DesignerController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/DesignerController.php',
        'PhpMyAdmin\\Controllers\\Database\\EventsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/EventsController.php',
        'PhpMyAdmin\\Controllers\\Database\\ExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/ExportController.php',
        'PhpMyAdmin\\Controllers\\Database\\ImportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/ImportController.php',
        'PhpMyAdmin\\Controllers\\Database\\MultiTableQueryController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/MultiTableQueryController.php',
        'PhpMyAdmin\\Controllers\\Database\\MultiTableQuery\\QueryController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/MultiTableQuery/QueryController.php',
        'PhpMyAdmin\\Controllers\\Database\\MultiTableQuery\\TablesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/MultiTableQuery/TablesController.php',
        'PhpMyAdmin\\Controllers\\Database\\OperationsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/OperationsController.php',
        'PhpMyAdmin\\Controllers\\Database\\Operations\\CollationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Operations/CollationController.php',
        'PhpMyAdmin\\Controllers\\Database\\PrivilegesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/PrivilegesController.php',
        'PhpMyAdmin\\Controllers\\Database\\QueryByExampleController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/QueryByExampleController.php',
        'PhpMyAdmin\\Controllers\\Database\\RoutinesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/RoutinesController.php',
        'PhpMyAdmin\\Controllers\\Database\\SearchController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/SearchController.php',
        'PhpMyAdmin\\Controllers\\Database\\SqlAutoCompleteController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/SqlAutoCompleteController.php',
        'PhpMyAdmin\\Controllers\\Database\\SqlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/SqlController.php',
        'PhpMyAdmin\\Controllers\\Database\\SqlFormatController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/SqlFormatController.php',
        'PhpMyAdmin\\Controllers\\Database\\StructureController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/StructureController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\AddPrefixController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/AddPrefixController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\AddPrefixTableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/AddPrefixTableController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\AddController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CentralColumns/AddController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\MakeConsistentController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CentralColumns/MakeConsistentController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\RemoveController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CentralColumns/RemoveController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\ChangePrefixFormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/ChangePrefixFormController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyFormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CopyFormController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyTableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CopyTableController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyTableWithPrefixController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/CopyTableWithPrefixController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\DropFormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/DropFormController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\DropTableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/DropTableController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\EmptyFormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/EmptyFormController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\EmptyTableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/EmptyTableController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\FavoriteTableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/FavoriteTableController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\RealRowCountController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/RealRowCountController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\ReplacePrefixController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/ReplacePrefixController.php',
        'PhpMyAdmin\\Controllers\\Database\\Structure\\ShowCreateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/Structure/ShowCreateController.php',
        'PhpMyAdmin\\Controllers\\Database\\TrackingController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/TrackingController.php',
        'PhpMyAdmin\\Controllers\\Database\\TriggersController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Database/TriggersController.php',
        'PhpMyAdmin\\Controllers\\ErrorReportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/ErrorReportController.php',
        'PhpMyAdmin\\Controllers\\Export\\CheckTimeOutController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/CheckTimeOutController.php',
        'PhpMyAdmin\\Controllers\\Export\\ExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/ExportController.php',
        'PhpMyAdmin\\Controllers\\Export\\TablesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/TablesController.php',
        'PhpMyAdmin\\Controllers\\Export\\Template\\CreateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/Template/CreateController.php',
        'PhpMyAdmin\\Controllers\\Export\\Template\\DeleteController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/Template/DeleteController.php',
        'PhpMyAdmin\\Controllers\\Export\\Template\\LoadController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/Template/LoadController.php',
        'PhpMyAdmin\\Controllers\\Export\\Template\\UpdateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Export/Template/UpdateController.php',
        'PhpMyAdmin\\Controllers\\GisDataEditorController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/GisDataEditorController.php',
        'PhpMyAdmin\\Controllers\\GitInfoController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/GitInfoController.php',
        'PhpMyAdmin\\Controllers\\HomeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/HomeController.php',
        'PhpMyAdmin\\Controllers\\Import\\ImportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Import/ImportController.php',
        'PhpMyAdmin\\Controllers\\Import\\SimulateDmlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Import/SimulateDmlController.php',
        'PhpMyAdmin\\Controllers\\Import\\StatusController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Import/StatusController.php',
        'PhpMyAdmin\\Controllers\\JavaScriptMessagesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/JavaScriptMessagesController.php',
        'PhpMyAdmin\\Controllers\\LicenseController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/LicenseController.php',
        'PhpMyAdmin\\Controllers\\LintController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/LintController.php',
        'PhpMyAdmin\\Controllers\\LogoutController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/LogoutController.php',
        'PhpMyAdmin\\Controllers\\NavigationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/NavigationController.php',
        'PhpMyAdmin\\Controllers\\NormalizationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/NormalizationController.php',
        'PhpMyAdmin\\Controllers\\PhpInfoController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/PhpInfoController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\ExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/ExportController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\FeaturesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/FeaturesController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\ImportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/ImportController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\MainPanelController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/MainPanelController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\ManageController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/ManageController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\NavigationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/NavigationController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\SqlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/SqlController.php',
        'PhpMyAdmin\\Controllers\\Preferences\\TwoFactorController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Preferences/TwoFactorController.php',
        'PhpMyAdmin\\Controllers\\RecentTablesListController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/RecentTablesListController.php',
        'PhpMyAdmin\\Controllers\\SchemaExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/SchemaExportController.php',
        'PhpMyAdmin\\Controllers\\Server\\BinlogController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/BinlogController.php',
        'PhpMyAdmin\\Controllers\\Server\\CollationsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/CollationsController.php',
        'PhpMyAdmin\\Controllers\\Server\\DatabasesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/DatabasesController.php',
        'PhpMyAdmin\\Controllers\\Server\\Databases\\CreateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Databases/CreateController.php',
        'PhpMyAdmin\\Controllers\\Server\\Databases\\DestroyController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Databases/DestroyController.php',
        'PhpMyAdmin\\Controllers\\Server\\EnginesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/EnginesController.php',
        'PhpMyAdmin\\Controllers\\Server\\ExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/ExportController.php',
        'PhpMyAdmin\\Controllers\\Server\\ImportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/ImportController.php',
        'PhpMyAdmin\\Controllers\\Server\\PluginsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/PluginsController.php',
        'PhpMyAdmin\\Controllers\\Server\\PrivilegesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/PrivilegesController.php',
        'PhpMyAdmin\\Controllers\\Server\\Privileges\\AccountLockController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Privileges/AccountLockController.php',
        'PhpMyAdmin\\Controllers\\Server\\Privileges\\AccountUnlockController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Privileges/AccountUnlockController.php',
        'PhpMyAdmin\\Controllers\\Server\\ReplicationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/ReplicationController.php',
        'PhpMyAdmin\\Controllers\\Server\\ShowEngineController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/ShowEngineController.php',
        'PhpMyAdmin\\Controllers\\Server\\SqlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/SqlController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\AbstractController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/AbstractController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\AdvisorController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/AdvisorController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\MonitorController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/MonitorController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\ChartingDataController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Monitor/ChartingDataController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\GeneralLogController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Monitor/GeneralLogController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\LogVarsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Monitor/LogVarsController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\QueryAnalyzerController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Monitor/QueryAnalyzerController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\SlowLogController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Monitor/SlowLogController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\ProcessesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/ProcessesController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Processes\\KillController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Processes/KillController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\Processes\\RefreshController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/Processes/RefreshController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\QueriesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/QueriesController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\StatusController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/StatusController.php',
        'PhpMyAdmin\\Controllers\\Server\\Status\\VariablesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Status/VariablesController.php',
        'PhpMyAdmin\\Controllers\\Server\\UserGroupsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/UserGroupsController.php',
        'PhpMyAdmin\\Controllers\\Server\\UserGroupsFormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/UserGroupsFormController.php',
        'PhpMyAdmin\\Controllers\\Server\\VariablesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/VariablesController.php',
        'PhpMyAdmin\\Controllers\\Server\\Variables\\GetVariableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Variables/GetVariableController.php',
        'PhpMyAdmin\\Controllers\\Server\\Variables\\SetVariableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Server/Variables/SetVariableController.php',
        'PhpMyAdmin\\Controllers\\Setup\\AbstractController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Setup/AbstractController.php',
        'PhpMyAdmin\\Controllers\\Setup\\ConfigController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Setup/ConfigController.php',
        'PhpMyAdmin\\Controllers\\Setup\\FormController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Setup/FormController.php',
        'PhpMyAdmin\\Controllers\\Setup\\HomeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Setup/HomeController.php',
        'PhpMyAdmin\\Controllers\\Setup\\ServersController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Setup/ServersController.php',
        'PhpMyAdmin\\Controllers\\Sql\\ColumnPreferencesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/ColumnPreferencesController.php',
        'PhpMyAdmin\\Controllers\\Sql\\DefaultForeignKeyCheckValueController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/DefaultForeignKeyCheckValueController.php',
        'PhpMyAdmin\\Controllers\\Sql\\EnumValuesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/EnumValuesController.php',
        'PhpMyAdmin\\Controllers\\Sql\\RelationalValuesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/RelationalValuesController.php',
        'PhpMyAdmin\\Controllers\\Sql\\SetValuesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/SetValuesController.php',
        'PhpMyAdmin\\Controllers\\Sql\\SqlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Sql/SqlController.php',
        'PhpMyAdmin\\Controllers\\TableController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/TableController.php',
        'PhpMyAdmin\\Controllers\\Table\\AbstractController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/AbstractController.php',
        'PhpMyAdmin\\Controllers\\Table\\AddFieldController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/AddFieldController.php',
        'PhpMyAdmin\\Controllers\\Table\\ChangeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ChangeController.php',
        'PhpMyAdmin\\Controllers\\Table\\ChangeRowsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ChangeRowsController.php',
        'PhpMyAdmin\\Controllers\\Table\\ChartController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ChartController.php',
        'PhpMyAdmin\\Controllers\\Table\\CreateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/CreateController.php',
        'PhpMyAdmin\\Controllers\\Table\\DeleteConfirmController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/DeleteConfirmController.php',
        'PhpMyAdmin\\Controllers\\Table\\DeleteRowsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/DeleteRowsController.php',
        'PhpMyAdmin\\Controllers\\Table\\DropColumnConfirmationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/DropColumnConfirmationController.php',
        'PhpMyAdmin\\Controllers\\Table\\DropColumnController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/DropColumnController.php',
        'PhpMyAdmin\\Controllers\\Table\\ExportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ExportController.php',
        'PhpMyAdmin\\Controllers\\Table\\ExportRowsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ExportRowsController.php',
        'PhpMyAdmin\\Controllers\\Table\\FindReplaceController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/FindReplaceController.php',
        'PhpMyAdmin\\Controllers\\Table\\GetFieldController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/GetFieldController.php',
        'PhpMyAdmin\\Controllers\\Table\\GisVisualizationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/GisVisualizationController.php',
        'PhpMyAdmin\\Controllers\\Table\\ImportController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ImportController.php',
        'PhpMyAdmin\\Controllers\\Table\\IndexRenameController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/IndexRenameController.php',
        'PhpMyAdmin\\Controllers\\Table\\IndexesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/IndexesController.php',
        'PhpMyAdmin\\Controllers\\Table\\Maintenance\\AnalyzeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Maintenance/AnalyzeController.php',
        'PhpMyAdmin\\Controllers\\Table\\Maintenance\\CheckController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Maintenance/CheckController.php',
        'PhpMyAdmin\\Controllers\\Table\\Maintenance\\ChecksumController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Maintenance/ChecksumController.php',
        'PhpMyAdmin\\Controllers\\Table\\Maintenance\\OptimizeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Maintenance/OptimizeController.php',
        'PhpMyAdmin\\Controllers\\Table\\Maintenance\\RepairController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Maintenance/RepairController.php',
        'PhpMyAdmin\\Controllers\\Table\\OperationsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/OperationsController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\AnalyzeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/AnalyzeController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\CheckController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/CheckController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\DropController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/DropController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\OptimizeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/OptimizeController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\RebuildController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/RebuildController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\RepairController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/RepairController.php',
        'PhpMyAdmin\\Controllers\\Table\\Partition\\TruncateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Partition/TruncateController.php',
        'PhpMyAdmin\\Controllers\\Table\\PrivilegesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/PrivilegesController.php',
        'PhpMyAdmin\\Controllers\\Table\\RecentFavoriteController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/RecentFavoriteController.php',
        'PhpMyAdmin\\Controllers\\Table\\RelationController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/RelationController.php',
        'PhpMyAdmin\\Controllers\\Table\\ReplaceController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ReplaceController.php',
        'PhpMyAdmin\\Controllers\\Table\\SearchController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/SearchController.php',
        'PhpMyAdmin\\Controllers\\Table\\SqlController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/SqlController.php',
        'PhpMyAdmin\\Controllers\\Table\\StructureController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/StructureController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\AddIndexController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/AddIndexController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\AddKeyController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/AddKeyController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\BrowseController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/BrowseController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\CentralColumnsAddController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/CentralColumnsAddController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\CentralColumnsRemoveController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/CentralColumnsRemoveController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\ChangeController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/ChangeController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\FulltextController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/FulltextController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\MoveColumnsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/MoveColumnsController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\PartitioningController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/PartitioningController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\PrimaryController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/PrimaryController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\ReservedWordCheckController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/ReservedWordCheckController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\SaveController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/SaveController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\SpatialController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/SpatialController.php',
        'PhpMyAdmin\\Controllers\\Table\\Structure\\UniqueController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/Structure/UniqueController.php',
        'PhpMyAdmin\\Controllers\\Table\\TrackingController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/TrackingController.php',
        'PhpMyAdmin\\Controllers\\Table\\TriggersController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/TriggersController.php',
        'PhpMyAdmin\\Controllers\\Table\\ZoomSearchController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Table/ZoomSearchController.php',
        'PhpMyAdmin\\Controllers\\ThemeSetController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/ThemeSetController.php',
        'PhpMyAdmin\\Controllers\\ThemesController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/ThemesController.php',
        'PhpMyAdmin\\Controllers\\Transformation\\OverviewController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Transformation/OverviewController.php',
        'PhpMyAdmin\\Controllers\\Transformation\\WrapperController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/Transformation/WrapperController.php',
        'PhpMyAdmin\\Controllers\\UserPasswordController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/UserPasswordController.php',
        'PhpMyAdmin\\Controllers\\VersionCheckController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/VersionCheckController.php',
        'PhpMyAdmin\\Controllers\\View\\CreateController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/View/CreateController.php',
        'PhpMyAdmin\\Controllers\\View\\OperationsController' => __DIR__ . '/../..' . '/libraries/classes/Controllers/View/OperationsController.php',
        'PhpMyAdmin\\Core' => __DIR__ . '/../..' . '/libraries/classes/Core.php',
        'PhpMyAdmin\\CreateAddField' => __DIR__ . '/../..' . '/libraries/classes/CreateAddField.php',
        'PhpMyAdmin\\Crypto\\Crypto' => __DIR__ . '/../..' . '/libraries/classes/Crypto/Crypto.php',
        'PhpMyAdmin\\DatabaseInterface' => __DIR__ . '/../..' . '/libraries/classes/DatabaseInterface.php',
        'PhpMyAdmin\\Database\\CentralColumns' => __DIR__ . '/../..' . '/libraries/classes/Database/CentralColumns.php',
        'PhpMyAdmin\\Database\\DatabaseList' => __DIR__ . '/../..' . '/libraries/classes/Database/DatabaseList.php',
        'PhpMyAdmin\\Database\\Designer' => __DIR__ . '/../..' . '/libraries/classes/Database/Designer.php',
        'PhpMyAdmin\\Database\\Designer\\Common' => __DIR__ . '/../..' . '/libraries/classes/Database/Designer/Common.php',
        'PhpMyAdmin\\Database\\Designer\\DesignerTable' => __DIR__ . '/../..' . '/libraries/classes/Database/Designer/DesignerTable.php',
        'PhpMyAdmin\\Database\\Events' => __DIR__ . '/../..' . '/libraries/classes/Database/Events.php',
        'PhpMyAdmin\\Database\\MultiTableQuery' => __DIR__ . '/../..' . '/libraries/classes/Database/MultiTableQuery.php',
        'PhpMyAdmin\\Database\\Qbe' => __DIR__ . '/../..' . '/libraries/classes/Database/Qbe.php',
        'PhpMyAdmin\\Database\\Routines' => __DIR__ . '/../..' . '/libraries/classes/Database/Routines.php',
        'PhpMyAdmin\\Database\\Search' => __DIR__ . '/../..' . '/libraries/classes/Database/Search.php',
        'PhpMyAdmin\\Database\\Triggers' => __DIR__ . '/../..' . '/libraries/classes/Database/Triggers.php',
        'PhpMyAdmin\\DbTableExists' => __DIR__ . '/../..' . '/libraries/classes/DbTableExists.php',
        'PhpMyAdmin\\Dbal\\DatabaseName' => __DIR__ . '/../..' . '/libraries/classes/Dbal/DatabaseName.php',
        'PhpMyAdmin\\Dbal\\DbalInterface' => __DIR__ . '/../..' . '/libraries/classes/Dbal/DbalInterface.php',
        'PhpMyAdmin\\Dbal\\DbiExtension' => __DIR__ . '/../..' . '/libraries/classes/Dbal/DbiExtension.php',
        'PhpMyAdmin\\Dbal\\DbiMysqli' => __DIR__ . '/../..' . '/libraries/classes/Dbal/DbiMysqli.php',
        'PhpMyAdmin\\Dbal\\MysqliResult' => __DIR__ . '/../..' . '/libraries/classes/Dbal/MysqliResult.php',
        'PhpMyAdmin\\Dbal\\ResultInterface' => __DIR__ . '/../..' . '/libraries/classes/Dbal/ResultInterface.php',
        'PhpMyAdmin\\Dbal\\TableName' => __DIR__ . '/../..' . '/libraries/classes/Dbal/TableName.php',
        'PhpMyAdmin\\Dbal\\Warning' => __DIR__ . '/../..' . '/libraries/classes/Dbal/Warning.php',
        'PhpMyAdmin\\Display\\Results' => __DIR__ . '/../..' . '/libraries/classes/Display/Results.php',
        'PhpMyAdmin\\Encoding' => __DIR__ . '/../..' . '/libraries/classes/Encoding.php',
        'PhpMyAdmin\\Engines\\Bdb' => __DIR__ . '/../..' . '/libraries/classes/Engines/Bdb.php',
        'PhpMyAdmin\\Engines\\Berkeleydb' => __DIR__ . '/../..' . '/libraries/classes/Engines/Berkeleydb.php',
        'PhpMyAdmin\\Engines\\Binlog' => __DIR__ . '/../..' . '/libraries/classes/Engines/Binlog.php',
        'PhpMyAdmin\\Engines\\Innobase' => __DIR__ . '/../..' . '/libraries/classes/Engines/Innobase.php',
        'PhpMyAdmin\\Engines\\Innodb' => __DIR__ . '/../..' . '/libraries/classes/Engines/Innodb.php',
        'PhpMyAdmin\\Engines\\Memory' => __DIR__ . '/../..' . '/libraries/classes/Engines/Memory.php',
        'PhpMyAdmin\\Engines\\Merge' => __DIR__ . '/../..' . '/libraries/classes/Engines/Merge.php',
        'PhpMyAdmin\\Engines\\MrgMyisam' => __DIR__ . '/../..' . '/libraries/classes/Engines/MrgMyisam.php',
        'PhpMyAdmin\\Engines\\Myisam' => __DIR__ . '/../..' . '/libraries/classes/Engines/Myisam.php',
        'PhpMyAdmin\\Engines\\Ndbcluster' => __DIR__ . '/../..' . '/libraries/classes/Engines/Ndbcluster.php',
        'PhpMyAdmin\\Engines\\Pbxt' => __DIR__ . '/../..' . '/libraries/classes/Engines/Pbxt.php',
        'PhpMyAdmin\\Engines\\PerformanceSchema' => __DIR__ . '/../..' . '/libraries/classes/Engines/PerformanceSchema.php',
        'PhpMyAdmin\\Error' => __DIR__ . '/../..' . '/libraries/classes/Error.php',
        'PhpMyAdmin\\ErrorHandler' => __DIR__ . '/../..' . '/libraries/classes/ErrorHandler.php',
        'PhpMyAdmin\\ErrorReport' => __DIR__ . '/../..' . '/libraries/classes/ErrorReport.php',
        'PhpMyAdmin\\Exceptions\\ExportException' => __DIR__ . '/../..' . '/libraries/classes/Exceptions/ExportException.php',
        'PhpMyAdmin\\Export' => __DIR__ . '/../..' . '/libraries/classes/Export.php',
        'PhpMyAdmin\\Export\\Options' => __DIR__ . '/../..' . '/libraries/classes/Export/Options.php',
        'PhpMyAdmin\\Export\\Template' => __DIR__ . '/../..' . '/libraries/classes/Export/Template.php',
        'PhpMyAdmin\\Export\\TemplateModel' => __DIR__ . '/../..' . '/libraries/classes/Export/TemplateModel.php',
        'PhpMyAdmin\\FieldMetadata' => __DIR__ . '/../..' . '/libraries/classes/FieldMetadata.php',
        'PhpMyAdmin\\File' => __DIR__ . '/../..' . '/libraries/classes/File.php',
        'PhpMyAdmin\\FileListing' => __DIR__ . '/../..' . '/libraries/classes/FileListing.php',
        'PhpMyAdmin\\FlashMessages' => __DIR__ . '/../..' . '/libraries/classes/FlashMessages.php',
        'PhpMyAdmin\\Font' => __DIR__ . '/../..' . '/libraries/classes/Font.php',
        'PhpMyAdmin\\Footer' => __DIR__ . '/../..' . '/libraries/classes/Footer.php',
        'PhpMyAdmin\\Gis\\GisFactory' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisFactory.php',
        'PhpMyAdmin\\Gis\\GisGeometry' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisGeometry.php',
        'PhpMyAdmin\\Gis\\GisGeometryCollection' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisGeometryCollection.php',
        'PhpMyAdmin\\Gis\\GisLineString' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisLineString.php',
        'PhpMyAdmin\\Gis\\GisMultiLineString' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisMultiLineString.php',
        'PhpMyAdmin\\Gis\\GisMultiPoint' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisMultiPoint.php',
        'PhpMyAdmin\\Gis\\GisMultiPolygon' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisMultiPolygon.php',
        'PhpMyAdmin\\Gis\\GisPoint' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisPoint.php',
        'PhpMyAdmin\\Gis\\GisPolygon' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisPolygon.php',
        'PhpMyAdmin\\Gis\\GisVisualization' => __DIR__ . '/../..' . '/libraries/classes/Gis/GisVisualization.php',
        'PhpMyAdmin\\Git' => __DIR__ . '/../..' . '/libraries/classes/Git.php',
        'PhpMyAdmin\\Header' => __DIR__ . '/../..' . '/libraries/classes/Header.php',
        'PhpMyAdmin\\Html\\Generator' => __DIR__ . '/../..' . '/libraries/classes/Html/Generator.php',
        'PhpMyAdmin\\Html\\MySQLDocumentation' => __DIR__ . '/../..' . '/libraries/classes/Html/MySQLDocumentation.php',
        'PhpMyAdmin\\Http\\Factory\\ServerRequestFactory' => __DIR__ . '/../..' . '/libraries/classes/Http/Factory/ServerRequestFactory.php',
        'PhpMyAdmin\\Http\\ServerRequest' => __DIR__ . '/../..' . '/libraries/classes/Http/ServerRequest.php',
        'PhpMyAdmin\\Image\\ImageWrapper' => __DIR__ . '/../..' . '/libraries/classes/Image/ImageWrapper.php',
        'PhpMyAdmin\\Import' => __DIR__ . '/../..' . '/libraries/classes/Import.php',
        'PhpMyAdmin\\Import\\Ajax' => __DIR__ . '/../..' . '/libraries/classes/Import/Ajax.php',
        'PhpMyAdmin\\Import\\SimulateDml' => __DIR__ . '/../..' . '/libraries/classes/Import/SimulateDml.php',
        'PhpMyAdmin\\Index' => __DIR__ . '/../..' . '/libraries/classes/Index.php',
        'PhpMyAdmin\\IndexColumn' => __DIR__ . '/../..' . '/libraries/classes/IndexColumn.php',
        'PhpMyAdmin\\InsertEdit' => __DIR__ . '/../..' . '/libraries/classes/InsertEdit.php',
        'PhpMyAdmin\\InternalRelations' => __DIR__ . '/../..' . '/libraries/classes/InternalRelations.php',
        'PhpMyAdmin\\IpAllowDeny' => __DIR__ . '/../..' . '/libraries/classes/IpAllowDeny.php',
        'PhpMyAdmin\\Language' => __DIR__ . '/../..' . '/libraries/classes/Language.php',
        'PhpMyAdmin\\LanguageManager' => __DIR__ . '/../..' . '/libraries/classes/LanguageManager.php',
        'PhpMyAdmin\\Linter' => __DIR__ . '/../..' . '/libraries/classes/Linter.php',
        'PhpMyAdmin\\ListAbstract' => __DIR__ . '/../..' . '/libraries/classes/ListAbstract.php',
        'PhpMyAdmin\\ListDatabase' => __DIR__ . '/../..' . '/libraries/classes/ListDatabase.php',
        'PhpMyAdmin\\Logging' => __DIR__ . '/../..' . '/libraries/classes/Logging.php',
        'PhpMyAdmin\\Menu' => __DIR__ . '/../..' . '/libraries/classes/Menu.php',
        'PhpMyAdmin\\Message' => __DIR__ . '/../..' . '/libraries/classes/Message.php',
        'PhpMyAdmin\\Mime' => __DIR__ . '/../..' . '/libraries/classes/Mime.php',
        'PhpMyAdmin\\MoTranslator\\CacheException' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/CacheException.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\ApcuCache' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/ApcuCache.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\ApcuCacheFactory' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/ApcuCacheFactory.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\CacheFactoryInterface' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/CacheFactoryInterface.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\CacheInterface' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/CacheInterface.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\GetAllInterface' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/GetAllInterface.php',
        'PhpMyAdmin\\MoTranslator\\Cache\\InMemoryCache' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Cache/InMemoryCache.php',
        'PhpMyAdmin\\MoTranslator\\Loader' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Loader.php',
        'PhpMyAdmin\\MoTranslator\\MoParser' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/MoParser.php',
        'PhpMyAdmin\\MoTranslator\\ReaderException' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/ReaderException.php',
        'PhpMyAdmin\\MoTranslator\\StringReader' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/StringReader.php',
        'PhpMyAdmin\\MoTranslator\\Translator' => __DIR__ . '/..' . '/phpmyadmin/motranslator/src/Translator.php',
        'PhpMyAdmin\\Navigation\\Navigation' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Navigation.php',
        'PhpMyAdmin\\Navigation\\NavigationTree' => __DIR__ . '/../..' . '/libraries/classes/Navigation/NavigationTree.php',
        'PhpMyAdmin\\Navigation\\NodeFactory' => __DIR__ . '/../..' . '/libraries/classes/Navigation/NodeFactory.php',
        'PhpMyAdmin\\Navigation\\Nodes\\Node' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/Node.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeColumn' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeColumn.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeColumnContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeColumnContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabase' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeDatabase.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseChild' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeDatabaseChild.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseChildContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeDatabaseChildContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeDatabaseContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeEvent' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeEvent.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeEventContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeEventContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeFunction' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeFunction.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeFunctionContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeFunctionContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeIndex' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeIndex.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeIndexContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeIndexContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeProcedure' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeProcedure.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeProcedureContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeProcedureContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeTable' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeTable.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeTableContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeTableContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeTrigger' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeTrigger.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeTriggerContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeTriggerContainer.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeView' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeView.php',
        'PhpMyAdmin\\Navigation\\Nodes\\NodeViewContainer' => __DIR__ . '/../..' . '/libraries/classes/Navigation/Nodes/NodeViewContainer.php',
        'PhpMyAdmin\\Normalization' => __DIR__ . '/../..' . '/libraries/classes/Normalization.php',
        'PhpMyAdmin\\OpenDocument' => __DIR__ . '/../..' . '/libraries/classes/OpenDocument.php',
        'PhpMyAdmin\\Operations' => __DIR__ . '/../..' . '/libraries/classes/Operations.php',
        'PhpMyAdmin\\OutputBuffering' => __DIR__ . '/../..' . '/libraries/classes/OutputBuffering.php',
        'PhpMyAdmin\\ParseAnalyze' => __DIR__ . '/../..' . '/libraries/classes/ParseAnalyze.php',
        'PhpMyAdmin\\Partitioning\\Maintenance' => __DIR__ . '/../..' . '/libraries/classes/Partitioning/Maintenance.php',
        'PhpMyAdmin\\Partitioning\\Partition' => __DIR__ . '/../..' . '/libraries/classes/Partitioning/Partition.php',
        'PhpMyAdmin\\Partitioning\\SubPartition' => __DIR__ . '/../..' . '/libraries/classes/Partitioning/SubPartition.php',
        'PhpMyAdmin\\Partitioning\\TablePartitionDefinition' => __DIR__ . '/../..' . '/libraries/classes/Partitioning/TablePartitionDefinition.php',
        'PhpMyAdmin\\Pdf' => __DIR__ . '/../..' . '/libraries/classes/Pdf.php',
        'PhpMyAdmin\\Plugins' => __DIR__ . '/../..' . '/libraries/classes/Plugins.php',
        'PhpMyAdmin\\Plugins\\Auth\\AuthenticationConfig' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Auth/AuthenticationConfig.php',
        'PhpMyAdmin\\Plugins\\Auth\\AuthenticationCookie' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Auth/AuthenticationCookie.php',
        'PhpMyAdmin\\Plugins\\Auth\\AuthenticationHttp' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Auth/AuthenticationHttp.php',
        'PhpMyAdmin\\Plugins\\Auth\\AuthenticationSignon' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Auth/AuthenticationSignon.php',
        'PhpMyAdmin\\Plugins\\AuthenticationPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/AuthenticationPlugin.php',
        'PhpMyAdmin\\Plugins\\ExportPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/ExportPlugin.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportCodegen' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportCodegen.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportCsv' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportCsv.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportExcel' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportExcel.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportHtmlword' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportHtmlword.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportJson' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportJson.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportLatex' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportLatex.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportMediawiki' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportMediawiki.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportOds' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportOds.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportOdt' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportOdt.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportPdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportPdf.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportPhparray' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportPhparray.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportSql' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportSql.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportTexytext' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportTexytext.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportXml' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportXml.php',
        'PhpMyAdmin\\Plugins\\Export\\ExportYaml' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/ExportYaml.php',
        'PhpMyAdmin\\Plugins\\Export\\Helpers\\Pdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/Helpers/Pdf.php',
        'PhpMyAdmin\\Plugins\\Export\\Helpers\\TableProperty' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Export/Helpers/TableProperty.php',
        'PhpMyAdmin\\Plugins\\IOTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/IOTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\ImportPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/ImportPlugin.php',
        'PhpMyAdmin\\Plugins\\Import\\AbstractImportCsv' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/AbstractImportCsv.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportCsv' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportCsv.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportLdi' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportLdi.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportMediawiki' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportMediawiki.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportOds' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportOds.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportShp' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportShp.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportSql' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportSql.php',
        'PhpMyAdmin\\Plugins\\Import\\ImportXml' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ImportXml.php',
        'PhpMyAdmin\\Plugins\\Import\\ShapeFileImport' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/ShapeFileImport.php',
        'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadNoplugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/Upload/UploadNoplugin.php',
        'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadProgress' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/Upload/UploadProgress.php',
        'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadSession' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Import/Upload/UploadSession.php',
        'PhpMyAdmin\\Plugins\\Plugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Plugin.php',
        'PhpMyAdmin\\Plugins\\SchemaPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/SchemaPlugin.php',
        'PhpMyAdmin\\Plugins\\Schema\\Dia\\Dia' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Dia/Dia.php',
        'PhpMyAdmin\\Plugins\\Schema\\Dia\\DiaRelationSchema' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Dia/DiaRelationSchema.php',
        'PhpMyAdmin\\Plugins\\Schema\\Dia\\RelationStatsDia' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Dia/RelationStatsDia.php',
        'PhpMyAdmin\\Plugins\\Schema\\Dia\\TableStatsDia' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Dia/TableStatsDia.php',
        'PhpMyAdmin\\Plugins\\Schema\\Eps\\Eps' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Eps/Eps.php',
        'PhpMyAdmin\\Plugins\\Schema\\Eps\\EpsRelationSchema' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Eps/EpsRelationSchema.php',
        'PhpMyAdmin\\Plugins\\Schema\\Eps\\RelationStatsEps' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Eps/RelationStatsEps.php',
        'PhpMyAdmin\\Plugins\\Schema\\Eps\\TableStatsEps' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Eps/TableStatsEps.php',
        'PhpMyAdmin\\Plugins\\Schema\\ExportRelationSchema' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/ExportRelationSchema.php',
        'PhpMyAdmin\\Plugins\\Schema\\Pdf\\Pdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Pdf/Pdf.php',
        'PhpMyAdmin\\Plugins\\Schema\\Pdf\\PdfRelationSchema' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php',
        'PhpMyAdmin\\Plugins\\Schema\\Pdf\\RelationStatsPdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Pdf/RelationStatsPdf.php',
        'PhpMyAdmin\\Plugins\\Schema\\Pdf\\TableStatsPdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Pdf/TableStatsPdf.php',
        'PhpMyAdmin\\Plugins\\Schema\\RelationStats' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/RelationStats.php',
        'PhpMyAdmin\\Plugins\\Schema\\SchemaDia' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/SchemaDia.php',
        'PhpMyAdmin\\Plugins\\Schema\\SchemaEps' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/SchemaEps.php',
        'PhpMyAdmin\\Plugins\\Schema\\SchemaPdf' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/SchemaPdf.php',
        'PhpMyAdmin\\Plugins\\Schema\\SchemaSvg' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/SchemaSvg.php',
        'PhpMyAdmin\\Plugins\\Schema\\Svg\\RelationStatsSvg' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Svg/RelationStatsSvg.php',
        'PhpMyAdmin\\Plugins\\Schema\\Svg\\Svg' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Svg/Svg.php',
        'PhpMyAdmin\\Plugins\\Schema\\Svg\\SvgRelationSchema' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Svg/SvgRelationSchema.php',
        'PhpMyAdmin\\Plugins\\Schema\\Svg\\TableStatsSvg' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/Svg/TableStatsSvg.php',
        'PhpMyAdmin\\Plugins\\Schema\\TableStats' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Schema/TableStats.php',
        'PhpMyAdmin\\Plugins\\TransformationsInterface' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TransformationsInterface.php',
        'PhpMyAdmin\\Plugins\\TransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\Bool2TextTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/Bool2TextTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\CodeMirrorEditorTransformationPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/CodeMirrorEditorTransformationPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\DateFormatTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/DateFormatTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\DownloadTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/DownloadTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ExternalTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/ExternalTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\FormattedTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/FormattedTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\HexTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/HexTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ImageLinkTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/ImageLinkTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ImageUploadTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/ImageUploadTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\InlineTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/InlineTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\LongToIPv4TransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/LongToIPv4TransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\PreApPendTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/PreApPendTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\RegexValidationTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/RegexValidationTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\SQLTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/SQLTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\SubstringTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/SubstringTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextFileUploadTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/TextFileUploadTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextImageLinkTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/TextImageLinkTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextLinkTransformationsPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Abs/TextLinkTransformationsPlugin.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Image_JPEG_Upload' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Image_JPEG_Upload.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_FileUpload' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_FileUpload.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_Iptobinary' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptobinary.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_Iptolong' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptolong.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_JsonEditor' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_JsonEditor.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_RegexValidation' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_RegexValidation.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_SqlEditor' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_SqlEditor.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_XmlEditor' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_XmlEditor.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Application_Octetstream_Download' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Application_Octetstream_Download.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Application_Octetstream_Hex' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Application_Octetstream_Hex.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_JPEG_Inline' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Image_JPEG_Inline.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_JPEG_Link' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Image_JPEG_Link.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_PNG_Inline' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Image_PNG_Inline.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Octetstream_Sql' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Octetstream_Sql.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Binarytoip' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Binarytoip.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Bool2Text' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Bool2Text.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Dateformat' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Dateformat.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_External' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_External.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Formatted' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Formatted.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Imagelink' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Imagelink.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Json' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Json.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Sql' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Sql.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Xml' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Xml.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Link' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Text_Plain_Link.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Longtoipv4' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Text_Plain_Longtoipv4.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_PreApPend' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Text_Plain_PreApPend.php',
        'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Substring' => __DIR__ . '/../..' . '/libraries/classes/Plugins/Transformations/Text_Plain_Substring.php',
        'PhpMyAdmin\\Plugins\\TwoFactorPlugin' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TwoFactorPlugin.php',
        'PhpMyAdmin\\Plugins\\TwoFactor\\Application' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TwoFactor/Application.php',
        'PhpMyAdmin\\Plugins\\TwoFactor\\Invalid' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TwoFactor/Invalid.php',
        'PhpMyAdmin\\Plugins\\TwoFactor\\Key' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TwoFactor/Key.php',
        'PhpMyAdmin\\Plugins\\TwoFactor\\Simple' => __DIR__ . '/../..' . '/libraries/classes/Plugins/TwoFactor/Simple.php',
        'PhpMyAdmin\\Plugins\\UploadInterface' => __DIR__ . '/../..' . '/libraries/classes/Plugins/UploadInterface.php',
        'PhpMyAdmin\\Profiling' => __DIR__ . '/../..' . '/libraries/classes/Profiling.php',
        'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertyMainGroup' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Groups/OptionsPropertyMainGroup.php',
        'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertyRootGroup' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Groups/OptionsPropertyRootGroup.php',
        'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertySubgroup' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Groups/OptionsPropertySubgroup.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\BoolPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/BoolPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\DocPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/DocPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\HiddenPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/HiddenPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\MessageOnlyPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/MessageOnlyPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\NumberPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/NumberPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\RadioPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/RadioPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\SelectPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/SelectPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\Items\\TextPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/Items/TextPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\OptionsPropertyGroup' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/OptionsPropertyGroup.php',
        'PhpMyAdmin\\Properties\\Options\\OptionsPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/OptionsPropertyItem.php',
        'PhpMyAdmin\\Properties\\Options\\OptionsPropertyOneItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Options/OptionsPropertyOneItem.php',
        'PhpMyAdmin\\Properties\\Plugins\\ExportPluginProperties' => __DIR__ . '/../..' . '/libraries/classes/Properties/Plugins/ExportPluginProperties.php',
        'PhpMyAdmin\\Properties\\Plugins\\ImportPluginProperties' => __DIR__ . '/../..' . '/libraries/classes/Properties/Plugins/ImportPluginProperties.php',
        'PhpMyAdmin\\Properties\\Plugins\\PluginPropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/Plugins/PluginPropertyItem.php',
        'PhpMyAdmin\\Properties\\Plugins\\SchemaPluginProperties' => __DIR__ . '/../..' . '/libraries/classes/Properties/Plugins/SchemaPluginProperties.php',
        'PhpMyAdmin\\Properties\\PropertyItem' => __DIR__ . '/../..' . '/libraries/classes/Properties/PropertyItem.php',
        'PhpMyAdmin\\Providers\\ServerVariables\\MariaDbMySqlKbsProvider' => __DIR__ . '/../..' . '/libraries/classes/Providers/ServerVariables/MariaDbMySqlKbsProvider.php',
        'PhpMyAdmin\\Providers\\ServerVariables\\ServerVariablesProvider' => __DIR__ . '/../..' . '/libraries/classes/Providers/ServerVariables/ServerVariablesProvider.php',
        'PhpMyAdmin\\Providers\\ServerVariables\\ServerVariablesProviderInterface' => __DIR__ . '/../..' . '/libraries/classes/Providers/ServerVariables/ServerVariablesProviderInterface.php',
        'PhpMyAdmin\\Providers\\ServerVariables\\VoidProvider' => __DIR__ . '/../..' . '/libraries/classes/Providers/ServerVariables/VoidProvider.php',
        'PhpMyAdmin\\Query\\Cache' => __DIR__ . '/../..' . '/libraries/classes/Query/Cache.php',
        'PhpMyAdmin\\Query\\Compatibility' => __DIR__ . '/../..' . '/libraries/classes/Query/Compatibility.php',
        'PhpMyAdmin\\Query\\Generator' => __DIR__ . '/../..' . '/libraries/classes/Query/Generator.php',
        'PhpMyAdmin\\Query\\Utilities' => __DIR__ . '/../..' . '/libraries/classes/Query/Utilities.php',
        'PhpMyAdmin\\RecentFavoriteTable' => __DIR__ . '/../..' . '/libraries/classes/RecentFavoriteTable.php',
        'PhpMyAdmin\\Replication' => __DIR__ . '/../..' . '/libraries/classes/Replication.php',
        'PhpMyAdmin\\ReplicationGui' => __DIR__ . '/../..' . '/libraries/classes/ReplicationGui.php',
        'PhpMyAdmin\\ReplicationInfo' => __DIR__ . '/../..' . '/libraries/classes/ReplicationInfo.php',
        'PhpMyAdmin\\ResponseRenderer' => __DIR__ . '/../..' . '/libraries/classes/ResponseRenderer.php',
        'PhpMyAdmin\\Routing' => __DIR__ . '/../..' . '/libraries/classes/Routing.php',
        'PhpMyAdmin\\Sanitize' => __DIR__ . '/../..' . '/libraries/classes/Sanitize.php',
        'PhpMyAdmin\\SavedSearches' => __DIR__ . '/../..' . '/libraries/classes/SavedSearches.php',
        'PhpMyAdmin\\Scripts' => __DIR__ . '/../..' . '/libraries/classes/Scripts.php',
        'PhpMyAdmin\\Server\\Plugin' => __DIR__ . '/../..' . '/libraries/classes/Server/Plugin.php',
        'PhpMyAdmin\\Server\\Plugins' => __DIR__ . '/../..' . '/libraries/classes/Server/Plugins.php',
        'PhpMyAdmin\\Server\\Privileges' => __DIR__ . '/../..' . '/libraries/classes/Server/Privileges.php',
        'PhpMyAdmin\\Server\\Privileges\\AccountLocking' => __DIR__ . '/../..' . '/libraries/classes/Server/Privileges/AccountLocking.php',
        'PhpMyAdmin\\Server\\Select' => __DIR__ . '/../..' . '/libraries/classes/Server/Select.php',
        'PhpMyAdmin\\Server\\Status\\Data' => __DIR__ . '/../..' . '/libraries/classes/Server/Status/Data.php',
        'PhpMyAdmin\\Server\\Status\\Monitor' => __DIR__ . '/../..' . '/libraries/classes/Server/Status/Monitor.php',
        'PhpMyAdmin\\Server\\Status\\Processes' => __DIR__ . '/../..' . '/libraries/classes/Server/Status/Processes.php',
        'PhpMyAdmin\\Server\\SysInfo\\Base' => __DIR__ . '/../..' . '/libraries/classes/Server/SysInfo/Base.php',
        'PhpMyAdmin\\Server\\SysInfo\\Linux' => __DIR__ . '/../..' . '/libraries/classes/Server/SysInfo/Linux.php',
        'PhpMyAdmin\\Server\\SysInfo\\SunOs' => __DIR__ . '/../..' . '/libraries/classes/Server/SysInfo/SunOs.php',
        'PhpMyAdmin\\Server\\SysInfo\\SysInfo' => __DIR__ . '/../..' . '/libraries/classes/Server/SysInfo/SysInfo.php',
        'PhpMyAdmin\\Server\\SysInfo\\WindowsNt' => __DIR__ . '/../..' . '/libraries/classes/Server/SysInfo/WindowsNt.php',
        'PhpMyAdmin\\Session' => __DIR__ . '/../..' . '/libraries/classes/Session.php',
        'PhpMyAdmin\\Setup\\ConfigGenerator' => __DIR__ . '/../..' . '/libraries/classes/Setup/ConfigGenerator.php',
        'PhpMyAdmin\\Setup\\FormProcessing' => __DIR__ . '/../..' . '/libraries/classes/Setup/FormProcessing.php',
        'PhpMyAdmin\\Setup\\Index' => __DIR__ . '/../..' . '/libraries/classes/Setup/Index.php',
        'PhpMyAdmin\\ShapeFile\\ShapeFile' => __DIR__ . '/..' . '/phpmyadmin/shapefile/src/ShapeFile.php',
        'PhpMyAdmin\\ShapeFile\\ShapeRecord' => __DIR__ . '/..' . '/phpmyadmin/shapefile/src/ShapeRecord.php',
        'PhpMyAdmin\\ShapeFile\\Util' => __DIR__ . '/..' . '/phpmyadmin/shapefile/src/Util.php',
        'PhpMyAdmin\\Sql' => __DIR__ . '/../..' . '/libraries/classes/Sql.php',
        'PhpMyAdmin\\SqlParser\\Component' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Component.php',
        'PhpMyAdmin\\SqlParser\\Components\\AlterOperation' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/AlterOperation.php',
        'PhpMyAdmin\\SqlParser\\Components\\Array2d' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Array2d.php',
        'PhpMyAdmin\\SqlParser\\Components\\ArrayObj' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/ArrayObj.php',
        'PhpMyAdmin\\SqlParser\\Components\\CaseExpression' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/CaseExpression.php',
        'PhpMyAdmin\\SqlParser\\Components\\Condition' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Condition.php',
        'PhpMyAdmin\\SqlParser\\Components\\CreateDefinition' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/CreateDefinition.php',
        'PhpMyAdmin\\SqlParser\\Components\\DataType' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/DataType.php',
        'PhpMyAdmin\\SqlParser\\Components\\Expression' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Expression.php',
        'PhpMyAdmin\\SqlParser\\Components\\ExpressionArray' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/ExpressionArray.php',
        'PhpMyAdmin\\SqlParser\\Components\\FunctionCall' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/FunctionCall.php',
        'PhpMyAdmin\\SqlParser\\Components\\GroupKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/GroupKeyword.php',
        'PhpMyAdmin\\SqlParser\\Components\\IndexHint' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/IndexHint.php',
        'PhpMyAdmin\\SqlParser\\Components\\IntoKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/IntoKeyword.php',
        'PhpMyAdmin\\SqlParser\\Components\\JoinKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/JoinKeyword.php',
        'PhpMyAdmin\\SqlParser\\Components\\Key' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Key.php',
        'PhpMyAdmin\\SqlParser\\Components\\Limit' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Limit.php',
        'PhpMyAdmin\\SqlParser\\Components\\LockExpression' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/LockExpression.php',
        'PhpMyAdmin\\SqlParser\\Components\\OptionsArray' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/OptionsArray.php',
        'PhpMyAdmin\\SqlParser\\Components\\OrderKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/OrderKeyword.php',
        'PhpMyAdmin\\SqlParser\\Components\\ParameterDefinition' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/ParameterDefinition.php',
        'PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/PartitionDefinition.php',
        'PhpMyAdmin\\SqlParser\\Components\\Reference' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/Reference.php',
        'PhpMyAdmin\\SqlParser\\Components\\RenameOperation' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/RenameOperation.php',
        'PhpMyAdmin\\SqlParser\\Components\\SetOperation' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/SetOperation.php',
        'PhpMyAdmin\\SqlParser\\Components\\UnionKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/UnionKeyword.php',
        'PhpMyAdmin\\SqlParser\\Components\\WithKeyword' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Components/WithKeyword.php',
        'PhpMyAdmin\\SqlParser\\Context' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Context.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100000' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100000.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100100' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100100.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100200' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100200.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100300' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100300.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100400' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100400.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100500' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100500.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100600' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100600.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50000' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50000.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50100' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50100.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50500' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50500.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50600' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50600.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50700' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50700.php',
        'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql80000' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql80000.php',
        'PhpMyAdmin\\SqlParser\\Core' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Core.php',
        'PhpMyAdmin\\SqlParser\\Exceptions\\LexerException' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Exceptions/LexerException.php',
        'PhpMyAdmin\\SqlParser\\Exceptions\\LoaderException' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Exceptions/LoaderException.php',
        'PhpMyAdmin\\SqlParser\\Exceptions\\ParserException' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Exceptions/ParserException.php',
        'PhpMyAdmin\\SqlParser\\Lexer' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Lexer.php',
        'PhpMyAdmin\\SqlParser\\Parser' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Parser.php',
        'PhpMyAdmin\\SqlParser\\Statement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\AlterStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/AlterStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/AnalyzeStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\BackupStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/BackupStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\CallStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/CallStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\CheckStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/CheckStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/ChecksumStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\CreateStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/CreateStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/DeleteStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\DropStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/DropStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/ExplainStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\InsertStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/InsertStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\LoadStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/LoadStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\LockStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/LockStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\MaintenanceStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/MaintenanceStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\NotImplementedStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/NotImplementedStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/OptimizeStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/PurgeStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\RenameStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/RenameStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\RepairStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/RepairStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/ReplaceStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/RestoreStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\SelectStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/SelectStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\SetStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/SetStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\ShowStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/ShowStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/TransactionStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/TruncateStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/UpdateStatement.php',
        'PhpMyAdmin\\SqlParser\\Statements\\WithStatement' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Statements/WithStatement.php',
        'PhpMyAdmin\\SqlParser\\Token' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Token.php',
        'PhpMyAdmin\\SqlParser\\TokensList' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/TokensList.php',
        'PhpMyAdmin\\SqlParser\\Tools\\ContextGenerator' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Tools/ContextGenerator.php',
        'PhpMyAdmin\\SqlParser\\Tools\\TestGenerator' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Tools/TestGenerator.php',
        'PhpMyAdmin\\SqlParser\\Translator' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Translator.php',
        'PhpMyAdmin\\SqlParser\\UtfString' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/UtfString.php',
        'PhpMyAdmin\\SqlParser\\Utils\\BufferedQuery' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/BufferedQuery.php',
        'PhpMyAdmin\\SqlParser\\Utils\\CLI' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/CLI.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Error' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Error.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Formatter' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Formatter.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Misc' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Misc.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Query' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Query.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Routine' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Routine.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Table' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Table.php',
        'PhpMyAdmin\\SqlParser\\Utils\\Tokens' => __DIR__ . '/..' . '/phpmyadmin/sql-parser/src/Utils/Tokens.php',
        'PhpMyAdmin\\SqlQueryForm' => __DIR__ . '/../..' . '/libraries/classes/SqlQueryForm.php',
        'PhpMyAdmin\\StorageEngine' => __DIR__ . '/../..' . '/libraries/classes/StorageEngine.php',
        'PhpMyAdmin\\SystemDatabase' => __DIR__ . '/../..' . '/libraries/classes/SystemDatabase.php',
        'PhpMyAdmin\\Table' => __DIR__ . '/../..' . '/libraries/classes/Table.php',
        'PhpMyAdmin\\Table\\ColumnsDefinition' => __DIR__ . '/../..' . '/libraries/classes/Table/ColumnsDefinition.php',
        'PhpMyAdmin\\Table\\Indexes' => __DIR__ . '/../..' . '/libraries/classes/Table/Indexes.php',
        'PhpMyAdmin\\Table\\Maintenance' => __DIR__ . '/../..' . '/libraries/classes/Table/Maintenance.php',
        'PhpMyAdmin\\Table\\Maintenance\\Message' => __DIR__ . '/../..' . '/libraries/classes/Table/Maintenance/Message.php',
        'PhpMyAdmin\\Table\\Search' => __DIR__ . '/../..' . '/libraries/classes/Table/Search.php',
        'PhpMyAdmin\\Template' => __DIR__ . '/../..' . '/libraries/classes/Template.php',
        'PhpMyAdmin\\Theme' => __DIR__ . '/../..' . '/libraries/classes/Theme.php',
        'PhpMyAdmin\\ThemeManager' => __DIR__ . '/../..' . '/libraries/classes/ThemeManager.php',
        'PhpMyAdmin\\Tracker' => __DIR__ . '/../..' . '/libraries/classes/Tracker.php',
        'PhpMyAdmin\\Tracking' => __DIR__ . '/../..' . '/libraries/classes/Tracking.php',
        'PhpMyAdmin\\Transformations' => __DIR__ . '/../..' . '/libraries/classes/Transformations.php',
        'PhpMyAdmin\\Twig\\AssetExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/AssetExtension.php',
        'PhpMyAdmin\\Twig\\CoreExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/CoreExtension.php',
        'PhpMyAdmin\\Twig\\Extensions\\I18nExtension' => __DIR__ . '/..' . '/phpmyadmin/twig-i18n-extension/src/I18nExtension.php',
        'PhpMyAdmin\\Twig\\Extensions\\Node\\TransNode' => __DIR__ . '/..' . '/phpmyadmin/twig-i18n-extension/src/Node/TransNode.php',
        'PhpMyAdmin\\Twig\\Extensions\\TokenParser\\TransTokenParser' => __DIR__ . '/..' . '/phpmyadmin/twig-i18n-extension/src/TokenParser/TransTokenParser.php',
        'PhpMyAdmin\\Twig\\FlashMessagesExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/FlashMessagesExtension.php',
        'PhpMyAdmin\\Twig\\I18nExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/I18nExtension.php',
        'PhpMyAdmin\\Twig\\MessageExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/MessageExtension.php',
        'PhpMyAdmin\\Twig\\RelationExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/RelationExtension.php',
        'PhpMyAdmin\\Twig\\SanitizeExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/SanitizeExtension.php',
        'PhpMyAdmin\\Twig\\TableExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/TableExtension.php',
        'PhpMyAdmin\\Twig\\TrackerExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/TrackerExtension.php',
        'PhpMyAdmin\\Twig\\TransformationsExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/TransformationsExtension.php',
        'PhpMyAdmin\\Twig\\UrlExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/UrlExtension.php',
        'PhpMyAdmin\\Twig\\UtilExtension' => __DIR__ . '/../..' . '/libraries/classes/Twig/UtilExtension.php',
        'PhpMyAdmin\\TwoFactor' => __DIR__ . '/../..' . '/libraries/classes/TwoFactor.php',
        'PhpMyAdmin\\Types' => __DIR__ . '/../..' . '/libraries/classes/Types.php',
        'PhpMyAdmin\\Url' => __DIR__ . '/../..' . '/libraries/classes/Url.php',
        'PhpMyAdmin\\UrlRedirector' => __DIR__ . '/../..' . '/libraries/classes/UrlRedirector.php',
        'PhpMyAdmin\\UserPassword' => __DIR__ . '/../..' . '/libraries/classes/UserPassword.php',
        'PhpMyAdmin\\UserPreferences' => __DIR__ . '/../..' . '/libraries/classes/UserPreferences.php',
        'PhpMyAdmin\\Util' => __DIR__ . '/../..' . '/libraries/classes/Util.php',
        'PhpMyAdmin\\Utils\\ForeignKey' => __DIR__ . '/../..' . '/libraries/classes/Utils/ForeignKey.php',
        'PhpMyAdmin\\Utils\\FormatConverter' => __DIR__ . '/../..' . '/libraries/classes/Utils/FormatConverter.php',
        'PhpMyAdmin\\Utils\\Gis' => __DIR__ . '/../..' . '/libraries/classes/Utils/Gis.php',
        'PhpMyAdmin\\Utils\\HttpRequest' => __DIR__ . '/../..' . '/libraries/classes/Utils/HttpRequest.php',
        'PhpMyAdmin\\Utils\\SessionCache' => __DIR__ . '/../..' . '/libraries/classes/Utils/SessionCache.php',
        'PhpMyAdmin\\Version' => __DIR__ . '/../..' . '/libraries/classes/Version.php',
        'PhpMyAdmin\\VersionInformation' => __DIR__ . '/../..' . '/libraries/classes/VersionInformation.php',
        'PhpMyAdmin\\ZipExtension' => __DIR__ . '/../..' . '/libraries/classes/ZipExtension.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'PragmaRX\\Google2FAQRCode\\Exceptions\\MissingQrCodeServiceException' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src/Exceptions/MissingQrCodeServiceException.php',
        'PragmaRX\\Google2FAQRCode\\Google2FA' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src/Google2FA.php',
        'PragmaRX\\Google2FAQRCode\\QRCode\\Bacon' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src/QRCode/Bacon.php',
        'PragmaRX\\Google2FAQRCode\\QRCode\\Chillerlan' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src/QRCode/Chillerlan.php',
        'PragmaRX\\Google2FAQRCode\\QRCode\\QRCodeServiceContract' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/src/QRCode/QRCodeServiceContract.php',
        'PragmaRX\\Google2FAQRCode\\Tests\\Constants' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/tests/Constants.php',
        'PragmaRX\\Google2FAQRCode\\Tests\\Google2FATest' => __DIR__ . '/..' . '/pragmarx/google2fa-qrcode/tests/Google2FATest.php',
        'PragmaRX\\Google2FA\\Exceptions\\Contracts\\Google2FA' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Contracts/Google2FA.php',
        'PragmaRX\\Google2FA\\Exceptions\\Contracts\\IncompatibleWithGoogleAuthenticator' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Contracts/IncompatibleWithGoogleAuthenticator.php',
        'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidAlgorithm' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidAlgorithm.php',
        'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidCharacters' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidCharacters.php',
        'PragmaRX\\Google2FA\\Exceptions\\Contracts\\SecretKeyTooShort' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Contracts/SecretKeyTooShort.php',
        'PragmaRX\\Google2FA\\Exceptions\\Google2FAException' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/Google2FAException.php',
        'PragmaRX\\Google2FA\\Exceptions\\IncompatibleWithGoogleAuthenticatorException' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/IncompatibleWithGoogleAuthenticatorException.php',
        'PragmaRX\\Google2FA\\Exceptions\\InvalidAlgorithmException' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/InvalidAlgorithmException.php',
        'PragmaRX\\Google2FA\\Exceptions\\InvalidCharactersException' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/InvalidCharactersException.php',
        'PragmaRX\\Google2FA\\Exceptions\\SecretKeyTooShortException' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Exceptions/SecretKeyTooShortException.php',
        'PragmaRX\\Google2FA\\Google2FA' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Google2FA.php',
        'PragmaRX\\Google2FA\\Support\\Base32' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Support/Base32.php',
        'PragmaRX\\Google2FA\\Support\\Constants' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Support/Constants.php',
        'PragmaRX\\Google2FA\\Support\\QRCode' => __DIR__ . '/..' . '/pragmarx/google2fa/src/Support/QRCode.php',
        'Psr\\Cache\\CacheException' => __DIR__ . '/..' . '/psr/cache/src/CacheException.php',
        'Psr\\Cache\\CacheItemInterface' => __DIR__ . '/..' . '/psr/cache/src/CacheItemInterface.php',
        'Psr\\Cache\\CacheItemPoolInterface' => __DIR__ . '/..' . '/psr/cache/src/CacheItemPoolInterface.php',
        'Psr\\Cache\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/cache/src/InvalidArgumentException.php',
        'Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerExceptionInterface.php',
        'Psr\\Container\\ContainerInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerInterface.php',
        'Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/NotFoundExceptionInterface.php',
        'Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/..' . '/psr/http-message/src/MessageInterface.php',
        'Psr\\Http\\Message\\RequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/RequestFactoryInterface.php',
        'Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/RequestInterface.php',
        'Psr\\Http\\Message\\ResponseFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ResponseFactoryInterface.php',
        'Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/..' . '/psr/http-message/src/ResponseInterface.php',
        'Psr\\Http\\Message\\ServerRequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
        'Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/ServerRequestInterface.php',
        'Psr\\Http\\Message\\StreamFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/StreamFactoryInterface.php',
        'Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/..' . '/psr/http-message/src/StreamInterface.php',
        'Psr\\Http\\Message\\UploadedFileFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
        'Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/..' . '/psr/http-message/src/UploadedFileInterface.php',
        'Psr\\Http\\Message\\UriFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UriFactoryInterface.php',
        'Psr\\Http\\Message\\UriInterface' => __DIR__ . '/..' . '/psr/http-message/src/UriInterface.php',
        'Psr\\Log\\AbstractLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/AbstractLogger.php',
        'Psr\\Log\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/log/Psr/Log/InvalidArgumentException.php',
        'Psr\\Log\\LogLevel' => __DIR__ . '/..' . '/psr/log/Psr/Log/LogLevel.php',
        'Psr\\Log\\LoggerAwareInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareInterface.php',
        'Psr\\Log\\LoggerAwareTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareTrait.php',
        'Psr\\Log\\LoggerInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerInterface.php',
        'Psr\\Log\\LoggerTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerTrait.php',
        'Psr\\Log\\NullLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/NullLogger.php',
        'Psr\\Log\\Test\\DummyTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/DummyTest.php',
        'Psr\\Log\\Test\\LoggerInterfaceTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
        'Psr\\Log\\Test\\TestLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/TestLogger.php',
        'QRcode' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/barcodes/qrcode.php',
        'ReCaptcha\\ReCaptcha' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/ReCaptcha.php',
        'ReCaptcha\\RequestMethod' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod.php',
        'ReCaptcha\\RequestMethod\\Curl' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod/Curl.php',
        'ReCaptcha\\RequestMethod\\CurlPost' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod/CurlPost.php',
        'ReCaptcha\\RequestMethod\\Post' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod/Post.php',
        'ReCaptcha\\RequestMethod\\Socket' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod/Socket.php',
        'ReCaptcha\\RequestMethod\\SocketPost' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestMethod/SocketPost.php',
        'ReCaptcha\\RequestParameters' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/RequestParameters.php',
        'ReCaptcha\\Response' => __DIR__ . '/..' . '/google/recaptcha/src/ReCaptcha/Response.php',
        'ReturnTypeWillChange' => __DIR__ . '/..' . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
        'Slim\\Psr7\\Cookies' => __DIR__ . '/..' . '/slim/psr7/src/Cookies.php',
        'Slim\\Psr7\\Environment' => __DIR__ . '/..' . '/slim/psr7/src/Environment.php',
        'Slim\\Psr7\\Factory\\RequestFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/RequestFactory.php',
        'Slim\\Psr7\\Factory\\ResponseFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/ResponseFactory.php',
        'Slim\\Psr7\\Factory\\ServerRequestFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/ServerRequestFactory.php',
        'Slim\\Psr7\\Factory\\StreamFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/StreamFactory.php',
        'Slim\\Psr7\\Factory\\UploadedFileFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/UploadedFileFactory.php',
        'Slim\\Psr7\\Factory\\UriFactory' => __DIR__ . '/..' . '/slim/psr7/src/Factory/UriFactory.php',
        'Slim\\Psr7\\Header' => __DIR__ . '/..' . '/slim/psr7/src/Header.php',
        'Slim\\Psr7\\Headers' => __DIR__ . '/..' . '/slim/psr7/src/Headers.php',
        'Slim\\Psr7\\Interfaces\\HeadersInterface' => __DIR__ . '/..' . '/slim/psr7/src/Interfaces/HeadersInterface.php',
        'Slim\\Psr7\\Message' => __DIR__ . '/..' . '/slim/psr7/src/Message.php',
        'Slim\\Psr7\\NonBufferedBody' => __DIR__ . '/..' . '/slim/psr7/src/NonBufferedBody.php',
        'Slim\\Psr7\\Request' => __DIR__ . '/..' . '/slim/psr7/src/Request.php',
        'Slim\\Psr7\\Response' => __DIR__ . '/..' . '/slim/psr7/src/Response.php',
        'Slim\\Psr7\\Stream' => __DIR__ . '/..' . '/slim/psr7/src/Stream.php',
        'Slim\\Psr7\\UploadedFile' => __DIR__ . '/..' . '/slim/psr7/src/UploadedFile.php',
        'Slim\\Psr7\\Uri' => __DIR__ . '/..' . '/slim/psr7/src/Uri.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'Symfony\\Component\\Cache\\Adapter\\AbstractAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/AbstractAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\AbstractTagAwareAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/AbstractTagAwareAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\AdapterInterface' => __DIR__ . '/..' . '/symfony/cache/Adapter/AdapterInterface.php',
        'Symfony\\Component\\Cache\\Adapter\\ApcuAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/ApcuAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\ArrayAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/ArrayAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\ChainAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/ChainAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\CouchbaseBucketAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/CouchbaseBucketAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\CouchbaseCollectionAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/CouchbaseCollectionAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\DoctrineAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/DoctrineAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\DoctrineDbalAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/DoctrineDbalAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\FilesystemAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/FilesystemAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\FilesystemTagAwareAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/FilesystemTagAwareAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\MemcachedAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/MemcachedAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\NullAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/NullAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\ParameterNormalizer' => __DIR__ . '/..' . '/symfony/cache/Adapter/ParameterNormalizer.php',
        'Symfony\\Component\\Cache\\Adapter\\PdoAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/PdoAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\PhpArrayAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/PhpArrayAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\PhpFilesAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/PhpFilesAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\ProxyAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/ProxyAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\Psr16Adapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/Psr16Adapter.php',
        'Symfony\\Component\\Cache\\Adapter\\RedisAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/RedisAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\RedisTagAwareAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/RedisTagAwareAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\TagAwareAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/TagAwareAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\TagAwareAdapterInterface' => __DIR__ . '/..' . '/symfony/cache/Adapter/TagAwareAdapterInterface.php',
        'Symfony\\Component\\Cache\\Adapter\\TraceableAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/TraceableAdapter.php',
        'Symfony\\Component\\Cache\\Adapter\\TraceableTagAwareAdapter' => __DIR__ . '/..' . '/symfony/cache/Adapter/TraceableTagAwareAdapter.php',
        'Symfony\\Component\\Cache\\CacheItem' => __DIR__ . '/..' . '/symfony/cache/CacheItem.php',
        'Symfony\\Component\\Cache\\DataCollector\\CacheDataCollector' => __DIR__ . '/..' . '/symfony/cache/DataCollector/CacheDataCollector.php',
        'Symfony\\Component\\Cache\\DependencyInjection\\CacheCollectorPass' => __DIR__ . '/..' . '/symfony/cache/DependencyInjection/CacheCollectorPass.php',
        'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolClearerPass' => __DIR__ . '/..' . '/symfony/cache/DependencyInjection/CachePoolClearerPass.php',
        'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolPass' => __DIR__ . '/..' . '/symfony/cache/DependencyInjection/CachePoolPass.php',
        'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolPrunerPass' => __DIR__ . '/..' . '/symfony/cache/DependencyInjection/CachePoolPrunerPass.php',
        'Symfony\\Component\\Cache\\DoctrineProvider' => __DIR__ . '/..' . '/symfony/cache/DoctrineProvider.php',
        'Symfony\\Component\\Cache\\Exception\\CacheException' => __DIR__ . '/..' . '/symfony/cache/Exception/CacheException.php',
        'Symfony\\Component\\Cache\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/symfony/cache/Exception/InvalidArgumentException.php',
        'Symfony\\Component\\Cache\\Exception\\LogicException' => __DIR__ . '/..' . '/symfony/cache/Exception/LogicException.php',
        'Symfony\\Component\\Cache\\LockRegistry' => __DIR__ . '/..' . '/symfony/cache/LockRegistry.php',
        'Symfony\\Component\\Cache\\Marshaller\\DefaultMarshaller' => __DIR__ . '/..' . '/symfony/cache/Marshaller/DefaultMarshaller.php',
        'Symfony\\Component\\Cache\\Marshaller\\DeflateMarshaller' => __DIR__ . '/..' . '/symfony/cache/Marshaller/DeflateMarshaller.php',
        'Symfony\\Component\\Cache\\Marshaller\\MarshallerInterface' => __DIR__ . '/..' . '/symfony/cache/Marshaller/MarshallerInterface.php',
        'Symfony\\Component\\Cache\\Marshaller\\SodiumMarshaller' => __DIR__ . '/..' . '/symfony/cache/Marshaller/SodiumMarshaller.php',
        'Symfony\\Component\\Cache\\Marshaller\\TagAwareMarshaller' => __DIR__ . '/..' . '/symfony/cache/Marshaller/TagAwareMarshaller.php',
        'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationDispatcher' => __DIR__ . '/..' . '/symfony/cache/Messenger/EarlyExpirationDispatcher.php',
        'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationHandler' => __DIR__ . '/..' . '/symfony/cache/Messenger/EarlyExpirationHandler.php',
        'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationMessage' => __DIR__ . '/..' . '/symfony/cache/Messenger/EarlyExpirationMessage.php',
        'Symfony\\Component\\Cache\\PruneableInterface' => __DIR__ . '/..' . '/symfony/cache/PruneableInterface.php',
        'Symfony\\Component\\Cache\\Psr16Cache' => __DIR__ . '/..' . '/symfony/cache/Psr16Cache.php',
        'Symfony\\Component\\Cache\\ResettableInterface' => __DIR__ . '/..' . '/symfony/cache/ResettableInterface.php',
        'Symfony\\Component\\Cache\\Traits\\AbstractAdapterTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/AbstractAdapterTrait.php',
        'Symfony\\Component\\Cache\\Traits\\ContractsTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/ContractsTrait.php',
        'Symfony\\Component\\Cache\\Traits\\FilesystemCommonTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/FilesystemCommonTrait.php',
        'Symfony\\Component\\Cache\\Traits\\FilesystemTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/FilesystemTrait.php',
        'Symfony\\Component\\Cache\\Traits\\ProxyTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/ProxyTrait.php',
        'Symfony\\Component\\Cache\\Traits\\RedisClusterNodeProxy' => __DIR__ . '/..' . '/symfony/cache/Traits/RedisClusterNodeProxy.php',
        'Symfony\\Component\\Cache\\Traits\\RedisClusterProxy' => __DIR__ . '/..' . '/symfony/cache/Traits/RedisClusterProxy.php',
        'Symfony\\Component\\Cache\\Traits\\RedisProxy' => __DIR__ . '/..' . '/symfony/cache/Traits/RedisProxy.php',
        'Symfony\\Component\\Cache\\Traits\\RedisTrait' => __DIR__ . '/..' . '/symfony/cache/Traits/RedisTrait.php',
        'Symfony\\Component\\Config\\Builder\\ClassBuilder' => __DIR__ . '/..' . '/symfony/config/Builder/ClassBuilder.php',
        'Symfony\\Component\\Config\\Builder\\ConfigBuilderGenerator' => __DIR__ . '/..' . '/symfony/config/Builder/ConfigBuilderGenerator.php',
        'Symfony\\Component\\Config\\Builder\\ConfigBuilderGeneratorInterface' => __DIR__ . '/..' . '/symfony/config/Builder/ConfigBuilderGeneratorInterface.php',
        'Symfony\\Component\\Config\\Builder\\ConfigBuilderInterface' => __DIR__ . '/..' . '/symfony/config/Builder/ConfigBuilderInterface.php',
        'Symfony\\Component\\Config\\Builder\\Method' => __DIR__ . '/..' . '/symfony/config/Builder/Method.php',
        'Symfony\\Component\\Config\\Builder\\Property' => __DIR__ . '/..' . '/symfony/config/Builder/Property.php',
        'Symfony\\Component\\Config\\ConfigCache' => __DIR__ . '/..' . '/symfony/config/ConfigCache.php',
        'Symfony\\Component\\Config\\ConfigCacheFactory' => __DIR__ . '/..' . '/symfony/config/ConfigCacheFactory.php',
        'Symfony\\Component\\Config\\ConfigCacheFactoryInterface' => __DIR__ . '/..' . '/symfony/config/ConfigCacheFactoryInterface.php',
        'Symfony\\Component\\Config\\ConfigCacheInterface' => __DIR__ . '/..' . '/symfony/config/ConfigCacheInterface.php',
        'Symfony\\Component\\Config\\Definition\\ArrayNode' => __DIR__ . '/..' . '/symfony/config/Definition/ArrayNode.php',
        'Symfony\\Component\\Config\\Definition\\BaseNode' => __DIR__ . '/..' . '/symfony/config/Definition/BaseNode.php',
        'Symfony\\Component\\Config\\Definition\\BooleanNode' => __DIR__ . '/..' . '/symfony/config/Definition/BooleanNode.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\ArrayNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/ArrayNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\BooleanNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/BooleanNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\BuilderAwareInterface' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/BuilderAwareInterface.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\EnumNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/EnumNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\ExprBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/ExprBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\FloatNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/FloatNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\IntegerNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/IntegerNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\MergeBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/MergeBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\NodeBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/NodeBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\NodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/NodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\NodeParentInterface' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/NodeParentInterface.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\NormalizationBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/NormalizationBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\NumericNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/NumericNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\ParentNodeDefinitionInterface' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/ParentNodeDefinitionInterface.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\ScalarNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/ScalarNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\TreeBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/TreeBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\ValidationBuilder' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/ValidationBuilder.php',
        'Symfony\\Component\\Config\\Definition\\Builder\\VariableNodeDefinition' => __DIR__ . '/..' . '/symfony/config/Definition/Builder/VariableNodeDefinition.php',
        'Symfony\\Component\\Config\\Definition\\ConfigurationInterface' => __DIR__ . '/..' . '/symfony/config/Definition/ConfigurationInterface.php',
        'Symfony\\Component\\Config\\Definition\\Dumper\\XmlReferenceDumper' => __DIR__ . '/..' . '/symfony/config/Definition/Dumper/XmlReferenceDumper.php',
        'Symfony\\Component\\Config\\Definition\\Dumper\\YamlReferenceDumper' => __DIR__ . '/..' . '/symfony/config/Definition/Dumper/YamlReferenceDumper.php',
        'Symfony\\Component\\Config\\Definition\\EnumNode' => __DIR__ . '/..' . '/symfony/config/Definition/EnumNode.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\DuplicateKeyException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/DuplicateKeyException.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\Exception' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/Exception.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\ForbiddenOverwriteException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/ForbiddenOverwriteException.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\InvalidConfigurationException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/InvalidConfigurationException.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\InvalidDefinitionException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/InvalidDefinitionException.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\InvalidTypeException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/InvalidTypeException.php',
        'Symfony\\Component\\Config\\Definition\\Exception\\UnsetKeyException' => __DIR__ . '/..' . '/symfony/config/Definition/Exception/UnsetKeyException.php',
        'Symfony\\Component\\Config\\Definition\\FloatNode' => __DIR__ . '/..' . '/symfony/config/Definition/FloatNode.php',
        'Symfony\\Component\\Config\\Definition\\IntegerNode' => __DIR__ . '/..' . '/symfony/config/Definition/IntegerNode.php',
        'Symfony\\Component\\Config\\Definition\\NodeInterface' => __DIR__ . '/..' . '/symfony/config/Definition/NodeInterface.php',
        'Symfony\\Component\\Config\\Definition\\NumericNode' => __DIR__ . '/..' . '/symfony/config/Definition/NumericNode.php',
        'Symfony\\Component\\Config\\Definition\\Processor' => __DIR__ . '/..' . '/symfony/config/Definition/Processor.php',
        'Symfony\\Component\\Config\\Definition\\PrototypeNodeInterface' => __DIR__ . '/..' . '/symfony/config/Definition/PrototypeNodeInterface.php',
        'Symfony\\Component\\Config\\Definition\\PrototypedArrayNode' => __DIR__ . '/..' . '/symfony/config/Definition/PrototypedArrayNode.php',
        'Symfony\\Component\\Config\\Definition\\ScalarNode' => __DIR__ . '/..' . '/symfony/config/Definition/ScalarNode.php',
        'Symfony\\Component\\Config\\Definition\\VariableNode' => __DIR__ . '/..' . '/symfony/config/Definition/VariableNode.php',
        'Symfony\\Component\\Config\\Exception\\FileLoaderImportCircularReferenceException' => __DIR__ . '/..' . '/symfony/config/Exception/FileLoaderImportCircularReferenceException.php',
        'Symfony\\Component\\Config\\Exception\\FileLocatorFileNotFoundException' => __DIR__ . '/..' . '/symfony/config/Exception/FileLocatorFileNotFoundException.php',
        'Symfony\\Component\\Config\\Exception\\LoaderLoadException' => __DIR__ . '/..' . '/symfony/config/Exception/LoaderLoadException.php',
        'Symfony\\Component\\Config\\FileLocator' => __DIR__ . '/..' . '/symfony/config/FileLocator.php',
        'Symfony\\Component\\Config\\FileLocatorInterface' => __DIR__ . '/..' . '/symfony/config/FileLocatorInterface.php',
        'Symfony\\Component\\Config\\Loader\\DelegatingLoader' => __DIR__ . '/..' . '/symfony/config/Loader/DelegatingLoader.php',
        'Symfony\\Component\\Config\\Loader\\FileLoader' => __DIR__ . '/..' . '/symfony/config/Loader/FileLoader.php',
        'Symfony\\Component\\Config\\Loader\\GlobFileLoader' => __DIR__ . '/..' . '/symfony/config/Loader/GlobFileLoader.php',
        'Symfony\\Component\\Config\\Loader\\Loader' => __DIR__ . '/..' . '/symfony/config/Loader/Loader.php',
        'Symfony\\Component\\Config\\Loader\\LoaderInterface' => __DIR__ . '/..' . '/symfony/config/Loader/LoaderInterface.php',
        'Symfony\\Component\\Config\\Loader\\LoaderResolver' => __DIR__ . '/..' . '/symfony/config/Loader/LoaderResolver.php',
        'Symfony\\Component\\Config\\Loader\\LoaderResolverInterface' => __DIR__ . '/..' . '/symfony/config/Loader/LoaderResolverInterface.php',
        'Symfony\\Component\\Config\\Loader\\ParamConfigurator' => __DIR__ . '/..' . '/symfony/config/Loader/ParamConfigurator.php',
        'Symfony\\Component\\Config\\ResourceCheckerConfigCache' => __DIR__ . '/..' . '/symfony/config/ResourceCheckerConfigCache.php',
        'Symfony\\Component\\Config\\ResourceCheckerConfigCacheFactory' => __DIR__ . '/..' . '/symfony/config/ResourceCheckerConfigCacheFactory.php',
        'Symfony\\Component\\Config\\ResourceCheckerInterface' => __DIR__ . '/..' . '/symfony/config/ResourceCheckerInterface.php',
        'Symfony\\Component\\Config\\Resource\\ClassExistenceResource' => __DIR__ . '/..' . '/symfony/config/Resource/ClassExistenceResource.php',
        'Symfony\\Component\\Config\\Resource\\ComposerResource' => __DIR__ . '/..' . '/symfony/config/Resource/ComposerResource.php',
        'Symfony\\Component\\Config\\Resource\\DirectoryResource' => __DIR__ . '/..' . '/symfony/config/Resource/DirectoryResource.php',
        'Symfony\\Component\\Config\\Resource\\FileExistenceResource' => __DIR__ . '/..' . '/symfony/config/Resource/FileExistenceResource.php',
        'Symfony\\Component\\Config\\Resource\\FileResource' => __DIR__ . '/..' . '/symfony/config/Resource/FileResource.php',
        'Symfony\\Component\\Config\\Resource\\GlobResource' => __DIR__ . '/..' . '/symfony/config/Resource/GlobResource.php',
        'Symfony\\Component\\Config\\Resource\\ReflectionClassResource' => __DIR__ . '/..' . '/symfony/config/Resource/ReflectionClassResource.php',
        'Symfony\\Component\\Config\\Resource\\ResourceInterface' => __DIR__ . '/..' . '/symfony/config/Resource/ResourceInterface.php',
        'Symfony\\Component\\Config\\Resource\\SelfCheckingResourceChecker' => __DIR__ . '/..' . '/symfony/config/Resource/SelfCheckingResourceChecker.php',
        'Symfony\\Component\\Config\\Resource\\SelfCheckingResourceInterface' => __DIR__ . '/..' . '/symfony/config/Resource/SelfCheckingResourceInterface.php',
        'Symfony\\Component\\Config\\Util\\Exception\\InvalidXmlException' => __DIR__ . '/..' . '/symfony/config/Util/Exception/InvalidXmlException.php',
        'Symfony\\Component\\Config\\Util\\Exception\\XmlParsingException' => __DIR__ . '/..' . '/symfony/config/Util/Exception/XmlParsingException.php',
        'Symfony\\Component\\Config\\Util\\XmlUtils' => __DIR__ . '/..' . '/symfony/config/Util/XmlUtils.php',
        'Symfony\\Component\\DependencyInjection\\Alias' => __DIR__ . '/..' . '/symfony/dependency-injection/Alias.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\AbstractArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/AbstractArgument.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\ArgumentInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/ArgumentInterface.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\BoundArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/BoundArgument.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\IteratorArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/IteratorArgument.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\ReferenceSetArgumentTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/ReferenceSetArgumentTrait.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\RewindableGenerator' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/RewindableGenerator.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\ServiceClosureArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/ServiceClosureArgument.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\ServiceLocator' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/ServiceLocator.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\ServiceLocatorArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/ServiceLocatorArgument.php',
        'Symfony\\Component\\DependencyInjection\\Argument\\TaggedIteratorArgument' => __DIR__ . '/..' . '/symfony/dependency-injection/Argument/TaggedIteratorArgument.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\AsTaggedItem' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/AsTaggedItem.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\Autoconfigure' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/Autoconfigure.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\AutoconfigureTag' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/AutoconfigureTag.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\TaggedIterator' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/TaggedIterator.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\TaggedLocator' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/TaggedLocator.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\Target' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/Target.php',
        'Symfony\\Component\\DependencyInjection\\Attribute\\When' => __DIR__ . '/..' . '/symfony/dependency-injection/Attribute/When.php',
        'Symfony\\Component\\DependencyInjection\\ChildDefinition' => __DIR__ . '/..' . '/symfony/dependency-injection/ChildDefinition.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AbstractRecursivePass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AbstractRecursivePass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AliasDeprecatedPublicServicesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AliasDeprecatedPublicServicesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AnalyzeServiceReferencesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AnalyzeServiceReferencesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AttributeAutoconfigurationPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AttributeAutoconfigurationPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AutoAliasServicePass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AutoAliasServicePass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AutowirePass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AutowirePass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AutowireRequiredMethodsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AutowireRequiredMethodsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\AutowireRequiredPropertiesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/AutowireRequiredPropertiesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckArgumentsValidityPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckArgumentsValidityPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckCircularReferencesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckCircularReferencesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckDefinitionValidityPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckDefinitionValidityPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckExceptionOnInvalidReferenceBehaviorPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckExceptionOnInvalidReferenceBehaviorPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckReferenceValidityPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckReferenceValidityPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CheckTypeDeclarationsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CheckTypeDeclarationsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\Compiler' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/Compiler.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\CompilerPassInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/CompilerPassInterface.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\DecoratorServicePass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/DecoratorServicePass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\DefinitionErrorExceptionPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/DefinitionErrorExceptionPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ExtensionCompilerPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ExtensionCompilerPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\InlineServiceDefinitionsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/InlineServiceDefinitionsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\MergeExtensionConfigurationPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/MergeExtensionConfigurationPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\PassConfig' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/PassConfig.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\PriorityTaggedServiceTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/PriorityTaggedServiceTrait.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterAutoconfigureAttributesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RegisterAutoconfigureAttributesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterEnvVarProcessorsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RegisterEnvVarProcessorsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterReverseContainerPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RegisterReverseContainerPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterServiceSubscribersPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RegisterServiceSubscribersPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RemoveAbstractDefinitionsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RemoveAbstractDefinitionsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RemovePrivateAliasesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RemovePrivateAliasesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\RemoveUnusedDefinitionsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/RemoveUnusedDefinitionsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ReplaceAliasByActualDefinitionPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ReplaceAliasByActualDefinitionPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveBindingsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveBindingsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveChildDefinitionsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveChildDefinitionsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveClassPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveClassPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveDecoratorStackPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveDecoratorStackPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveEnvPlaceholdersPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveEnvPlaceholdersPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveFactoryClassPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveFactoryClassPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveHotPathPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveHotPathPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveInstanceofConditionalsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveInstanceofConditionalsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveInvalidReferencesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveInvalidReferencesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveNamedArgumentsPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveNamedArgumentsPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveNoPreloadPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveNoPreloadPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveParameterPlaceHoldersPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveParameterPlaceHoldersPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolvePrivatesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolvePrivatesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveReferencesToAliasesPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveReferencesToAliasesPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveServiceSubscribersPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveServiceSubscribersPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveTaggedIteratorArgumentPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ResolveTaggedIteratorArgumentPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceLocatorTagPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ServiceLocatorTagPass.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraph' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ServiceReferenceGraph.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraphEdge' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ServiceReferenceGraphEdge.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraphNode' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ServiceReferenceGraphNode.php',
        'Symfony\\Component\\DependencyInjection\\Compiler\\ValidateEnvPlaceholdersPass' => __DIR__ . '/..' . '/symfony/dependency-injection/Compiler/ValidateEnvPlaceholdersPass.php',
        'Symfony\\Component\\DependencyInjection\\Config\\ContainerParametersResource' => __DIR__ . '/..' . '/symfony/dependency-injection/Config/ContainerParametersResource.php',
        'Symfony\\Component\\DependencyInjection\\Config\\ContainerParametersResourceChecker' => __DIR__ . '/..' . '/symfony/dependency-injection/Config/ContainerParametersResourceChecker.php',
        'Symfony\\Component\\DependencyInjection\\Container' => __DIR__ . '/..' . '/symfony/dependency-injection/Container.php',
        'Symfony\\Component\\DependencyInjection\\ContainerAwareInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/ContainerAwareInterface.php',
        'Symfony\\Component\\DependencyInjection\\ContainerAwareTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/ContainerAwareTrait.php',
        'Symfony\\Component\\DependencyInjection\\ContainerBuilder' => __DIR__ . '/..' . '/symfony/dependency-injection/ContainerBuilder.php',
        'Symfony\\Component\\DependencyInjection\\ContainerInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/ContainerInterface.php',
        'Symfony\\Component\\DependencyInjection\\Definition' => __DIR__ . '/..' . '/symfony/dependency-injection/Definition.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\Dumper' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/Dumper.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\DumperInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/DumperInterface.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\GraphvizDumper' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/GraphvizDumper.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\PhpDumper' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/PhpDumper.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\Preloader' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/Preloader.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\XmlDumper' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/XmlDumper.php',
        'Symfony\\Component\\DependencyInjection\\Dumper\\YamlDumper' => __DIR__ . '/..' . '/symfony/dependency-injection/Dumper/YamlDumper.php',
        'Symfony\\Component\\DependencyInjection\\EnvVarLoaderInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/EnvVarLoaderInterface.php',
        'Symfony\\Component\\DependencyInjection\\EnvVarProcessor' => __DIR__ . '/..' . '/symfony/dependency-injection/EnvVarProcessor.php',
        'Symfony\\Component\\DependencyInjection\\EnvVarProcessorInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/EnvVarProcessorInterface.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\AutowiringFailedException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/AutowiringFailedException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\BadMethodCallException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/BadMethodCallException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\EnvNotFoundException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/EnvNotFoundException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\EnvParameterException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/EnvParameterException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/ExceptionInterface.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/InvalidArgumentException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\InvalidParameterTypeException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/InvalidParameterTypeException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\LogicException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/LogicException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\OutOfBoundsException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/OutOfBoundsException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\ParameterCircularReferenceException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/ParameterCircularReferenceException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\ParameterNotFoundException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/ParameterNotFoundException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\RuntimeException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/RuntimeException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\ServiceCircularReferenceException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/ServiceCircularReferenceException.php',
        'Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException' => __DIR__ . '/..' . '/symfony/dependency-injection/Exception/ServiceNotFoundException.php',
        'Symfony\\Component\\DependencyInjection\\ExpressionLanguage' => __DIR__ . '/..' . '/symfony/dependency-injection/ExpressionLanguage.php',
        'Symfony\\Component\\DependencyInjection\\ExpressionLanguageProvider' => __DIR__ . '/..' . '/symfony/dependency-injection/ExpressionLanguageProvider.php',
        'Symfony\\Component\\DependencyInjection\\Extension\\ConfigurationExtensionInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Extension/ConfigurationExtensionInterface.php',
        'Symfony\\Component\\DependencyInjection\\Extension\\Extension' => __DIR__ . '/..' . '/symfony/dependency-injection/Extension/Extension.php',
        'Symfony\\Component\\DependencyInjection\\Extension\\ExtensionInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Extension/ExtensionInterface.php',
        'Symfony\\Component\\DependencyInjection\\Extension\\PrependExtensionInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/Extension/PrependExtensionInterface.php',
        'Symfony\\Component\\DependencyInjection\\LazyProxy\\Instantiator\\InstantiatorInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/LazyProxy/Instantiator/InstantiatorInterface.php',
        'Symfony\\Component\\DependencyInjection\\LazyProxy\\Instantiator\\RealServiceInstantiator' => __DIR__ . '/..' . '/symfony/dependency-injection/LazyProxy/Instantiator/RealServiceInstantiator.php',
        'Symfony\\Component\\DependencyInjection\\LazyProxy\\PhpDumper\\DumperInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/LazyProxy/PhpDumper/DumperInterface.php',
        'Symfony\\Component\\DependencyInjection\\LazyProxy\\PhpDumper\\NullDumper' => __DIR__ . '/..' . '/symfony/dependency-injection/LazyProxy/PhpDumper/NullDumper.php',
        'Symfony\\Component\\DependencyInjection\\LazyProxy\\ProxyHelper' => __DIR__ . '/..' . '/symfony/dependency-injection/LazyProxy/ProxyHelper.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\ClosureLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/ClosureLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AbstractConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/AbstractConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AbstractServiceConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/AbstractServiceConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AliasConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/AliasConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ClosureReferenceConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ClosureReferenceConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ContainerConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ContainerConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\DefaultsConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/DefaultsConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\EnvConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/EnvConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\InlineServiceConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/InlineServiceConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\InstanceofConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/InstanceofConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ParametersConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ParametersConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\PrototypeConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/PrototypeConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ReferenceConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ReferenceConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ServiceConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ServiceConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ServicesConfigurator' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/ServicesConfigurator.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AbstractTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/AbstractTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ArgumentTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/ArgumentTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AutoconfigureTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/AutoconfigureTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AutowireTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/AutowireTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\BindTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/BindTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\CallTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/CallTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ClassTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/ClassTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ConfiguratorTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/ConfiguratorTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\DecorateTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/DecorateTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\DeprecateTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/DeprecateTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\FactoryTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/FactoryTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\FileTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/FileTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\LazyTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/LazyTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ParentTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/ParentTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\PropertyTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/PropertyTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\PublicTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/PublicTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ShareTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/ShareTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\SyntheticTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/SyntheticTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\TagTrait' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/Configurator/Traits/TagTrait.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\DirectoryLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/DirectoryLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\FileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/FileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\GlobFileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/GlobFileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\IniFileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/IniFileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\PhpFileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/PhpFileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\XmlFileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/XmlFileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Loader\\YamlFileLoader' => __DIR__ . '/..' . '/symfony/dependency-injection/Loader/YamlFileLoader.php',
        'Symfony\\Component\\DependencyInjection\\Parameter' => __DIR__ . '/..' . '/symfony/dependency-injection/Parameter.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBag' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/ContainerBag.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBagInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/ContainerBagInterface.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\EnvPlaceholderParameterBag' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/EnvPlaceholderParameterBag.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\FrozenParameterBag' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/FrozenParameterBag.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBag' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/ParameterBag.php',
        'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/ParameterBag/ParameterBagInterface.php',
        'Symfony\\Component\\DependencyInjection\\Reference' => __DIR__ . '/..' . '/symfony/dependency-injection/Reference.php',
        'Symfony\\Component\\DependencyInjection\\ReverseContainer' => __DIR__ . '/..' . '/symfony/dependency-injection/ReverseContainer.php',
        'Symfony\\Component\\DependencyInjection\\ServiceLocator' => __DIR__ . '/..' . '/symfony/dependency-injection/ServiceLocator.php',
        'Symfony\\Component\\DependencyInjection\\TaggedContainerInterface' => __DIR__ . '/..' . '/symfony/dependency-injection/TaggedContainerInterface.php',
        'Symfony\\Component\\DependencyInjection\\TypedReference' => __DIR__ . '/..' . '/symfony/dependency-injection/TypedReference.php',
        'Symfony\\Component\\DependencyInjection\\Variable' => __DIR__ . '/..' . '/symfony/dependency-injection/Variable.php',
        'Symfony\\Component\\ExpressionLanguage\\Compiler' => __DIR__ . '/..' . '/symfony/expression-language/Compiler.php',
        'Symfony\\Component\\ExpressionLanguage\\Expression' => __DIR__ . '/..' . '/symfony/expression-language/Expression.php',
        'Symfony\\Component\\ExpressionLanguage\\ExpressionFunction' => __DIR__ . '/..' . '/symfony/expression-language/ExpressionFunction.php',
        'Symfony\\Component\\ExpressionLanguage\\ExpressionFunctionProviderInterface' => __DIR__ . '/..' . '/symfony/expression-language/ExpressionFunctionProviderInterface.php',
        'Symfony\\Component\\ExpressionLanguage\\ExpressionLanguage' => __DIR__ . '/..' . '/symfony/expression-language/ExpressionLanguage.php',
        'Symfony\\Component\\ExpressionLanguage\\Lexer' => __DIR__ . '/..' . '/symfony/expression-language/Lexer.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\ArgumentsNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/ArgumentsNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\ArrayNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/ArrayNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\BinaryNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/BinaryNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\ConditionalNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/ConditionalNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\ConstantNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/ConstantNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\FunctionNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/FunctionNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\GetAttrNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/GetAttrNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\NameNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/NameNode.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\Node' => __DIR__ . '/..' . '/symfony/expression-language/Node/Node.php',
        'Symfony\\Component\\ExpressionLanguage\\Node\\UnaryNode' => __DIR__ . '/..' . '/symfony/expression-language/Node/UnaryNode.php',
        'Symfony\\Component\\ExpressionLanguage\\ParsedExpression' => __DIR__ . '/..' . '/symfony/expression-language/ParsedExpression.php',
        'Symfony\\Component\\ExpressionLanguage\\Parser' => __DIR__ . '/..' . '/symfony/expression-language/Parser.php',
        'Symfony\\Component\\ExpressionLanguage\\SerializedParsedExpression' => __DIR__ . '/..' . '/symfony/expression-language/SerializedParsedExpression.php',
        'Symfony\\Component\\ExpressionLanguage\\SyntaxError' => __DIR__ . '/..' . '/symfony/expression-language/SyntaxError.php',
        'Symfony\\Component\\ExpressionLanguage\\Token' => __DIR__ . '/..' . '/symfony/expression-language/Token.php',
        'Symfony\\Component\\ExpressionLanguage\\TokenStream' => __DIR__ . '/..' . '/symfony/expression-language/TokenStream.php',
        'Symfony\\Component\\Filesystem\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/symfony/filesystem/Exception/ExceptionInterface.php',
        'Symfony\\Component\\Filesystem\\Exception\\FileNotFoundException' => __DIR__ . '/..' . '/symfony/filesystem/Exception/FileNotFoundException.php',
        'Symfony\\Component\\Filesystem\\Exception\\IOException' => __DIR__ . '/..' . '/symfony/filesystem/Exception/IOException.php',
        'Symfony\\Component\\Filesystem\\Exception\\IOExceptionInterface' => __DIR__ . '/..' . '/symfony/filesystem/Exception/IOExceptionInterface.php',
        'Symfony\\Component\\Filesystem\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/symfony/filesystem/Exception/InvalidArgumentException.php',
        'Symfony\\Component\\Filesystem\\Exception\\RuntimeException' => __DIR__ . '/..' . '/symfony/filesystem/Exception/RuntimeException.php',
        'Symfony\\Component\\Filesystem\\Filesystem' => __DIR__ . '/..' . '/symfony/filesystem/Filesystem.php',
        'Symfony\\Component\\Filesystem\\Path' => __DIR__ . '/..' . '/symfony/filesystem/Path.php',
        'Symfony\\Component\\VarExporter\\Exception\\ClassNotFoundException' => __DIR__ . '/..' . '/symfony/var-exporter/Exception/ClassNotFoundException.php',
        'Symfony\\Component\\VarExporter\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/symfony/var-exporter/Exception/ExceptionInterface.php',
        'Symfony\\Component\\VarExporter\\Exception\\NotInstantiableTypeException' => __DIR__ . '/..' . '/symfony/var-exporter/Exception/NotInstantiableTypeException.php',
        'Symfony\\Component\\VarExporter\\Instantiator' => __DIR__ . '/..' . '/symfony/var-exporter/Instantiator.php',
        'Symfony\\Component\\VarExporter\\Internal\\Exporter' => __DIR__ . '/..' . '/symfony/var-exporter/Internal/Exporter.php',
        'Symfony\\Component\\VarExporter\\Internal\\Hydrator' => __DIR__ . '/..' . '/symfony/var-exporter/Internal/Hydrator.php',
        'Symfony\\Component\\VarExporter\\Internal\\Reference' => __DIR__ . '/..' . '/symfony/var-exporter/Internal/Reference.php',
        'Symfony\\Component\\VarExporter\\Internal\\Registry' => __DIR__ . '/..' . '/symfony/var-exporter/Internal/Registry.php',
        'Symfony\\Component\\VarExporter\\Internal\\Values' => __DIR__ . '/..' . '/symfony/var-exporter/Internal/Values.php',
        'Symfony\\Component\\VarExporter\\VarExporter' => __DIR__ . '/..' . '/symfony/var-exporter/VarExporter.php',
        'Symfony\\Contracts\\Cache\\CacheInterface' => __DIR__ . '/..' . '/symfony/cache-contracts/CacheInterface.php',
        'Symfony\\Contracts\\Cache\\CacheTrait' => __DIR__ . '/..' . '/symfony/cache-contracts/CacheTrait.php',
        'Symfony\\Contracts\\Cache\\CallbackInterface' => __DIR__ . '/..' . '/symfony/cache-contracts/CallbackInterface.php',
        'Symfony\\Contracts\\Cache\\ItemInterface' => __DIR__ . '/..' . '/symfony/cache-contracts/ItemInterface.php',
        'Symfony\\Contracts\\Cache\\TagAwareCacheInterface' => __DIR__ . '/..' . '/symfony/cache-contracts/TagAwareCacheInterface.php',
        'Symfony\\Contracts\\Service\\Attribute\\Required' => __DIR__ . '/..' . '/symfony/service-contracts/Attribute/Required.php',
        'Symfony\\Contracts\\Service\\Attribute\\SubscribedService' => __DIR__ . '/..' . '/symfony/service-contracts/Attribute/SubscribedService.php',
        'Symfony\\Contracts\\Service\\ResetInterface' => __DIR__ . '/..' . '/symfony/service-contracts/ResetInterface.php',
        'Symfony\\Contracts\\Service\\ServiceLocatorTrait' => __DIR__ . '/..' . '/symfony/service-contracts/ServiceLocatorTrait.php',
        'Symfony\\Contracts\\Service\\ServiceProviderInterface' => __DIR__ . '/..' . '/symfony/service-contracts/ServiceProviderInterface.php',
        'Symfony\\Contracts\\Service\\ServiceSubscriberInterface' => __DIR__ . '/..' . '/symfony/service-contracts/ServiceSubscriberInterface.php',
        'Symfony\\Contracts\\Service\\ServiceSubscriberTrait' => __DIR__ . '/..' . '/symfony/service-contracts/ServiceSubscriberTrait.php',
        'Symfony\\Contracts\\Service\\Test\\ServiceLocatorTest' => __DIR__ . '/..' . '/symfony/service-contracts/Test/ServiceLocatorTest.php',
        'Symfony\\Polyfill\\Ctype\\Ctype' => __DIR__ . '/..' . '/symfony/polyfill-ctype/Ctype.php',
        'Symfony\\Polyfill\\Mbstring\\Mbstring' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/Mbstring.php',
        'Symfony\\Polyfill\\Php73\\Php73' => __DIR__ . '/..' . '/symfony/polyfill-php73/Php73.php',
        'Symfony\\Polyfill\\Php80\\Php80' => __DIR__ . '/..' . '/symfony/polyfill-php80/Php80.php',
        'Symfony\\Polyfill\\Php80\\PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/PhpToken.php',
        'Symfony\\Polyfill\\Php81\\Php81' => __DIR__ . '/..' . '/symfony/polyfill-php81/Php81.php',
        'TCPDF' => __DIR__ . '/..' . '/tecnickcom/tcpdf/tcpdf.php',
        'TCPDF2DBarcode' => __DIR__ . '/..' . '/tecnickcom/tcpdf/tcpdf_barcodes_2d.php',
        'TCPDFBarcode' => __DIR__ . '/..' . '/tecnickcom/tcpdf/tcpdf_barcodes_1d.php',
        'TCPDF_COLORS' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_colors.php',
        'TCPDF_FILTERS' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_filters.php',
        'TCPDF_FONTS' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_fonts.php',
        'TCPDF_FONT_DATA' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_font_data.php',
        'TCPDF_IMAGES' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_images.php',
        'TCPDF_IMPORT' => __DIR__ . '/..' . '/tecnickcom/tcpdf/tcpdf_import.php',
        'TCPDF_PARSER' => __DIR__ . '/..' . '/tecnickcom/tcpdf/tcpdf_parser.php',
        'TCPDF_STATIC' => __DIR__ . '/..' . '/tecnickcom/tcpdf/include/tcpdf_static.php',
        'Twig\\Cache\\CacheInterface' => __DIR__ . '/..' . '/twig/twig/src/Cache/CacheInterface.php',
        'Twig\\Cache\\FilesystemCache' => __DIR__ . '/..' . '/twig/twig/src/Cache/FilesystemCache.php',
        'Twig\\Cache\\NullCache' => __DIR__ . '/..' . '/twig/twig/src/Cache/NullCache.php',
        'Twig\\Compiler' => __DIR__ . '/..' . '/twig/twig/src/Compiler.php',
        'Twig\\Environment' => __DIR__ . '/..' . '/twig/twig/src/Environment.php',
        'Twig\\Error\\Error' => __DIR__ . '/..' . '/twig/twig/src/Error/Error.php',
        'Twig\\Error\\LoaderError' => __DIR__ . '/..' . '/twig/twig/src/Error/LoaderError.php',
        'Twig\\Error\\RuntimeError' => __DIR__ . '/..' . '/twig/twig/src/Error/RuntimeError.php',
        'Twig\\Error\\SyntaxError' => __DIR__ . '/..' . '/twig/twig/src/Error/SyntaxError.php',
        'Twig\\ExpressionParser' => __DIR__ . '/..' . '/twig/twig/src/ExpressionParser.php',
        'Twig\\ExtensionSet' => __DIR__ . '/..' . '/twig/twig/src/ExtensionSet.php',
        'Twig\\Extension\\AbstractExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/AbstractExtension.php',
        'Twig\\Extension\\CoreExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/CoreExtension.php',
        'Twig\\Extension\\DebugExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/DebugExtension.php',
        'Twig\\Extension\\EscaperExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/EscaperExtension.php',
        'Twig\\Extension\\ExtensionInterface' => __DIR__ . '/..' . '/twig/twig/src/Extension/ExtensionInterface.php',
        'Twig\\Extension\\GlobalsInterface' => __DIR__ . '/..' . '/twig/twig/src/Extension/GlobalsInterface.php',
        'Twig\\Extension\\OptimizerExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/OptimizerExtension.php',
        'Twig\\Extension\\ProfilerExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/ProfilerExtension.php',
        'Twig\\Extension\\RuntimeExtensionInterface' => __DIR__ . '/..' . '/twig/twig/src/Extension/RuntimeExtensionInterface.php',
        'Twig\\Extension\\SandboxExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/SandboxExtension.php',
        'Twig\\Extension\\StagingExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/StagingExtension.php',
        'Twig\\Extension\\StringLoaderExtension' => __DIR__ . '/..' . '/twig/twig/src/Extension/StringLoaderExtension.php',
        'Twig\\FileExtensionEscapingStrategy' => __DIR__ . '/..' . '/twig/twig/src/FileExtensionEscapingStrategy.php',
        'Twig\\Lexer' => __DIR__ . '/..' . '/twig/twig/src/Lexer.php',
        'Twig\\Loader\\ArrayLoader' => __DIR__ . '/..' . '/twig/twig/src/Loader/ArrayLoader.php',
        'Twig\\Loader\\ChainLoader' => __DIR__ . '/..' . '/twig/twig/src/Loader/ChainLoader.php',
        'Twig\\Loader\\FilesystemLoader' => __DIR__ . '/..' . '/twig/twig/src/Loader/FilesystemLoader.php',
        'Twig\\Loader\\LoaderInterface' => __DIR__ . '/..' . '/twig/twig/src/Loader/LoaderInterface.php',
        'Twig\\Markup' => __DIR__ . '/..' . '/twig/twig/src/Markup.php',
        'Twig\\NodeTraverser' => __DIR__ . '/..' . '/twig/twig/src/NodeTraverser.php',
        'Twig\\NodeVisitor\\AbstractNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/AbstractNodeVisitor.php',
        'Twig\\NodeVisitor\\EscaperNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/EscaperNodeVisitor.php',
        'Twig\\NodeVisitor\\MacroAutoImportNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/MacroAutoImportNodeVisitor.php',
        'Twig\\NodeVisitor\\NodeVisitorInterface' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/NodeVisitorInterface.php',
        'Twig\\NodeVisitor\\OptimizerNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/OptimizerNodeVisitor.php',
        'Twig\\NodeVisitor\\SafeAnalysisNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/SafeAnalysisNodeVisitor.php',
        'Twig\\NodeVisitor\\SandboxNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/NodeVisitor/SandboxNodeVisitor.php',
        'Twig\\Node\\AutoEscapeNode' => __DIR__ . '/..' . '/twig/twig/src/Node/AutoEscapeNode.php',
        'Twig\\Node\\BlockNode' => __DIR__ . '/..' . '/twig/twig/src/Node/BlockNode.php',
        'Twig\\Node\\BlockReferenceNode' => __DIR__ . '/..' . '/twig/twig/src/Node/BlockReferenceNode.php',
        'Twig\\Node\\BodyNode' => __DIR__ . '/..' . '/twig/twig/src/Node/BodyNode.php',
        'Twig\\Node\\CheckSecurityCallNode' => __DIR__ . '/..' . '/twig/twig/src/Node/CheckSecurityCallNode.php',
        'Twig\\Node\\CheckSecurityNode' => __DIR__ . '/..' . '/twig/twig/src/Node/CheckSecurityNode.php',
        'Twig\\Node\\CheckToStringNode' => __DIR__ . '/..' . '/twig/twig/src/Node/CheckToStringNode.php',
        'Twig\\Node\\DeprecatedNode' => __DIR__ . '/..' . '/twig/twig/src/Node/DeprecatedNode.php',
        'Twig\\Node\\DoNode' => __DIR__ . '/..' . '/twig/twig/src/Node/DoNode.php',
        'Twig\\Node\\EmbedNode' => __DIR__ . '/..' . '/twig/twig/src/Node/EmbedNode.php',
        'Twig\\Node\\Expression\\AbstractExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/AbstractExpression.php',
        'Twig\\Node\\Expression\\ArrayExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/ArrayExpression.php',
        'Twig\\Node\\Expression\\ArrowFunctionExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/ArrowFunctionExpression.php',
        'Twig\\Node\\Expression\\AssignNameExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/AssignNameExpression.php',
        'Twig\\Node\\Expression\\Binary\\AbstractBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/AbstractBinary.php',
        'Twig\\Node\\Expression\\Binary\\AddBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/AddBinary.php',
        'Twig\\Node\\Expression\\Binary\\AndBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/AndBinary.php',
        'Twig\\Node\\Expression\\Binary\\BitwiseAndBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/BitwiseAndBinary.php',
        'Twig\\Node\\Expression\\Binary\\BitwiseOrBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/BitwiseOrBinary.php',
        'Twig\\Node\\Expression\\Binary\\BitwiseXorBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/BitwiseXorBinary.php',
        'Twig\\Node\\Expression\\Binary\\ConcatBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/ConcatBinary.php',
        'Twig\\Node\\Expression\\Binary\\DivBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/DivBinary.php',
        'Twig\\Node\\Expression\\Binary\\EndsWithBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/EndsWithBinary.php',
        'Twig\\Node\\Expression\\Binary\\EqualBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/EqualBinary.php',
        'Twig\\Node\\Expression\\Binary\\FloorDivBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/FloorDivBinary.php',
        'Twig\\Node\\Expression\\Binary\\GreaterBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/GreaterBinary.php',
        'Twig\\Node\\Expression\\Binary\\GreaterEqualBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/GreaterEqualBinary.php',
        'Twig\\Node\\Expression\\Binary\\InBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/InBinary.php',
        'Twig\\Node\\Expression\\Binary\\LessBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/LessBinary.php',
        'Twig\\Node\\Expression\\Binary\\LessEqualBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/LessEqualBinary.php',
        'Twig\\Node\\Expression\\Binary\\MatchesBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/MatchesBinary.php',
        'Twig\\Node\\Expression\\Binary\\ModBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/ModBinary.php',
        'Twig\\Node\\Expression\\Binary\\MulBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/MulBinary.php',
        'Twig\\Node\\Expression\\Binary\\NotEqualBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/NotEqualBinary.php',
        'Twig\\Node\\Expression\\Binary\\NotInBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/NotInBinary.php',
        'Twig\\Node\\Expression\\Binary\\OrBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/OrBinary.php',
        'Twig\\Node\\Expression\\Binary\\PowerBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/PowerBinary.php',
        'Twig\\Node\\Expression\\Binary\\RangeBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/RangeBinary.php',
        'Twig\\Node\\Expression\\Binary\\SpaceshipBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/SpaceshipBinary.php',
        'Twig\\Node\\Expression\\Binary\\StartsWithBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/StartsWithBinary.php',
        'Twig\\Node\\Expression\\Binary\\SubBinary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Binary/SubBinary.php',
        'Twig\\Node\\Expression\\BlockReferenceExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/BlockReferenceExpression.php',
        'Twig\\Node\\Expression\\CallExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/CallExpression.php',
        'Twig\\Node\\Expression\\ConditionalExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/ConditionalExpression.php',
        'Twig\\Node\\Expression\\ConstantExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/ConstantExpression.php',
        'Twig\\Node\\Expression\\FilterExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/FilterExpression.php',
        'Twig\\Node\\Expression\\Filter\\DefaultFilter' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Filter/DefaultFilter.php',
        'Twig\\Node\\Expression\\FunctionExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/FunctionExpression.php',
        'Twig\\Node\\Expression\\GetAttrExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/GetAttrExpression.php',
        'Twig\\Node\\Expression\\InlinePrint' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/InlinePrint.php',
        'Twig\\Node\\Expression\\MethodCallExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/MethodCallExpression.php',
        'Twig\\Node\\Expression\\NameExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/NameExpression.php',
        'Twig\\Node\\Expression\\NullCoalesceExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/NullCoalesceExpression.php',
        'Twig\\Node\\Expression\\ParentExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/ParentExpression.php',
        'Twig\\Node\\Expression\\TempNameExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/TempNameExpression.php',
        'Twig\\Node\\Expression\\TestExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/TestExpression.php',
        'Twig\\Node\\Expression\\Test\\ConstantTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/ConstantTest.php',
        'Twig\\Node\\Expression\\Test\\DefinedTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/DefinedTest.php',
        'Twig\\Node\\Expression\\Test\\DivisiblebyTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/DivisiblebyTest.php',
        'Twig\\Node\\Expression\\Test\\EvenTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/EvenTest.php',
        'Twig\\Node\\Expression\\Test\\NullTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/NullTest.php',
        'Twig\\Node\\Expression\\Test\\OddTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/OddTest.php',
        'Twig\\Node\\Expression\\Test\\SameasTest' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Test/SameasTest.php',
        'Twig\\Node\\Expression\\Unary\\AbstractUnary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Unary/AbstractUnary.php',
        'Twig\\Node\\Expression\\Unary\\NegUnary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Unary/NegUnary.php',
        'Twig\\Node\\Expression\\Unary\\NotUnary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Unary/NotUnary.php',
        'Twig\\Node\\Expression\\Unary\\PosUnary' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/Unary/PosUnary.php',
        'Twig\\Node\\Expression\\VariadicExpression' => __DIR__ . '/..' . '/twig/twig/src/Node/Expression/VariadicExpression.php',
        'Twig\\Node\\FlushNode' => __DIR__ . '/..' . '/twig/twig/src/Node/FlushNode.php',
        'Twig\\Node\\ForLoopNode' => __DIR__ . '/..' . '/twig/twig/src/Node/ForLoopNode.php',
        'Twig\\Node\\ForNode' => __DIR__ . '/..' . '/twig/twig/src/Node/ForNode.php',
        'Twig\\Node\\IfNode' => __DIR__ . '/..' . '/twig/twig/src/Node/IfNode.php',
        'Twig\\Node\\ImportNode' => __DIR__ . '/..' . '/twig/twig/src/Node/ImportNode.php',
        'Twig\\Node\\IncludeNode' => __DIR__ . '/..' . '/twig/twig/src/Node/IncludeNode.php',
        'Twig\\Node\\MacroNode' => __DIR__ . '/..' . '/twig/twig/src/Node/MacroNode.php',
        'Twig\\Node\\ModuleNode' => __DIR__ . '/..' . '/twig/twig/src/Node/ModuleNode.php',
        'Twig\\Node\\Node' => __DIR__ . '/..' . '/twig/twig/src/Node/Node.php',
        'Twig\\Node\\NodeCaptureInterface' => __DIR__ . '/..' . '/twig/twig/src/Node/NodeCaptureInterface.php',
        'Twig\\Node\\NodeOutputInterface' => __DIR__ . '/..' . '/twig/twig/src/Node/NodeOutputInterface.php',
        'Twig\\Node\\PrintNode' => __DIR__ . '/..' . '/twig/twig/src/Node/PrintNode.php',
        'Twig\\Node\\SandboxNode' => __DIR__ . '/..' . '/twig/twig/src/Node/SandboxNode.php',
        'Twig\\Node\\SetNode' => __DIR__ . '/..' . '/twig/twig/src/Node/SetNode.php',
        'Twig\\Node\\TextNode' => __DIR__ . '/..' . '/twig/twig/src/Node/TextNode.php',
        'Twig\\Node\\WithNode' => __DIR__ . '/..' . '/twig/twig/src/Node/WithNode.php',
        'Twig\\Parser' => __DIR__ . '/..' . '/twig/twig/src/Parser.php',
        'Twig\\Profiler\\Dumper\\BaseDumper' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Dumper/BaseDumper.php',
        'Twig\\Profiler\\Dumper\\BlackfireDumper' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Dumper/BlackfireDumper.php',
        'Twig\\Profiler\\Dumper\\HtmlDumper' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Dumper/HtmlDumper.php',
        'Twig\\Profiler\\Dumper\\TextDumper' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Dumper/TextDumper.php',
        'Twig\\Profiler\\NodeVisitor\\ProfilerNodeVisitor' => __DIR__ . '/..' . '/twig/twig/src/Profiler/NodeVisitor/ProfilerNodeVisitor.php',
        'Twig\\Profiler\\Node\\EnterProfileNode' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Node/EnterProfileNode.php',
        'Twig\\Profiler\\Node\\LeaveProfileNode' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Node/LeaveProfileNode.php',
        'Twig\\Profiler\\Profile' => __DIR__ . '/..' . '/twig/twig/src/Profiler/Profile.php',
        'Twig\\RuntimeLoader\\ContainerRuntimeLoader' => __DIR__ . '/..' . '/twig/twig/src/RuntimeLoader/ContainerRuntimeLoader.php',
        'Twig\\RuntimeLoader\\FactoryRuntimeLoader' => __DIR__ . '/..' . '/twig/twig/src/RuntimeLoader/FactoryRuntimeLoader.php',
        'Twig\\RuntimeLoader\\RuntimeLoaderInterface' => __DIR__ . '/..' . '/twig/twig/src/RuntimeLoader/RuntimeLoaderInterface.php',
        'Twig\\Sandbox\\SecurityError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityError.php',
        'Twig\\Sandbox\\SecurityNotAllowedFilterError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityNotAllowedFilterError.php',
        'Twig\\Sandbox\\SecurityNotAllowedFunctionError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityNotAllowedFunctionError.php',
        'Twig\\Sandbox\\SecurityNotAllowedMethodError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityNotAllowedMethodError.php',
        'Twig\\Sandbox\\SecurityNotAllowedPropertyError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityNotAllowedPropertyError.php',
        'Twig\\Sandbox\\SecurityNotAllowedTagError' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityNotAllowedTagError.php',
        'Twig\\Sandbox\\SecurityPolicy' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityPolicy.php',
        'Twig\\Sandbox\\SecurityPolicyInterface' => __DIR__ . '/..' . '/twig/twig/src/Sandbox/SecurityPolicyInterface.php',
        'Twig\\Source' => __DIR__ . '/..' . '/twig/twig/src/Source.php',
        'Twig\\Template' => __DIR__ . '/..' . '/twig/twig/src/Template.php',
        'Twig\\TemplateWrapper' => __DIR__ . '/..' . '/twig/twig/src/TemplateWrapper.php',
        'Twig\\Test\\IntegrationTestCase' => __DIR__ . '/..' . '/twig/twig/src/Test/IntegrationTestCase.php',
        'Twig\\Test\\NodeTestCase' => __DIR__ . '/..' . '/twig/twig/src/Test/NodeTestCase.php',
        'Twig\\Token' => __DIR__ . '/..' . '/twig/twig/src/Token.php',
        'Twig\\TokenParser\\AbstractTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/AbstractTokenParser.php',
        'Twig\\TokenParser\\ApplyTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/ApplyTokenParser.php',
        'Twig\\TokenParser\\AutoEscapeTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/AutoEscapeTokenParser.php',
        'Twig\\TokenParser\\BlockTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/BlockTokenParser.php',
        'Twig\\TokenParser\\DeprecatedTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/DeprecatedTokenParser.php',
        'Twig\\TokenParser\\DoTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/DoTokenParser.php',
        'Twig\\TokenParser\\EmbedTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/EmbedTokenParser.php',
        'Twig\\TokenParser\\ExtendsTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/ExtendsTokenParser.php',
        'Twig\\TokenParser\\FlushTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/FlushTokenParser.php',
        'Twig\\TokenParser\\ForTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/ForTokenParser.php',
        'Twig\\TokenParser\\FromTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/FromTokenParser.php',
        'Twig\\TokenParser\\IfTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/IfTokenParser.php',
        'Twig\\TokenParser\\ImportTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/ImportTokenParser.php',
        'Twig\\TokenParser\\IncludeTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/IncludeTokenParser.php',
        'Twig\\TokenParser\\MacroTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/MacroTokenParser.php',
        'Twig\\TokenParser\\SandboxTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/SandboxTokenParser.php',
        'Twig\\TokenParser\\SetTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/SetTokenParser.php',
        'Twig\\TokenParser\\TokenParserInterface' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/TokenParserInterface.php',
        'Twig\\TokenParser\\UseTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/UseTokenParser.php',
        'Twig\\TokenParser\\WithTokenParser' => __DIR__ . '/..' . '/twig/twig/src/TokenParser/WithTokenParser.php',
        'Twig\\TokenStream' => __DIR__ . '/..' . '/twig/twig/src/TokenStream.php',
        'Twig\\TwigFilter' => __DIR__ . '/..' . '/twig/twig/src/TwigFilter.php',
        'Twig\\TwigFunction' => __DIR__ . '/..' . '/twig/twig/src/TwigFunction.php',
        'Twig\\TwigTest' => __DIR__ . '/..' . '/twig/twig/src/TwigTest.php',
        'Twig\\Util\\DeprecationCollector' => __DIR__ . '/..' . '/twig/twig/src/Util/DeprecationCollector.php',
        'Twig\\Util\\TemplateDirIterator' => __DIR__ . '/..' . '/twig/twig/src/Util/TemplateDirIterator.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
        'Webmozart\\Assert\\Assert' => __DIR__ . '/..' . '/webmozart/assert/src/Assert.php',
        'Webmozart\\Assert\\InvalidArgumentException' => __DIR__ . '/..' . '/webmozart/assert/src/InvalidArgumentException.php',
        'Webmozart\\Assert\\Mixin' => __DIR__ . '/..' . '/webmozart/assert/src/Mixin.php',
        'Williamdes\\MariaDBMySQLKBS\\KBDocumentation' => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src/KBDocumentation.php',
        'Williamdes\\MariaDBMySQLKBS\\KBEntry' => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src/KBEntry.php',
        'Williamdes\\MariaDBMySQLKBS\\KBException' => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src/KBException.php',
        'Williamdes\\MariaDBMySQLKBS\\Search' => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src/Search.php',
        'Williamdes\\MariaDBMySQLKBS\\SlimData' => __DIR__ . '/..' . '/williamdes/mariadb-mysql-kbs/src/SlimData.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit30dc56dbcd95b1f5db28729d0c4615c4::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit30dc56dbcd95b1f5db28729d0c4615c4::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit30dc56dbcd95b1f5db28729d0c4615c4::$classMap;

        }, null, ClassLoader::class);
    }
}
