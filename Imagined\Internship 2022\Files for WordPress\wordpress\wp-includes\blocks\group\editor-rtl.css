/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
/**
* Group: All Alignment Settings
*/
.wp-block-group .block-editor-block-list__insertion-point {
  right: 0;
  left: 0;
}

[data-type="core/group"].is-selected .block-list-appender {
  margin-right: 0;
  margin-left: 0;
}
[data-type="core/group"].is-selected .has-background .block-list-appender {
  margin-top: 18px;
  margin-bottom: 18px;
}

.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child {
  gap: inherit;
  pointer-events: none;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child,
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-default-block-appender__content,
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-inserter {
  display: inherit;
  width: 100%;
  flex-direction: inherit;
  flex: 1;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child::after {
  content: "";
  display: flex;
  border: 1px dashed currentColor;
  opacity: 0.4;
  border-radius: 2px;
  flex: 1;
  pointer-events: none;
  min-height: 48px;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-inserter {
  pointer-events: all;
}