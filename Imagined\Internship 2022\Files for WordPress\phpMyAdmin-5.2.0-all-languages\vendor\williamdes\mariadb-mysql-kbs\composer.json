{"name": "william<PERSON>/mariadb-mysql-kbs", "description": "An index of the MariaDB and MySQL Knowledge bases", "homepage": "https://github.com/williamdes/mariadb-mysql-kbs", "type": "library", "readme": "https://github.com/williamdes/mariadb-mysql-kbs#readme", "license": "MPL-2.0", "funding": [{"type": "tidelift", "url": "https://tidelift.com/funding/github/packagist/williamdes/mariadb-mysql-kbs"}, {"type": "github", "url": "https://github.com/sponsors/williamdes"}], "keywords": ["ma<PERSON>b", "mysql", "dataset", "kb", "knowledge-base", "json", "library", "mysql-knowledge-bases", "mariadb-knowledge-bases", "composer-package", "npm-package"], "support": {"email": "william<PERSON>@wdes.fr", "issues": "https://github.com/williamdes/mariadb-mysql-kbs/issues", "source": "https://github.com/williamdes/mariadb-mysql-kbs"}, "authors": [{"name": "<PERSON>", "email": "william<PERSON>@wdes.fr"}], "scripts": {"build": "./src/merge.php", "test": "./vendor/bin/phpunit", "phpstan": "./vendor/bin/phpstan", "phpcs": "./vendor/bin/phpcs --no-cache", "phpcbf": "./vendor/bin/phpcbf"}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9", "phpstan/phpstan": "^1.2", "wdes/coding-standard": "^3", "swaggest/json-schema": "^0.12.29"}, "autoload": {"psr-4": {"Williamdes\\MariaDBMySQLKBS\\": "src/"}}, "autoload-dev": {"psr-4": {"Williamdes\\MariaDBMySQLKBS\\Test\\": "test/"}}, "non-feature-branches": ["update/*"]}