@charset "UTF-8";

/*
Theme Name: Twenty Twenty-One
Theme URI: https://wordpress.org/themes/twentytwentyone/
Author: the WordPress team
Author URI: https://wordpress.org/
Description: Twenty Twenty-One is a blank canvas for your ideas and it makes the block editor your best brush. With new block patterns, which allow you to create a beautiful layout in a matter of seconds, this theme’s soft colors and eye-catching — yet timeless — design will let your work shine. Take it for a spin! See how Twenty Twenty-One elevates your portfolio, business website, or personal blog.
Requires at least: 5.3
Tested up to: 6.0
Requires PHP: 5.6
Version: 1.6
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: twentytwentyone
Tags: one-column, accessibility-ready, custom-colors, custom-menu, custom-logo, editor-style, featured-images, footer-widgets, block-patterns, rtl-language-support, sticky-post, threaded-comments, translation-ready

Twenty Twenty-One WordPress Theme, (C) 2020 WordPress.org
Twenty Twenty-One is distributed under the terms of the GNU GPL.
*/

/**
 * SETTINGS
 * File-header..........The file header for the themes style.css file.
 * Fonts................Any font files, if the project needs specific fonts.
 * Global...............Project-specific, globally available variables.
 *
 * TOOLS
 * Functions............Global functions.
 * Mixins...............Global mixins.
 *
 * GENERIC
 * Normalize.css........Normalise browser defaults.
 * Breakpoints..........Mixins and variables for responsive styles
 * Vertical-margins.....Vertical spacing for the main components.
 * Reset................Reset specific elements to make them easier to style in other contexts.
 * Clearings............Clearings for the main components.
 *
 * ELEMENTS
 * Blockquote...........Default blockquote.
 * Forms................Element-level form styling.
 * Headings.............H1–H6
 * Links................Default links.
 * Lists................Default lists.
 * Media................Images, Figure, Figcaption, Embed, iFrame, Objects, Video.
 *
 * BLOCKS
 * Audio................Specific styles for the audio block.
 * Button...............Specific styles for the button block.
 * Code.................Specific styles for the code block.
 * Columns..............Specific styles for the columns block.
 * Cover................Specific styles for the cover block.
 * File.................Specific styles for the file block.
 * Gallery..............Specific styles for the gallery block.
 * Group................Specific styles for the group block.
 * Heading..............Specific styles for the heading block.
 * Image................Specific styles for the image block.
 * Latest comments......Specific styles for the latest comments block.
 * Latest posts.........Specific styles for the latest posts block.
 * Legacy...............Specific styles for the legacy gallery.
 * List.................Specific styles for the list block.
 * Media text...........Specific styles for the media and text block.
 * Navigation...........Specific styles for the navigation block.
 * Paragraph............Specific styles for the paragraph block.
 * Pullquote............Specific styles for the pullquote block.
 * Quote................Specific styles for the quote block.
 * Search...............Specific styles for the search block.
 * Separator............Specific styles for the separator block.
 * Spacer...............Specific styles for the spacer block.
 * Table................Specific styles for the table block.
 * Verse................Specific styles for the verse block.
 * Video................Specific styles for the video block.
 * Utilities............Block alignments.
 *
 * COMPONENTS
 * Header...............Header styles.
 * Footer...............Footer styles.
 * Comments.............Comment styles.
 * Archives.............Archive styles.
 * 404..................404 styles.
 * Search...............Search styles.
 * Navigation...........Navigation styles.
 * Footer Navigation....Footer Navigation styles.
 * Pagination...........Pagination styles.
 * Single...............Single page and post styles.
 * Posts and pages......Misc, sticky post styles.
 * Entry................Entry, author biography.
 * Widget...............Widget styles.
 * Editor...............Editor styles.
 *
 * UTILITIES
 * A11y.................Screen reader text, prefers reduced motion etc.
 * Color Palette........Classes for the color palette colors.
 * Editor Font Sizes....Editor Font Sizes.
 * Measure..............The width of a line of text, in characters.
 */

/* Categories 01 to 03 are the basics. */

/* Variables */
:root {

	/* Font Family */

	/* Font Size */

	/* Line Height */

	/* Headings */

	/* Block: Latest posts */

	/* Colors */

	/* Body text color, site title, footer text color. */

	/* Headings */

	/* Mint, default body background */

	/* Used for borders (separators) */

	/* Spacing */

	/* Elevation */

	/* Forms */

	/* Cover block */

	/* Buttons */

	/* entry */

	/* Header */

	/* Main navigation */

	/* Pagination */

	/* Footer */

	/* Block: Pull quote */

	/* Block: Table */

	/* Widgets */

	/* Admin-bar height */
}

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
	line-height: 1.15;

	/* 1 */
	-webkit-text-size-adjust: 100%;

	/* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers.
 */
body {
	margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
	display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
	box-sizing: content-box;

	/* 1 */
	height: 0;

	/* 1 */
	overflow: visible;

	/* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
	font-family: monospace;

	/* 1 */
	font-size: 1em;

	/* 2 */
}

/* Text-level semantics
   ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */
a {
	background-color: transparent;
	text-decoration-thickness: 1px;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
	border-bottom: none;

	/* 1 */
	text-decoration: underline;

	/* 2 */
	text-decoration-style: dotted;

	/* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
	font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
	font-family: monospace;

	/* 1 */
	font-size: 1em;

	/* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small {
	font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */
img {
	border-style: none;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;

	/* 1 */
	font-size: 100%;

	/* 1 */
	line-height: 1.15;

	/* 1 */
	margin: 0;

	/* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {

	/* 1 */
	overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {

	/* 1 */
	text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type=button],
[type=reset],
[type=submit] {
	-webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
	border-style: none;
	padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
	outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
	padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
	box-sizing: border-box;

	/* 1 */
	color: inherit;

	/* 2 */
	display: table;

	/* 1 */
	max-width: 100%;

	/* 1 */
	padding: 0;

	/* 3 */
	white-space: normal;

	/* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
	vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
	overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type=checkbox],
[type=radio] {
	box-sizing: border-box;

	/* 1 */
	padding: 0;

	/* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
	height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
	-webkit-appearance: textfield;

	/* 1 */
	outline-offset: -2px;

	/* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
	-webkit-appearance: button;

	/* 1 */
	font: inherit;

	/* 2 */
}

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
	display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
	display: list-item;
}

/* Misc
   ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */
template {
	display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
	display: none;
}

/**
 * Responsive Styles
 */

/**
 * Required Variables
 */

/**
 * Root Media Query Variables
 */

/**
 * Extends
 */
.post-thumbnail {
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	.post-thumbnail {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	.post-thumbnail {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.entry-content .wp-audio-shortcode {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}
@media only screen and (min-width: 482px) {

	.entry-content .wp-audio-shortcode {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	.entry-content .wp-audio-shortcode {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.entry-content > *:not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}
@media only screen and (min-width: 482px) {

	.entry-content > *:not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	.entry-content > *:not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

*[class*=inner-container] > *:not(.entry-content):not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}
@media only screen and (min-width: 482px) {

	*[class*=inner-container] > *:not(.entry-content):not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	*[class*=inner-container] > *:not(.entry-content):not(.alignwide):not(.alignfull):not(.alignleft):not(.alignright):not(.wp-block-separator):not(.woocommerce) {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.default-max-width {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}
@media only screen and (min-width: 482px) {

	.default-max-width {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	.default-max-width {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.widget-area {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.widget-area {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.widget-area {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.pagination {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.pagination {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.pagination {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.comments-pagination {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.comments-pagination {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.comments-pagination {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.post-navigation {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.post-navigation {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.post-navigation {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.site-footer {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.site-footer {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.site-footer {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.site-header {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.site-header {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.site-header {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.alignwide {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.alignwide {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.alignwide {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.wide-max-width {
	max-width: calc(100vw - 30px);
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.wide-max-width {
		max-width: calc(100vw - 100px);
	}
}

@media only screen and (min-width: 822px) {

	.wide-max-width {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.alignfull {
	max-width: 100%;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}

.wp-block-group .wp-block-group__inner-container > *.alignfull {
	max-width: 100%;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}

.full-max-width {
	max-width: 100%;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}

@media only screen and (min-width: 482px) {

	.alignfull,
	.full-max-width {
		max-width: 100%;
		width: auto;
		margin-left: auto;
		margin-right: auto;
	}
}

.entry-header .post-thumbnail {
	margin-left: auto;
	margin-right: auto;
	width: calc(100vw - 30px);
	max-width: 100%;
}
@media only screen and (min-width: 482px) {

	.entry-header .post-thumbnail {
		width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.entry-header .post-thumbnail {
		width: min(calc(100vw - 200px), 1240px);
	}
}

.singular .post-thumbnail {
	margin-left: auto;
	margin-right: auto;
	width: calc(100vw - 30px);
	max-width: 100%;
}
@media only screen and (min-width: 482px) {

	.singular .post-thumbnail {
		width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.singular .post-thumbnail {
		width: min(calc(100vw - 200px), 1240px);
	}
}

.alignfull [class*=inner-container] > .alignwide {
	margin-left: auto;
	margin-right: auto;
	width: calc(100vw - 30px);
	max-width: 100%;
}
@media only screen and (min-width: 482px) {

	.alignfull [class*=inner-container] > .alignwide {
		width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.alignfull [class*=inner-container] > .alignwide {
		width: min(calc(100vw - 200px), 1240px);
	}
}

.alignwide [class*=inner-container] > .alignwide {
	margin-left: auto;
	margin-right: auto;
	width: calc(100vw - 30px);
	max-width: 100%;
}
@media only screen and (min-width: 482px) {

	.alignwide [class*=inner-container] > .alignwide {
		width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.alignwide [class*=inner-container] > .alignwide {
		width: min(calc(100vw - 200px), 1240px);
	}
}

@media only screen and (min-width: 482px) {

	.entry-content > .alignleft {

		/*rtl:ignore*/
		margin-left: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);

		/*rtl:ignore*/
		margin-right: 25px;
	}
	@media only screen and (min-width: 482px) {

		.entry-content > .alignleft {
			margin-left: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
	@media only screen and (min-width: 822px) {

		.entry-content > .alignleft {
			margin-left: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
}
@media only screen and (min-width: 482px) {

	.entry-content > .alignright {

		/*rtl:ignore*/
		margin-left: 25px;

		/*rtl:ignore*/
		margin-right: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
	}
	@media only screen and (min-width: 482px) {

		.entry-content > .alignright {
			margin-right: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
	@media only screen and (min-width: 822px) {

		.entry-content > .alignright {
			margin-right: calc((100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
}

/**
 * Site Structure
 *
 * - Set vertical margins and responsive widths on
 *   top-level wrappers and content wrappers
 * - `--global--width-content` is a responsive variable
 * - See: globals/_global-width-responsive.scss
 */

/**
 * Top Level Wrappers (header, main, footer)
 * - Set vertical padding and horizontal margins
 */
.site-header,
.site-main,
.widget-area,
.site-footer {
	padding-top: 30px;
	padding-bottom: 30px;
	margin-left: auto;
	margin-right: auto;
}

.site-header {
	padding-top: 23px;
	padding-bottom: 60px;
}
@media only screen and (min-width: 482px) {

	.site-header {
		padding-bottom: 90px;
	}
}

/**
 * Site-main children wrappers
 * - Add double vertical margins here for clearer hierarchy
 */
.site-main > * {
	margin-top: 90px;
	margin-bottom: 90px;
}

.site-main > *:first-child {
	margin-top: 0;
}

.site-main > *:last-child {
	margin-bottom: 0;
}

/**
 * Set the default maximum responsive content-width
 */

/**
 * Set the wide maximum responsive content-width
 */

/**
 * Set the full maximum responsive content-width
 */

/*
 * Block & non-gutenberg content wrappers
 * - Set margins
 */
.entry-header,
.post-thumbnail,
.entry-content,
.entry-footer,
.author-bio {
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 30px;
	margin-left: auto;
}

/*
 * Block & non-gutenberg content wrapper children
 * - Sets spacing-vertical margin logic
 */
.site-main > article > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.site-main > .not-found > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.entry-content > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

[class*=inner-container] > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.wp-block-template-part > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.wp-block-post-template :where(li > *) {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.site-main > article > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.site-main > .not-found > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.entry-content > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	[class*=inner-container] > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.wp-block-template-part > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.wp-block-post-template :where(li > *) {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.site-main > article > *:first-child,
.site-main > .not-found > *:first-child,
.entry-content > *:first-child,
[class*=inner-container] > *:first-child,
.wp-block-template-part > *:first-child,
.wp-block-post-template :where(li > *):first-child {
	margin-top: 0;
}

.site-main > article > *:last-child,
.site-main > .not-found > *:last-child,
.entry-content > *:last-child,
[class*=inner-container] > *:last-child,
.wp-block-template-part > *:last-child,
.wp-block-post-template :where(li > *):last-child {
	margin-bottom: 0;
}

.site-footer > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.widget-area > * {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.site-footer > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.widget-area > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

/*
 * Block & non-gutenberg content wrapper children
 * - Sets spacing-unit margins
 */
.entry-header > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.post-thumbnail > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.page-content > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.comment-content > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.widget > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.entry-header > *:first-child,
.post-thumbnail > *:first-child,
.page-content > *:first-child,
.comment-content > *:first-child,
.widget > *:first-child {
	margin-top: 0;
}

.entry-header > *:last-child,
.post-thumbnail > *:last-child,
.page-content > *:last-child,
.comment-content > *:last-child,
.widget > *:last-child {
	margin-bottom: 0;
}

/*
 * .entry-content children specific controls
 * - Adds special margin overrides for alignment utility classes
 */
.entry-content > * {

	/* Reset alignleft and alignright margins after alignfull */
}

.entry-content > *.alignleft,
.entry-content > *.alignright,
.entry-content > *.alignleft:first-child + *,
.entry-content > *.alignright:first-child + *,
.entry-content > *.alignfull.has-background {
	margin-top: 0;
}

.entry-content > *:last-child,
.entry-content > *.alignfull.has-background {
	margin-bottom: 0;
}

.entry-content > *.alignfull + .alignleft {
	margin-top: 30px;
}

.entry-content > *.alignfull + .alignright {
	margin-top: 30px;
}

/**
 * Reset specific elements to make them easier to style in other contexts.
 */
html,
body,
p,
ol,
ul,
li,
dl,
dt,
dd,
blockquote,
figure,
fieldset,
form,
legend,
textarea,
pre,
iframe,
hr,
h1,
h2,
h3,
h4,
h5,
h6 {
	padding: 0;
	margin: 0;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
}

/**
 * Apply generic border-box to all elements.
 * See:
 * https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
html {

	/* Apply border-box across the entire page. */
	box-sizing: border-box;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	line-height: 1.7;
}

/**
 * Relax the definition a bit, to allow components to override it manually.
 */
*,
*::before,
*::after {
	box-sizing: inherit;
}

body {
	font-size: 1.25rem;
	font-weight: normal;
	color: #28303d;
	text-align: left;
	background-color: #d1e4dd;
}

.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
	content: "";
	display: table;
	table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
	clear: both;
}

/* Category 04 can contain any default HTML element. Do not add classes here, just give the elements some basic styles. */
blockquote {
	padding: 0;
	position: relative;
	margin: 30px 0 30px 25px;
}

blockquote > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

blockquote > *:first-child {
	margin-top: 0;
}

blockquote > *:last-child {
	margin-bottom: 0;
}

blockquote p {
	letter-spacing: normal;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	font-style: normal;
	font-weight: 700;
	line-height: 1.7;
}

blockquote cite,
blockquote footer {
	font-weight: normal;
	letter-spacing: normal;
}

blockquote.alignleft,
blockquote.alignright {
	padding-left: inherit;
}

blockquote.alignleft p,
blockquote.alignright p {
	font-size: 1.125rem;
	max-width: inherit;
	width: inherit;
}

blockquote.alignleft cite,
blockquote.alignleft footer,
blockquote.alignright cite,
blockquote.alignright footer {
	font-size: 1rem;
	letter-spacing: normal;
}

blockquote strong {
	font-weight: bolder;
}

blockquote:before {
	content: "“";
	font-size: 1.25rem;
	line-height: 1.7;
	position: absolute;
	left: -12px;
}

blockquote .wp-block-quote__citation,
blockquote cite,
blockquote footer {
	color: #28303d;
	font-size: 1rem;
	font-style: normal;
}
@media only screen and (max-width: 481px) {

	blockquote {
		padding-left: 13px;
	}

	blockquote:before {
		left: 0;
	}
}

input[type=text] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=email] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=url] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=password] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=search] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=number] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=tel] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=date] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=month] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=week] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=time] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=datetime] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=datetime-local] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=color] {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

.site textarea {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	padding: 10px;
	margin: 0 2px;
	max-width: 100%;
}

input[type=text]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=email]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=url]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=password]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=search]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=number]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=tel]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=date]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=month]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=week]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=time]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=datetime]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=datetime-local]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=color]:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

.site textarea:focus {
	color: #28303d;
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

input[type=text]:disabled,
input[type=email]:disabled,
input[type=url]:disabled,
input[type=password]:disabled,
input[type=search]:disabled,
input[type=number]:disabled,
input[type=tel]:disabled,
input[type=date]:disabled,
input[type=month]:disabled,
input[type=week]:disabled,
input[type=time]:disabled,
input[type=datetime]:disabled,
input[type=datetime-local]:disabled,
input[type=color]:disabled,
.site textarea:disabled {
	opacity: 0.7;
}

.is-dark-theme input[type=text] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=email] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=url] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=password] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=search] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=number] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=tel] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=date] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=month] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=week] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=time] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=datetime] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=datetime-local] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme input[type=color] {
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme .site textarea {
	background: rgba(255, 255, 255, 0.9);
}

input[type=search]:focus {
	outline-offset: -7px;
}

.is-dark-theme input[type=search]:focus {
	outline-color: #d1e4dd;
}

input[type=color] {
	padding: 5px;
	height: 40px;
}

input[type=email],
input[type=url] {

	/*rtl:ignore*/
	direction: ltr;
}

select {
	border: 3px solid #39414d;
	color: #28303d;
	-moz-appearance: none;
	-webkit-appearance: none;
	appearance: none;
	line-height: 1.7;
	padding: 10px 30px 10px 10px;
	background: #fff url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%2328303d'><polygon points='0,0 10,0 5,5'/></svg>") no-repeat;
	background-position: right 10px top 60%;
}

select:focus {
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

.is-dark-theme select {
	background: rgba(255, 255, 255, 0.9) url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%2328303d'><polygon points='0,0 10,0 5,5'/></svg>") no-repeat;
	background-position: right 10px top 60%;
}

textarea {
	width: 100%;
}

label {
	font-size: 1.125rem;
	font-weight: 500;
	margin-bottom: 10px;
}

/**
https://css-tricks.com/custom-styling-form-inputs-with-modern-css-features/
https://codepen.io/aaroniker/pen/ZEYoxEY by Aaron Iker.
License: MIT.
*/
@supports (-webkit-appearance: none) or (-moz-appearance: none) {

	input[type=checkbox] {
		-webkit-appearance: none;
		-moz-appearance: none;
		position: relative;
		width: 25px;
		height: 25px;
		border: 3px solid #39414d;
		background: #fff;
	}

	input[type=radio] {
		-webkit-appearance: none;
		-moz-appearance: none;
		position: relative;
		width: 25px;
		height: 25px;
		border: 3px solid #39414d;
		background: #fff;
	}

	input[type=checkbox]:disabled,
	input[type=radio]:disabled {
		opacity: 0.7;
	}

	.is-dark-theme input[type=checkbox] {
		background: rgba(255, 255, 255, 0.9);
	}

	.is-dark-theme input[type=radio] {
		background: rgba(255, 255, 255, 0.9);
	}

	input[type=checkbox]:focus {
		outline-offset: 2px;
		outline: 2px dotted #39414d;
	}

	input[type=checkbox]:after {
		content: "";
		opacity: 0;
		display: block;
		left: 5px;
		top: 2px;
		position: absolute;
		width: 7px;
		height: 13px;
		border: 3px solid #28303d;
		border-top: 0;
		border-left: 0;
		transform: rotate(30deg);
	}

	input[type=checkbox]:checked {
		color: #28303d;
	}

	input[type=checkbox]:checked:after {
		opacity: 1;
	}

	input[type=radio] {
		border-radius: 50%;
	}

	input[type=radio]:focus {
		outline-offset: 2px;
		outline: 2px dotted #39414d;
	}

	input[type=radio]:after {
		content: "";
		opacity: 0;
		display: block;
		left: 3px;
		top: 3px;
		position: absolute;
		width: 11px;
		height: 11px;
		border-radius: 50%;
		background: #28303d;
	}

	input[type=radio]:checked {
		border: 4px solid #39414d;
	}

	input[type=radio]:checked:after {
		opacity: 1;
	}

	input[type=radio]:checked:focus {
		outline-offset: 4px;
		outline: 2px dotted #39414d;
	}
}

input[type=checkbox] + label {
	display: inline-block;
	padding-left: 10px;
	font-size: 1rem;
	vertical-align: top;
}

input[type=radio] + label {
	display: inline-block;
	padding-left: 10px;
	font-size: 1rem;
	vertical-align: top;
}

/**
 * https://css-tricks.com/styling-cross-browser-compatible-range-inputs-css/
*/
@supports (-webkit-appearance: none) or (-moz-appearance: none) {

	input[type=range] {
		-webkit-appearance: none;

		/* Hides the slider so that custom slider can be made */
		width: 100%;

		/* Specific width is required for Firefox. */
		height: 6px;
		background: #39414d;
		border-radius: 6px;
		outline-offset: 10px;
	}

	input[type=range]:disabled {
		opacity: 0.7;
	}

	input[type=range]::-webkit-slider-thumb {
		-webkit-appearance: none;
		border: 3px solid #39414d;
		height: 44px;
		width: 44px;
		border-radius: 50%;
		background: #d1e4dd;
		cursor: pointer;
	}

	input[type=range]::-moz-range-thumb {
		border: 3px solid #39414d;
		height: 44px;
		width: 44px;
		border-radius: 50%;
		background: #d1e4dd;
		cursor: pointer;
		box-sizing: border-box;
	}
}

input[type=range]::-ms-track {
	width: 100%;
	height: 6px;
	border-radius: 6px;
	border-width: 19px 0;
	border-color: #d1e4dd;
	background: transparent;
	color: transparent;
	cursor: pointer;
}

input[type=range]::-ms-fill-upper {
	background: #39414d;
	border-radius: 6px;
}

input[type=range]::-ms-fill-lower {
	background: #39414d;
	border-radius: 6px;
}

input[type=range]::-ms-thumb {
	border: 3px solid #39414d;
	height: 44px;
	width: 44px;
	border-radius: 50%;
	background: #d1e4dd;
	cursor: pointer;
}

fieldset {
	display: grid;
	border-color: #39414d;
	padding: 25px;
}

fieldset legend {
	font-size: 1.5rem;
}

fieldset input[type=submit] {
	max-width: max-content;
}

fieldset input:not([type=submit]) {
	margin-bottom: 20px;
}

fieldset input[type=radio],
fieldset input[type=checkbox] {
	margin-bottom: 0;
}

fieldset input[type=radio] + label {
	font-size: 1.125rem;
	padding-left: 0;
	margin-bottom: 20px;
}

fieldset input[type=checkbox] + label {
	font-size: 1.125rem;
	padding-left: 0;
	margin-bottom: 20px;
}

::-moz-placeholder {
	opacity: 1;
}

.post-password-message {
	font-size: 1.5rem;
}

.post-password-form {
	display: flex;
	flex-wrap: wrap;
}

.post-password-form__label {
	width: 100%;
	margin-bottom: 0;
}

.post-password-form input[type=password] {
	flex-grow: 1;
	margin-top: 10px;
	margin-right: 17px;
}

.post-password-form__submit {
	margin-top: 10px;
}
@media only screen and (min-width: 592px) {

	.post-password-form__submit {
		margin-left: 10px;
	}
}

img {
	height: auto;
	vertical-align: middle;
}

/* Classic editor images */

/* Make sure embeds and iframes fit their containers. */
img,
.entry-content img,
embed,
iframe,
object,
video {
	max-width: 100%;
}

/* Media captions */
figcaption,
.wp-caption,
.wp-caption-text,
.wp-block-embed figcaption {
	color: currentColor;
	font-size: 1rem;
	line-height: 1.7;
	margin-top: 10px;
	margin-bottom: 20px;
	text-align: center;
}

.alignleft figcaption,
.alignright figcaption,
.alignleft .wp-caption,
.alignright .wp-caption,
.alignleft .wp-caption-text,
.alignright .wp-caption-text,
.alignleft .wp-block-embed figcaption,
.alignright .wp-block-embed figcaption {
	margin-bottom: 0;
}

/* WP Smiley */
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	border: none;
	margin-bottom: 0;
	margin-top: 0;
	padding: 0;
}

/* Over here, place any elements that do not need to have their own file. */
b,
strong {
	font-weight: 700;
}

dfn,
cite,
em,
i {
	font-style: italic;
}

pre {
	white-space: pre;
	overflow-x: auto;
}

/*
 * text-underline-offset doesn't work in Chrome at all 👎
 * But looks nice in Safari/Firefox, so let's keep it and
 * maybe Chrome will support it soon.
 */
a {
	cursor: pointer;
	color: #28303d;
	text-underline-offset: 3px;
	text-decoration-skip-ink: all;
}

a:hover {
	text-decoration-style: dotted;
	text-decoration-skip-ink: none;
}

.site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) {

	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	text-decoration: underline 1px dotted currentColor;
	text-decoration-skip-ink: none;
	background: rgba(255, 255, 255, 0.9);
}

.is-dark-theme .site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) {
	background: #000;
	color: #fff;
	text-decoration: none;
}

.is-dark-theme .site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) .meta-nav {
	color: #fff;
}

.has-background-white .site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) {
	background: rgba(0, 0, 0, 0.9);
	color: #fff;
}

.has-background-white .site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) .meta-nav {
	color: #fff;
}

.site a:focus:not(.wp-block-button__link):not(.wp-block-file__button).skip-link {

	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	outline-offset: -2px;
}

.site a:focus:not(.wp-block-button__link):not(.wp-block-file__button).skip-link:focus {
	color: #21759b;
	background-color: #f1f1f1;
}

.site a:focus:not(.wp-block-button__link):not(.wp-block-file__button).custom-logo-link {
	background: none;
}

.site a:focus:not(.wp-block-button__link):not(.wp-block-file__button) img {
	outline: 2px dotted #28303d;
}

.has-background .has-link-color a,
.has-background.has-link-color a {
	color: #28303d;
}

/* Category 05 is all about adjusting the default block styles to the given layout. I only added three blocks as examples. */
.wp-block-audio audio:focus {
	outline-offset: 5px;
	outline: 2px solid #28303d;
}

/**
 * Button
 */
.site .button,
button {
	border: 3px solid transparent;
	border-radius: 0;
	cursor: pointer;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	line-height: 1.5;
	padding: 15px 30px;
	text-decoration: none;
}

input[type=submit] {
	border: 3px solid transparent;
	border-radius: 0;
	cursor: pointer;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	line-height: 1.5;
	padding: 15px 30px;
	text-decoration: none;
}

input[type=reset] {
	border: 3px solid transparent;
	border-radius: 0;
	cursor: pointer;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	line-height: 1.5;
	padding: 15px 30px;
	text-decoration: none;
}

.wp-block-search .wp-block-search__button,
.wp-block-button .wp-block-button__link,
.wp-block-file a.wp-block-file__button {
	border: 3px solid transparent;
	border-radius: 0;
	cursor: pointer;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	line-height: 1.5;
	padding: 15px 30px;
	text-decoration: none;
}

.site .button:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

button:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

input[type=submit]:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

input[type=reset]:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.wp-block-search .wp-block-search__button:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.wp-block-button .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.wp-block-file a.wp-block-file__button:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.has-background .site .button:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background button:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background input[type=submit]:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background input[type=reset]:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .wp-block-search .wp-block-search__button:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .wp-block-button .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .wp-block-file a.wp-block-file__button:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .site .button:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background button:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background input[type=submit]:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background input[type=reset]:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background .wp-block-search .wp-block-search__button:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background .wp-block-button .wp-block-button__link:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.has-background .wp-block-file a.wp-block-file__button:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.site .button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

input[type=submit]:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

input[type=reset]:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.wp-block-search .wp-block-search__button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.wp-block-button .wp-block-button__link:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.wp-block-file a.wp-block-file__button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background .site .button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background input[type=submit]:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background input[type=reset]:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background .wp-block-search .wp-block-search__button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background .wp-block-button .wp-block-button__link:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background .wp-block-file a.wp-block-file__button:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.site .button:hover,
.site .button:active,
button:hover,
button:active,
input[type=submit]:hover,
input[type=submit]:active,
input[type=reset]:hover,
input[type=reset]:active,
.wp-block-search .wp-block-search__button:hover,
.wp-block-search .wp-block-search__button:active,
.wp-block-button .wp-block-button__link:hover,
.wp-block-button .wp-block-button__link:active,
.wp-block-file a.wp-block-file__button:hover,
.wp-block-file a.wp-block-file__button:active {
	background-color: transparent;
	border-color: currentColor;
	color: inherit;
}

.site .button:focus,
button:focus,
input[type=submit]:focus,
input[type=reset]:focus,
.wp-block-search .wp-block-search__button:focus,
.wp-block-button .wp-block-button__link:focus,
.wp-block-file a.wp-block-file__button:focus {
	outline-offset: -6px;
	outline: 2px dotted currentColor;
}

.site .button:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

button:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

input[type=submit]:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

input[type=reset]:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

.wp-block-search .wp-block-search__button:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

.wp-block-button .wp-block-button__link:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

.wp-block-file a.wp-block-file__button:disabled {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: rgba(255, 255, 255, 0.5);
	color: #39414d;
}

/**
 * Block Options
 */
.wp-block-button:not(.is-style-outline) .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.has-background .wp-block-button:not(.is-style-outline) .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #d1e4dd;
}

.has-background .wp-block-button:not(.is-style-outline) .wp-block-button__link:not(:hover):not(:active):not(.has-text-color).has-background {
	color: #28303d;
}

.wp-block-button:not(.is-style-outline) .wp-block-button__link:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.has-background .wp-block-button:not(.is-style-outline) .wp-block-button__link:not(:hover):not(:active):not(.has-background) {
	background-color: #28303d;
}

.wp-block-button:not(.is-style-outline) .wp-block-button__link:hover,
.wp-block-button:not(.is-style-outline) .wp-block-button__link:active {
	border-color: currentColor !important;
	background-color: transparent !important;
	color: inherit !important;
}

.wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active):not(.has-text-color),
.wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active):not(.has-background),
.wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active).has-background {
	border-color: currentColor;
}

.wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active):not(.has-text-color) {
	color: #28303d;
}

.has-background .wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active).has-background:not(.has-text-color) {
	color: inherit;
}

.wp-block-button.is-style-outline .wp-block-button__link:not(:hover):not(:active):not(.has-background) {
	background-color: transparent;
}

.wp-block-button.is-style-outline .wp-block-button__link:hover {
	border-color: transparent !important;
	background-color: #28303d !important;
	color: #d1e4dd !important;
}

.wp-block-button.is-style-outline .wp-block-button__link:active {
	border-color: transparent !important;
	background-color: #28303d !important;
	color: #d1e4dd !important;
}

.has-background .wp-block-button.is-style-outline .wp-block-button__link:hover {
	background-color: #28303d !important;
	color: #d1e4dd !important;
}

.has-background .wp-block-button.is-style-outline .wp-block-button__link:active {
	background-color: #28303d !important;
	color: #d1e4dd !important;
}

.has-text-color .wp-block-button.is-style-outline .wp-block-button__link:hover {
	color: #d1e4dd !important;
}

.has-text-color .wp-block-button.is-style-outline .wp-block-button__link:active {
	color: #d1e4dd !important;
}

.wp-block-button .is-style-squared .wp-block-button__link {
	border-radius: 0;
}

.is-style-outline .wp-block-button__link[style*=radius]:focus {
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

.wp-block-button a.wp-block-button__link[style*=radius]:focus {
	outline-offset: 2px;
	outline: 2px dotted #39414d;
}

.wp-block-code {
	border-color: #28303d;
	border-radius: 0;
	border-style: solid;
	border-width: 0.1rem;
	padding: 20px;
}

.wp-block-code code {
	color: #28303d;
	white-space: pre;
	overflow-x: auto;
	display: block;
}

.wp-block-columns:not(.alignwide):not(.alignfull) {
	clear: both;
}

.wp-block-columns .wp-block-column > * {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-columns .wp-block-column > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.wp-block-columns .wp-block-column > *:first-child {
	margin-top: 0;
}

.wp-block-columns .wp-block-column > *:last-child {
	margin-bottom: 0;
}

.wp-block-columns .wp-block-column:last-child {
	margin-bottom: 0;
}

.wp-block-columns .wp-block-column:not(:last-child) {
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-columns .wp-block-column:not(:last-child) {
		margin-bottom: 30px;
	}
}
@media only screen and (min-width: 822px) {

	.wp-block-columns .wp-block-column:not(:last-child) {
		margin-bottom: 0;
	}
}

.wp-block-columns.is-style-twentytwentyone-columns-overlap {
	justify-content: space-around;
}
@media only screen and (min-width: 652px) {

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) {
		margin-left: -50px;
		margin-top: 63px;
		z-index: 2;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > p:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h1:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h2:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h3:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h4:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h5:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > h6:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > ul:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > ol:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > pre:not(.has-background) {
		background-color: #d1e4dd;
		padding: 20px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > ul:not(.has-background) {
		padding-left: 50px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n) > ol:not(.has-background) {
		padding-left: 50px;
	}

	.wp-block-columns.is-style-twentytwentyone-columns-overlap .wp-block-column:nth-child(2n).is-vertically-aligned-center {
		margin-top: 0;
	}
}

.wp-block-columns.alignfull .wp-block-column p:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h1:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h2:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h3:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h4:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h5:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-columns.alignfull .wp-block-column h6:not(.has-background) {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-cover,
.wp-block-cover-image {
	background-color: #000;
	min-height: 450px;
	margin-top: inherit;
	margin-bottom: inherit;

	/* default & custom background-color */

	/* Treating H2 separately to account for legacy /core styles */

	/* Block Styles */

	/* The background color class is used just for the overlay, and does not need to be applied to the inner container. */
}

.wp-block-cover:not(.alignwide):not(.alignfull),
.wp-block-cover-image:not(.alignwide):not(.alignfull) {
	clear: both;
}

.wp-block-cover.alignfull,
.wp-block-cover-image.alignfull {
	margin-top: 0;
	margin-bottom: 0;
}

.wp-block-cover .wp-block-cover__inner-container,
.wp-block-cover .wp-block-cover-image-text,
.wp-block-cover .wp-block-cover-text,
.wp-block-cover-image .wp-block-cover__inner-container,
.wp-block-cover-image .wp-block-cover-image-text,
.wp-block-cover-image .wp-block-cover-text {
	color: currentColor;
	margin-top: 30px;
	margin-bottom: 30px;
}

.wp-block-cover .wp-block-cover__inner-container a:not(.wp-block-button__link):not(.wp-block-file__button),
.wp-block-cover .wp-block-cover-image-text a:not(.wp-block-button__link):not(.wp-block-file__button),
.wp-block-cover .wp-block-cover-text a:not(.wp-block-button__link):not(.wp-block-file__button),
.wp-block-cover-image .wp-block-cover__inner-container a:not(.wp-block-button__link):not(.wp-block-file__button),
.wp-block-cover-image .wp-block-cover-image-text a:not(.wp-block-button__link):not(.wp-block-file__button),
.wp-block-cover-image .wp-block-cover-text a:not(.wp-block-button__link):not(.wp-block-file__button) {
	color: currentColor;
}

.wp-block-cover .wp-block-cover__inner-container .has-link-color a,
.wp-block-cover .wp-block-cover-image-text .has-link-color a,
.wp-block-cover .wp-block-cover-text .has-link-color a,
.wp-block-cover-image .wp-block-cover__inner-container .has-link-color a,
.wp-block-cover-image .wp-block-cover-image-text .has-link-color a,
.wp-block-cover-image .wp-block-cover-text .has-link-color a {
	color: #28303d;
}

.wp-block-cover:not([class*=background-color]) .wp-block-cover__inner-container {
	color: #fff;
}

.wp-block-cover:not([class*=background-color]) .wp-block-cover-image-text {
	color: #fff;
}

.wp-block-cover:not([class*=background-color]) .wp-block-cover-text {
	color: #fff;
}

.wp-block-cover-image:not([class*=background-color]) .wp-block-cover__inner-container {
	color: #fff;
}

.wp-block-cover-image:not([class*=background-color]) .wp-block-cover-image-text {
	color: #fff;
}

.wp-block-cover-image:not([class*=background-color]) .wp-block-cover-text {
	color: #fff;
}

.wp-block-cover h2 {
	font-size: 2.25rem;
	letter-spacing: normal;
	line-height: 1.3;
	max-width: inherit;
	text-align: inherit;
	padding: 0;
}
@media only screen and (min-width: 652px) {

	.wp-block-cover h2 {
		font-size: 3rem;
	}
}

.wp-block-cover-image h2 {
	font-size: 2.25rem;
	letter-spacing: normal;
	line-height: 1.3;
	max-width: inherit;
	text-align: inherit;
	padding: 0;
}
@media only screen and (min-width: 652px) {

	.wp-block-cover-image h2 {
		font-size: 3rem;
	}
}

.wp-block-cover h2.has-text-align-left,
.wp-block-cover-image h2.has-text-align-left {
	text-align: left;
}

.wp-block-cover h2.has-text-align-center,
.wp-block-cover-image h2.has-text-align-center {
	text-align: center;
}

.wp-block-cover h2.has-text-align-right,
.wp-block-cover-image h2.has-text-align-right {
	text-align: right;
}

.wp-block-cover .wp-block-cover__inner-container,
.wp-block-cover-image .wp-block-cover__inner-container {
	width: calc(100% - 60px);
}

.wp-block-cover .wp-block-cover__inner-container > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.wp-block-cover-image .wp-block-cover__inner-container > * {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-cover .wp-block-cover__inner-container > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.wp-block-cover-image .wp-block-cover__inner-container > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.wp-block-cover .wp-block-cover__inner-container > *:first-child,
.wp-block-cover-image .wp-block-cover__inner-container > *:first-child {
	margin-top: 0;
}

.wp-block-cover .wp-block-cover__inner-container > *:last-child,
.wp-block-cover-image .wp-block-cover__inner-container > *:last-child {
	margin-bottom: 0;
}

.wp-block-cover.alignleft,
.wp-block-cover.alignright,
.wp-block-cover-image.alignleft,
.wp-block-cover-image.alignright {
	margin-top: 0;
}

.wp-block-cover.alignleft > * {
	margin-top: 60px;
	margin-bottom: 60px;
	padding-left: 25px;
	padding-right: 25px;
	width: 100%;
}

.wp-block-cover.alignright > * {
	margin-top: 60px;
	margin-bottom: 60px;
	padding-left: 25px;
	padding-right: 25px;
	width: 100%;
}

.wp-block-cover-image.alignleft > * {
	margin-top: 60px;
	margin-bottom: 60px;
	padding-left: 25px;
	padding-right: 25px;
	width: 100%;
}

.wp-block-cover-image.alignright > * {
	margin-top: 60px;
	margin-bottom: 60px;
	padding-left: 25px;
	padding-right: 25px;
	width: 100%;
}

.wp-block-cover.has-left-content,
.wp-block-cover.has-right-content,
.wp-block-cover-image.has-left-content,
.wp-block-cover-image.has-right-content {
	justify-content: center;
}

.wp-block-cover.is-style-twentytwentyone-border,
.wp-block-cover-image.is-style-twentytwentyone-border {
	border: 3px solid #28303d;
}

.wp-block-cover[class*=-background-color][class] .wp-block-cover__inner-container,
.wp-block-cover-image[class*=-background-color][class] .wp-block-cover__inner-container {
	background-color: unset;
}

.wp-block-file a.wp-block-file__button:active,
.wp-block-file a.wp-block-file__button:focus,
.wp-block-file a.wp-block-file__button:hover {
	opacity: inherit;
}

.wp-block-file a.wp-block-file__button {
	display: inline-block;
}

.wp-block-gallery {
	margin: 0 auto;
}

.wp-block-gallery .blocks-gallery-image,
.wp-block-gallery .blocks-gallery-item {
	width: calc(50% - 10px);
}

.wp-block-gallery .blocks-gallery-image figcaption,
.wp-block-gallery .blocks-gallery-item figcaption {
	margin: 0;
	color: #fff;
	font-size: 1rem;
}

.wp-block-gallery .blocks-gallery-image figcaption a,
.wp-block-gallery .blocks-gallery-item figcaption a {
	color: #fff;
}

.wp-block-gallery .blocks-gallery-image figcaption a:focus {
	background-color: transparent;
	outline: 2px solid #28303d;
	text-decoration: none;
}

.wp-block-gallery .blocks-gallery-item figcaption a:focus {
	background-color: transparent;
	outline: 2px solid #28303d;
	text-decoration: none;
}

.wp-block-gallery .blocks-gallery-image a:focus img,
.wp-block-gallery .blocks-gallery-item a:focus img {
	outline-offset: 2px;
}

.wp-block-group {
	display: block;
	clear: both;
	display: flow-root;
}

.wp-block-group:before,
.wp-block-group:after {
	content: "";
	display: block;
	clear: both;
}

.wp-block-group .wp-block-group__inner-container {
	margin-left: auto;
	margin-right: auto;
}

.wp-block-group .wp-block-group__inner-container > * {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-group .wp-block-group__inner-container > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.wp-block-group .wp-block-group__inner-container > *:first-child {
	margin-top: 0;
}

.wp-block-group .wp-block-group__inner-container > *:last-child {
	margin-bottom: 0;
}

.wp-block-group.has-background {
	padding: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-group.has-background {
		padding: 30px;
	}
}

.wp-block-group.is-style-twentytwentyone-border {
	border: 3px solid #28303d;
	padding: 30px;
}

.wp-block-group.has-background .wp-block-group__inner-container > .alignfull {
	max-width: calc(100% + 60px);
	width: calc(100% + 60px);
	margin-left: -30px;
}

.wp-block-group.has-background .wp-block-group__inner-container > hr.wp-block-separator:not(.is-style-dots):not(.alignwide).alignfull {
	max-width: calc(100% + 60px);
	width: calc(100% + 60px);
	margin-left: -30px;
}

.wp-block-group.is-style-twentytwentyone-border .wp-block-group__inner-container > .alignfull {
	max-width: calc(100% + 60px);
	width: calc(100% + 60px);
	margin-left: -30px;
}

.wp-block-group.is-style-twentytwentyone-border .wp-block-group__inner-container > hr.wp-block-separator:not(.is-style-dots):not(.alignwide).alignfull {
	max-width: calc(100% + 60px);
	width: calc(100% + 60px);
	margin-left: -30px;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	clear: both;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-weight: normal;
}

h1 strong,
.h1 strong,
h2 strong,
.h2 strong,
h3 strong,
.h3 strong,
h4 strong,
.h4 strong,
h5 strong,
.h5 strong,
h6 strong,
.h6 strong {
	font-weight: 600;
}

h1 {
	font-size: 4rem;
	letter-spacing: normal;
	line-height: 1.1;
}

@media only screen and (min-width: 652px) {

	h1 {
		font-size: 6rem;
	}
}

.h1 {
	font-size: 4rem;
	letter-spacing: normal;
	line-height: 1.1;
}

@media only screen and (min-width: 652px) {

	.h1 {
		font-size: 6rem;
	}
}

h2 {
	font-size: 2.25rem;
	letter-spacing: normal;
	line-height: 1.3;
}

@media only screen and (min-width: 652px) {

	h2 {
		font-size: 3rem;
	}
}

.h2 {
	font-size: 2.25rem;
	letter-spacing: normal;
	line-height: 1.3;
}

@media only screen and (min-width: 652px) {

	.h2 {
		font-size: 3rem;
	}
}

h3 {
	font-size: 2rem;
	letter-spacing: normal;
	line-height: 1.3;
}

@media only screen and (min-width: 652px) {

	h3 {
		font-size: 2rem;
	}
}

.h3 {
	font-size: 2rem;
	letter-spacing: normal;
	line-height: 1.3;
}

@media only screen and (min-width: 652px) {

	.h3 {
		font-size: 2rem;
	}
}

h4,
.h4 {
	font-size: 1.5rem;
	font-weight: 600;
	letter-spacing: normal;
	line-height: 1.3;
}

h5,
.h5 {
	font-size: 1.125rem;
	font-weight: 600;
	letter-spacing: 0.05em;
	line-height: 1.3;
}

h6,
.h6 {
	font-size: 1rem;
	font-weight: 600;
	letter-spacing: 0.05em;
	line-height: 1.3;
}

.wp-block-image {
	text-align: center;
}

.wp-block-image figcaption {
	color: #28303d;
	font-size: 1rem;
	line-height: 1.7;
	margin-top: 10px;
	margin-bottom: 20px;
	text-align: center;
}

.wp-block-image .alignright {
	margin-left: 25px;
}

.wp-block-image .alignleft {
	margin-right: 25px;
}

.wp-block-image a:focus img {
	outline-offset: 2px;
}

.entry-content > *[class=wp-block-image],
.entry-content [class*=inner-container] > *[class=wp-block-image] {
	margin-top: 0;
	margin-bottom: 0;
}

.entry-content > *[class=wp-block-image] + *,
.entry-content [class*=inner-container] > *[class=wp-block-image] + * {
	margin-top: 0;
}

.wp-block-image.is-style-twentytwentyone-border img,
.wp-block-image.is-style-twentytwentyone-image-frame img {
	border: 3px solid #28303d;
}

.wp-block-image.is-style-twentytwentyone-image-frame img {
	padding: 20px;
}

@media only screen and (min-width: 482px) {

	.entry-content > .wp-block-image > .alignleft,
	.entry-content > .wp-block-image > .alignright {
		max-width: 50%;
	}
}
@media only screen and (max-width: 481px) {

	.entry-content > .wp-block-image > .alignleft,
	.entry-content > .wp-block-image > .alignright {
		margin-left: 0;
		margin-right: 0;
	}
}

.wp-block-latest-comments {
	padding-left: 0;
}

.wp-block-latest-comments .wp-block-latest-comments__comment {
	font-size: 1.125rem;
	line-height: 1.7;

	/* Vertical margins logic */
	margin-top: 30px;
	margin-bottom: 30px;
}

.wp-block-latest-comments .wp-block-latest-comments__comment:first-child {
	margin-top: 0;
}

.wp-block-latest-comments .wp-block-latest-comments__comment:last-child {
	margin-bottom: 0;
}

.wp-block-latest-comments .wp-block-latest-comments__comment-meta {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-block-latest-comments .wp-block-latest-comments__comment-date {
	color: #28303d;
	font-size: 1.125rem;
}

.wp-block-latest-comments .wp-block-latest-comments__comment-excerpt p {
	font-size: 1.125rem;
	line-height: 1.7;
	margin: 0;
}

.wp-block-latest-posts {
	padding-left: 0;
}

.wp-block-latest-posts:not(.is-grid) > li {
	margin-top: 50px;
	margin-bottom: 50px;
}

.wp-block-latest-posts:not(.is-grid) > li:first-child {
	margin-top: 0;
}

.wp-block-latest-posts:not(.is-grid) > li:last-child {
	margin-bottom: 0;
}

.widget-area .wp-block-latest-posts:not(.is-grid) > li {
	margin-top: 0;
	margin-bottom: 0;
}

.wp-block-latest-posts.is-grid {
	word-wrap: break-word;
	word-break: break-word;
}

.wp-block-latest-posts.is-grid > li {
	margin-bottom: 30px;
}

.wp-block-latest-posts.is-grid > li:last-child {
	margin-bottom: 0;
}

.wp-block-latest-posts.is-grid.columns-2 > li:nth-last-child(-n+2):nth-child(2n+1),
.wp-block-latest-posts.is-grid.columns-2 > li:nth-last-child(-n+2):nth-child(2n+1) ~ li,
.wp-block-latest-posts.is-grid.columns-3 > li:nth-last-child(-n+3):nth-child(3n+1),
.wp-block-latest-posts.is-grid.columns-3 > li:nth-last-child(-n+3):nth-child(3n+1) ~ li,
.wp-block-latest-posts.is-grid.columns-4 > li:nth-last-child(-n+4):nth-child(4n+1),
.wp-block-latest-posts.is-grid.columns-4 > li:nth-last-child(-n+4):nth-child(4n+1) ~ li,
.wp-block-latest-posts.is-grid.columns-5 > li:nth-last-child(-n+5):nth-child(5n+1),
.wp-block-latest-posts.is-grid.columns-5 > li:nth-last-child(-n+5):nth-child(5n+1) ~ li,
.wp-block-latest-posts.is-grid.columns-6 > li:nth-last-child(-n+6):nth-child(6n+1),
.wp-block-latest-posts.is-grid.columns-6 > li:nth-last-child(-n+6):nth-child(6n+1) ~ li {
	margin-bottom: 0;
}

.wp-block-latest-posts > li > * {
	margin-top: 10px;
	margin-bottom: 10px;
}

.wp-block-latest-posts > li > *:first-child {
	margin-top: 0;
}

.wp-block-latest-posts > li > *:last-child {
	margin-bottom: 0;
}

.wp-block-latest-posts > li > a {
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 2rem;
	font-weight: normal;
	line-height: 1.3;
	margin-bottom: 10px;
}
@media only screen and (min-width: 652px) {

	.wp-block-latest-posts > li > a {
		font-size: 2rem;
	}
}

.widget-area .wp-block-latest-posts > li > a {
	font-size: 1.125rem;
	margin-bottom: 0;
}

.wp-block-latest-posts .wp-block-latest-posts__post-author {
	color: #28303d;
	font-size: 1.25rem;
	line-height: 1.7;
}

.wp-block-latest-posts .wp-block-latest-posts__post-date {
	color: #28303d;
	font-size: 1rem;
	line-height: 1.7;
}

[class*=inner-container] .wp-block-latest-posts .wp-block-latest-posts__post-date,
.has-background .wp-block-latest-posts .wp-block-latest-posts__post-date {
	color: currentColor;
}

.wp-block-latest-posts .wp-block-latest-posts__post-excerpt,
.wp-block-latest-posts .wp-block-latest-posts__post-full-content {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.125rem;
	line-height: 1.7;
	margin-top: 20px;
}

.wp-block-latest-posts.alignfull {
	padding-left: 20px;
	padding-right: 20px;
}

.entry-content [class*=inner-container] .wp-block-latest-posts.alignfull,
.entry-content .has-background .wp-block-latest-posts.alignfull {
	padding-left: 0;
	padding-right: 0;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers {
	border-top: 3px solid #28303d;
	border-bottom: 3px solid #28303d;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers:not(.is-grid) > li {
	padding-bottom: 30px;
	border-bottom: 1px solid #28303d;
	margin-top: 30px;
	margin-bottom: 30px;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers > li {
	padding-bottom: 30px;
	border-bottom: 1px solid #28303d;
	margin-top: 30px;
	margin-bottom: 30px;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers:not(.is-grid) > li:last-child,
.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers > li:last-child {
	padding-bottom: 0;
	border-bottom: none;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid {
	box-shadow: inset 0 -1px 0 0 #28303d;
	border-bottom: 2px solid #28303d;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid li {
	margin: 0;
	padding-top: 30px;
	padding-right: 25px;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid li:last-child {
	padding-bottom: 30px;
}
@media screen and (min-width: 600px) {

	.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid.columns-2 li {
		width: 50%;
	}

	.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid.columns-3 li {
		width: 33%;
	}

	.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid.columns-4 li {
		width: 25%;
	}

	.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid.columns-5 li {
		width: 20%;
	}

	.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-dividers.is-grid.columns-6 li {
		width: 17%;
	}
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-borders li {
	border: 3px solid #28303d;
	padding: 30px 25px;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-borders li:last-child {
	padding-bottom: 30px;
}

.wp-block-latest-posts.is-style-twentytwentyone-latest-posts-borders:not(.is-grid) li {
	margin-top: 25px;
	margin-bottom: 25px;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	vertical-align: top;
	width: 100%;
}

.gallery-item a {
	display: block;
}

.gallery-item a:focus img {
	outline-offset: -2px;
}

.gallery-columns-2 .gallery-item {
	max-width: 50%;
}

.gallery-columns-3 .gallery-item {
	max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
	max-width: 25%;
}

.gallery-columns-5 .gallery-item {
	max-width: 20%;
}

.gallery-columns-6 .gallery-item {
	max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
	max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
	max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
	max-width: 11.11%;
}

.gallery-caption {
	display: block;
}

figure.wp-caption a:focus img {
	outline-offset: 2px;
}

ul,
ol {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	margin: 0;
	padding-left: 50px;
}

ul.aligncenter,
ol.aligncenter,
ul.alignright,
ol.alignright {
	list-style-position: inside;
	padding: 0;
}

ul.alignright,
ol.alignright {
	text-align: right;
}

ul {
	list-style-type: disc;
}

ul ul {
	list-style-type: circle;
}

ol {
	list-style-type: decimal;
}

ol ul {
	list-style-type: circle;
}

dt {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-weight: bold;
}

dd {
	margin: 0;
	padding-left: 50px;
}

.wp-block-media-text {

	/**
   * Block Options
   */
}

.wp-block-media-text.alignfull {
	margin-top: 0;
	margin-bottom: 0;
}

.wp-block-media-text a:focus img {
	outline-offset: -1px;
}

.wp-block-media-text .wp-block-media-text__content {
	padding: 25px;
}
@media only screen and (min-width: 592px) {

	.wp-block-media-text .wp-block-media-text__content {
		padding: 30px;
	}
}

.wp-block-media-text .wp-block-media-text__content > * {
	margin-top: 20px;
	margin-bottom: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-media-text .wp-block-media-text__content > * {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.wp-block-media-text .wp-block-media-text__content > *:first-child {
	margin-top: 0;
}

.wp-block-media-text .wp-block-media-text__content > *:last-child {
	margin-bottom: 0;
}
@media only screen and (min-width: 482px) {

	.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content {
		padding-top: 30px;
		padding-bottom: 30px;
	}
}

.wp-block-media-text.is-style-twentytwentyone-border {
	border: 3px solid #28303d;
}

.wp-block-navigation .wp-block-navigation-link {
	padding: 0;
}

.wp-block-navigation .wp-block-navigation-link .wp-block-navigation-link__content {
	padding: 13px;
}

.wp-block-navigation .wp-block-navigation-link .wp-block-navigation-link__label {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.25rem;
	font-weight: normal;
}

.wp-block-navigation .wp-block-navigation-link__submenu-icon {
	padding: 0;
}

.wp-block-navigation > .wp-block-navigation__container .has-child .wp-block-navigation-link {
	display: inherit;
}

.wp-block-navigation > .wp-block-navigation__container .has-child .wp-block-navigation__container {
	border: none;
	left: 0;
	margin-left: 13px;
	min-width: max-content;
	opacity: 0;
	padding: 0;
	position: inherit;
	top: inherit;
}

.wp-block-navigation > .wp-block-navigation__container .has-child .wp-block-navigation__container .wp-block-navigation-link .wp-block-navigation-link__content {
	display: inline-block;
	padding: 7px 13px;
}

.wp-block-navigation > .wp-block-navigation__container .has-child .wp-block-navigation__container .wp-block-navigation-link__submenu-icon {
	display: none;
}

.wp-block-navigation > .wp-block-navigation__container .has-child:hover .wp-block-navigation__container,
.wp-block-navigation > .wp-block-navigation__container .has-child:focus-within .wp-block-navigation__container {
	display: block;
	opacity: 1;
	visibility: visible;
}

.wp-block-navigation > .wp-block-navigation__container > .has-child > .wp-block-navigation__container {
	background: #d1e4dd;
	margin: 0;
	padding: 0;
	position: absolute;
	top: 100%;
	border: 1px solid #28303d;
}

.wp-block-navigation > .wp-block-navigation__container > .has-child > .wp-block-navigation__container:before {
	content: "";
	display: block;
	position: absolute;
	width: 0;
	top: -10px;
	left: 25px;
	border-style: solid;
	border-color: #28303d transparent;
	border-width: 0 7px 10px 7px;
}

.wp-block-navigation > .wp-block-navigation__container > .has-child > .wp-block-navigation__container:after {
	content: "";
	display: block;
	position: absolute;
	width: 0;
	top: -10px;
	left: 25px;
	border-style: solid;
	border-color: #28303d transparent;
	border-width: 0 7px 10px 7px;
}

.wp-block-navigation > .wp-block-navigation__container > .has-child > .wp-block-navigation__container:after {
	top: -9px;
	border-color: #d1e4dd transparent;
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__container {
	background: #d1e4dd;
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__container .wp-block-navigation__container {
	background: #d1e4dd;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation-link > a:hover {
	color: #28303d;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation-link > a:focus {
	color: #28303d;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation-link > a:hover {
	text-decoration: underline;
	text-decoration-style: dotted;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation-link__content {
	color: currentColor;
}

p {
	line-height: 1.7;
}

p.has-background {
	padding: 20px;
}

p.has-text-color a {
	color: #28303d;
}

pre.wp-block-preformatted {
	overflow-x: auto;
	white-space: pre;
}

.wp-block-pullquote {
	padding: 40px 0;
	text-align: center;
	border-width: 3px;
	border-bottom-style: solid;
	border-top-style: solid;
	color: currentColor;
	border-color: currentColor;
	position: relative;

	/**
   * Block Options
   */
}

.wp-block-pullquote blockquote::before {
	color: currentColor;
	content: "“";
	display: block;
	position: relative;
	left: 0;
	font-size: 3rem;
	font-weight: 500;
	line-height: 1;
}

.wp-block-pullquote p {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 2rem;
	font-style: normal;
	font-weight: 700;
	letter-spacing: normal;
	line-height: 1.3;
	margin: 0;
}
@media only screen and (min-width: 652px) {

	.wp-block-pullquote p {
		font-size: 2rem;
	}
}

.wp-block-pullquote a {
	color: currentColor;
}

.wp-block-pullquote .wp-block-pullquote__citation,
.wp-block-pullquote cite,
.wp-block-pullquote footer {
	color: currentColor;
	display: block;
	font-size: 1rem;
	font-style: normal;
	text-transform: none;
}

.wp-block-pullquote:not(.is-style-solid-color) {
	background: none;
}

.wp-block-pullquote.alignleft:not(.is-style-solid-color) blockquote:before,
.wp-block-pullquote.alignleft:not(.is-style-solid-color) cite {
	text-align: center;
}

.wp-block-pullquote.alignwide > p {
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	.wp-block-pullquote.alignwide > p {
		max-width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.wp-block-pullquote.alignwide > p {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.wp-block-pullquote.alignwide blockquote {
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	.wp-block-pullquote.alignwide blockquote {
		max-width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	.wp-block-pullquote.alignwide blockquote {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

.wp-block-pullquote.alignfull:not(.is-style-solid-color) > p {
	padding: 0 40px;
}

.wp-block-pullquote.alignfull:not(.is-style-solid-color) blockquote {
	padding: 0 40px;
}

.wp-block-pullquote.is-style-solid-color {
	color: #28303d;
	padding: 50px;
	border-width: 3px;
	border-style: solid;
	border-color: #28303d;
}
@media (min-width: 600px) {

	.wp-block-pullquote.is-style-solid-color {
		padding: 100px;
	}
}

.wp-block-pullquote.is-style-solid-color blockquote::before {
	text-align: left;
}

.wp-block-pullquote.is-style-solid-color blockquote {
	margin: 0;
	max-width: inherit;
}

.wp-block-pullquote.is-style-solid-color blockquote p {
	font-size: 2rem;
}
@media only screen and (min-width: 652px) {

	.wp-block-pullquote.is-style-solid-color blockquote p {
		font-size: 2rem;
	}
}

.wp-block-pullquote.is-style-solid-color .wp-block-pullquote__citation,
.wp-block-pullquote.is-style-solid-color cite,
.wp-block-pullquote.is-style-solid-color footer {
	color: currentColor;
}

.wp-block-pullquote.is-style-solid-color.alignleft,
.wp-block-pullquote.is-style-solid-color.alignright {
	padding: 20px;
}

.wp-block-pullquote.is-style-solid-color.alignleft blockquote,
.wp-block-pullquote.is-style-solid-color.alignright blockquote {
	max-width: initial;
}

.wp-block-query.has-background {
	padding: 20px;
}
@media only screen and (min-width: 482px) {

	.wp-block-query.has-background {
		padding: 30px;
	}
}

.wp-block-quote {
	border-left: none;

	/**
   * Block Options
   */
}

.wp-block-quote:before {
	content: "“";
	font-size: 1.25rem;
	line-height: 1.7;
	left: 8px;
}

.has-background .wp-block-quote .wp-block-quote__citation,
[class*=background-color] .wp-block-quote .wp-block-quote__citation,
[style*=background-color] .wp-block-quote .wp-block-quote__citation,
.wp-block-cover[style*=background-image] .wp-block-quote .wp-block-quote__citation,
.has-background .wp-block-quote cite,
[class*=background-color] .wp-block-quote cite,
[style*=background-color] .wp-block-quote cite,
.wp-block-cover[style*=background-image] .wp-block-quote cite,
.has-background .wp-block-quote footer,
[class*=background-color] .wp-block-quote footer,
[style*=background-color] .wp-block-quote footer,
.wp-block-cover[style*=background-image] .wp-block-quote footer {
	color: currentColor;
}

.wp-block-quote.has-text-align-right {
	margin: 30px 25px 30px auto;
	padding-right: 0;
	border-right: none;
}

.wp-block-quote.has-text-align-right:before {
	display: none;
}

.wp-block-quote.has-text-align-right p:before {
	content: "”";
	font-size: 1.25rem;
	font-weight: normal;
	line-height: 1.7;
	margin-right: 5px;
}

.wp-block-quote.has-text-align-center {
	margin: 30px auto;
}

.wp-block-quote.has-text-align-center:before {
	display: none;
}

.wp-block-quote.is-large,
.wp-block-quote.is-style-large {
	padding-left: 0;
	padding-right: 0;

	/* Resetting margins to match _block-container.scss */
	margin-top: 30px;
	margin-bottom: 30px;
}

.wp-block-quote.is-large p {
	font-size: 2.25rem;
	font-style: normal;
	line-height: 1.35;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-large p {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-style-large p {
	font-size: 2.25rem;
	font-style: normal;
	line-height: 1.35;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-style-large p {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-large:before {
	font-size: 2.25rem;
	line-height: 1.35;
	left: -25px;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-large:before {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-style-large:before {
	font-size: 2.25rem;
	line-height: 1.35;
	left: -25px;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-style-large:before {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-large.has-text-align-right:before,
.wp-block-quote.is-style-large.has-text-align-right:before {
	display: none;
}

.wp-block-quote.is-large.has-text-align-right p:before {
	content: "”";
	font-size: 2.25rem;
	font-weight: normal;
	line-height: 1.35;
	margin-right: 10px;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-large.has-text-align-right p:before {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-style-large.has-text-align-right p:before {
	content: "”";
	font-size: 2.25rem;
	font-weight: normal;
	line-height: 1.35;
	margin-right: 10px;
}
@media only screen and (min-width: 652px) {

	.wp-block-quote.is-style-large.has-text-align-right p:before {
		font-size: 2.5rem;
	}
}

.wp-block-quote.is-large .wp-block-quote__citation,
.wp-block-quote.is-large cite,
.wp-block-quote.is-large footer,
.wp-block-quote.is-style-large .wp-block-quote__citation,
.wp-block-quote.is-style-large cite,
.wp-block-quote.is-style-large footer {
	color: #28303d;
	font-size: 1.125rem;
}
@media only screen and (max-width: 481px) {

	.wp-block-quote.is-large,
	.wp-block-quote.is-style-large {
		padding-left: 25px;
	}

	.wp-block-quote.is-large:before,
	.wp-block-quote.is-style-large:before {
		left: 0;
	}

	.wp-block-quote.is-large.has-text-align-right,
	.wp-block-quote.is-style-large.has-text-align-right {
		padding-left: 0;
		padding-right: 25px;
	}

	.wp-block-quote.is-large.has-text-align-right:before,
	.wp-block-quote.is-style-large.has-text-align-right:before {
		right: 0;
	}

	.wp-block-quote.is-large.has-text-align-center,
	.wp-block-quote.is-style-large.has-text-align-center {
		padding-left: 0;
		padding-right: 0;
	}

	.wp-block-quote.has-text-align-right {
		padding-left: 0;
		padding-right: 13px;
	}
}
@media only screen and (max-width: 481px) {

	.wp-block-quote.has-text-align-right:before {
		right: 0;
	}

	.wp-block-quote.has-text-align-center {
		padding-left: 0;
		padding-right: 0;
	}
}

.wp-block-rss {
	padding-left: 0;
}

.wp-block-rss > li {
	list-style: none;
}

.wp-block-rss:not(.is-grid) > li {
	margin-top: 50px;
	margin-bottom: 50px;
}

.wp-block-rss:not(.is-grid) > li:first-child {
	margin-top: 0;
}

.wp-block-rss:not(.is-grid) > li:last-child {
	margin-bottom: 0;
}

.wp-block-rss.is-grid > li {
	margin-bottom: 30px;
}

.wp-block-rss.is-grid > li:last-child {
	margin-bottom: 0;
}

.wp-block-rss.is-grid.columns-2 > li:nth-last-child(-n+2):nth-child(2n+1),
.wp-block-rss.is-grid.columns-2 > li:nth-last-child(-n+2):nth-child(2n+1) ~ li,
.wp-block-rss.is-grid.columns-3 > li:nth-last-child(-n+3):nth-child(3n+1),
.wp-block-rss.is-grid.columns-3 > li:nth-last-child(-n+3):nth-child(3n+1) ~ li,
.wp-block-rss.is-grid.columns-4 > li:nth-last-child(-n+4):nth-child(4n+1),
.wp-block-rss.is-grid.columns-4 > li:nth-last-child(-n+4):nth-child(4n+1) ~ li,
.wp-block-rss.is-grid.columns-5 > li:nth-last-child(-n+5):nth-child(5n+1),
.wp-block-rss.is-grid.columns-5 > li:nth-last-child(-n+5):nth-child(5n+1) ~ li,
.wp-block-rss.is-grid.columns-6 > li:nth-last-child(-n+6):nth-child(6n+1),
.wp-block-rss.is-grid.columns-6 > li:nth-last-child(-n+6):nth-child(6n+1) ~ li {
	margin-bottom: 0;
}

.wp-block-rss > li > * {
	margin-top: 10px;
	margin-bottom: 10px;
}

.wp-block-rss > li > *:first-child {
	margin-top: 0;
}

.wp-block-rss > li > *:last-child {
	margin-bottom: 0;
}

.wp-block-rss .wp-block-rss__item-title > a {
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 2rem;
	font-weight: normal;
	line-height: 1.3;
	margin-bottom: 10px;
}
@media only screen and (min-width: 652px) {

	.wp-block-rss .wp-block-rss__item-title > a {
		font-size: 2rem;
	}
}

.wp-block-rss .wp-block-rss__item-author {
	color: #28303d;
	font-size: 1.25rem;
	line-height: 1.7;
}

.wp-block-rss .wp-block-rss__item-publish-date {
	color: #28303d;
	font-size: 1rem;
	line-height: 1.7;
}

[class*=inner-container] .wp-block-rss .wp-block-rss__item-publish-date,
.has-background .wp-block-rss .wp-block-rss__item-publish-date {
	color: currentColor;
}

.wp-block-rss .wp-block-rss__item-excerpt,
.wp-block-rss .wp-block-rss__item-full-content {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.125rem;
	line-height: 1.7;
	margin-top: 20px;
}

.wp-block-rss.alignfull {
	padding-left: 20px;
	padding-right: 20px;
}

.entry-content [class*=inner-container] .wp-block-rss.alignfull,
.entry-content .has-background .wp-block-rss.alignfull {
	padding-left: 0;
	padding-right: 0;
}

.wp-block-search {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.wp-block-search {
		max-width: min(calc(100vw - 100px), 610px);
	}
}

@media only screen and (min-width: 822px) {

	.wp-block-search {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.wp-block-search__button-only.aligncenter .wp-block-search__inside-wrapper {
	justify-content: center;
}

.wp-block-search .wp-block-search__label {
	font-size: 1.125rem;
	font-weight: 500;
	margin-bottom: 10px;
}

.wp-block-search .wp-block-search__input {
	border: 3px solid #39414d;
	border-radius: 0;
	color: #28303d;
	line-height: 1.7;
	max-width: inherit;
	margin-right: -3px;
	padding: 10px;
}

.wp-block-search .wp-block-search__input:focus {
	color: #28303d;
	border-color: #39414d;
}

.has-background .wp-block-search .wp-block-search__input {
	border-color: #28303d !important;
}

.wp-block-search button.wp-block-search__button {
	margin-left: 0;
	line-height: 1;
}

.wp-block-search button.wp-block-search__button.has-icon {
	padding: 6px 15px;
}

.wp-block-search button.wp-block-search__button.has-icon svg {
	width: 40px;
	height: 40px;
	fill: currentColor;
}

.has-background .wp-block-search button.wp-block-search__button:hover {
	background-color: #d1e4dd !important;
	color: #28303d !important;
}

.has-background .wp-block-search button.wp-block-search__button:active {
	background-color: #d1e4dd !important;
	color: #28303d !important;
}

.has-text-color .wp-block-search button.wp-block-search__button:hover {
	color: #28303d !important;
}

.has-text-color .wp-block-search button.wp-block-search__button:active {
	color: #28303d !important;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
	background-color: #fff;
	border: 3px solid #39414d;
	border-radius: 0;
	padding: 3px;
}

.has-background .wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
	border-color: #28303d !important;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input {
	margin-left: 0;
	margin-right: 0;
	padding-left: 10px;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input:focus {
	color: #28303d;
	outline-offset: -2px;
	outline: 2px dotted #39414d;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button {
	padding: 15px 30px;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button:hover {
	color: #28303d;
}

.is-dark-theme .wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button {
	color: #28303d;
}

.is-dark-theme .wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button:hover {
	background-color: #28303d;
	color: #fff;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button.has-icon {
	padding: 6px 15px;
}

.wp-block-search__button {
	box-shadow: none;
}

hr {
	border-style: none;
	clear: both;
	margin-left: auto;
	margin-right: auto;
}

hr,
hr.wp-block-separator {
	border-bottom: 1px solid #28303d;
}

hr.wp-block-separator {
	opacity: 1;

	/**
   * Block Options
   */
}

hr.wp-block-separator:not(.is-style-dots):not(.alignwide) {
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	hr.wp-block-separator:not(.is-style-dots):not(.alignwide) {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	hr.wp-block-separator:not(.is-style-dots):not(.alignwide) {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

hr.wp-block-separator:not(.is-style-dots).alignwide {
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	hr.wp-block-separator:not(.is-style-dots).alignwide {
		max-width: calc(100vw - 100px);
	}
}
@media only screen and (min-width: 822px) {

	hr.wp-block-separator:not(.is-style-dots).alignwide {
		max-width: min(calc(100vw - 200px), 1240px);
	}
}

hr.wp-block-separator:not(.is-style-dots).alignfull {
	max-width: 100%;
}

hr.wp-block-separator.is-style-twentytwentyone-separator-thick {
	border-bottom-width: 3px;
}

hr.wp-block-separator.is-style-dots.has-background,
hr.wp-block-separator.is-style-dots.has-text-color {
	background-color: transparent !important;
}

hr.wp-block-separator.is-style-dots.has-background:before,
hr.wp-block-separator.is-style-dots.has-text-color:before {
	color: currentColor !important;
}

hr.wp-block-separator.is-style-dots:before {
	color: #28303d;
	font-size: 2.25rem;
	letter-spacing: 1.125rem;
	padding-left: 1.125rem;
}
@media only screen and (min-width: 652px) {

	hr.wp-block-separator.is-style-dots:before {
		font-size: 2.5rem;
	}
}

.has-background hr.wp-block-separator,
[class*=background-color] hr.wp-block-separator,
[style*=background-color] hr.wp-block-separator,
.wp-block-cover[style*=background-image] hr.wp-block-separator {
	border-color: currentColor;
}

.wp-block-social-links a:focus {
	color: #28303d;
}

.wp-block-social-links.is-style-twentytwentyone-social-icons-color a {
	color: #28303d;
}

.wp-block-social-links.is-style-twentytwentyone-social-icons-color .wp-social-link,
.wp-block-social-links.is-style-twentytwentyone-social-icons-color.has-icon-background-color.has-icon-background-color .wp-social-link {
	background: none;
}

.wp-block-spacer {
	display: block;
	margin-bottom: 0 !important;
	margin-top: 0 !important;
}
@media only screen and (max-width: 481px) {

	.wp-block-spacer[style] {
		height: 20px !important;
	}
}

table,
.wp-block-table {
	width: 100%;
	min-width: 240px;
	border-collapse: collapse;
}

table thead,
table tfoot,
.wp-block-table thead,
.wp-block-table tfoot {
	text-align: center;
}

table th,
.wp-block-table th {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

table td,
table th,
.wp-block-table td,
.wp-block-table th {
	padding: 10px;
	border: 1px solid;
}

table figcaption,
.wp-block-table figcaption {
	color: #28303d;
	font-size: 1rem;
}

table.is-style-regular .has-background,
table.is-style-stripes .has-background,
table.is-style-stripes .has-background thead tr,
table.is-style-stripes .has-background tfoot tr,
table.is-style-stripes .has-background tbody tr,
.wp-block-table.is-style-regular .has-background,
.wp-block-table.is-style-stripes .has-background,
.wp-block-table.is-style-stripes .has-background thead tr,
.wp-block-table.is-style-stripes .has-background tfoot tr,
.wp-block-table.is-style-stripes .has-background tbody tr {
	color: #28303d;
}

table.is-style-stripes,
.wp-block-table.is-style-stripes {
	border-color: #f0f0f0;
}

table.is-style-stripes th,
table.is-style-stripes td,
.wp-block-table.is-style-stripes th,
.wp-block-table.is-style-stripes td {
	border-width: 0;
}

table.is-style-stripes tbody tr:nth-child(odd) {
	background-color: #f0f0f0;
}

.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
	background-color: #f0f0f0;
}

table.is-style-stripes .has-background tbody tr:nth-child(odd) {
	background-color: rgba(255, 255, 255, 0.9);
}

.wp-block-table.is-style-stripes .has-background tbody tr:nth-child(odd) {
	background-color: rgba(255, 255, 255, 0.9);
}

table.wp-calendar-table td,
table.wp-calendar-table th {
	background: transparent;
	border: 0;
	text-align: center;
	line-height: 2;
	vertical-align: middle;
	word-break: normal;
}

table.wp-calendar-table th {
	font-weight: bold;
}

table.wp-calendar-table thead,
table.wp-calendar-table tbody {
	color: currentColor;
	border: 1px solid;
}

table.wp-calendar-table caption {
	font-weight: bold;
	text-align: left;
	margin-bottom: 20px;
	color: currentColor;
}

.wp-calendar-nav {
	text-align: left;
	margin-top: 10px;
}

.wp-calendar-nav svg {
	height: 1em;
	vertical-align: middle;
}

.wp-calendar-nav svg path {
	fill: currentColor;
}

.wp-calendar-nav .wp-calendar-nav-next {
	float: right;
}

.wp-block-tag-cloud.alignfull {
	padding-left: 20px;
	padding-right: 20px;
}

.wp-block-verse {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-block-video figcaption {
	color: #28303d;
	font-size: 1rem;
	margin-top: 10px;
	margin-bottom: 20px;
	text-align: center;
}

* > figure > video {
	max-width: unset;
	width: 100%;
	vertical-align: middle;
}

:root .is-extra-small-text {
	font-size: 1rem;
}

:root .has-extra-small-font-size {
	font-size: 1rem;
}

:root .is-small-text {
	font-size: 1.125rem;
}

:root .has-small-font-size {
	font-size: 1.125rem;
}

:root .is-regular-text {
	font-size: 1.25rem;
}

:root .has-regular-font-size {
	font-size: 1.25rem;
}

:root .is-normal-font-size {
	font-size: 1.25rem;
}

:root .has-normal-font-size {
	font-size: 1.25rem;
}

:root .has-medium-font-size {
	font-size: 1.25rem;
}

:root .is-large-text {
	font-size: 1.5rem;
	line-height: 1.3;
}

:root .has-large-font-size {
	font-size: 1.5rem;
	line-height: 1.3;
}

:root .is-larger-text {
	font-size: 2.5rem;
	line-height: 1.3;
}
@media only screen and (min-width: 652px) {

	:root .is-larger-text {
		font-size: 2.5rem;
	}
}

:root .has-larger-font-size {
	font-size: 2.5rem;
	line-height: 1.3;
}
@media only screen and (min-width: 652px) {

	:root .has-larger-font-size {
		font-size: 2.5rem;
	}
}

:root .is-extra-large-text {
	font-size: 2.5rem;
	line-height: 1.3;
}
@media only screen and (min-width: 652px) {

	:root .is-extra-large-text {
		font-size: 2.5rem;
	}
}

:root .has-extra-large-font-size {
	font-size: 2.5rem;
	line-height: 1.3;
}
@media only screen and (min-width: 652px) {

	:root .has-extra-large-font-size {
		font-size: 2.5rem;
	}
}

:root .is-huge-text {
	font-size: 6rem;
	line-height: 1.3;
	font-weight: 300;
}
@media only screen and (min-width: 652px) {

	:root .is-huge-text {
		font-size: 6rem;
	}
}

:root .has-huge-font-size {
	font-size: 6rem;
	line-height: 1.3;
	font-weight: 300;
}
@media only screen and (min-width: 652px) {

	:root .has-huge-font-size {
		font-size: 6rem;
	}
}

:root .is-gigantic-text {
	font-size: 9rem;
	line-height: 1.3;
	font-weight: 300;
}
@media only screen and (min-width: 652px) {

	:root .is-gigantic-text {
		font-size: 9rem;
	}
}

:root .has-gigantic-font-size {
	font-size: 9rem;
	line-height: 1.3;
	font-weight: 300;
}
@media only screen and (min-width: 652px) {

	:root .has-gigantic-font-size {
		font-size: 9rem;
	}
}

/* Block Alignments */

/**
 * These selectors set the default max width for content appearing inside a post or page.
 */

/**
 * .alignleft
 */
.alignleft {

	/*rtl:ignore*/
	text-align: left;
	margin-top: 0;
}

.entry-content > .alignleft {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.entry-content > .alignleft {
		max-width: min(calc(100vw - 100px), 610px);
	}
}

@media only screen and (min-width: 822px) {

	.entry-content > .alignleft {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

@media only screen and (min-width: 482px) {

	.alignleft {

		/*rtl:ignore*/
		float: left;

		/*rtl:ignore*/
		margin-right: 25px;
		margin-bottom: 30px;
	}

	.entry-content > .alignleft {
		max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
	}

	@media only screen and (min-width: 482px) {

		.entry-content > .alignleft {
			max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}

	@media only screen and (min-width: 822px) {

		.entry-content > .alignleft {
			max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
}

/**
 * .aligncenter
 */
.aligncenter {
	clear: both;
	display: block;
	float: none;
	margin-right: auto;
	margin-left: auto;
	text-align: center;
}

/**
 * .alignright
 */
.alignright {
	margin-top: 0;
	margin-bottom: 30px;
}

.entry-content > .alignright {
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.entry-content > .alignright {
		max-width: min(calc(100vw - 100px), 610px);
	}
}

@media only screen and (min-width: 822px) {

	.entry-content > .alignright {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

@media only screen and (min-width: 482px) {

	.alignright {

		/*rtl:ignore*/
		float: right;

		/*rtl:ignore*/
		margin-left: 25px;
	}

	.entry-content > .alignright {
		max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
	}

	@media only screen and (min-width: 482px) {

		.entry-content > .alignright {
			max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}

	@media only screen and (min-width: 822px) {

		.entry-content > .alignright {
			max-width: calc(50% - (100vw - min(calc(100vw - 4 * 25px), 610px)) *1);
		}
	}
}

[class*=inner-container] > .alignleft + *,
[class*=inner-container] > .alignright + * {
	margin-top: 0;
}

/**
 * .alignwide
 */

/**
 * .alignfull
 */
.alignwide,
.alignfull {
	clear: both;
}

.has-left-content {
	justify-content: flex-start;
}

.has-right-content {
	justify-content: flex-end;
}

.has-parallax {
	background-attachment: fixed;
}

.has-drop-cap:not(:focus)::first-letter {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-weight: normal;
	line-height: 0.66;
	text-transform: uppercase;
	font-style: normal;
	float: left;
	margin: 0.1em 0.1em 0 0;
	font-size: 5rem;
}

@media only screen and (min-width: 652px) {

	.has-drop-cap:not(:focus)::first-letter {
		font-size: 7rem;
	}
}

.has-drop-cap:not(:focus)::after {
	content: "";
	display: table;
	clear: both;
	padding-top: 14px;
}

.desktop-only {
	display: none;
}
@media only screen and (min-width: 482px) {

	.desktop-only {
		display: block;
	}
}

/* Category 06 contains all "bigger" components which contain elements of the previous two categories like header, footer, page template, single template, comments section, archives, ... */
.site-header {
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	row-gap: 30px;
}

.wp-custom-logo .site-header {
	align-items: center;
}
@media only screen and (min-width: 482px) {

	.site-header {
		padding-top: 40px;
	}
}
@media only screen and (min-width: 822px) {

	.site-header {
		padding-top: 72px;
	}
}

.site-branding {
	color: #28303d;
	margin-right: 140px;
}

.site-branding:last-child {
	margin-right: 0;
	width: 100%;
	text-align: center;
}
@media only screen and (min-width: 482px) {

	.site-branding {
		margin-right: initial;
		margin-top: 4px;
	}
}

.site-title {
	color: #28303d;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.5rem;
	letter-spacing: normal;
	text-transform: uppercase;
	line-height: 1.3;
	margin-bottom: 5px;
}

.site-title a {
	color: currentColor;
	font-weight: normal;
}

.site-title a:link,
.site-title a:visited,
.site-title a:active {
	color: currentColor;
}

.site-title a:hover {
	color: #39414d;
}

.site-title a:focus {
	color: #39414d;
}
@media only screen and (min-width: 482px) {

	.site-title {
		font-size: 1.5rem;
	}
}

.site-description {
	color: currentColor;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.125rem;
	line-height: 1.4;
}

.site-title > a {
	text-decoration-color: #39414d;
}

.site-logo {
	margin: 15px 0;
}

.site-header > .site-logo {
	width: 100%;
	padding-bottom: 45px;
	border-bottom: 1px solid;
	text-align: center;
}

.site-logo .custom-logo {
	margin-left: auto;
	margin-right: auto;
	max-width: 96px;
	max-height: 96px;
	height: auto;
	display: inline-block;
	width: auto;
}
@media only screen and (min-width: 482px) {

	.site-logo .custom-logo {
		max-width: 300px;
		max-height: 100px;
		height: auto;
		width: auto;
	}
}

@media only screen and (max-width: 481px) {

	.site-header.has-logo:not(.has-title-and-tagline).has-menu .site-logo {
		position: absolute;
		padding-top: 15px;
		margin-top: 0;
		top: 0;
	}

	.primary-navigation-open .site-header.has-logo:not(.has-title-and-tagline).has-menu .site-logo {
		display: none;
	}

	.site-header.has-logo:not(.has-title-and-tagline).has-menu .site-logo img {
		max-height: calc(10px + 2em);
	}

	.site-header.has-logo.has-title-and-tagline {
		align-items: flex-start;
	}

	.site-header.has-logo.has-title-and-tagline.has-menu {
		justify-content: space-between;
	}

	.site-header.has-logo.has-title-and-tagline.has-menu .site-branding {
		max-width: calc(100% - 160px);
	}

	.site-header.has-logo.has-title-and-tagline .site-branding {
		margin-right: 0;
	}

	body:not(.primary-navigation-open) .site-header.has-logo.has-title-and-tagline:after {
		display: none;
	}

	body:not(.primary-navigation-open) .site-header.has-logo.has-title-and-tagline .primary-navigation {
		position: relative;
		top: 0;
	}

	body:not(.primary-navigation-open) .site-header.has-logo.has-title-and-tagline .menu-button-container {
		position: relative;
		padding-top: 0;
		margin-top: -10px;
	}

	body:not(.primary-navigation-open) .site-header.has-logo.has-title-and-tagline .menu-button-container #primary-mobile-menu {
		padding-left: 11px;
		padding-right: 11px;
		margin-right: -15px;
	}

	.site-header:not(.has-logo).has-title-and-tagline .site-branding {
		margin-right: 0;
		max-width: calc(100% - 160px);
	}

	.site-header:not(.has-menu) {
		justify-content: center;
	}
}

.site-footer {
	padding-top: 0;
	padding-bottom: 51px;
}

.no-widgets .site-footer {
	margin-top: 180px;
}
@media only screen and (max-width: 481px) {

	.no-widgets .site-footer {
		margin-top: 90px;
	}
}

.site-footer > .site-info {
	padding-top: 30px;
	color: #28303d;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.125rem;
	line-height: 1.7;
	border-top: 3px solid #28303d;
}

.site-footer > .site-info .site-name {
	text-transform: uppercase;
	font-size: 1.5rem;
}

.site-footer > .site-info .privacy-policy {
	margin-top: 15px;
}

.site-footer > .site-info .powered-by {
	margin-top: 15px;
}
@media only screen and (min-width: 822px) {

	.site-footer > .site-info {
		display: flex;
		align-items: center;
	}

	.site-footer > .site-info .site-name {
		margin-right: 15px;
	}

	.site-footer > .site-info .privacy-policy,
	.site-footer > .site-info .powered-by {
		margin-top: initial;
		margin-left: auto;
	}

	.site-footer > .site-info .privacy-policy + .powered-by {
		margin-left: 15px;
	}
}

.site-footer > .site-info a {
	color: #28303d;
}

.site-footer > .site-info a:link {
	color: #28303d;
}

.site-footer > .site-info a:visited {
	color: #28303d;
}

.site-footer > .site-info a:active {
	color: #28303d;
}

.site-footer > .site-info a:hover {
	color: #28303d;
}

.site-footer > .site-info a:focus {
	color: #28303d;
}

.is-dark-theme .site-footer > .site-info a:focus {
	color: #d1e4dd;
}

.has-background-white .site-footer > .site-info a:focus {
	color: #fff;
}

.singular .entry-header {
	border-bottom: 3px solid #28303d;
	padding-bottom: 60px;
	margin-bottom: 90px;
}

.home .entry-header {
	border-bottom: none;
	padding-bottom: 0;
	margin-bottom: 0;
}

.singular .has-post-thumbnail .entry-header {
	border-bottom: none;
	padding-bottom: 39px;
	margin-bottom: 0;
}

.no-results.not-found > *:first-child {
	margin-bottom: 90px;
}

.page-links {
	clear: both;
}

.page-links .post-page-numbers {
	display: inline-block;
	margin-left: 13px;
	margin-right: 13px;
	min-width: 44px;
	min-height: 44px;
}

.page-links .post-page-numbers:first-child {
	margin-left: 0;
}

.entry-title {
	color: #28303d;
	font-size: 2.25rem;
	letter-spacing: normal;
	line-height: 1.3;
	overflow-wrap: break-word;
}

@media only screen and (min-width: 652px) {

	.entry-title {
		font-size: 3rem;
	}
}

.entry-title a {
	color: currentColor;
	text-underline-offset: 0.15em;
}

.entry-title a:hover {
	color: #28303d;
}

.entry-title a:focus {
	color: #39414d;
}

.entry-title a:active {
	color: currentColor;
}

.singular .entry-title {
	font-size: 4rem;
}

@media only screen and (min-width: 652px) {

	.singular .entry-title {
		font-size: 6rem;
	}
}

h1.entry-title {
	line-height: 1.1;
	font-weight: 300;
}

/**
 * Entry Content
 */
.entry-content,
.entry-summary {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.entry-content p {
	word-wrap: break-word;
}

.entry-content > iframe[style] {
	margin: 30px 0 !important;
	max-width: 100% !important;
}

.entry-footer {
	color: #28303d;
	clear: both;
	float: none;
	font-size: 1rem;
	display: block;
}

.entry-footer > span {
	display: inline-block;
}

.entry-footer a {
	color: currentColor;
}

.entry-footer a:hover {
	color: #28303d;
}

.entry-footer a:focus {
	color: #28303d;
}

.entry-footer a:active {
	color: currentColor;
}

.site-main > article > .entry-footer {
	margin-top: 30px;
	padding-top: 20px;
	padding-bottom: 90px;
	border-bottom: 1px solid #28303d;
}

body:not(.single) .site-main > article:last-of-type .entry-footer {
	border-bottom: 1px solid transparent;
}

.single .site-main > article > .entry-footer {
	margin-top: 102px;
	margin-bottom: 102px;
	padding-bottom: 0;
	padding-top: 24px;
	border-top: 3px solid #28303d;
	border-bottom: 1px solid transparent;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	column-gap: 50px;
}

.single .site-main > article > .entry-footer .post-taxonomies,
.single .site-main > article > .entry-footer .full-size-link {
	justify-content: flex-end;
	text-align: right;
}

.single .site-main > article > .entry-footer .full-size-link:first-child:last-child {
	grid-column: span 2;
}

.single .site-main > article > .entry-footer .posted-on,
.single .site-main > article > .entry-footer .byline,
.single .site-main > article > .entry-footer .cat-links,
.single .site-main > article > .entry-footer .tags-links {
	display: block;
}
@media only screen and (max-width: 481px) {

	.single .site-main > article > .entry-footer {
		display: block;
	}

	.single .site-main > article > .entry-footer .full-size-link {
		display: block;
	}

	.single .site-main > article > .entry-footer .post-taxonomies,
	.single .site-main > article > .entry-footer .full-size-link {
		text-align: left;
	}
}

/**
 * Post Thumbnails
 */
.post-thumbnail {
	text-align: center;
}

.post-thumbnail .wp-post-image {
	display: block;
	width: auto;
	max-width: 100%;
	margin-left: auto;
	margin-right: auto;
	margin-top: 60px;
}

/**
 * Author
 */
.author-bio {
	position: relative;
	font-size: 1rem;
	max-width: calc(100vw - 30px);
}
@media only screen and (min-width: 482px) {

	.author-bio {
		max-width: min(calc(100vw - 100px), 610px);
	}
}
@media only screen and (min-width: 822px) {

	.author-bio {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.site-main > article > .author-bio {
	margin-top: 60px;
}

.author-bio.show-avatars .avatar {
	display: inline-block;
	vertical-align: top;
	border-radius: 50%;
}

.author-bio.show-avatars .author-bio-content {
	display: inline-block;
	padding-left: 25px;
	max-width: calc(100vw - 120px);
}
@media only screen and (min-width: 482px) {

	.author-bio.show-avatars .author-bio-content {
		max-width: calc(min(calc(100vw - 4 * 25px), 610px) - 90px);
	}
}
@media only screen and (min-width: 822px) {

	.author-bio.show-avatars .author-bio-content {
		max-width: calc(min(calc(100vw - 8 * 25px), 610px) - 90px);
	}
}

.author-bio .author-bio-content .author-title {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.5rem;
	display: inline;
}

.author-bio .author-bio-content .author-description {
	font-size: 1rem;
	margin-top: 15px;
	margin-bottom: 15px;
}

.page-title {
	font-size: 4rem;
}

@media only screen and (min-width: 652px) {

	.page-title {
		font-size: 6rem;
	}
}

h1.page-title,
h2.page-title {
	font-weight: 300;
}

h1.page-title {
	line-height: 1.1;
}

.page-header {
	border-bottom: 3px solid #28303d;
	padding-bottom: 60px;
}

.archive .content-area .format-aside .entry-content,
.archive .content-area .format-status .entry-content,
.archive .content-area .format-link .entry-content,
.search .content-area .format-aside .entry-content,
.search .content-area .format-status .entry-content,
.search .content-area .format-link .entry-content,
.blog .content-area .format-aside .entry-content,
.blog .content-area .format-status .entry-content,
.blog .content-area .format-link .entry-content {
	font-size: 1.5rem;
}

.archive .format-image .entry-content,
.archive .format-gallery .entry-content,
.archive .format-video .entry-content,
.search .format-image .entry-content,
.search .format-gallery .entry-content,
.search .format-video .entry-content,
.blog .format-image .entry-content,
.blog .format-gallery .entry-content,
.blog .format-video .entry-content {
	margin-top: 60px;
}

.archive .entry-footer .cat-links,
.archive .entry-footer .tags-links,
.search .entry-footer .cat-links,
.search .entry-footer .tags-links,
.blog .entry-footer .cat-links,
.blog .entry-footer .tags-links {
	display: block;
}

.archive.logged-in .entry-footer .posted-on,
.search.logged-in .entry-footer .posted-on,
.blog.logged-in .entry-footer .posted-on {
	margin-right: 10px;
}

.archive-description {
	margin-top: 30px;
	font-size: 2.25rem;
	line-height: 1.3;
}

@media only screen and (min-width: 652px) {

	.archive-description {
		font-size: 2.5rem;
	}
}

.error404 main p {
	font-size: 1.5rem;
	margin-bottom: 50px;
}

.search-no-results .page-content {
	margin-top: 90px;
}

/**
 * Comments Wrapper
 */
.comments-area > * {
	margin-top: 30px;
	margin-bottom: 30px;
}

.comments-area > *:first-child {
	margin-top: 0;
}

.comments-area > *:last-child {
	margin-bottom: 0;
}

.comments-area.show-avatars .avatar {
	border-radius: 50%;
	position: absolute;
	top: 10px;
}

.comments-area.show-avatars .fn {
	display: inline-block;
	padding-left: 85px;
}

.comments-area.show-avatars .comment-metadata {
	padding: 8px 0 9px 85px;
}

/**
 * Comment Title
 */
.comments-title {
	font-size: 2.25rem;
	letter-spacing: normal;
}
@media only screen and (min-width: 652px) {

	.comments-title {
		font-size: 3rem;
	}
}

.comment-reply-title {
	font-size: 2.25rem;
	letter-spacing: normal;
}
@media only screen and (min-width: 652px) {

	.comment-reply-title {
		font-size: 3rem;
	}
}

.comment-reply-title {
	display: flex;
	justify-content: space-between;
}

.comment-reply-title small a {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1rem;
	font-style: normal;
	font-weight: normal;
	letter-spacing: normal;
}

/* Nested comment reply title*/
.comment .comment-respond .comment-reply-title {
	font-size: 1.5rem;
}

/**
 * Comment Lists
 */
.comment-list {
	padding-left: 0;
	list-style: none;
}

.comment-list > li {
	margin-top: 30px;
	margin-bottom: 30px;
}

.comment-list .children {
	list-style: none;
	padding-left: 0;
}

.comment-list .children > li {
	margin-top: 30px;
	margin-bottom: 30px;
}

@media only screen and (min-width: 482px) {

	.comment-list .depth-2,
	.comment-list .depth-3 {
		padding-left: 100px;
	}
}

/**
 * Comment Meta
 */
.comment-meta .comment-author {
	line-height: 1.3;
	margin-bottom: 5px;
}
@media only screen and (min-width: 482px) {

	.comment-meta .comment-author {
		margin-bottom: 0;
		padding-right: 0;
	}
}

.comment-meta .comment-author .fn {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-weight: normal;
	font-size: 1.5rem;
	hyphens: auto;
	word-wrap: break-word;
	word-break: break-word;
}

.comment-meta .comment-metadata {
	color: #28303d;
	font-size: 1rem;
	padding: 8px 0 9px 0;
}

.comment-meta .comment-metadata .edit-link {
	margin-left: 25px;
}
@media only screen and (min-width: 482px) {

	.comment-meta {
		margin-right: inherit;
	}

	.comment-meta .comment-author {
		max-width: inherit;
	}
}

.reply {
	font-size: 1.125rem;
	line-height: 1.3;
}

.bypostauthor {
	display: block;
}

.says {
	display: none;
}

.pingback .url,
.trackback .url {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.comment-body {
	position: relative;
	margin-bottom: 51px;
}

.comment-body > * {
	margin-top: 30px;
	margin-bottom: 30px;
}

.comment-body .reply {
	margin: 0;
}

.comment-content {
	word-wrap: break-word;
}

.pingback .comment-body,
.trackback .comment-body {
	margin-top: 30px;
	margin-bottom: 30px;
}

.comment-respond {
	margin-top: 30px;
}

.comment-respond > * {
	margin-top: 20px;
	margin-bottom: 20px;
}

.comment-respond > *:first-child {
	margin-top: 0;
}

.comment-respond > *:last-child {
	margin-bottom: 0;
}

.comment-respond > *:last-child.comment-form {
	margin-bottom: 30px;
}

.comment-author {
	padding-top: 3px;
}

.comment-author .url {
	color: currentColor;
}

.comment-form {
	display: flex;
	flex-wrap: wrap;
}

.comment-form > * {
	flex-basis: 100%;
}

.comment-form .comment-notes {
	font-size: 1.125rem;
}

.comment-form .comment-form-url,
.comment-form .comment-form-comment {
	width: 100%;
}

.comment-form .comment-form-author,
.comment-form .comment-form-email {
	flex-basis: 0;
	flex-grow: 1;
}
@media only screen and (max-width: 481px) {

	.comment-form .comment-form-author,
	.comment-form .comment-form-email {
		flex-basis: 100%;
	}
}

.comment-form .comment-form-cookies-consent > label {
	font-size: 1rem;
	font-weight: normal;
}

.comment-form .comment-notes {
	font-size: 1rem;
	font-weight: normal;
}

.comment-form > p {
	margin-bottom: 20px;
}

.comment-form > p:first-of-type {
	margin-top: 0;
}

.comment-form > p:last-of-type {
	margin-bottom: 0;
}

.comment-form > p label {
	display: block;
	font-size: 1.125rem;
	margin-bottom: 10px;
	width: 100%;
	font-weight: 500;
}

.comment-form > p input[type=email] {
	display: block;
	font-size: 1.125rem;
	margin-bottom: 10px;
	width: 100%;
	font-weight: 500;
}

.comment-form > p input[type=text] {
	display: block;
	font-size: 1.125rem;
	margin-bottom: 10px;
	width: 100%;
	font-weight: 500;
}

.comment-form > p input[type=url] {
	display: block;
	font-size: 1.125rem;
	margin-bottom: 10px;
	width: 100%;
	font-weight: 500;
}

.comment-form > p textarea {
	display: block;
	font-size: 1.125rem;
	margin-bottom: 10px;
	width: 100%;
	font-weight: 500;
}

.comment-form > p.comment-form-cookies-consent {
	display: flex;
}
@media only screen and (min-width: 482px) {

	.comment-form > p.comment-form-author {
		margin-right: 38px;
	}

	.comment-form > p.comment-notes,
	.comment-form > p.logged-in-as {
		display: block;
	}
}

.menu-button-container {
	display: none;
	justify-content: space-between;
	position: absolute;
	right: 0;
	padding-top: 15px;
	padding-bottom: 8px;
}
@media only screen and (max-width: 481px) {

	.menu-button-container {
		display: flex;
	}
}

.menu-button-container #primary-mobile-menu {
	display: flex;
	margin-left: auto;
	padding: 10px 15px;
	font-size: 1rem;
	font-weight: 500;
	background-color: transparent;
	border: none;
	color: #28303d;
}

.menu-button-container #primary-mobile-menu .dropdown-icon {
	display: flex;
	align-items: center;
}

.menu-button-container #primary-mobile-menu .dropdown-icon .svg-icon {
	margin-left: 5px;
}

.menu-button-container #primary-mobile-menu .dropdown-icon.open .svg-icon {
	position: relative;
	top: -1px;
}

.menu-button-container #primary-mobile-menu .dropdown-icon.close {
	display: none;
}

.menu-button-container #primary-mobile-menu[aria-expanded*=true] .dropdown-icon.open {
	display: none;
}

.menu-button-container #primary-mobile-menu[aria-expanded*=true] .dropdown-icon.close {
	display: flex;
}

.has-logo.has-title-and-tagline .menu-button-container #primary-mobile-menu[aria-expanded*=true] .dropdown-icon.close {
	animation-name: twentytwentyone-close-button-transition;
	animation-duration: 0.3s;
}

.primary-navigation-open .menu-button-container {
	width: 100%;
	z-index: 500;
	background-color: #d1e4dd;
}

.primary-navigation-open .menu-button-container #primary-mobile-menu {
	position: static;
}

.primary-navigation {
	position: absolute;
	top: 0;
	right: 0;
	color: #28303d;
	font-size: 1.25rem;
	line-height: 1.15;
	margin-top: 0;
	margin-bottom: 0;
}

.primary-navigation > .primary-menu-container {
	position: fixed;
	visibility: hidden;
	opacity: 0;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	padding-top: calc(2rem + 47px);
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 25px;
	background-color: #d1e4dd;
	transform: translateY(30px);
}
@media (prefers-reduced-motion: no-preference) {

	.primary-navigation > .primary-menu-container {
		transition: all 0.15s ease-in-out;
	}
}
@media only screen and (max-width: 481px) {

	.primary-navigation > .primary-menu-container {
		height: 100vh;
		z-index: 499;
		overflow-x: hidden;
		overflow-y: auto;
		border: 2px solid transparent;
	}

	.has-logo.has-title-and-tagline .primary-navigation > .primary-menu-container {
		position: fixed;
		transform: translateY(0) translateX(100%);
	}

	.admin-bar .has-logo.has-title-and-tagline .primary-navigation > .primary-menu-container {
		top: 32px;
	}
	@media only screen and (max-width: 782px) {

		.admin-bar .has-logo.has-title-and-tagline .primary-navigation > .primary-menu-container {
			top: 46px;
		}
	}

	.admin-bar .primary-navigation > .primary-menu-container {
		height: calc(100vh - 32px);
	}
	@media only screen and (max-width: 782px) {

		.admin-bar .primary-navigation > .primary-menu-container {
			height: calc(100vh - 46px);
		}
	}

	.primary-navigation > .primary-menu-container:focus {
		border: 2px solid #28303d;
	}
}
@media only screen and (max-width: 481px) {

	.primary-navigation-open .primary-navigation {
		width: 100%;
		position: fixed;
		z-index: 2;
	}
}

.primary-navigation-open .primary-navigation > .primary-menu-container {
	position: absolute;
	visibility: visible;
	opacity: 1;
	transform: translateY(0);
}
@media only screen and (max-width: 481px) {

	.primary-navigation-open .has-logo.has-title-and-tagline .primary-navigation > .primary-menu-container {
		transform: translateX(0) translateY(0);
	}
}
@media only screen and (min-width: 482px) {

	.primary-navigation {
		position: relative;
		margin-left: auto;
	}

	.primary-navigation > .primary-menu-container {
		visibility: visible;
		opacity: 1;
		position: relative;
		padding: 0;
		background-color: transparent;
		overflow: initial;
		transform: none;
	}

	.primary-navigation #toggle-menu {
		display: none;
	}

	.primary-navigation > .primary-menu-container ul > li .sub-menu-toggle[aria-expanded=false] ~ ul {
		display: none;
	}

	.admin-bar .primary-navigation {
		top: initial;
	}

	.admin-bar .primary-navigation > .primary-menu-container {
		top: initial;
	}
}

.primary-navigation > div > .menu-wrapper {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	list-style: none;
	margin: 0;
	max-width: none;
	padding-left: 0;
	position: relative;
}
@media only screen and (max-width: 481px) {

	.primary-navigation > div > .menu-wrapper {
		padding-bottom: 100px;
	}

	.primary-navigation > div > .menu-wrapper ul {
		padding-left: 0;
	}
}

.primary-navigation > div > .menu-wrapper li {
	display: block;
	position: relative;
	width: 100%;
}
@media only screen and (min-width: 482px) {

	.primary-navigation > div > .menu-wrapper li {
		margin: 0;
		width: inherit;
	}

	.primary-navigation > div > .menu-wrapper li:last-child {
		margin-right: 0;
	}
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle {
	display: flex;
	height: calc(27px + 1em);
	width: 44px;
	padding: 0;
	justify-content: center;
	align-items: center;
	background: transparent;
	color: currentColor;
	border: none;
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle:focus {
	outline: 2px solid #28303d;
}
@media only screen and (max-width: 481px) {

	.primary-navigation > div > .menu-wrapper .sub-menu-toggle {
		display: none;
	}
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle .icon-plus,
.primary-navigation > div > .menu-wrapper .sub-menu-toggle .icon-minus {
	height: 100%;
	display: flex;
	align-items: center;
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle .icon-plus svg,
.primary-navigation > div > .menu-wrapper .sub-menu-toggle .icon-minus svg {
	margin-top: -1px;
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle .icon-minus {
	display: none;
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle[aria-expanded=true] .icon-minus {
	display: flex;
}

.primary-navigation > div > .menu-wrapper .sub-menu-toggle[aria-expanded=true] .icon-plus {
	display: none;
}

.primary-navigation > div > .menu-wrapper > li > .sub-menu {
	position: relative;
}
@media only screen and (min-width: 482px) and (prefers-reduced-motion: no-preference) {

	.primary-navigation > div > .menu-wrapper > li > .sub-menu {
		transition: all 0.5s ease;
	}
}
@media only screen and (min-width: 482px) {

	.primary-navigation > div > .menu-wrapper > li > .sub-menu {
		left: 0;
		margin: 0;
		min-width: max-content;
		position: absolute;
		top: 100%;
		padding-top: 3px;
		z-index: 88888;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu:before {
		content: "";
		display: block;
		position: absolute;
		width: 0;
		top: -10px;
		left: 25px;
		border-style: solid;
		border-color: #28303d transparent;
		border-width: 0 7px 10px 7px;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu:after {
		content: "";
		display: block;
		position: absolute;
		width: 0;
		top: -10px;
		left: 25px;
		border-style: solid;
		border-color: #28303d transparent;
		border-width: 0 7px 10px 7px;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu:after {
		top: -9px;
		border-color: #d1e4dd transparent;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu li {
		background: #d1e4dd;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-left {

		/* rtl:ignore */
		left: 0;

		/* rtl:ignore */
		right: auto;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-left:before {

		/* rtl:ignore */
		left: 25px;

		/* rtl:ignore */
		right: auto;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-left:after {

		/* rtl:ignore */
		left: 25px;

		/* rtl:ignore */
		right: auto;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-right {

		/* rtl:ignore */
		right: 0;

		/* rtl:ignore */
		left: auto;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-right:before {

		/* rtl:ignore */
		left: auto;

		/* rtl:ignore */
		right: 25px;
	}

	.primary-navigation > div > .menu-wrapper > li > .sub-menu.submenu-reposition-right:after {

		/* rtl:ignore */
		left: auto;

		/* rtl:ignore */
		right: 25px;
	}
}

.primary-navigation .primary-menu > .menu-item:hover > a {
	color: #28303d;
}
@media only screen and (min-width: 482px) {

	.primary-navigation .primary-menu-container {
		margin-right: -13px;
		margin-left: -13px;
	}

	.primary-navigation .primary-menu-container > ul > .menu-item {
		display: flex;
	}

	.primary-navigation .primary-menu-container > ul > .menu-item > a {
		padding-left: 13px;
		padding-right: 13px;
	}

	.primary-navigation .primary-menu-container > ul > .menu-item > a + .sub-menu-toggle {
		margin-left: -8px;
	}
}

.primary-navigation a {
	display: block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.125rem;
	font-weight: normal;
	padding: 13px 0;
	text-decoration: none;
}
@media only screen and (min-width: 482px) {

	.primary-navigation a {
		display: block;
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
		font-size: 1.25rem;
		font-weight: normal;
	}
}

.primary-navigation a + svg {
	fill: #28303d;
}

.primary-navigation a:hover {
	color: #28303d;
}

.primary-navigation a:link {
	color: #28303d;
}

.primary-navigation a:visited {
	color: #28303d;
}

.primary-navigation a:hover {
	text-decoration: underline;
	text-decoration-style: dotted;
}

.primary-navigation a:focus {
	position: relative;
	z-index: 99999;
	outline-offset: 0;
	text-decoration-thickness: 2px;
}

.primary-navigation .current-menu-item > a:first-child,
.primary-navigation .current_page_item > a:first-child {
	text-decoration: underline;
	text-decoration-style: solid;
}

.primary-navigation .current-menu-item > a:first-child:hover,
.primary-navigation .current_page_item > a:first-child:hover {
	text-decoration: underline;
	text-decoration-style: dotted;
}

.primary-navigation .sub-menu {
	margin: 0;
	padding: 0;
	list-style: none;
	margin-left: 13px;
	border: 1px solid #28303d;
}

.primary-navigation .sub-menu .sub-menu {
	border: none;
}
@media only screen and (min-width: 482px) {

	.primary-navigation .sub-menu > .menu-item > .sub-menu {
		padding: 0;
	}
}
@media only screen and (max-width: 481px) {

	.primary-navigation .sub-menu .menu-item:last-child {
		margin-bottom: 0;
	}
}

.primary-navigation .sub-menu .menu-item > a {
	padding: 17px 13px;
	display: block;
	font-size: 1.125rem;
	font-style: normal;
}
@media only screen and (min-width: 482px) {

	.primary-navigation .sub-menu .menu-item > a {
		font-size: 1rem;
		font-style: normal;
	}
}

.primary-navigation .menu-item-has-children > .svg-icon {
	display: none;
}
@media only screen and (min-width: 482px) {

	.primary-navigation .menu-item-has-children > .svg-icon {
		display: inline-block;
		height: 100%;
	}

	.primary-navigation .menu-item-has-children .sub-menu .svg-icon {
		display: none;
	}
}

.primary-navigation .menu-item-description {
	display: block;
	clear: both;
	font-size: 1rem;
	text-transform: none;
	line-height: 1.7;
}

.primary-navigation .menu-item-description > span {
	display: inline-block;
}

@media only screen and (max-width: 481px) {

	.lock-scrolling .site {
		position: fixed;
		max-width: 100%;
		width: 100%;
	}
}
@keyframes twentytwentyone-close-button-transition {

	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

.footer-navigation {
	margin-top: 60px;
	margin-bottom: 30px;
	color: #28303d;
	font-size: 1rem;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.footer-navigation-wrapper {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	list-style: none;
	padding-left: 0;
}

.footer-navigation-wrapper li {
	display: inline;
	line-height: 3;
}

.footer-navigation-wrapper li a {
	padding: 17px 13px;
	color: #28303d;
}

.footer-navigation-wrapper li a:link {
	color: #28303d;
}

.footer-navigation-wrapper li a:visited {
	color: #28303d;
}

.footer-navigation-wrapper li a:active {
	color: #28303d;
}

.footer-navigation-wrapper li a:hover {
	text-decoration: underline;
	text-decoration-style: dotted;
	text-decoration-skip-ink: none;
	color: #28303d;
}

.is-dark-theme .footer-navigation-wrapper li a:focus .svg-icon {
	fill: #d1e4dd;
}

.has-background-white .footer-navigation-wrapper li a:focus .svg-icon {
	fill: #fff;
}

.footer-navigation-wrapper li .svg-icon {
	vertical-align: middle;
	fill: #28303d;
}

.footer-navigation-wrapper li .svg-icon:hover {
	transform: scale(1.1);
}
@media (prefers-reduced-motion: no-preference) {

	.footer-navigation-wrapper li .svg-icon {
		transition: transform 0.1s ease;
	}
}

.footer-navigation-wrapper .sub-menu-toggle,
.footer-navigation-wrapper .menu-item-description {
	display: none;
}

/* Next/Previous navigation */
.navigation,
.navigation a {
	color: #28303d;
}

.navigation a {
	text-decoration: none;
}

.navigation a:hover {
	color: #28303d;
	text-decoration: underline;
	text-decoration-style: dotted;
}

.navigation a:focus {
	color: #39414d;
}

.navigation a:active {
	color: #28303d;
}

.navigation .nav-links > * {
	min-width: 44px;
	min-height: 44px;
}

.navigation .nav-links .nav-next a,
.navigation .nav-links .nav-previous a {
	display: flex;
	flex-direction: column;
}

.navigation .nav-links .dots {
	text-align: center;
}
@media only screen and (min-width: 592px) {

	.navigation .nav-links {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}

	.navigation .nav-links .nav-next,
	.navigation .nav-links .nav-previous {
		flex: 0 1 auto;
		margin-bottom: inherit;
		margin-top: inherit;
		max-width: calc(50% - 10px);
	}

	.navigation .nav-links .nav-next {
		text-align: right;
	}
}

.navigation .svg-icon {
	display: inline-block;
	fill: currentColor;
	vertical-align: middle;
	position: relative;
}

.navigation .nav-previous .svg-icon,
.navigation .prev .svg-icon {
	top: -2px;
	margin-right: 5px;
}

.navigation .nav-next .svg-icon,
.navigation .next .svg-icon {
	top: -1px;
	margin-left: 5px;
}

.post-navigation {
	margin: 30px auto;
}
@media only screen and (min-width: 822px) {

	.post-navigation {
		margin: 30px auto;
	}
}

.post-navigation .meta-nav {
	line-height: 1.7;
	color: #28303d;
}

.post-navigation .post-title {
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.5rem;
	font-weight: 600;
	line-height: 1.3;
}
@media only screen and (min-width: 822px) {

	.post-navigation .post-title {
		margin: 5px 29px 0;
	}
}
@media only screen and (min-width: 482px) {

	.post-navigation .nav-links {
		justify-content: space-between;
	}
}

.post-navigation .nav-next,
.post-navigation .nav-previous {
	margin-top: 30px;
	margin-bottom: 30px;
}

.post-navigation .nav-next:first-child,
.post-navigation .nav-previous:first-child {
	margin-top: 0;
}

.post-navigation .nav-next:last-child,
.post-navigation .nav-previous:last-child {
	margin-bottom: 0;
}

.pagination,
.comments-pagination {
	border-top: 3px solid #28303d;
	padding-top: 30px;
	margin: 30px auto;
}
@media only screen and (min-width: 822px) {

	.pagination,
	.comments-pagination {
		margin: 30px auto;
	}
}

.pagination .nav-links,
.comments-pagination .nav-links {
	margin-top: -30px;
}

.pagination .nav-links a:hover {
	color: #28303d;
}

.comments-pagination .nav-links a:hover {
	color: #28303d;
}

.is-dark-theme .pagination .nav-links a:active {
	color: #d1e4dd;
}

.is-dark-theme .pagination .nav-links a:hover:active {
	color: #d1e4dd;
}

.is-dark-theme .pagination .nav-links a:hover:focus {
	color: #d1e4dd;
}

.is-dark-theme .comments-pagination .nav-links a:active {
	color: #d1e4dd;
}

.is-dark-theme .comments-pagination .nav-links a:hover:active {
	color: #d1e4dd;
}

.is-dark-theme .comments-pagination .nav-links a:hover:focus {
	color: #d1e4dd;
}

.has-background-white .pagination .nav-links a:active {
	color: #fff;
}

.has-background-white .pagination .nav-links a:hover:active {
	color: #fff;
}

.has-background-white .pagination .nav-links a:hover:focus {
	color: #fff;
}

.has-background-white .comments-pagination .nav-links a:active {
	color: #fff;
}

.has-background-white .comments-pagination .nav-links a:hover:active {
	color: #fff;
}

.has-background-white .comments-pagination .nav-links a:hover:focus {
	color: #fff;
}

.pagination .nav-links > * {
	color: #28303d;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.5rem;
	font-weight: normal;
	margin-top: 30px;
	margin-left: 13px;
	margin-right: 13px;
}

.comments-pagination .nav-links > * {
	color: #28303d;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1.5rem;
	font-weight: normal;
	margin-top: 30px;
	margin-left: 13px;
	margin-right: 13px;
}

.pagination .nav-links > *.current,
.comments-pagination .nav-links > *.current {
	text-decoration: underline;
}

.pagination .nav-links > *:not(.dots):not(.current):hover,
.comments-pagination .nav-links > *:not(.dots):not(.current):hover {
	text-decoration-style: dotted;
}

.pagination .nav-links > *:first-child,
.comments-pagination .nav-links > *:first-child {
	margin-left: 0;
}

.pagination .nav-links > *:last-child,
.comments-pagination .nav-links > *:last-child {
	margin-right: 0;
}

.pagination .nav-links > *.next,
.comments-pagination .nav-links > *.next {
	margin-left: auto;
}

.pagination .nav-links > *.prev,
.comments-pagination .nav-links > *.prev {
	margin-right: auto;
}
@media only screen and (max-width: 821px) {

	.pagination .nav-links,
	.comments-pagination .nav-links {
		display: flex;
		flex-wrap: wrap;
	}

	.pagination .page-numbers,
	.comments-pagination .page-numbers {
		display: none;
	}

	.pagination .page-numbers.prev,
	.pagination .page-numbers.next,
	.comments-pagination .page-numbers.prev,
	.comments-pagination .page-numbers.next {
		display: inline-block;
		flex: 0 1 auto;
	}
}
@media only screen and (max-width: 481px) {

	.pagination .nav-short,
	.comments-pagination .nav-short {
		display: none;
	}
}

.comments-pagination {
	padding-top: 20px;
	margin: 90px auto;
}
@media only screen and (min-width: 822px) {

	.comments-pagination {
		margin: 90px auto 120px auto;
	}
}

.comments-pagination .nav-links > * {
	font-size: 1.25rem;
}

.widget-area {
	margin-top: 180px;
	padding-bottom: 10px;
	color: #28303d;
	font-size: 1.125rem;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}
@media only screen and (min-width: 652px) {

	.widget-area {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		column-gap: 50px;
	}
}
@media only screen and (min-width: 1024px) {

	.widget-area {
		grid-template-columns: repeat(3, 1fr);
	}
}
@media only screen and (max-width: 481px) {

	.widget-area {
		margin-top: 90px;
	}
}

.widget-area .wp-block-social-links.alignright {
	margin-top: 30px;
	justify-content: flex-end;
}

.widget-area .wp-block-social-links.alignleft {
	margin-top: 30px;
}

.widget-area:after {
	content: "";
	display: table;
	clear: both;
}

.widget h1,
.widget h2,
.widget h3,
.widget h4,
.widget h5,
.widget h6 {
	font-weight: 700;
	line-height: 1.4;
}

.widget h1 {
	font-size: 1.25rem;
}

.widget h2 {
	font-size: 1.125rem;
}

.widget h3,
.widget h4,
.widget h5,
.widget h6 {
	font-size: 1rem;
}

.widget ul {
	list-style-type: none;
	padding: 0;
}

.widget ul li {
	line-height: 1.9;
}

.widget ul.sub-menu,
.widget ul.children {
	margin-left: 13px;
}

.widget ul .sub-menu-toggle {
	display: none;
}

.widget a {
	color: #28303d;
	text-decoration: underline;
	text-decoration-style: solid;
	text-decoration-color: currentColor;
}

.widget a:link {
	color: #28303d;
}

.widget a:visited {
	color: #28303d;
}

.widget a:active {
	color: #28303d;
}

.widget a:hover {
	color: #28303d;
	text-decoration-style: dotted;
}

.search-form {
	display: flex;
	flex-wrap: wrap;
	margin: auto;
	max-width: calc(100vw - 30px);
}

@media only screen and (min-width: 482px) {

	.search-form {
		max-width: min(calc(100vw - 100px), 610px);
	}
}

@media only screen and (min-width: 822px) {

	.search-form {
		max-width: min(calc(100vw - 200px), 610px);
	}
}

.search-form > label {
	width: 100%;
	margin-bottom: 0;
	font-weight: 500;
}

.search-form .search-field {
	flex-grow: 1;
	max-width: inherit;
	margin-top: 10px;
	margin-right: 17px;
}

.search-form .search-submit {
	margin-top: 10px;
	margin-left: 10px;
}

.widget_search > .search-form .search-field {
	margin-right: -3px;
	-webkit-appearance: none;
	margin-bottom: 15px;
}

.widget_search > .search-form .search-submit {
	margin-left: 0;
	margin-bottom: 15px;
}

.widget_rss a.rsswidget .rss-widget-icon {
	display: none;
}

/* Category 07 is for any utility classes that are not assigned to a specific component. */
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	-webkit-clip-path: inset(50%);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
	word-break: normal;
}

.skip-link:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	-webkit-clip-path: none;
	clip-path: none;
	color: #21759b;
	display: block;
	font-size: 0.875rem;
	font-weight: 700;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000;
}

/* Do not show the outline on the skip link target. */
#content[tabindex="-1"]:focus {
	outline: 0;
}

.has-black-color[class] {
	color: #000;
}

.has-black-color[class] > [class*=__inner-container] {
	color: #000;
}

.has-gray-color[class] {
	color: #39414d;
}

.has-gray-color[class] > [class*=__inner-container] {
	color: #39414d;
}

.has-dark-gray-color[class] {
	color: #28303d;
}

.has-dark-gray-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-green-color[class] {
	color: #d1e4dd;
}

.has-green-color[class] > [class*=__inner-container] {
	color: #d1e4dd;
}

.has-blue-color[class] {
	color: #d1dfe4;
}

.has-blue-color[class] > [class*=__inner-container] {
	color: #d1dfe4;
}

.has-purple-color[class] {
	color: #d1d1e4;
}

.has-purple-color[class] > [class*=__inner-container] {
	color: #d1d1e4;
}

.has-red-color[class] {
	color: #e4d1d1;
}

.has-red-color[class] > [class*=__inner-container] {
	color: #e4d1d1;
}

.has-orange-color[class] {
	color: #e4dad1;
}

.has-orange-color[class] > [class*=__inner-container] {
	color: #e4dad1;
}

.has-yellow-color[class] {
	color: #eeeadd;
}

.has-yellow-color[class] > [class*=__inner-container] {
	color: #eeeadd;
}

.has-white-color[class] {
	color: #fff;
}

.has-white-color[class] > [class*=__inner-container] {
	color: #fff;
}

.has-background a,
.has-background p,
.has-background h1,
.has-background h2,
.has-background h3,
.has-background h4,
.has-background h5,
.has-background h6 {
	color: currentColor;
}

.has-black-background-color[class] {
	background-color: #000;
}

.has-black-background-color[class] > [class*=__inner-container] {
	background-color: #000;
}

.has-dark-gray-background-color[class] {
	background-color: #28303d;
}

.has-dark-gray-background-color[class] > [class*=__inner-container] {
	background-color: #28303d;
}

.has-gray-background-color[class] {
	background-color: #39414d;
}

.has-gray-background-color[class] > [class*=__inner-container] {
	background-color: #39414d;
}

.has-light-gray-background-color[class] {
	background-color: #f0f0f0;
}

.has-light-gray-background-color[class] > [class*=__inner-container] {
	background-color: #f0f0f0;
}

.has-green-background-color[class] {
	background-color: #d1e4dd;
}

.has-green-background-color[class] > [class*=__inner-container] {
	background-color: #d1e4dd;
}

.has-blue-background-color[class] {
	background-color: #d1dfe4;
}

.has-blue-background-color[class] > [class*=__inner-container] {
	background-color: #d1dfe4;
}

.has-purple-background-color[class] {
	background-color: #d1d1e4;
}

.has-purple-background-color[class] > [class*=__inner-container] {
	background-color: #d1d1e4;
}

.has-red-background-color[class] {
	background-color: #e4d1d1;
}

.has-red-background-color[class] > [class*=__inner-container] {
	background-color: #e4d1d1;
}

.has-orange-background-color[class] {
	background-color: #e4dad1;
}

.has-orange-background-color[class] > [class*=__inner-container] {
	background-color: #e4dad1;
}

.has-yellow-background-color[class] {
	background-color: #eeeadd;
}

.has-yellow-background-color[class] > [class*=__inner-container] {
	background-color: #eeeadd;
}

.has-white-background-color[class] {
	background-color: #fff;
}

.has-white-background-color[class] > [class*=__inner-container] {
	background-color: #fff;
}

.has-background:not(.has-text-color).has-black-background-color[class] {
	color: #fff;
}

.has-background:not(.has-text-color).has-gray-background-color[class] {
	color: #fff;
}

.has-background:not(.has-text-color).has-dark-gray-background-color[class] {
	color: #fff;
}

.has-background:not(.has-text-color).has-black-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-gray-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-dark-gray-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-green-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-blue-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-purple-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-red-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-orange-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-yellow-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-white-background-color[class] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-green-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-blue-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-purple-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-red-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-orange-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-yellow-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-background:not(.has-text-color).has-white-background-color[class] > [class*=__inner-container] {
	color: #28303d;
}

.has-purple-to-yellow-gradient-background {
	background: linear-gradient(160deg, #d1d1e4, #eeeadd);
}

.has-yellow-to-purple-gradient-background {
	background: linear-gradient(160deg, #eeeadd, #d1d1e4);
}

.has-green-to-yellow-gradient-background {
	background: linear-gradient(160deg, #d1e4dd, #eeeadd);
}

.has-yellow-to-green-gradient-background {
	background: linear-gradient(160deg, #eeeadd, #d1e4dd);
}

.has-red-to-yellow-gradient-background {
	background: linear-gradient(160deg, #e4d1d1, #eeeadd);
}

.has-yellow-to-red-gradient-background {
	background: linear-gradient(160deg, #eeeadd, #e4d1d1);
}

.has-purple-to-red-gradient-background {
	background: linear-gradient(160deg, #d1d1e4, #e4d1d1);
}

.has-red-to-purple-gradient-background {
	background: linear-gradient(160deg, #e4d1d1, #d1d1e4);
}

header *,
main *,
footer * {
	max-width: unset;
}

html,
body,
div,
header,
nav,
article,
figure,
hr,
main,
section,
footer {
	max-width: none;
}

.is-IE.is-dark-theme {
	color: #fff;
}

.is-IE.is-dark-theme *,
.is-IE.is-dark-theme a,
.is-IE.is-dark-theme .site-description,
.is-IE.is-dark-theme .entry-title,
.is-IE.is-dark-theme .entry-footer,
.is-IE.is-dark-theme .widget-area,
.is-IE.is-dark-theme .post-navigation .meta-nav,
.is-IE.is-dark-theme .footer-navigation-wrapper li a:link,
.is-IE.is-dark-theme .site-footer > .site-info,
.is-IE.is-dark-theme .site-footer > .site-info a,
.is-IE.is-dark-theme .site-footer > .site-info a:visited {
	color: #fff;
}

.is-IE.is-dark-theme .sub-menu-toggle svg,
.is-IE.is-dark-theme .sub-menu-toggle path,
.is-IE.is-dark-theme .post-navigation .meta-nav svg,
.is-IE.is-dark-theme .post-navigation .meta-nav path {
	fill: #fff;
}

.is-IE.is-dark-theme .primary-navigation > div > .menu-wrapper > li > .sub-menu li {
	background: #000;
}
@media only screen and (max-width: 481px) {

	.is-IE.is-dark-theme.primary-navigation-open .primary-navigation > .primary-menu-container,
	.is-IE.is-dark-theme.primary-navigation-open .menu-button-container {
		background-color: #000;
	}
}

.is-IE.is-dark-theme .skip-link:focus {
	color: #21759b;
}

.is-IE .navigation .nav-links {
	display: block;
}

.is-IE .post-thumbnail .wp-post-image {
	min-width: auto;
}
