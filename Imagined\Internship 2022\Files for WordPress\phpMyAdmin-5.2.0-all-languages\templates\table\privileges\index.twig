{% if is_superuser %}
  <form id="usersForm" action="{{ url('/server/privileges') }}">
    {{ get_hidden_inputs(db, table) }}

    <fieldset class="pma-fieldset">
      <legend>
        {{ get_icon('b_usrcheck') }}
        {{ 'Users having access to "%s"'|trans|format('<a href="' ~ table_url ~ get_common({
          'db': db,
          'table': table
        }, '&') ~ '">' ~ db|escape('html') ~ '.' ~ table|escape('html') ~ '</a>')|raw }}
      </legend>

      <div class="table-responsive-md jsresponsive">
        <table class="table table-light table-striped table-hover w-auto">
          <thead class="table-light">
            <tr>
              <th></th>
              <th>{% trans 'User name' %}</th>
              <th>{% trans 'Host name' %}</th>
              <th>{% trans 'Type' %}</th>
              <th>{% trans 'Privileges' %}</th>
              <th>{% trans 'Grant' %}</th>
              <th colspan="2">{% trans 'Action' %}</th>
            </tr>
          </thead>

          <tbody>
            {% for privilege in privileges %}
              {% set privileges_amount = privilege.privileges|length %}
              <tr>
                <td{% if privileges_amount > 1 %} class="align-middle" rowspan="{{ privileges_amount }}"{% endif %}>
                  <input type="checkbox" class="checkall" name="selected_usr[]" id="checkbox_sel_users_{{ loop.index0 }}" value="
                    {{- privilege.user ~ '&amp;#27;' ~ privilege.host }}">
                </td>
                <td{% if privileges_amount > 1 %} class="align-middle" rowspan="{{ privileges_amount }}"{% endif %}>
                  {% if privilege.user is empty %}
                    <span class="text-danger">{% trans 'Any' %}</span>
                  {% else %}
                    {{ privilege.user }}
                  {% endif %}
                </td>
                <td{% if privileges_amount > 1 %} class="align-middle" rowspan="{{ privileges_amount }}"{% endif %}>
                  {{ privilege.host }}
                </td>
                {% for priv in privilege.privileges %}
                  <td>
                    {% if priv.type == 'g' %}
                      {% trans 'global' %}
                    {% elseif priv.type == 'd' %}
                      {% if priv.database == db|replace({'_': '\\_', '%': '\\%'}) %}
                        {% trans 'database-specific' %}
                      {% else %}
                        {% trans 'wildcard' %}: <code>{{ priv.database }}</code>
                      {% endif %}
                    {% elseif priv.type == 't' %}
                      {% trans 'table-specific' %}
                    {% elseif priv.type == 'r' %}
                      {% trans 'routine' %}
                    {% endif %}
                  </td>
                  <td>
                    <code>
                      {% if priv.type == 'r' %}
                        {{ priv.routine }}
                        ({{ priv.privileges|join(', ')|upper }})
                      {% else %}
                        {{ priv.privileges|join(', ')|raw }}
                      {% endif %}
                    </code>
                  </td>
                  <td>
                    {{ priv.has_grant ? 'Yes'|trans : 'No'|trans }}
                  </td>
                  <td>
                    {% if is_grantuser %}
                      <a class="edit_user_anchor" href="{{ url('/server/privileges', {
                        'username': privilege.user,
                        'hostname': privilege.host,
                        'dbname': priv.database != '*' ? priv.database,
                        'tablename': priv.table is defined and priv.table != '*' ? priv.table,
                        'routinename': priv.routine ?? ''
                      }) }}">
                        {{ get_icon('b_usredit', 'Edit privileges'|trans) }}
                      </a>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <a class="export_user_anchor ajax" href="{{ url('/server/privileges', {
                      'username': privilege.user,
                      'hostname': privilege.host,
                      'export': true,
                      'initial': ''
                    }) }}">
                      {{ get_icon('b_tblexport', 'Export'|trans) }}
                    </a>
                  </td>
                </tr>
                  {% if privileges_amount > 1 %}
                    <tr class="noclick">
                  {% endif %}
                {% endfor %}
            {% else %}
              <tr>
                <td colspan="7">
                  {% trans 'No user found.' %}
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="float-start">
        <img class="selectallarrow" src="{{ image('arrow_' ~ text_dir ~ '.png') }}" alt="
          {%- trans 'With selected:' %}" width="38" height="22">
        <input type="checkbox" id="usersForm_checkall" class="checkall_box" title="{% trans 'Check all' %}">
        <label for="usersForm_checkall">{% trans 'Check all' %}</label>
        <em class="with-selected">{% trans 'With selected:' %}</em>
        <button class="btn btn-link mult_submit" type="submit" name="submit_mult" value="export" title="{% trans 'Export' %}">
          {{ get_icon('b_tblexport', 'Export'|trans) }}
        </button>
      </div>
    </fieldset>
  </form>
{% else %}
  {{ 'Not enough privilege to view users.'|trans|error }}
{% endif %}

{% if is_createuser %}
  <div class="row">
    <div class="col-12">
      <fieldset class="pma-fieldset" id="fieldset_add_user">
        <legend>{% trans %}New{% context %}Create new user{% endtrans %}</legend>
        <a id="add_user_anchor" href="{{ url('/server/privileges', {
          'adduser': true,
          'dbname': db,
          'tablename': table
        }) }}" rel="{{ get_common({'checkprivsdb': db, 'checkprivstable': table}) }}">
          {{ get_icon('b_usradd', 'Add user account'|trans) }}
        </a>
      </fieldset>
    </div>
  </div>
{% endif %}
