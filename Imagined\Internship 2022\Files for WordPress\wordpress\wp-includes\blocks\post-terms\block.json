{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/post-terms", "title": "Post Terms", "category": "theme", "description": "Post terms.", "textdomain": "default", "attributes": {"term": {"type": "string"}, "textAlign": {"type": "string"}, "separator": {"type": "string", "default": ", "}}, "usesContext": ["postId", "postType"], "supports": {"html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "typography": {"lineHeight": true, "fontSize": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}}, "style": "wp-block-post-terms"}