
.footer-navigation {
	margin-top: calc(2 * var(--global--spacing-vertical));
	margin-bottom: var(--global--spacing-vertical);
	color: var(--footer--color-text);
	font-size: var(--global--font-size-xs);
	font-family: var(--footer--font-family);
}

.footer-navigation-wrapper {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	list-style: none;
	padding-left: 0;

	li {
		display: inline;
		// This is to prevent hover styles from overlapping when the menu wraps.
		line-height: 3;

		a {
			padding: calc(1.25 * var(--primary-nav--padding)) var(--primary-nav--padding);
			color: var(--footer--color-link);

			&:link,
			&:visited,
			&:active {
				color: var(--footer--color-link);
			}

			&:hover {
				text-decoration: underline;
				text-decoration-style: dotted;
				text-decoration-skip-ink: none;
				color: var(--footer--color-link-hover);
			}

			&:focus {

				.is-dark-theme & {

					.svg-icon {
						fill: var(--wp--style--color--link, var(--global--color-background));
					}
				}

				// Change colors when the body background is white.
				.has-background-white & {

					.svg-icon {
						fill: var(--wp--style--color--link, var(--global--color-white));
					}
				}
			}
		}

		.svg-icon {
			vertical-align: middle;
			fill: var(--footer--color-link);

			&:hover {
				transform: scale(1.1);
			}

			@media (prefers-reduced-motion: no-preference) {
				transition: transform 0.1s ease;
			}
		}
	}

	.sub-menu-toggle,
	.menu-item-description {
		display: none;
	}
}
