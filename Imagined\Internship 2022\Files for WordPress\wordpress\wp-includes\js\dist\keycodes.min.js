/*! This file is auto-generated */
!function(){"use strict";var n={d:function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o:function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},r:function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})}},t={};n.r(t),n.d(t,{ALT:function(){return S},BACKSPACE:function(){return i},COMMAND:function(){return A},CTRL:function(){return E},DELETE:function(){return C},DOWN:function(){return w},END:function(){return s},ENTER:function(){return c},ESCAPE:function(){return f},F10:function(){return g},HOME:function(){return p},LEFT:function(){return h},PAGEDOWN:function(){return d},PAGEUP:function(){return a},RIGHT:function(){return m},SHIFT:function(){return P},SPACE:function(){return l},TAB:function(){return u},UP:function(){return y},ZERO:function(){return v},displayShortcut:function(){return T},displayShortcutList:function(){return L},isKeyboardEvent:function(){return M},modifiers:function(){return O},rawShortcut:function(){return b},shortcutAriaLabel:function(){return _}});var e=window.lodash,r=window.wp.i18n;function o(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!n){if("undefined"==typeof window)return!1;n=window}const{platform:t}=n.navigator;return-1!==t.indexOf("Mac")||(0,e.includes)(["iPad","iPhone"],t)}const i=8,u=9,c=13,f=27,l=32,a=33,d=34,s=35,p=36,h=37,y=38,m=39,w=40,C=46,g=121,S="alt",E="ctrl",A="meta",P="shift",v=48,O={primary:n=>n()?[A]:[E],primaryShift:n=>n()?[P,A]:[E,P],primaryAlt:n=>n()?[S,A]:[E,S],secondary:n=>n()?[P,S,A]:[E,P,S],access:n=>n()?[E,S]:[P,S],ctrl:()=>[E],alt:()=>[S],ctrlShift:()=>[E,P],shift:()=>[P],shiftAlt:()=>[P,S],undefined:()=>[]},b=(0,e.mapValues)(O,(n=>function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;return[...n(e),t.toLowerCase()].join("+")})),L=(0,e.mapValues)(O,(n=>function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;const i=r(),u={[S]:i?"⌥":"Alt",[E]:i?"⌃":"Ctrl",[A]:"⌘",[P]:i?"⇧":"Shift"},c=n(r).reduce(((n,t)=>{const r=(0,e.get)(u,t,t);return i?[...n,r]:[...n,r,"+"]}),[]),f=(0,e.capitalize)(t);return[...c,f]})),T=(0,e.mapValues)(L,(n=>function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;return n(t,e).join("")})),_=(0,e.mapValues)(O,(n=>function(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;const u=i(),c={[P]:"Shift",[A]:u?"Command":"Control",[E]:"Control",[S]:u?"Option":"Alt",",":(0,r.__)("Comma"),".":(0,r.__)("Period"),"`":(0,r.__)("Backtick")};return[...n(i),t].map((n=>(0,e.capitalize)((0,e.get)(c,n,n)))).join(u?" ":" + ")}));function j(n){return[S,E,A,P].filter((t=>n[`${t}Key`]))}const M=(0,e.mapValues)(O,(n=>function(t,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o;const u=n(i),c=j(t);if((0,e.xor)(u,c).length)return!1;let f=t.key.toLowerCase();return r?(t.altKey&&1===r.length&&(f=String.fromCharCode(t.keyCode).toLowerCase()),"del"===r&&(r="delete"),f===r.toLowerCase()):(0,e.includes)(u,f)}));(window.wp=window.wp||{}).keycodes=t}();