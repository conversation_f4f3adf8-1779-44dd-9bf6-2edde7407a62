.table {
  th,
  td {
    text-shadow: 0 1px 0 #fff;
    vertical-align: middle;
  }

  th {
    text-align: left;
  }

  td {
    touch-action: manipulation;
  }

  thead th {
    border-right: 1px solid #fff;
    background-image: linear-gradient(#fff, #ccc);
  }
}

.table-hover {
  tbody tr {
    &:hover {
      background: linear-gradient(#ced6df, #b6c6d7);
    }
  }
}

@media only screen and (min-width: 768px) {
  .table th.position-sticky {
    top: 60px;
  }
}
