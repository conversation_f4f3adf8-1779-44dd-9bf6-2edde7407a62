/**
* Editor Post Title
* - Needs a special styles
*/

// Post title style
.wp-block.editor-post-title__block {
	border-bottom: 3px solid var(--global--color-border);
	padding-bottom: calc(2 * var(--global--spacing-vertical));
	margin-bottom: calc(3 * var(--global--spacing-vertical));
	max-width: var(--responsive--alignwide-width);

	.editor-post-title__input {
		color: var(--global--color-secondary);
		font-family: var(--heading--font-family);
		font-size: var(--global--font-size-page-title);
		font-weight: var(--heading--font-weight-page-title);
		line-height: var(--heading--line-height-h1);
	}
}

// Editor UI font styles
.wp-block.block-editor-default-block-appender > textarea {
	font-family: var(--global--font-secondary);
	font-size: var(--global--font-size-md);
}

// Gutenberg text color options
.has-primary-color[class] {
	color: var(--global--color-primary);
}

.has-secondary-color[class] {
	color: var(--global--color-secondary);
}

// Gutenberg background-color options
.has-background {

	a,
	p,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		color: currentColor;
	}
}

.has-primary-background-color[class] {
	background-color: var(--global--color-primary);
	color: var(--global--color-background);
}

.has-secondary-background-color[class] {
	background-color: var(--global--color-secondary);
	color: var(--global--color-background);
}

.has-white-background-color[class] {
	background-color: var(--global--color-white);
	color: var(--global--color-secondary);
}

.has-black-background-color[class] {
	background-color: var(--global--color-black);
	color: var(--global--color-primary);
}

// Spacing Overrides
[data-block] {
	margin-top: var(--global--spacing-vertical);
	margin-bottom: var(--global--spacing-vertical);
}

// Block Alignments
.wp-block {

	// Gutenberg injects a rule that limits the max width of .wp-block to 580px
	// This line overrides it to use the responsive spacing rules for default width content
	max-width: var(--responsive--aligndefault-width);

	// Use the theme's max-width for wide alignment.
	&[data-align="wide"],
	&.alignwide {
		max-width: var(--responsive--alignwide-width);
	}

	&[data-align="full"],
	&.alignfull {
		max-width: none;
	}
}

.alignleft {
	margin: 0;
	margin-right: var(--global--spacing-horizontal);
}

.alignright {
	margin: 0;
	margin-left: var(--global--spacing-horizontal);
}

// Drop cap
.has-drop-cap:not(:focus)::first-letter {
	font-family: var(--heading--font-family);
	font-weight: var(--heading--font-weight);
	line-height: 0.66;
	text-transform: uppercase;
	font-style: normal;
	float: left;
	margin: 0.1em 0.1em 0 0;
	font-size: calc(1.2 * var(--heading--font-size-h1));
}

@media only screen and (min-width: 482px) {

	.wp-block[data-align="left"] > * {
		max-width: 290px;
		margin-right: var(--global--spacing-horizontal);
	}

	.wp-block[data-align="right"] > * {
		max-width: 290px;
		margin-left: var(--global--spacing-horizontal);
	}
}

// Remove the border of blockquotes inside the classic block.
.wp-block-freeform.block-library-rich-text__tinymce blockquote {
	border: none;
}

// Adjust the position of the quote symbol for blockquotes inside the classic block.
.wp-block-freeform.block-library-rich-text__tinymce blockquote:before {
	left: 5px;
}
