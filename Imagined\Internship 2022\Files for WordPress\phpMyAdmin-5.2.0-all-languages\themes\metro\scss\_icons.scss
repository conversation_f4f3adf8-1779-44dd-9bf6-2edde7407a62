.icon {
  margin: 0;
  margin-left: 0.3em;
  padding: 0 !important;
  width: 16px;
  height: 16px;
}

.icon_fulltext {
  width: 50px;
  height: 19px;
}

.ic_asc_order {
  background-image: url('../img/asc_order.png');
}

.ic_b_bookmark {
  background-image: url('../img/b_bookmark.png');
}

.ic_b_browse {
  background-image: url('../img/b_browse.png');
}

.ic_b_calendar {
  background-image: url('../img/b_calendar.png');
}

.ic_b_chart {
  background-image: url('../img/b_chart.png');
}

.ic_b_close {
  background-image: url('../img/b_close.png');
}

.ic_b_column_add {
  background-image: url('../img/b_column_add.png');
}

.ic_b_comment {
  background-image: url('../img/b_comment.png');
}

.ic_b_dbstatistics {
  background-image: url('../img/b_dbstatistics.png');
}

.ic_b_deltbl {
  background-image: url('../img/b_deltbl.png');
}

.ic_b_docs {
  background-image: url('../img/b_docs.png');
}

.ic_b_docsql {
  background-image: url('../img/b_docsql.png');
}

.ic_b_drop {
  background-image: url('../img/b_drop.png');
}

.ic_b_edit {
  background-image: url('../img/b_edit.png');
}

.ic_b_empty {
  background-image: url('../img/b_empty.png');
}

.ic_b_engine {
  background-image: url('../img/b_engine.png');
}

.ic_b_event_add {
  background-image: url('../img/b_event_add.png');
}

.ic_b_events {
  background-image: url('../img/b_events.png');
}

.ic_b_export {
  background-image: url('../img/b_export.png');
}

.ic_b_favorite {
  background-image: url('../img/b_favorite.png');
}

.ic_b_find_replace {
  background-image: url('../img/b_find_replace.png');
}

.ic_b_firstpage {
  background-image: url('../img/b_firstpage.png');
}

.ic_b_ftext {
  background-image: url('../img/b_ftext.png');
}

.ic_b_globe {
  background-image: url('../img/b_globe.gif');
}

.ic_b_group {
  background-image: url('../img/b_group.png');
}

.ic_b_help {
  background-image: url('../img/b_help.png');
}

.ic_b_home {
  background-image: url('../img/b_home.png');
}

.ic_b_import {
  background-image: url('../img/b_import.png');
}

.ic_b_index {
  background-image: url('../img/b_index.png');
}

.ic_b_index_add {
  background-image: url('../img/b_index_add.png');
}

.ic_b_inline_edit {
  background-image: url('../img/b_inline_edit.png');
}

.ic_b_insrow {
  background-image: url('../img/b_insrow.png');
}

.ic_b_lastpage {
  background-image: url('../img/b_lastpage.png');
}

.ic_b_minus {
  background-image: url('../img/b_minus.png');
}

.ic_b_more {
  background-image: url('../img/b_more.png');
}

.ic_b_move {
  background-image: url('../img/b_move.png');
}

.ic_b_newdb {
  background-image: url('../img/b_newdb.png');
}

.ic_db_drop {
  background-image: url('../img/b_deltbl.png');
}

.ic_b_newtbl {
  background-image: url('../img/b_newtbl.png');
}

.ic_b_nextpage {
  background-image: url('../img/b_nextpage.png');
}

.ic_b_no_favorite {
  background-image: url('../img/b_no_favorite.png');
}

.ic_b_pdfdoc {
  background-image: url('../img/b_pdfdoc.png');
}

.ic_b_plugin {
  background-image: url('../img/b_plugin.png');
}

.ic_b_plus {
  background-image: url('../img/b_plus.png');
}

.ic_b_prevpage {
  background-image: url('../img/b_prevpage.png');
}

.ic_b_primary {
  background-image: url('../img/b_primary.png');
}

.ic_b_print {
  background-image: url('../img/b_print.png');
}

.ic_b_props {
  background-image: url('../img/b_props.png');
}

.ic_b_relations {
  background-image: url('../img/b_relations.png');
}

.ic_b_report {
  background-image: url('../img/b_report.png');
}

.ic_b_rename {
  background-image: url('../img/b_rename.svg');
}

.ic_b_routine_add {
  background-image: url('../img/b_routine_add.png');
}

.ic_b_routines {
  background-image: url('../img/b_routines.png');
}

.ic_b_save {
  background-image: url('../img/b_save.png');
}

.ic_b_saveimage {
  background-image: url('../img/b_saveimage.png');
}

.ic_b_sbrowse {
  background-image: url('../img/b_sbrowse.png');
}

.ic_b_sdb {
  background-image: url('../img/b_sdb.png');
  width: 10px;
  height: 10px;
}

.ic_b_search {
  background-image: url('../img/b_search.png');
}

.ic_b_select {
  background-image: url('../img/b_select.png');
}

.ic_b_snewtbl {
  background-image: url('../img/b_snewtbl.png');
}

.ic_b_spatial {
  background-image: url('../img/b_spatial.png');
}

.ic_b_sql {
  background-image: url('../img/b_sql.png');
}

.ic_b_sqldoc {
  background-image: url('../img/b_sqldoc.png');
}

.ic_b_sqlhelp {
  background-image: url('../img/b_sqlhelp.png');
}

.ic_b_table_add {
  background-image: url('../img/b_table_add.png');
}

.ic_b_tblanalyse {
  background-image: url('../img/b_tblanalyse.png');
}

.ic_b_tblexport {
  background-image: url('../img/b_tblexport.png');
}

.ic_b_tblimport {
  background-image: url('../img/b_tblimport.png');
}

.ic_b_tblops {
  background-image: url('../img/b_tblops.png');
}

.ic_b_tbloptimize {
  background-image: url('../img/b_tbloptimize.png');
}

.ic_b_tipp {
  background-image: url('../img/b_tipp.png');
}

.ic_b_trigger_add {
  background-image: url('../img/b_trigger_add.png');
}

.ic_b_triggers {
  background-image: url('../img/b_triggers.png');
}

.ic_b_undo {
  background-image: url('../../pmahomme/img/b_undo.png');
}

.ic_b_unique {
  background-image: url('../img/b_unique.png');
}

.ic_b_usradd {
  background-image: url('../img/b_usradd.png');
}

.ic_b_usrcheck {
  background-image: url('../img/b_usrcheck.png');
}

.ic_b_usrdrop {
  background-image: url('../img/b_usrdrop.png');
}

.ic_b_usredit {
  background-image: url('../img/b_usredit.png');
}

.ic_b_usrlist {
  background-image: url('../img/b_usrlist.png');
}

.ic_b_versions {
  background-image: url('../img/b_versions.png');
}

.ic_b_view {
  background-image: url('../img/b_view.png');
}

.ic_b_view_add {
  background-image: url('../img/b_view_add.png');
}

.ic_b_views {
  background-image: url('../img/b_views.png');
}

.ic_bd_browse {
  background-image: url('../img/bd_browse.png');
}

.ic_bd_deltbl {
  background-image: url('../img/bd_deltbl.png');
}

.ic_bd_drop {
  background-image: url('../img/bd_drop.png');
}

.ic_bd_edit {
  background-image: url('../img/bd_edit.png');
}

.ic_bd_empty {
  background-image: url('../img/bd_empty.png');
}

.ic_bd_export {
  background-image: url('../img/bd_export.png');
}

.ic_bd_firstpage {
  background-image: url('../img/bd_firstpage.png');
}

.ic_bd_ftext {
  background-image: url('../img/bd_ftext.png');
}

.ic_bd_index {
  background-image: url('../img/bd_index.png');
}

.ic_bd_insrow {
  background-image: url('../img/bd_insrow.png');
}

.ic_bd_lastpage {
  background-image: url('../img/bd_lastpage.png');
}

.ic_bd_nextpage {
  background-image: url('../img/bd_nextpage.png');
}

.ic_bd_prevpage {
  background-image: url('../img/bd_prevpage.png');
}

.ic_bd_primary {
  background-image: url('../img/bd_primary.png');
}

.ic_bd_routine_add {
  background-image: url('../img/bd_routine_add.png');
}

.ic_bd_sbrowse {
  background-image: url('../img/bd_sbrowse.png');
}

.ic_bd_select {
  background-image: url('../img/bd_select.png');
}

.ic_bd_spatial {
  background-image: url('../img/bd_spatial.png');
}

.ic_bd_unique {
  background-image: url('../img/bd_unique.png');
}

.ic_centralColumns {
  background-image: url('../img/centralColumns.png');
}

.ic_centralColumns_add {
  background-image: url('../img/centralColumns_add.png');
}

.ic_centralColumns_delete {
  background-image: url('../img/centralColumns_delete.png');
}

.ic_col_drop {
  background-image: url('../img/col_drop.png');
}

.ic_console {
  background-image: url('../img/console.png');
}

.ic_database {
  background-image: url('../img/database.png');
}

.ic_eye {
  background-image: url('../img/eye.png');
}

.ic_eye_grey {
  background-image: url('../img/eye_grey.png');
}

.ic_hide {
  background-image: url('../img/hide.png');
}

.ic_item {
  background-image: url('../img/item.png');
  width: 9px;
  height: 9px;
}

.ic_lightbulb {
  background-image: url('../img/lightbulb.png');
}

.ic_lightbulb_off {
  background-image: url('../img/lightbulb_off.png');
}

.ic_more {
  background-image: url('../img/more.png');
  width: 13px;
}

.ic_new_data {
  background-image: url('../img/new_data.png');
}

.ic_new_data_hovered {
  background-image: url('../img/new_data_hovered.png');
}

.ic_new_data_selected {
  background-image: url('../img/new_data_selected.png');
}

.ic_new_data_selected_hovered {
  background-image: url('../img/new_data_selected_hovered.png');
}

.ic_new_struct {
  background-image: url('../img/new_struct.png');
}

.ic_new_struct_hovered {
  background-image: url('../img/new_struct_hovered.png');
}

.ic_new_struct_selected {
  background-image: url('../img/new_struct_selected.png');
}

.ic_new_struct_selected_hovered {
  background-image: url('../img/new_struct_selected_hovered.png');
}

.ic_normalize {
  background-image: url('../img/normalize.png');
}

.ic_pause {
  background-image: url('../img/pause.png');
}

.ic_php_sym {
  background-image: url('../img/php_sym.png');
}

.ic_play {
  background-image: url('../img/play.png');
}

.ic_s_asc {
  background-image: url('../img/s_asc.png');
}

.ic_s_asci {
  background-image: url('../img/s_asci.png');
}

.ic_s_attention {
  background-image: url('../img/s_attention.png');
}

.ic_s_cancel {
  background-image: url('../img/s_cancel.png');
}

.ic_s_cancel2 {
  background-image: url('../img/s_cancel2.png');
}

.ic_s_cog {
  background-image: url('../img/s_cog.png');
}

.ic_s_db {
  background-image: url('../img/s_db.png');
}

.ic_s_desc {
  background-image: url('../img/s_desc.png');
}

.ic_s_error {
  background-image: url('../img/s_error.png');
}

.ic_s_host {
  background-image: url('../img/s_host.png');
}

.ic_s_info {
  background-image: url('../img/s_info.png');
}

.ic_s_lang {
  background-image: url('../img/s_lang.png');
}

.ic_s_link {
  background-image: url('../img/s_link.png');
}

.ic_s_lock {
  background-image: url('../img/s_lock.png');
}

.ic_s_unlock {
  background-image: url('../img/lock_open.png');
}

.ic_s_loggoff {
  background-image: url('../img/s_loggoff.png');
}

.ic_s_notice {
  background-image: url('../img/s_notice.png');
}

.ic_s_okay {
  background-image: url('../img/s_okay.png');
}

.ic_s_passwd {
  background-image: url('../img/s_passwd.png');
}

.ic_s_process {
  background-image: url('../img/s_process.png');
}

.ic_s_really {
  background-image: url('../img/s_really.png');
  width: 11px;
  height: 11px;
}

.ic_s_reload {
  background-image: url('../img/s_reload.png');
}

.ic_s_replication {
  background-image: url('../img/s_replication.png');
}

.ic_s_rights {
  background-image: url('../img/s_rights.png');
}

.ic_s_sortable {
  background-image: url('../img/s_sortable.png');
}

.ic_s_status {
  background-image: url('../img/s_status.png');
}

.ic_s_success {
  background-image: url('../img/s_success.png');
}

.ic_s_sync {
  background-image: url('../img/s_sync.png');
}

.ic_s_tbl {
  background-image: url('../img/s_tbl.png');
}

.ic_s_theme {
  background-image: url('../img/s_theme.png');
}

.ic_s_top {
  background-image: url('../img/s_top.png');
}

.ic_s_unlink {
  background-image: url('../img/s_unlink.png');
}

.ic_s_vars {
  background-image: url('../img/s_vars.png');
}

.ic_s_views {
  background-image: url('../img/s_views.png');
}

.ic_show {
  background-image: url('../img/show.png');
}

.ic_window-new {
  background-image: url('../img/window-new.png');
}

.ic_ajax_clock_small {
  background-image: url('../img/ajax_clock_small.gif');
}

.ic_s_partialtext {
  background-image: url('../img/s_partialtext.png');
}

.ic_s_fulltext {
  background-image: url('../img/s_fulltext.png');
}
