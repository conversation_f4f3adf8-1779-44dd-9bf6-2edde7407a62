{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/navigation", "title": "Navigation", "category": "theme", "description": "A collection of blocks that allow visitors to get around your site.", "keywords": ["menu", "navigation", "links"], "textdomain": "default", "attributes": {"ref": {"type": "number"}, "textColor": {"type": "string"}, "customTextColor": {"type": "string"}, "rgbTextColor": {"type": "string"}, "backgroundColor": {"type": "string"}, "customBackgroundColor": {"type": "string"}, "rgbBackgroundColor": {"type": "string"}, "showSubmenuIcon": {"type": "boolean", "default": true}, "openSubmenusOnClick": {"type": "boolean", "default": false}, "overlayMenu": {"type": "string", "default": "mobile"}, "hasIcon": {"type": "boolean", "default": true}, "__unstableLocation": {"type": "string"}, "overlayBackgroundColor": {"type": "string"}, "customOverlayBackgroundColor": {"type": "string"}, "overlayTextColor": {"type": "string"}, "customOverlayTextColor": {"type": "string"}, "maxNestingLevel": {"type": "number", "default": 5}}, "providesContext": {"textColor": "textColor", "customTextColor": "customTextColor", "backgroundColor": "backgroundColor", "customBackgroundColor": "customBackgroundColor", "overlayTextColor": "overlayTextColor", "customOverlayTextColor": "customOverlayTextColor", "overlayBackgroundColor": "overlayBackgroundColor", "customOverlayBackgroundColor": "customOverlayBackgroundColor", "fontSize": "fontSize", "customFontSize": "customFontSize", "showSubmenuIcon": "showSubmenuIcon", "openSubmenusOnClick": "openSubmenusOnClick", "style": "style", "orientation": "orientation", "maxNestingLevel": "maxNestingLevel"}, "supports": {"align": ["wide", "full"], "anchor": true, "html": false, "inserter": true, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalTextTransform": true, "__experimentalFontFamily": true, "__experimentalTextDecoration": true, "__experimentalSkipSerialization": ["textDecoration"], "__experimentalDefaultControls": {"fontSize": true}}, "spacing": {"blockGap": true, "units": ["px", "em", "rem", "vh", "vw"], "__experimentalDefaultControls": {"blockGap": true}}, "__experimentalLayout": {"allowSwitching": false, "allowInheriting": false, "allowVerticalAlignment": false, "default": {"type": "flex"}}}, "viewScript": "file:./view.min.js", "editorStyle": "wp-block-navigation-editor", "style": "wp-block-navigation"}