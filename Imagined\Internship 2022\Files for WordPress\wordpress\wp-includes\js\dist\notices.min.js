/*! This file is auto-generated */
!function(){"use strict";var t={d:function(e,n){for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{store:function(){return E}});var n={};t.r(n),t.d(n,{createErrorNotice:function(){return v},createInfoNotice:function(){return g},createNotice:function(){return a},createSuccessNotice:function(){return f},createWarningNotice:function(){return p},removeNotice:function(){return w}});var o={};t.r(o),t.d(o,{getNotices:function(){return b}});var i=window.wp.data,r=window.lodash;var c=t=>e=>function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;const i=o[t];if(void 0===i)return n;const r=e(n[i],o);return r===n[i]?n:{...n,[i]:r}};const u=c("context")((function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"CREATE_NOTICE":return[...(0,r.reject)(t,{id:e.notice.id}),e.notice];case"REMOVE_NOTICE":return(0,r.reject)(t,{id:e.id})}return t}));var s=u;const l="global",d="info";function a(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{speak:o=!0,isDismissible:i=!0,context:c=l,id:u=(0,r.uniqueId)(c),actions:s=[],type:a="default",__unstableHTML:f,icon:g=null,explicitDismiss:v=!1,onDismiss:p}=n;return e=String(e),{type:"CREATE_NOTICE",context:c,notice:{id:u,status:t,content:e,spokenMessage:o?e:null,__unstableHTML:f,isDismissible:i,actions:s,type:a,icon:g,explicitDismiss:v,onDismiss:p}}}function f(t,e){return a("success",t,e)}function g(t,e){return a("info",t,e)}function v(t,e){return a("error",t,e)}function p(t,e){return a("warning",t,e)}function w(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return{type:"REMOVE_NOTICE",id:t,context:e}}const y=[];function b(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return t[e]||y}const E=(0,i.createReduxStore)("core/notices",{reducer:s,actions:n,selectors:o});(0,i.register)(E),(window.wp=window.wp||{}).notices=e}();