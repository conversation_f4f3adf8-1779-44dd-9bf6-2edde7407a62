.wp-full-overlay-sidebar {
	overflow: visible;
}

/**
 * Hide all sidebar sections by default, only show them (via J<PERSON>) once the
 * preview loads and we know whether the sidebars are used in the template.
 */

.control-section.control-section-sidebar,
.customize-control-sidebar_widgets label,
.customize-control-sidebar_widgets .hide-if-js {
	/* The link in .customize-control-sidebar_widgets .hide-if-js will fail if it ever gets used. */
	display: none;
}

.control-section.control-section-sidebar .accordion-section-content.ui-sortable {
	overflow: visible;
}

/* Note: widget-tops are more compact when (max-height: 700px) and (min-width: 981px). */
.customize-control-widget_form .widget-top {
	background: #fff;
	transition: opacity 0.5s;
}

.customize-control .widget-action {
	color: #787c82;
}

.customize-control .widget-top:hover .widget-action,
.customize-control .widget-action:focus {
	color: #1d2327;
}

.customize-control-widget_form:not(.widget-rendered) .widget-top {
	opacity: 0.5;
}

.customize-control-widget_form .widget-control-save {
	display: none;
}

.customize-control-widget_form .spinner {
	visibility: hidden;
	margin-top: 0;
}

.customize-control-widget_form.previewer-loading .spinner {
	visibility: visible;
}

.customize-control-widget_form.widget-form-disabled .widget-content {
	opacity: 0.7;
	pointer-events: none;
	-webkit-user-select: none;
	user-select: none;
}

.customize-control-widget_form .widget {
	margin-bottom: 0;
}

.customize-control-widget_form.wide-widget-control .widget-inside {
	position: fixed;
	left: 299px;
	top: 25%;
	border: 1px solid #dcdcde;
	overflow: auto;
}
.customize-control-widget_form.wide-widget-control .widget-inside > .form {
	padding: 20px;
}

.customize-control-widget_form.wide-widget-control .widget-top {
	transition: background-color 0.4s;
}
.customize-control-widget_form.wide-widget-control.expanding .widget-top,
.customize-control-widget_form.wide-widget-control.expanded:not(.collapsing) .widget-top {
	background-color: #dcdcde;
}

.widget-inside {
	padding: 1px 10px 10px;
	border-top: none;
	line-height: 1.23076923;
}

.customize-control-widget_form.expanded .widget-action .toggle-indicator:before {
	content: "\f142";
}

.customize-control-widget_form.wide-widget-control .widget-action .toggle-indicator:before {
	content: "\f139";
}

.customize-control-widget_form.wide-widget-control.expanded .widget-action .toggle-indicator:before {
	content: "\f141";
}

.widget-title-action {
	cursor: pointer;
}

.widget-top,
.customize-control-widget_form .widget .customize-control-title {
	cursor: move;
}

.control-section.accordion-section.highlighted > .accordion-section-title,
.customize-control-widget_form.highlighted {
	outline: none;
	box-shadow: 0 0 2px rgba(79, 148, 212, 0.8);
	position: relative;
	z-index: 1;
}

#widget-customizer-control-templates {
	display: none;
}

/**
 * Widget reordering styles
 */

#customize-theme-controls .widget-reorder-nav {
	display: none;
	float: right;
	background-color: #f6f7f7;
}

.move-widget:before {
	content: "\f504";
}

#customize-theme-controls .move-widget-area {
	display: none;
	background: #fff;
	border: 1px solid #c3c4c7;
	border-top: none;
	cursor: auto;
}

#customize-theme-controls .reordering .move-widget-area.active {
	display: block;
}

#customize-theme-controls .move-widget-area .description {
	margin: 0;
	padding: 15px 20px;
	font-weight: 400;
}

#customize-theme-controls .widget-area-select {
	margin: 0;
	padding: 0;
	list-style: none;
}

#customize-theme-controls .widget-area-select li {
	position: relative;
	margin: 0;
	padding: 13px 15px 15px 42px;
	color: #50575e;
	border-top: 1px solid #c3c4c7;
	cursor: pointer;
	-webkit-user-select: none;
	user-select: none;
}

#customize-theme-controls .widget-area-select li:before {
	display: none;
	content: "\f147";
	position: absolute;
	top: 12px;
	left: 10px;
	font: normal 20px/1 dashicons;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#customize-theme-controls .widget-area-select li:last-child {
	border-bottom: 1px solid #c3c4c7;
}

#customize-theme-controls .widget-area-select .selected {
	color: #fff;
	background: #2271b1;
}

#customize-theme-controls .widget-area-select .selected:before {
	display: block;
}

#customize-theme-controls .move-widget-actions {
	text-align: right;
	padding: 12px;
}

#customize-theme-controls .reordering .widget-title-action {
	display: none;
}

#customize-theme-controls .reordering .widget-reorder-nav {
	display: block;
}

/* Text Widget */
.wp-customizer div.mce-inline-toolbar-grp,
.wp-customizer div.mce-tooltip {
	z-index: 500100 !important;
}
.wp-customizer .ui-autocomplete.wplink-autocomplete {
	z-index: 500110; /* originally 100110, but z-index of .wp-full-overlay is 500000 */
}
.wp-customizer #wp-link-backdrop {
	z-index: 500100; /* originally 100100, but z-index of .wp-full-overlay is 500000 */
}
.wp-customizer #wp-link-wrap {
	z-index: 500105; /* originally 100105, but z-index of .wp-full-overlay is 500000 */
}

/**
 * Styles for new widget addition panel
 */

/* override widgets admin page rules in wp-admin/css/widgets.css */
#widgets-left #available-widgets .widget {
	float: none !important;
	width: auto !important;
}

/* Keep rule that is no longer necessary on widgets.php. */
#available-widgets .widget-action {
	display: none;
}

.ios #available-widgets {
	transition: left 0s;
}

#available-widgets .widget-tpl:hover,
#available-widgets .widget-tpl.selected {
	background: #f6f7f7;
	border-bottom-color: #c3c4c7;
	color: #2271b1;
	border-left: 4px solid #2271b1;
}

#customize-controls .widget-title h3 {
	font-size: 1em;
}

#available-widgets .widget-title h3 {
	padding: 0 0 5px;
	font-size: 14px;
}

#available-widgets .widget .widget-description {
	padding: 0;
	color: #646970;
}

#customize-preview {
	transition: all 0.2s;
}

body.adding-widget #available-widgets {
	left: 0;
	visibility: visible;
}

body.adding-widget .wp-full-overlay-main {
	left: 300px;
}

body.adding-widget #customize-preview {
	opacity: 0.4;
}


/**
 * Widget Icon styling
 * No plurals in naming.
 * Ordered from lowest to highest specificity.
 */

#available-widgets .widget-title {
	position: relative;
}

#available-widgets .widget-title:before {
	content: "\f132";
	position: absolute;
	top: -3px;
	right: 100%;
	margin-right: 20px;
	width: 20px;
	height: 20px;
	color: #2c3338;
	font: normal 20px/1 dashicons;
	text-align: center;
	box-sizing: border-box;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* dashicons-smiley */
#available-widgets [class*="easy"] .widget-title:before { content: "\f328"; top: -4px; }

/* dashicons-star-filled */
#available-widgets [class*="super"] .widget-title:before,
#available-widgets [class*="like"] .widget-title:before { content: "\f155"; top: -4px; }

/* dashicons-wordpress */
#available-widgets [class*="meta"] .widget-title:before { content: "\f120"; }

/* dashicons-archive */
#available-widgets [class*="archives"] .widget-title:before { content: "\f480"; top: -4px; }

/* dashicons-category */
#available-widgets [class*="categor"] .widget-title:before { content: "\f318"; top: -4px; }

/* dashicons-admin-comments */
#available-widgets [class*="comment"] .widget-title:before,
#available-widgets [class*="testimonial"] .widget-title:before,
#available-widgets [class*="chat"] .widget-title:before { content: "\f101"; }

/* dashicons-admin-post */
#available-widgets [class*="post"] .widget-title:before { content: "\f109"; }

/* dashicons-admin-page */
#available-widgets [class*="page"] .widget-title:before { content: "\f105"; }

/* dashicons-text */
#available-widgets [class*="text"] .widget-title:before { content: "\f478"; }

/* dashicons-admin-links */
#available-widgets [class*="link"] .widget-title:before { content: "\f103"; }

/* dashicons-search */
#available-widgets [class*="search"] .widget-title:before { content: "\f179"; }

/* dashicons-menu */
#available-widgets [class*="menu"] .widget-title:before,
#available-widgets [class*="nav"] .widget-title:before { content: "\f333"; }

/* dashicons-tagcloud */
#available-widgets [class*="tag"] .widget-title:before { content: "\f479"; }

/* dashicons-rss */
#available-widgets [class*="rss"] .widget-title:before { content: "\f303"; top: -6px; }

/* dashicons-calendar */
#available-widgets [class*="event"] .widget-title:before,
#available-widgets [class*="calendar"] .widget-title:before { content: "\f145"; top: -4px;}

/* dashicons-format-image */
#available-widgets [class*="image"] .widget-title:before,
#available-widgets [class*="photo"] .widget-title:before,
#available-widgets [class*="slide"] .widget-title:before,
#available-widgets [class*="instagram"] .widget-title:before { content: "\f128"; }

/* dashicons-format-gallery */
#available-widgets [class*="album"] .widget-title:before,
#available-widgets [class*="galler"] .widget-title:before { content: "\f161"; }

/* dashicons-format-video */
#available-widgets [class*="video"] .widget-title:before,
#available-widgets [class*="tube"] .widget-title:before { content: "\f126"; }

/* dashicons-format-audio */
#available-widgets [class*="music"] .widget-title:before,
#available-widgets [class*="radio"] .widget-title:before,
#available-widgets [class*="audio"] .widget-title:before { content: "\f127"; }

/* dashicons-admin-users */
#available-widgets [class*="login"] .widget-title:before,
#available-widgets [class*="user"] .widget-title:before,
#available-widgets [class*="member"] .widget-title:before,
#available-widgets [class*="avatar"] .widget-title:before,
#available-widgets [class*="subscriber"] .widget-title:before,
#available-widgets [class*="profile"] .widget-title:before,
#available-widgets [class*="grofile"] .widget-title:before { content: "\f110"; }

/* dashicons-cart */
#available-widgets [class*="commerce"] .widget-title:before,
#available-widgets [class*="shop"] .widget-title:before,
#available-widgets [class*="cart"] .widget-title:before { content: "\f174"; top: -4px; }

/* dashicons-shield */
#available-widgets [class*="secur"] .widget-title:before,
#available-widgets [class*="firewall"] .widget-title:before { content: "\f332"; }

/* dashicons-chart-bar */
#available-widgets [class*="analytic"] .widget-title:before,
#available-widgets [class*="stat"] .widget-title:before,
#available-widgets [class*="poll"] .widget-title:before { content: "\f185"; }

/* dashicons-feedback */
#available-widgets [class*="form"] .widget-title:before { content: "\f175"; }

/* dashicons-email-alt */
#available-widgets [class*="subscribe"] .widget-title:before,
#available-widgets [class*="news"] .widget-title:before,
#available-widgets [class*="contact"] .widget-title:before,
#available-widgets [class*="mail"] .widget-title:before { content: "\f466"; }

/* dashicons-share */
#available-widgets [class*="share"] .widget-title:before,
#available-widgets [class*="socia"] .widget-title:before { content: "\f237"; }

/* dashicons-translation */
#available-widgets [class*="lang"] .widget-title:before,
#available-widgets [class*="translat"] .widget-title:before { content: "\f326"; }

/* dashicons-location-alt */
#available-widgets [class*="locat"] .widget-title:before,
#available-widgets [class*="map"] .widget-title:before { content: "\f231"; }

/* dashicons-download */
#available-widgets [class*="download"] .widget-title:before { content: "\f316"; }

/* dashicons-cloud */
#available-widgets [class*="weather"] .widget-title:before { content: "\f176"; top: -4px;}

/* dashicons-facebook */
#available-widgets [class*="facebook"] .widget-title:before { content: "\f304"; }

/* dashicons-twitter */
#available-widgets [class*="tweet"] .widget-title:before,
#available-widgets [class*="twitter"] .widget-title:before { content: "\f301"; }

@media screen and (max-height: 700px) and (min-width: 981px) {
	/* Compact widget-tops on smaller laptops, but not tablets. See ticket #27112#comment:4 */
	.customize-control-widget_form {
		margin-bottom: 0;
	}

	.widget-top {
		box-shadow: none;
		margin-top: -1px;
	}

	.widget-top:hover {
		position: relative;
		z-index: 1;
	}

	.last-widget {
		margin-bottom: 15px;
	}

	.widget-title h3 {
		padding: 13px 15px;
	}

	.widget-top .widget-action {
		padding: 8px 10px;
	}

	.widget-reorder-nav span {
		height: 39px;
	}

	.widget-reorder-nav span:before {
		line-height: 39px;
	}

	/* Compact the move widget areas. */
	#customize-theme-controls .widget-area-select li {
		padding: 9px 15px 11px 42px;
	}

	#customize-theme-controls .widget-area-select li:before {
		top: 8px;
	}
}
