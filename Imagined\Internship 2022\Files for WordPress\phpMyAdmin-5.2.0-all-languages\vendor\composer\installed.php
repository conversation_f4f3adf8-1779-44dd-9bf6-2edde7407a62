<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(
            0 => '5.2.x-dev',
        ),
        'reference' => '89c68f74a9575b7fff3a9787b87663c2d60b976b',
        'name' => 'phpmyadmin/phpmyadmin',
        'dev' => false,
    ),
    'versions' => array(
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.7',
            'version' => '2.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'reference' => 'd70c840f68657ce49094b8d91f9ee0cc07fbf66c',
            'dev_requirement' => false,
        ),
        'code-lts/u2f-php-server' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../code-lts/u2f-php-server',
            'aliases' => array(),
            'reference' => '59b3b28185e7fa255180a61278f6f65739082771',
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'reference' => '4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b',
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'reference' => '5abf82f213618696dda8e3bf6f64dd042d8542b2',
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'dev_requirement' => false,
        ),
        'google/recaptcha' => array(
            'pretty_version' => '1.2.4',
            'version' => '1.2.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/recaptcha',
            'aliases' => array(),
            'reference' => '614f25a9038be4f3f2da7cbfd778dc5b357d2419',
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '2.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'reference' => '9229e15f2e6ba772f0c55dd6986c563b937170a8',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.17.1',
            'version' => '1.17.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'reference' => 'ac994053faac18d386328c91c7900f930acadf1e',
            'dev_requirement' => false,
        ),
        'phpmyadmin/motranslator' => array(
            'pretty_version' => '5.3.0',
            'version' => '5.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/motranslator',
            'aliases' => array(),
            'reference' => '87baa97809ec556c40e4cba4bdef998a2de2a003',
            'dev_requirement' => false,
        ),
        'phpmyadmin/phpmyadmin' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(
                0 => '5.2.x-dev',
            ),
            'reference' => '89c68f74a9575b7fff3a9787b87663c2d60b976b',
            'dev_requirement' => false,
        ),
        'phpmyadmin/shapefile' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/shapefile',
            'aliases' => array(),
            'reference' => 'c232198ef49d3484f26acfe2d12cab103da9371a',
            'dev_requirement' => false,
        ),
        'phpmyadmin/sql-parser' => array(
            'pretty_version' => '5.5.0',
            'version' => '5.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/sql-parser',
            'aliases' => array(),
            'reference' => '8ab99cd0007d880f49f5aa1807033dbfa21b1cb5',
            'dev_requirement' => false,
        ),
        'phpmyadmin/twig-i18n-extension' => array(
            'pretty_version' => 'v4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/twig-i18n-extension',
            'aliases' => array(),
            'reference' => 'c0d0dd171cd1c7733bf152fd44b61055843df052',
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => '8.0.0',
            'version' => '8.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'reference' => '26c4c5cf30a2844ba121760fd7301f8ad240100b',
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa-qrcode' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa-qrcode',
            'aliases' => array(),
            'reference' => '0459a5d7bab06b11a09a365288d41a41d2afe63f',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => '8622567409010282b7aeebe4bb841fe98b58dcaf',
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'samyoul/u2f-php-server' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'slim/psr7' => array(
            'pretty_version' => '1.4',
            'version' => '1.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/psr7',
            'aliases' => array(),
            'reference' => '0dca983ca32a26f4a91fb11173b7b9eaee29e9d6',
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v5.4.8',
            'version' => '5.4.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'reference' => '4c6747cf7e56c6b8e3094dd24852bd3e364375b1',
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'reference' => '64be4a7acb83b6f2bf6de9a02cee6dad41277ebc',
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/config' => array(
            'pretty_version' => 'v5.4.8',
            'version' => '5.4.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'reference' => '9f8964f56f7234f8ace16f66cb3fbae950c04e68',
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v5.4.8',
            'version' => '5.4.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'reference' => '855e29cd715ad62bb840c9841fe09a7cde50811f',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
            'dev_requirement' => false,
        ),
        'symfony/expression-language' => array(
            'pretty_version' => 'v5.4.8',
            'version' => '5.4.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/expression-language',
            'aliases' => array(),
            'reference' => '9d186e1eecf9e3461c6adbdf1acf614b8da9def9',
            'dev_requirement' => false,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v5.4.7',
            'version' => '5.4.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'reference' => '3a4442138d80c9f7b600fb297534ac718b61d37f',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '30885182c981ab175d4d034db0f6f469898070ab',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '0abb51d2f102e00a4eefcf46ba7fec406d245825',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'reference' => 'cc5db0e22b3cb4111010e48785a97f670b350ca5',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '4407588e0d3f1f52efb65fbe92babe41f37fe50c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'reference' => '5de4ba2d41b15f9bd0e19b2ab9674135813ec98f',
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'reference' => '24d9dc654b83e91aa59f9d167b131bc3b5bea24c',
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v5.4.8',
            'version' => '5.4.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'reference' => '7e132a3fcd4b57add721b4207236877b6017ec93',
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.4.4',
            'version' => '6.4.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'reference' => '42cd0f9786af7e5db4fcedaa66f717b0d0032320',
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.3.10',
            'version' => '3.3.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'reference' => '8442df056c51b706793adf80a9fd363406dd3674',
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'reference' => '6964c76c7804814a842473e0c8fd15bab0f18e25',
            'dev_requirement' => false,
        ),
        'williamdes/mariadb-mysql-kbs' => array(
            'pretty_version' => 'v1.2.13',
            'version' => '1.2.13.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../williamdes/mariadb-mysql-kbs',
            'aliases' => array(),
            'reference' => 'f5c1b00d4bcfb27c06595ae172aa69da1815bfa9',
            'dev_requirement' => false,
        ),
    ),
);
