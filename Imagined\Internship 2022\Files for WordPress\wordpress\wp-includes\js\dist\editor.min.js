/*! This file is auto-generated */
!function(){var e={6411:function(e,t){var n,o,r;
/*!
	autosize 4.0.4
	license: MIT
	http://www.jacklmoore.com/autosize
*/o=[e,t],n=function(e,t){"use strict";var n="function"==typeof Map?new Map:function(){var e=[],t=[];return{has:function(t){return e.indexOf(t)>-1},get:function(n){return t[e.indexOf(n)]},set:function(n,o){-1===e.indexOf(n)&&(e.push(n),t.push(o))},delete:function(n){var o=e.indexOf(n);o>-1&&(e.splice(o,1),t.splice(o,1))}}}(),o=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){o=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function r(e){if(e&&e.nodeName&&"TEXTAREA"===e.nodeName&&!n.has(e)){var t=null,r=null,i=null,s=function(){e.clientWidth!==r&&p()},a=function(t){window.removeEventListener("resize",s,!1),e.removeEventListener("input",p,!1),e.removeEventListener("keyup",p,!1),e.removeEventListener("autosize:destroy",a,!1),e.removeEventListener("autosize:update",p,!1),Object.keys(t).forEach((function(n){e.style[n]=t[n]})),n.delete(e)}.bind(e,{height:e.style.height,resize:e.style.resize,overflowY:e.style.overflowY,overflowX:e.style.overflowX,wordWrap:e.style.wordWrap});e.addEventListener("autosize:destroy",a,!1),"onpropertychange"in e&&"oninput"in e&&e.addEventListener("keyup",p,!1),window.addEventListener("resize",s,!1),e.addEventListener("input",p,!1),e.addEventListener("autosize:update",p,!1),e.style.overflowX="hidden",e.style.wordWrap="break-word",n.set(e,{destroy:a,update:p}),l()}function l(){var n=window.getComputedStyle(e,null);"vertical"===n.resize?e.style.resize="none":"both"===n.resize&&(e.style.resize="horizontal"),t="content-box"===n.boxSizing?-(parseFloat(n.paddingTop)+parseFloat(n.paddingBottom)):parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth),isNaN(t)&&(t=0),p()}function c(t){var n=e.style.width;e.style.width="0px",e.offsetWidth,e.style.width=n,e.style.overflowY=t}function u(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}function d(){if(0!==e.scrollHeight){var n=u(e),o=document.documentElement&&document.documentElement.scrollTop;e.style.height="",e.style.height=e.scrollHeight+t+"px",r=e.clientWidth,n.forEach((function(e){e.node.scrollTop=e.scrollTop})),o&&(document.documentElement.scrollTop=o)}}function p(){d();var t=Math.round(parseFloat(e.style.height)),n=window.getComputedStyle(e,null),r="content-box"===n.boxSizing?Math.round(parseFloat(n.height)):e.offsetHeight;if(r<t?"hidden"===n.overflowY&&(c("scroll"),d(),r="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight):"hidden"!==n.overflowY&&(c("hidden"),d(),r="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight),i!==r){i=r;var s=o("autosize:resized");try{e.dispatchEvent(s)}catch(e){}}}}function i(e){var t=n.get(e);t&&t.destroy()}function s(e){var t=n.get(e);t&&t.update()}var a=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((a=function(e){return e}).destroy=function(e){return e},a.update=function(e){return e}):((a=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],(function(e){return r(e,t)})),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],i),e},a.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],s),e}),t.default=a,e.exports=t.default},void 0===(r="function"==typeof n?n.apply(t,o):n)||(e.exports=r)},4403:function(e,t){var n;
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var s=r.apply(null,n);s&&e.push(s)}}else if("object"===i)if(n.toString===Object.prototype.toString)for(var a in n)o.call(n,a)&&n[a]&&e.push(a);else e.push(n.toString())}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},4827:function(e){e.exports=function(e,t,n){return((n=window.getComputedStyle)?n(e):e.currentStyle)[t.replace(/-(\w)/gi,(function(e,t){return t.toUpperCase()}))]}},9894:function(e,t,n){var o=n(4827);e.exports=function(e){var t=o(e,"line-height"),n=parseFloat(t,10);if(t===n+""){var r=e.style.lineHeight;e.style.lineHeight=t+"em",t=o(e,"line-height"),n=parseFloat(t,10),r?e.style.lineHeight=r:delete e.style.lineHeight}if(-1!==t.indexOf("pt")?(n*=4,n/=3):-1!==t.indexOf("mm")?(n*=96,n/=25.4):-1!==t.indexOf("cm")?(n*=96,n/=2.54):-1!==t.indexOf("in")?n*=96:-1!==t.indexOf("pc")&&(n*=16),n=Math.round(n),"normal"===t){var i=e.nodeName,s=document.createElement(i);s.innerHTML="&nbsp;","TEXTAREA"===i.toUpperCase()&&s.setAttribute("rows","1");var a=o(e,"font-size");s.style.fontSize=a,s.style.padding="0px",s.style.border="0px";var l=document.body;l.appendChild(s),n=s.offsetHeight,l.removeChild(s)}return n}},5372:function(e,t,n){"use strict";var o=n(9567);function r(){}function i(){}i.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,i,s){if(s!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return n.PropTypes=n,n}},2652:function(e,t,n){e.exports=n(5372)()},9567:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5438:function(e,t,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},s=this&&this.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&(n[o[r]]=e[o[r]])}return n};t.__esModule=!0;var a=n(9196),l=n(2652),c=n(6411),u=n(9894),d="autosize:resized",p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={lineHeight:null},t.textarea=null,t.onResize=function(e){t.props.onResize&&t.props.onResize(e)},t.updateLineHeight=function(){t.textarea&&t.setState({lineHeight:u(t.textarea)})},t.onChange=function(e){var n=t.props.onChange;t.currentValue=e.currentTarget.value,n&&n(e)},t}return r(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.maxRows,o=t.async;"number"==typeof n&&this.updateLineHeight(),"number"==typeof n||o?setTimeout((function(){return e.textarea&&c(e.textarea)})):this.textarea&&c(this.textarea),this.textarea&&this.textarea.addEventListener(d,this.onResize)},t.prototype.componentWillUnmount=function(){this.textarea&&(this.textarea.removeEventListener(d,this.onResize),c.destroy(this.textarea))},t.prototype.render=function(){var e=this,t=this.props,n=(t.onResize,t.maxRows),o=(t.onChange,t.style),r=(t.innerRef,t.children),l=s(t,["onResize","maxRows","onChange","style","innerRef","children"]),c=this.state.lineHeight,u=n&&c?c*n:null;return a.createElement("textarea",i({},l,{onChange:this.onChange,style:u?i({},o,{maxHeight:u}):o,ref:function(t){e.textarea=t,"function"==typeof e.props.innerRef?e.props.innerRef(t):e.props.innerRef&&(e.props.innerRef.current=t)}}),r)},t.prototype.componentDidUpdate=function(){this.textarea&&c.update(this.textarea)},t.defaultProps={rows:1,async:!1},t.propTypes={rows:l.number,maxRows:l.number,onResize:l.func,innerRef:l.any,async:l.bool},t}(a.Component);t.TextareaAutosize=a.forwardRef((function(e,t){return a.createElement(p,i({},e,{innerRef:t}))}))},773:function(e,t,n){"use strict";var o=n(5438);t.Z=o.TextareaAutosize},9196:function(e){"use strict";e.exports=window.React}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};!function(){"use strict";n.r(o),n.d(o,{AlignmentToolbar:function(){return ss},Autocomplete:function(){return is},AutosaveMonitor:function(){return lo},BlockAlignmentToolbar:function(){return as},BlockControls:function(){return ls},BlockEdit:function(){return cs},BlockEditorKeyboardShortcuts:function(){return us},BlockFormatControls:function(){return ds},BlockIcon:function(){return ps},BlockInspector:function(){return ms},BlockList:function(){return hs},BlockMover:function(){return gs},BlockNavigationDropdown:function(){return fs},BlockSelectionClearer:function(){return vs},BlockSettingsMenu:function(){return _s},BlockTitle:function(){return ys},BlockToolbar:function(){return bs},ColorPalette:function(){return Es},ContrastChecker:function(){return ws},CopyHandler:function(){return Ss},DefaultBlockAppender:function(){return Ps},DocumentOutline:function(){return bo},DocumentOutlineCheck:function(){return Eo},EditorHistoryRedo:function(){return Io},EditorHistoryUndo:function(){return No},EditorKeyboardShortcutsRegister:function(){return Co},EditorNotices:function(){return Do},EditorProvider:function(){return Ji},EditorSnackbars:function(){return Lo},EntitiesSavedStates:function(){return Vo},ErrorBoundary:function(){return Go},FontSizePicker:function(){return ks},InnerBlocks:function(){return Ts},Inserter:function(){return Cs},InspectorAdvancedControls:function(){return xs},InspectorControls:function(){return Bs},LocalAutosaveMonitor:function(){return qo},MediaPlaceholder:function(){return Ls},MediaUpload:function(){return Os},MediaUploadCheck:function(){return Fs},MultiSelectScrollIntoView:function(){return Us},NavigableToolbar:function(){return Ms},ObserveTyping:function(){return zs},PageAttributesCheck:function(){return Qo},PageAttributesOrder:function(){return Zo},PageAttributesParent:function(){return ir},PageTemplate:function(){return sr},PanelColorSettings:function(){return As},PlainText:function(){return Is},PostAuthor:function(){return ur},PostAuthorCheck:function(){return dr},PostComments:function(){return pr},PostExcerpt:function(){return mr},PostExcerptCheck:function(){return hr},PostFeaturedImage:function(){return Sr},PostFeaturedImageCheck:function(){return fr},PostFormat:function(){return Cr},PostFormatCheck:function(){return Pr},PostLastRevision:function(){return Br},PostLastRevisionCheck:function(){return xr},PostLockedModal:function(){return Ar},PostPendingStatus:function(){return Nr},PostPendingStatusCheck:function(){return Ir},PostPingbacks:function(){return Rr},PostPreviewButton:function(){return Lr},PostPublishButton:function(){return Ur},PostPublishButtonLabel:function(){return Or},PostPublishPanel:function(){return yi},PostSavedState:function(){return ki},PostSchedule:function(){return Kr},PostScheduleCheck:function(){return Ci},PostScheduleLabel:function(){return qr},PostSlug:function(){return Bi},PostSlugCheck:function(){return Ti},PostSticky:function(){return Ii},PostStickyCheck:function(){return Ai},PostSwitchToDraftButton:function(){return Pi},PostTaxonomies:function(){return Ni},PostTaxonomiesCheck:function(){return Ri},PostTaxonomiesFlatTermSelector:function(){return ni},PostTaxonomiesHierarchicalTermSelector:function(){return di},PostTextEditor:function(){return Li},PostTitle:function(){return Fi},PostTrash:function(){return Ui},PostTrashCheck:function(){return Mi},PostTypeSupportCheck:function(){return Yo},PostVisibility:function(){return Gr},PostVisibilityCheck:function(){return zi},PostVisibilityLabel:function(){return jr},RichText:function(){return rs},RichTextShortcut:function(){return Ns},RichTextToolbarButton:function(){return Rs},ServerSideRender:function(){return ts()},SkipToSelectedBlock:function(){return Vs},TableOfContents:function(){return $i},TextEditorGlobalKeyboardShortcuts:function(){return ko},ThemeSupportCheck:function(){return gr},URLInput:function(){return Hs},URLInputButton:function(){return Ws},URLPopover:function(){return Gs},UnsavedChangesWarning:function(){return Ki},VisualEditorGlobalKeyboardShortcuts:function(){return Po},Warning:function(){return js},WordCount:function(){return Wi},WritingFlow:function(){return $s},__unstableRichTextInputEvent:function(){return Ds},cleanForSlug:function(){return na},createCustomColorsHOC:function(){return Ks},getColorClassName:function(){return qs},getColorObjectByAttributeValues:function(){return Qs},getColorObjectByColorValue:function(){return Ys},getFontSize:function(){return Xs},getFontSizeClass:function(){return Zs},getTemplatePartIcon:function(){return U},mediaUpload:function(){return Xi},store:function(){return oo},storeConfig:function(){return no},transformStyles:function(){return p.transformStyles},userAutocompleter:function(){return so},withColorContext:function(){return Js},withColors:function(){return ea},withFontSizes:function(){return ta}});var e={};n.r(e),n.d(e,{__experimentalGetDefaultTemplatePartAreas:function(){return Ht},__experimentalGetDefaultTemplateType:function(){return Wt},__experimentalGetDefaultTemplateTypes:function(){return Vt},__experimentalGetTemplateInfo:function(){return Gt},__unstableIsEditorReady:function(){return Me},canInsertBlockType:function(){return Ft},canUserUseUnfilteredHTML:function(){return Re},didPostSaveRequestFail:function(){return fe},didPostSaveRequestSucceed:function(){return ge},getActivePostLock:function(){return Ne},getAdjacentBlockClientId:function(){return lt},getAutosaveAttribute:function(){return ne},getBlock:function(){return Ke},getBlockAttributes:function(){return $e},getBlockCount:function(){return Je},getBlockHierarchyRootClientId:function(){return at},getBlockIndex:function(){return wt},getBlockInsertionPoint:function(){return Nt},getBlockListSettings:function(){return zt},getBlockMode:function(){return Bt},getBlockName:function(){return Ge},getBlockOrder:function(){return Et},getBlockRootClientId:function(){return st},getBlockSelectionEnd:function(){return tt},getBlockSelectionStart:function(){return et},getBlocks:function(){return qe},getBlocksByClientId:function(){return Ze},getClientIdsOfDescendants:function(){return Qe},getClientIdsWithDescendants:function(){return Ye},getCurrentPost:function(){return q},getCurrentPostAttribute:function(){return ee},getCurrentPostId:function(){return Y},getCurrentPostLastRevisionId:function(){return Z},getCurrentPostRevisionsCount:function(){return X},getCurrentPostType:function(){return Q},getEditedPostAttribute:function(){return te},getEditedPostContent:function(){return Ee},getEditedPostPreviewLink:function(){return ye},getEditedPostSlug:function(){return ke},getEditedPostVisibility:function(){return oe},getEditorBlocks:function(){return Le},getEditorSelection:function(){return Ue},getEditorSelectionEnd:function(){return Fe},getEditorSelectionStart:function(){return Oe},getEditorSettings:function(){return ze},getFirstMultiSelectedBlockClientId:function(){return ht},getGlobalBlockCount:function(){return Xe},getInserterItems:function(){return Ut},getLastMultiSelectedBlockClientId:function(){return gt},getMultiSelectedBlockClientIds:function(){return pt},getMultiSelectedBlocks:function(){return mt},getMultiSelectedBlocksEndClientId:function(){return bt},getMultiSelectedBlocksStartClientId:function(){return yt},getNextBlockClientId:function(){return ut},getPermalink:function(){return Pe},getPermalinkParts:function(){return Ce},getPostEdits:function(){return J},getPostLockUser:function(){return Ie},getPostTypeLabel:function(){return jt},getPreviousBlockClientId:function(){return ct},getSelectedBlock:function(){return it},getSelectedBlockClientId:function(){return rt},getSelectedBlockCount:function(){return nt},getSelectedBlocksInitialCaretPosition:function(){return dt},getStateBeforeOptimisticTransaction:function(){return Ve},getSuggestedPostFormat:function(){return be},getTemplate:function(){return Lt},getTemplateLock:function(){return Ot},hasChangedContent:function(){return G},hasEditorRedo:function(){return H},hasEditorUndo:function(){return V},hasInserterItems:function(){return Mt},hasMultiSelection:function(){return Ct},hasNonPostEntityChanges:function(){return $},hasSelectedBlock:function(){return ot},hasSelectedInnerBlock:function(){return Pt},inSomeHistory:function(){return He},isAncestorMultiSelected:function(){return _t},isAutosavingPost:function(){return ve},isBlockInsertionPointVisible:function(){return Rt},isBlockMultiSelected:function(){return vt},isBlockSelected:function(){return St},isBlockValid:function(){return je},isBlockWithinSelection:function(){return kt},isCaretWithinFormattedText:function(){return It},isCleanNewPost:function(){return K},isCurrentPostPending:function(){return re},isCurrentPostPublished:function(){return ie},isCurrentPostScheduled:function(){return se},isEditedPostAutosaveable:function(){return ue},isEditedPostBeingScheduled:function(){return de},isEditedPostDateFloating:function(){return pe},isEditedPostDirty:function(){return j},isEditedPostEmpty:function(){return ce},isEditedPostNew:function(){return W},isEditedPostPublishable:function(){return ae},isEditedPostSaveable:function(){return le},isFirstMultiSelectedBlock:function(){return ft},isMultiSelecting:function(){return Tt},isPermalinkEditable:function(){return Se},isPostAutosavingLocked:function(){return Be},isPostLockTakeover:function(){return Ae},isPostLocked:function(){return Te},isPostSavingLocked:function(){return xe},isPreviewingPost:function(){return _e},isPublishSidebarEnabled:function(){return De},isPublishingPost:function(){return we},isSavingNonPostEntityChanges:function(){return he},isSavingPost:function(){return me},isSelectionEnabled:function(){return xt},isTyping:function(){return At},isValidTemplate:function(){return Dt}});var t={};function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},r.apply(this,arguments)}n.r(t),n.d(t,{__experimentalTearDownEditor:function(){return nn},autosave:function(){return dn},clearSelectedBlock:function(){return Rn},createUndoLevel:function(){return hn},disablePublishSidebar:function(){return vn},editPost:function(){return an},enablePublishSidebar:function(){return fn},enterFormattedText:function(){return Zn},exitFormattedText:function(){return Jn},hideInsertionPoint:function(){return Wn},insertBlock:function(){return zn},insertBlocks:function(){return Vn},insertDefaultBlock:function(){return eo},lockPostAutosaving:function(){return bn},lockPostSaving:function(){return _n},mergeBlocks:function(){return $n},moveBlockToPosition:function(){return Mn},moveBlocksDown:function(){return Fn},moveBlocksUp:function(){return Un},multiSelect:function(){return Nn},receiveBlocks:function(){return Cn},redo:function(){return pn},refreshPost:function(){return cn},removeBlock:function(){return qn},removeBlocks:function(){return Kn},replaceBlock:function(){return On},replaceBlocks:function(){return Ln},resetBlocks:function(){return kn},resetEditorBlocks:function(){return wn},resetPost:function(){return on},savePost:function(){return ln},selectBlock:function(){return Bn},setTemplateValidity:function(){return Gn},setupEditor:function(){return tn},setupEditorState:function(){return sn},showInsertionPoint:function(){return Hn},startMultiSelect:function(){return An},startTyping:function(){return Yn},stopMultiSelect:function(){return In},stopTyping:function(){return Xn},synchronizeTemplate:function(){return jn},toggleBlockMode:function(){return Qn},toggleSelection:function(){return Dn},trashPost:function(){return un},undo:function(){return mn},unlockPostAutosaving:function(){return En},unlockPostSaving:function(){return yn},updateBlock:function(){return Tn},updateBlockAttributes:function(){return xn},updateBlockListSettings:function(){return to},updateEditorSettings:function(){return Sn},updatePost:function(){return rn},updatePostLock:function(){return gn}});var i=window.wp.element,s=window.lodash,a=window.wp.blocks,l=window.wp.data,c=window.wp.coreData,u=window.wp.compose,d=window.wp.hooks,p=window.wp.blockEditor;const m={...p.SETTINGS_DEFAULTS,richEditingEnabled:!0,codeEditingEnabled:!0,enableCustomFields:void 0,supportsLayout:!0};function h(e){return e&&"object"==typeof e&&"raw"in e?e.raw:e}var g,f,v=(0,l.combineReducers)({postId:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return"SETUP_EDITOR_STATE"===t.type?t.post.id:e},postType:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return"SETUP_EDITOR_STATE"===t.type?t.post.type:e},saving:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REQUEST_POST_UPDATE_START":case"REQUEST_POST_UPDATE_FINISH":return{pending:"REQUEST_POST_UPDATE_START"===t.type,options:t.options||{}}}return e},postLock:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isLocked:!1},t=arguments.length>1?arguments[1]:void 0;return"UPDATE_POST_LOCK"===t.type?t.lock:e},template:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isValid:!0},t=arguments.length>1?arguments[1]:void 0;return"SET_TEMPLATE_VALIDITY"===t.type?{...e,isValid:t.isValid}:e},postSavingLock:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOCK_POST_SAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_SAVING":return(0,s.omit)(e,t.lockName)}return e},isReady:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR_STATE":return!0;case"TEAR_DOWN_EDITOR":return!1}return e},editorSettings:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m,t=arguments.length>1?arguments[1]:void 0;return"UPDATE_EDITOR_SETTINGS"===t.type?{...e,...t.settings}:e},postAutosavingLock:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOCK_POST_AUTOSAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_AUTOSAVING":return(0,s.omit)(e,t.lockName)}return e}});function _(e){return[e]}function y(){var e={clear:function(){e.head=null}};return e}function b(e,t,n){var o;if(e.length!==t.length)return!1;for(o=n;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}function E(e,t){var n,o;function r(){n=f?new WeakMap:y()}function i(){var n,r,i,s,a,l=arguments.length;for(s=new Array(l),i=0;i<l;i++)s[i]=arguments[i];for(a=t.apply(null,s),(n=o(a)).isUniqueByDependants||(n.lastDependants&&!b(a,n.lastDependants,0)&&n.clear(),n.lastDependants=a),r=n.head;r;){if(b(r.args,s,1))return r!==n.head&&(r.prev.next=r.next,r.next&&(r.next.prev=r.prev),r.next=n.head,r.prev=null,n.head.prev=r,n.head=r),r.val;r=r.next}return r={val:e.apply(null,s)},s[0]=null,r.args=s,n.head&&(n.head.prev=r,r.next=n.head),n.head=r,r.val}return t||(t=_),o=f?function(e){var t,o,r,i,s,a=n,l=!0;for(t=0;t<e.length;t++){if(o=e[t],!(s=o)||"object"!=typeof s){l=!1;break}a.has(o)?a=a.get(o):(r=new WeakMap,a.set(o,r),a=r)}return a.has(g)||((i=y()).isUniqueByDependants=l,a.set(g,i)),a.get(g)}:function(){return n},i.getDependants=t,i.clear=r,r(),i}g={},f="undefined"!=typeof WeakMap;var w=window.wp.date,S=window.wp.url,P=window.wp.deprecated,k=n.n(P),C=window.wp.primitives;var T=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})),x=window.wp.preferences;const B=new Set(["meta"]),A="SAVE_POST_NOTICE_ID",I="TRASH_POST_NOTICE_ID",N=/%(?:postname|pagename)%/,R=["title","excerpt","content"];var D=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M18.5 10.5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}));var L=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{fillRule:"evenodd",d:"M18 5.5h-8v8h8.5V6a.5.5 0 00-.5-.5zm-9.5 8h-3V6a.5.5 0 01.5-.5h2.5v8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}));var O=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}));var F=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"}));function U(e){return"header"===e?D:"footer"===e?L:"sidebar"===e?O:F}const M={},z=[],V=(0,l.createRegistrySelector)((e=>()=>e(c.store).hasUndo())),H=(0,l.createRegistrySelector)((e=>()=>e(c.store).hasRedo()));function W(e){return"auto-draft"===q(e).status}function G(e){const t=J(e);return"blocks"in t||"content"in t}const j=(0,l.createRegistrySelector)((e=>t=>{const n=Q(t),o=Y(t);return!!e(c.store).hasEditsForEntityRecord("postType",n,o)})),$=(0,l.createRegistrySelector)((e=>t=>{const n=e(c.store).__experimentalGetDirtyEntityRecords(),{type:o,id:r}=q(t);return(0,s.some)(n,(e=>"postType"!==e.kind||e.name!==o||e.key!==r))}));function K(e){return!j(e)&&W(e)}const q=(0,l.createRegistrySelector)((e=>t=>{const n=Y(t),o=Q(t),r=e(c.store).getRawEntityRecord("postType",o,n);return r||M}));function Q(e){return e.postType}function Y(e){return e.postId}function X(e){return(0,s.get)(q(e),["_links","version-history",0,"count"],0)}function Z(e){return(0,s.get)(q(e),["_links","predecessor-version",0,"id"],null)}const J=(0,l.createRegistrySelector)((e=>t=>{const n=Q(t),o=Y(t);return e(c.store).getEntityRecordEdits("postType",n,o)||M}));function ee(e,t){switch(t){case"type":return Q(e);case"id":return Y(e);default:const n=q(e);if(!n.hasOwnProperty(t))break;return h(n[t])}}function te(e,t){if("content"===t)return Ee(e);const n=J(e);return n.hasOwnProperty(t)?B.has(t)?((e,t)=>{const n=J(e);return n.hasOwnProperty(t)?{...ee(e,t),...n[t]}:ee(e,t)})(e,t):n[t]:ee(e,t)}const ne=(0,l.createRegistrySelector)((e=>(t,n)=>{if(!(0,s.includes)(R,n)&&"preview_link"!==n)return;const o=Q(t),r=Y(t),i=(0,s.get)(e(c.store).getCurrentUser(),["id"]),a=e(c.store).getAutosave(o,r,i);return a?h(a[n]):void 0}));function oe(e){if("private"===te(e,"status"))return"private";return te(e,"password")?"password":"public"}function re(e){return"pending"===q(e).status}function ie(e,t){const n=t||q(e);return-1!==["publish","private"].indexOf(n.status)||"future"===n.status&&!(0,w.isInTheFuture)(new Date(Number((0,w.getDate)(n.date))-6e4))}function se(e){return"future"===q(e).status&&!ie(e)}function ae(e){const t=q(e);return j(e)||-1===["publish","private","future"].indexOf(t.status)}function le(e){return!me(e)&&(!!te(e,"title")||!!te(e,"excerpt")||!ce(e)||"native"===i.Platform.OS)}function ce(e){const t=Le(e);if(t.length){if(t.length>1)return!1;const e=t[0].name;if(e!==(0,a.getDefaultBlockName)()&&e!==(0,a.getFreeformContentHandlerName)())return!1}return!Ee(e)}const ue=(0,l.createRegistrySelector)((e=>t=>{if(!le(t))return!1;if(Be(t))return!1;const n=Q(t),o=Y(t),r=e(c.store).hasFetchedAutosaves(n,o),i=(0,s.get)(e(c.store).getCurrentUser(),["id"]),a=e(c.store).getAutosave(n,o,i);return!!r&&(!a||(!!G(t)||["title","excerpt"].some((e=>h(a[e])!==te(t,e)))))}));function de(e){const t=te(e,"date"),n=new Date(Number((0,w.getDate)(t))-6e4);return(0,w.isInTheFuture)(n)}function pe(e){const t=te(e,"date"),n=te(e,"modified"),o=q(e).status;return("draft"===o||"auto-draft"===o||"pending"===o)&&(t===n||null===t)}const me=(0,l.createRegistrySelector)((e=>t=>{const n=Q(t),o=Y(t);return e(c.store).isSavingEntityRecord("postType",n,o)})),he=(0,l.createRegistrySelector)((e=>t=>{const n=e(c.store).__experimentalGetEntitiesBeingSaved(),{type:o,id:r}=q(t);return(0,s.some)(n,(e=>"postType"!==e.kind||e.name!==o||e.key!==r))})),ge=(0,l.createRegistrySelector)((e=>t=>{const n=Q(t),o=Y(t);return!e(c.store).getLastEntitySaveError("postType",n,o)})),fe=(0,l.createRegistrySelector)((e=>t=>{const n=Q(t),o=Y(t);return!!e(c.store).getLastEntitySaveError("postType",n,o)}));function ve(e){return!!me(e)&&!!(0,s.get)(e.saving,["options","isAutosave"])}function _e(e){return!!me(e)&&!!(0,s.get)(e.saving,["options","isPreview"])}function ye(e){if(e.saving.pending||me(e))return;let t=ne(e,"preview_link");t&&"draft"!==q(e).status||(t=te(e,"link"),t&&(t=(0,S.addQueryArgs)(t,{preview:!0})));const n=te(e,"featured_media");return t&&n?(0,S.addQueryArgs)(t,{_thumbnail_id:n}):t}function be(e){const t=Le(e);if(t.length>2)return null;let n;if(1===t.length&&(n=t[0].name,"core/embed"===n)){var o;const e=null===(o=t[0].attributes)||void 0===o?void 0:o.providerNameSlug;["youtube","vimeo"].includes(e)?n="core/video":["spotify","soundcloud"].includes(e)&&(n="core/audio")}switch(2===t.length&&"core/paragraph"===t[1].name&&(n=t[0].name),n){case"core/image":return"image";case"core/quote":case"core/pullquote":return"quote";case"core/gallery":return"gallery";case"core/video":return"video";case"core/audio":return"audio";default:return null}}const Ee=(0,l.createRegistrySelector)((e=>t=>{const n=Y(t),o=Q(t),r=e(c.store).getEditedEntityRecord("postType",o,n);if(r){if("function"==typeof r.content)return r.content(r);if(r.blocks)return(0,a.__unstableSerializeAndClean)(r.blocks);if(r.content)return r.content}return""}));function we(e){return me(e)&&!ie(e)&&"publish"===te(e,"status")}function Se(e){const t=te(e,"permalink_template");return N.test(t)}function Pe(e){const t=Ce(e);if(!t)return null;const{prefix:n,postName:o,suffix:r}=t;return Se(e)?n+o+r:n}function ke(e){return te(e,"slug")||(0,S.cleanForSlug)(te(e,"title"))||Y(e)}function Ce(e){const t=te(e,"permalink_template");if(!t)return null;const n=te(e,"slug")||te(e,"generated_slug"),[o,r]=t.split(N);return{prefix:o,postName:n,suffix:r}}function Te(e){return e.postLock.isLocked}function xe(e){return Object.keys(e.postSavingLock).length>0}function Be(e){return Object.keys(e.postAutosavingLock).length>0}function Ae(e){return e.postLock.isTakeover}function Ie(e){return e.postLock.user}function Ne(e){return e.postLock.activePostLock}function Re(e){return(0,s.has)(q(e),["_links","wp:action-unfiltered-html"])}const De=(0,l.createRegistrySelector)((e=>()=>!!e(x.store).get("core/edit-post","isPublishSidebarEnabled")));function Le(e){return te(e,"blocks")||z}function Oe(e){var t;return k()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),null===(t=te(e,"selection"))||void 0===t?void 0:t.selectionStart}function Fe(e){var t;return k()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),null===(t=te(e,"selection"))||void 0===t?void 0:t.selectionEnd}function Ue(e){return te(e,"selection")}function Me(e){return e.isReady}function ze(e){return e.editorSettings}function Ve(){return k()("select('core/editor').getStateBeforeOptimisticTransaction",{since:"5.7",hint:"No state history is kept on this store anymore"}),null}function He(){return k()("select('core/editor').inSomeHistory",{since:"5.7",hint:"No state history is kept on this store anymore"}),!1}function We(e){return(0,l.createRegistrySelector)((t=>function(n){k()("`wp.data.select( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.select( 'core/block-editor' )."+e+"`",version:"6.2"});for(var o=arguments.length,r=new Array(o>1?o-1:0),i=1;i<o;i++)r[i-1]=arguments[i];return t(p.store)[e](...r)}))}const Ge=We("getBlockName"),je=We("isBlockValid"),$e=We("getBlockAttributes"),Ke=We("getBlock"),qe=We("getBlocks"),Qe=We("getClientIdsOfDescendants"),Ye=We("getClientIdsWithDescendants"),Xe=We("getGlobalBlockCount"),Ze=We("getBlocksByClientId"),Je=We("getBlockCount"),et=We("getBlockSelectionStart"),tt=We("getBlockSelectionEnd"),nt=We("getSelectedBlockCount"),ot=We("hasSelectedBlock"),rt=We("getSelectedBlockClientId"),it=We("getSelectedBlock"),st=We("getBlockRootClientId"),at=We("getBlockHierarchyRootClientId"),lt=We("getAdjacentBlockClientId"),ct=We("getPreviousBlockClientId"),ut=We("getNextBlockClientId"),dt=We("getSelectedBlocksInitialCaretPosition"),pt=We("getMultiSelectedBlockClientIds"),mt=We("getMultiSelectedBlocks"),ht=We("getFirstMultiSelectedBlockClientId"),gt=We("getLastMultiSelectedBlockClientId"),ft=We("isFirstMultiSelectedBlock"),vt=We("isBlockMultiSelected"),_t=We("isAncestorMultiSelected"),yt=We("getMultiSelectedBlocksStartClientId"),bt=We("getMultiSelectedBlocksEndClientId"),Et=We("getBlockOrder"),wt=We("getBlockIndex"),St=We("isBlockSelected"),Pt=We("hasSelectedInnerBlock"),kt=We("isBlockWithinSelection"),Ct=We("hasMultiSelection"),Tt=We("isMultiSelecting"),xt=We("isSelectionEnabled"),Bt=We("getBlockMode"),At=We("isTyping"),It=We("isCaretWithinFormattedText"),Nt=We("getBlockInsertionPoint"),Rt=We("isBlockInsertionPointVisible"),Dt=We("isValidTemplate"),Lt=We("getTemplate"),Ot=We("getTemplateLock"),Ft=We("canInsertBlockType"),Ut=We("getInserterItems"),Mt=We("hasInserterItems"),zt=We("getBlockListSettings");function Vt(e){var t;return null===(t=ze(e))||void 0===t?void 0:t.defaultTemplateTypes}const Ht=E((e=>{var t;const n=(null===(t=ze(e))||void 0===t?void 0:t.defaultTemplatePartAreas)||[];return null==n?void 0:n.map((e=>({...e,icon:U(e.icon)})))}),(e=>{var t;return[null===(t=ze(e))||void 0===t?void 0:t.defaultTemplatePartAreas]})),Wt=E(((e,t)=>(0,s.find)(Vt(e),{slug:t})||{}),((e,t)=>[Vt(e),t]));function Gt(e,t){var n;if(!t)return{};const{excerpt:o,slug:r,title:i,area:a}=t,{title:l,description:c}=Wt(e,r),u=(0,s.isString)(i)?i:null==i?void 0:i.rendered;return{title:u&&u!==r?u:l||r,description:((0,s.isString)(o)?o:null==o?void 0:o.raw)||c,icon:(null===(n=Ht(e).find((e=>a===e.area)))||void 0===n?void 0:n.icon)||T}}const jt=(0,l.createRegistrySelector)((e=>t=>{var n;const o=Q(t),r=e(c.store).getPostType(o);return null==r||null===(n=r.labels)||void 0===n?void 0:n.singular_name}));var $t=window.wp.apiFetch,Kt=n.n($t),qt=window.wp.notices;function Qt(e,t){return`wp-autosave-block-editor-post-${t?"auto-draft":e}`}function Yt(e,t,n,o,r){window.sessionStorage.setItem(Qt(e,t),JSON.stringify({post_title:n,content:o,excerpt:r}))}function Xt(e,t){window.sessionStorage.removeItem(Qt(e,t))}var Zt=window.wp.i18n;function Jt(e){const{previousPost:t,post:n,postType:o}=e;if((0,s.get)(e.options,["isAutosave"]))return[];if("trash"===n.status&&"trash"!==t.status)return[];const r=["publish","private","future"],i=(0,s.includes)(r,t.status),a=(0,s.includes)(r,n.status);let l,c,u=(0,s.get)(o,["viewable"],!1);i||a?i&&!a?(l=o.labels.item_reverted_to_draft,u=!1):l=!i&&a?{publish:o.labels.item_published,private:o.labels.item_published_privately,future:o.labels.item_scheduled}[n.status]:o.labels.item_updated:(l=(0,Zt.__)("Draft saved."),c=!0);const d=[];return u&&d.push({label:c?(0,Zt.__)("View Preview"):o.labels.view_item,url:n.link}),[l,{id:A,type:"snackbar",actions:d}]}function en(e){const{post:t,edits:n,error:o}=e;if(o&&"rest_autosave_no_changes"===o.code)return[];const r=["publish","private","future"],i=-1!==r.indexOf(t.status),s={publish:(0,Zt.__)("Publishing failed."),private:(0,Zt.__)("Publishing failed."),future:(0,Zt.__)("Scheduling failed.")};let a=i||-1===r.indexOf(n.status)?(0,Zt.__)("Updating failed."):s[n.status];return o.message&&!/<\/?[^>]*>/.test(o.message)&&(a=[a,o.message].join(" ")),[a,{id:A}]}const tn=(e,t,n)=>o=>{let{dispatch:r}=o;r.setupEditorState(e);if("auto-draft"===e.status&&n){let o;o=(0,s.has)(t,["content"])?t.content:e.content.raw;let i=(0,a.parse)(o);i=(0,a.synchronizeBlocksWithTemplate)(i,n),r.resetEditorBlocks(i,{__unstableShouldCreateUndoLevel:!1})}t&&Object.values(t).some((t=>{var n,o;let[r,i]=t;return i!==(null!==(n=null===(o=e[r])||void 0===o?void 0:o.raw)&&void 0!==n?n:e[r])}))&&r.editPost(t)};function nn(){return{type:"TEAR_DOWN_EDITOR"}}function on(){return k()("wp.data.dispatch( 'core/editor' ).resetPost",{since:"6.0",version:"6.3",alternative:"Initialize the editor with the setupEditorState action"}),{type:"DO_NOTHING"}}function rn(){return k()("wp.data.dispatch( 'core/editor' ).updatePost",{since:"5.7",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function sn(e){return{type:"SETUP_EDITOR_STATE",post:e}}const an=(e,t)=>n=>{let{select:o,registry:r}=n;const{id:i,type:s}=o.getCurrentPost();r.dispatch(c.store).editEntityRecord("postType",s,i,e,t)},ln=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return async t=>{let{select:n,dispatch:o,registry:r}=t;if(!n.isEditedPostSaveable())return;const i=n.getEditedPostContent();e.isAutosave||o.editPost({content:i},{undoIgnore:!0});const s=n.getCurrentPost(),a={id:s.id,...r.select(c.store).getEntityRecordNonTransientEdits("postType",s.type,s.id),content:i};o({type:"REQUEST_POST_UPDATE_START",options:e}),await r.dispatch(c.store).saveEntityRecord("postType",s.type,a,e),o({type:"REQUEST_POST_UPDATE_FINISH",options:e});const l=r.select(c.store).getLastEntitySaveError("postType",s.type,s.id);if(l){const e=en({post:s,edits:a,error:l});e.length&&r.dispatch(qt.store).createErrorNotice(...e)}else{const t=n.getCurrentPost(),o=Jt({previousPost:s,post:t,postType:await r.resolveSelect(c.store).getPostType(t.type),options:e});o.length&&r.dispatch(qt.store).createSuccessNotice(...o),e.isAutosave||r.dispatch(p.store).__unstableMarkLastChangeAsPersistent()}}};function cn(){return k()("wp.data.dispatch( 'core/editor' ).refreshPost",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}const un=()=>async e=>{let{select:t,dispatch:n,registry:o}=e;const r=t.getCurrentPostType(),i=await o.resolveSelect(c.store).getPostType(r);o.dispatch(qt.store).removeNotice(I);try{const e=t.getCurrentPost();await Kt()({path:`/wp/v2/${i.rest_base}/${e.id}`,method:"DELETE"}),await n.savePost()}catch(e){o.dispatch(qt.store).createErrorNotice(...(s={error:e},[s.error.message&&"unknown_error"!==s.error.code?s.error.message:(0,Zt.__)("Trashing failed"),{id:I}]))}var s},dn=function(){let{local:e=!1,...t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return async n=>{let{select:o,dispatch:r}=n;if(e){const e=o.getCurrentPost(),t=o.isEditedPostNew(),n=o.getEditedPostAttribute("title"),r=o.getEditedPostAttribute("content"),i=o.getEditedPostAttribute("excerpt");Yt(e.id,t,n,r,i)}else await r.savePost({isAutosave:!0,...t})}},pn=()=>e=>{let{registry:t}=e;t.dispatch(c.store).redo()},mn=()=>e=>{let{registry:t}=e;t.dispatch(c.store).undo()};function hn(){return k()("wp.data.dispatch( 'core/editor' ).createUndoLevel",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function gn(e){return{type:"UPDATE_POST_LOCK",lock:e}}const fn=()=>e=>{let{registry:t}=e;t.dispatch(x.store).set("core/edit-post","isPublishSidebarEnabled",!0)},vn=()=>e=>{let{registry:t}=e;t.dispatch(x.store).set("core/edit-post","isPublishSidebarEnabled",!1)};function _n(e){return{type:"LOCK_POST_SAVING",lockName:e}}function yn(e){return{type:"UNLOCK_POST_SAVING",lockName:e}}function bn(e){return{type:"LOCK_POST_AUTOSAVING",lockName:e}}function En(e){return{type:"UNLOCK_POST_AUTOSAVING",lockName:e}}const wn=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=>{let{select:o,dispatch:r,registry:i}=n;const{__unstableShouldCreateUndoLevel:s,selection:l}=t,u={blocks:e,selection:l};if(!1!==s){const{id:e,type:t}=o.getCurrentPost();if(i.select(c.store).getEditedEntityRecord("postType",t,e).blocks===u.blocks)return void i.dispatch(c.store).__unstableCreateUndoLevel("postType",t,e);u.content=e=>{let{blocks:t=[]}=e;return(0,a.__unstableSerializeAndClean)(t)}}r.editPost(u)}};function Sn(e){return{type:"UPDATE_EDITOR_SETTINGS",settings:e}}const Pn=e=>function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return t=>{let{registry:o}=t;k()("`wp.data.dispatch( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.dispatch( 'core/block-editor' )."+e+"`",version:"6.2"}),o.dispatch(p.store)[e](...n)}},kn=Pn("resetBlocks"),Cn=Pn("receiveBlocks"),Tn=Pn("updateBlock"),xn=Pn("updateBlockAttributes"),Bn=Pn("selectBlock"),An=Pn("startMultiSelect"),In=Pn("stopMultiSelect"),Nn=Pn("multiSelect"),Rn=Pn("clearSelectedBlock"),Dn=Pn("toggleSelection"),Ln=Pn("replaceBlocks"),On=Pn("replaceBlock"),Fn=Pn("moveBlocksDown"),Un=Pn("moveBlocksUp"),Mn=Pn("moveBlockToPosition"),zn=Pn("insertBlock"),Vn=Pn("insertBlocks"),Hn=Pn("showInsertionPoint"),Wn=Pn("hideInsertionPoint"),Gn=Pn("setTemplateValidity"),jn=Pn("synchronizeTemplate"),$n=Pn("mergeBlocks"),Kn=Pn("removeBlocks"),qn=Pn("removeBlock"),Qn=Pn("toggleBlockMode"),Yn=Pn("startTyping"),Xn=Pn("stopTyping"),Zn=Pn("enterFormattedText"),Jn=Pn("exitFormattedText"),eo=Pn("insertDefaultBlock"),to=Pn("updateBlockListSettings"),no={reducer:v,selectors:e,actions:t},oo=(0,l.createReduxStore)("core/editor",{...no});(0,l.register)(oo);function ro(e){const t=(0,s.mapValues)((0,s.pickBy)(e.attributes,{source:"meta"}),"meta");return(0,s.isEmpty)(t)||(e.edit=(e=>(0,u.createHigherOrderComponent)((t=>n=>{let{attributes:o,setAttributes:a,...u}=n;const d=(0,l.useSelect)((e=>e(oo).getCurrentPostType()),[]),[p,m]=(0,c.useEntityProp)("postType",d,"meta"),h=(0,i.useMemo)((()=>({...o,...(0,s.mapValues)(e,(e=>p[e]))})),[o,p]);return(0,i.createElement)(t,r({attributes:h,setAttributes:t=>{const n=(0,s.mapKeys)((0,s.pickBy)(t,((t,n)=>e[n])),((t,n)=>e[n]));(0,s.isEmpty)(n)||m(n),a(t)}},u))}),"withMetaAttributeSource"))(t)(e.edit)),e}function io(e){const t=e.avatar_urls&&e.avatar_urls[24]?(0,i.createElement)("img",{className:"editor-autocompleters__user-avatar",alt:"",src:e.avatar_urls[24]}):(0,i.createElement)("span",{className:"editor-autocompleters__no-avatar"});return(0,i.createElement)(i.Fragment,null,t,(0,i.createElement)("span",{className:"editor-autocompleters__user-name"},e.name),(0,i.createElement)("span",{className:"editor-autocompleters__user-slug"},e.slug))}(0,d.addFilter)("blocks.registerBlockType","core/editor/custom-sources-backwards-compatibility/shim-attribute-source",ro),(0,l.select)(a.store).getBlockTypes().map((e=>{let{name:t}=e;return(0,l.select)(a.store).getBlockType(t)})).forEach(ro);var so={name:"users",className:"editor-autocompleters__user",triggerPrefix:"@",useItems(e){const t=(0,l.useSelect)((t=>{const{getUsers:n}=t(c.store);return n({context:"view",search:encodeURIComponent(e)})}),[e]),n=(0,i.useMemo)((()=>t?t.map((e=>({key:`user-${e.slug}`,value:e,label:io(e)}))):[]),[t]);return[n]},getOptionCompletion:e=>`@${e.slug}`};(0,d.addFilter)("editor.Autocomplete.completers","editor/autocompleters/set-default-completers",(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.push((0,s.clone)(so)),e}));class ao extends i.Component{constructor(e){super(e),this.needsAutosave=!(!e.isDirty||!e.isAutosaveable)}componentDidMount(){this.props.disableIntervalChecks||this.setAutosaveTimer()}componentDidUpdate(e){this.props.disableIntervalChecks?this.props.editsReference!==e.editsReference&&this.props.autosave():(this.props.interval!==e.interval&&(clearTimeout(this.timerId),this.setAutosaveTimer()),this.props.isDirty&&(!this.props.isAutosaving||e.isAutosaving)?this.props.editsReference!==e.editsReference&&(this.needsAutosave=!0):this.needsAutosave=!1)}componentWillUnmount(){clearTimeout(this.timerId)}setAutosaveTimer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3*this.props.interval;this.timerId=setTimeout((()=>{this.autosaveTimerHandler()}),e)}autosaveTimerHandler(){this.props.isAutosaveable?(this.needsAutosave&&(this.needsAutosave=!1,this.props.autosave()),this.setAutosaveTimer()):this.setAutosaveTimer(1e3)}render(){return null}}var lo=(0,u.compose)([(0,l.withSelect)(((e,t)=>{const{getReferenceByDistinctEdits:n}=e(c.store),{isEditedPostDirty:o,isEditedPostAutosaveable:r,isAutosavingPost:i,getEditorSettings:s}=e(oo),{interval:a=s().autosaveInterval}=t;return{editsReference:n(),isDirty:o(),isAutosaveable:r(),isAutosaving:i(),interval:a}})),(0,l.withDispatch)(((e,t)=>({autosave(){const{autosave:n=e(oo).autosave}=t;n()}})))])(ao),co=window.wp.richText,uo=n(4403),po=n.n(uo);var mo=e=>{let{children:t,isValid:n,level:o,href:r,onSelect:s}=e;return(0,i.createElement)("li",{className:po()("document-outline__item",`is-${o.toLowerCase()}`,{"is-invalid":!n})},(0,i.createElement)("a",{href:r,className:"document-outline__button",onClick:s},(0,i.createElement)("span",{className:"document-outline__emdash","aria-hidden":"true"}),(0,i.createElement)("strong",{className:"document-outline__level"},o),(0,i.createElement)("span",{className:"document-outline__item-content"},t)))};const ho=(0,i.createElement)("em",null,(0,Zt.__)("(Empty heading)")),go=[(0,i.createElement)("br",{key:"incorrect-break"}),(0,i.createElement)("em",{key:"incorrect-message"},(0,Zt.__)("(Incorrect heading level)"))],fo=[(0,i.createElement)("br",{key:"incorrect-break-h1"}),(0,i.createElement)("em",{key:"incorrect-message-h1"},(0,Zt.__)("(Your theme may already use a H1 for the post title)"))],vo=[(0,i.createElement)("br",{key:"incorrect-break-multiple-h1"}),(0,i.createElement)("em",{key:"incorrect-message-multiple-h1"},(0,Zt.__)("(Multiple H1 headings are not recommended)"))],_o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return(0,s.flatMap)(e,(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return"core/heading"===e.name?{...e,level:e.attributes.level,isEmpty:yo(e)}:_o(e.innerBlocks)}))},yo=e=>!e.attributes.content||0===e.attributes.content.length;var bo=(0,u.compose)((0,l.withSelect)((e=>{const{getBlocks:t}=e(p.store),{getEditedPostAttribute:n}=e(oo),{getPostType:o}=e(c.store),r=o(n("type"));return{title:n("title"),blocks:t(),isTitleSupported:(0,s.get)(r,["supports","title"],!1)}})))((e=>{let{blocks:t=[],title:n,onSelect:o,isTitleSupported:r,hasOutlineItemsDisabled:a}=e;const l=_o(t);if(l.length<1)return null;let c=1;const u=document.querySelector(".editor-post-title__input"),d=r&&n&&u,p=(0,s.countBy)(l,"level")[1]>1;return(0,i.createElement)("div",{className:"document-outline"},(0,i.createElement)("ul",null,d&&(0,i.createElement)(mo,{level:(0,Zt.__)("Title"),isValid:!0,onSelect:o,href:`#${u.id}`,isDisabled:a},n),l.map(((e,t)=>{const n=e.level>c+1,r=!(e.isEmpty||n||!e.level||1===e.level&&(p||d));return c=e.level,(0,i.createElement)(mo,{key:t,level:`H${e.level}`,isValid:r,isDisabled:a,href:`#block-${e.clientId}`,onSelect:o},e.isEmpty?ho:(0,co.getTextContent)((0,co.create)({html:e.attributes.content})),n&&go,1===e.level&&p&&vo,d&&1===e.level&&!p&&fo)}))))}));var Eo=(0,l.withSelect)((e=>({blocks:e(p.store).getBlocks()})))((function(e){let{blocks:t,children:n}=e;return(0,s.filter)(t,(e=>"core/heading"===e.name)).length<1?null:n})),wo=window.wp.keyboardShortcuts;var So=function(e){let{resetBlocksOnSave:t}=e;const{resetEditorBlocks:n,savePost:o}=(0,l.useDispatch)(oo),{isEditedPostDirty:r,getPostEdits:i,isPostSavingLocked:s}=(0,l.useSelect)(oo);return(0,wo.useShortcut)("core/editor/save",(e=>{if(e.preventDefault(),!s()&&r()){if(t){const e=i();if(e.content&&"string"==typeof e.content){const t=(0,a.parse)(e.content);n(t)}}o()}})),null};var Po=function(){const{redo:e,undo:t}=(0,l.useDispatch)(oo);return(0,wo.useShortcut)("core/editor/undo",(e=>{t(),e.preventDefault()})),(0,wo.useShortcut)("core/editor/redo",(t=>{e(),t.preventDefault()})),(0,i.createElement)(So,null)};function ko(){return(0,i.createElement)(So,{resetBlocksOnSave:!0})}var Co=function(){const{registerShortcut:e}=(0,l.useDispatch)(wo.store);return(0,i.useEffect)((()=>{e({name:"core/editor/save",category:"global",description:(0,Zt.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/editor/undo",category:"global",description:(0,Zt.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/editor/redo",category:"global",description:(0,Zt.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}})}),[e]),(0,i.createElement)(p.BlockEditorKeyboardShortcuts.Register,null)},To=window.wp.components,xo=window.wp.keycodes;var Bo=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"}));var Ao=(0,i.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(C.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"}));var Io=(0,i.forwardRef)((function(e,t){const n=(0,l.useSelect)((e=>e(oo).hasEditorRedo()),[]),{redo:o}=(0,l.useDispatch)(oo);return(0,i.createElement)(To.Button,r({},e,{ref:t,icon:(0,Zt.isRTL)()?Ao:Bo,label:(0,Zt.__)("Redo"),shortcut:xo.displayShortcut.primaryShift("z"),"aria-disabled":!n,onClick:n?o:void 0,className:"editor-history__redo"}))}));var No=(0,i.forwardRef)((function(e,t){const n=(0,l.useSelect)((e=>e(oo).hasEditorUndo()),[]),{undo:o}=(0,l.useDispatch)(oo);return(0,i.createElement)(To.Button,r({},e,{ref:t,icon:(0,Zt.isRTL)()?Bo:Ao,label:(0,Zt.__)("Undo"),shortcut:xo.displayShortcut.primary("z"),"aria-disabled":!n,onClick:n?o:void 0,className:"editor-history__undo"}))}));var Ro=(0,u.compose)([(0,l.withSelect)((e=>({isValid:e(p.store).isValidTemplate()}))),(0,l.withDispatch)((e=>{const{setTemplateValidity:t,synchronizeTemplate:n}=e(p.store);return{resetTemplateValidity:()=>t(!0),synchronizeTemplate:n}}))])((function(e){let{isValid:t,...n}=e;return t?null:(0,i.createElement)(To.Notice,{className:"editor-template-validation-notice",isDismissible:!1,status:"warning",actions:[{label:(0,Zt.__)("Keep it as is"),onClick:n.resetTemplateValidity},{label:(0,Zt.__)("Reset the template"),onClick:()=>{window.confirm((0,Zt.__)("Resetting the template may result in loss of content, do you want to continue?"))&&n.synchronizeTemplate()}}]},(0,Zt.__)("The content of your post doesn’t match the template assigned to your post type."))}));var Do=(0,u.compose)([(0,l.withSelect)((e=>({notices:e(qt.store).getNotices()}))),(0,l.withDispatch)((e=>({onRemove:e(qt.store).removeNotice})))])((function(e){let{notices:t,onRemove:n}=e;const o=(0,s.filter)(t,{isDismissible:!0,type:"default"}),r=(0,s.filter)(t,{isDismissible:!1,type:"default"});return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(To.NoticeList,{notices:r,className:"components-editor-notices__pinned"}),(0,i.createElement)(To.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:n},(0,i.createElement)(Ro,null)))}));function Lo(){const e=(0,l.useSelect)((e=>e(qt.store).getNotices()),[]),{removeNotice:t}=(0,l.useDispatch)(qt.store),n=(0,s.filter)(e,{type:"snackbar"});return(0,i.createElement)(To.SnackbarList,{notices:n,className:"components-editor-notices__snackbar",onRemove:t})}var Oo=window.wp.htmlEntities;function Fo(e){let{record:t,checked:n,onChange:o,closePanel:r}=e;const{name:s,kind:a,title:u,key:d}=t,m=(0,l.useSelect)((e=>{var t;const{blocks:n=[]}=e(c.store).getEditedEntityRecord(a,s,d),o=e(p.store).getBlockParents(null===(t=n[0])||void 0===t?void 0:t.clientId);return o[o.length-1]}),[]),h=(0,l.useSelect)((e=>{if("postType"!==a||"wp_template"!==s)return u;const t=e(c.store).getEditedEntityRecord(a,s,d);return e(oo).__experimentalGetTemplateInfo(t).title}),[s,a,u,d]),g=(0,l.useSelect)((e=>e(p.store).getSelectedBlockClientId()===m),[m]),f=g?(0,Zt.__)("Selected"):(0,Zt.__)("Select"),{selectBlock:v}=(0,l.useDispatch)(p.store),_=(0,i.useCallback)((()=>v(m)),[m]),y=(0,i.useCallback)((()=>{v(m),r()}),[m]);return(0,i.createElement)(To.PanelRow,null,(0,i.createElement)(To.CheckboxControl,{label:(0,i.createElement)("strong",null,(0,Oo.decodeEntities)(h)||(0,Zt.__)("Untitled")),checked:n,onChange:o}),m?(0,i.createElement)(i.Fragment,null,(0,i.createElement)(To.Button,{onClick:_,className:"entities-saved-states__find-entity",disabled:g},f),(0,i.createElement)(To.Button,{onClick:y,className:"entities-saved-states__find-entity-small",disabled:g},f)):null)}function Uo(e){let{list:t,unselectedEntities:n,setUnselectedEntities:o,closePanel:r}=e;const a=t.length,u=t[0],d=(0,l.useSelect)((e=>e(c.store).getEntityConfig(u.kind,u.name)),[u.kind,u.name]),{name:p}=u;let m=d.label;"wp_template_part"===p&&(m=1===a?(0,Zt.__)("Template Part"):(0,Zt.__)("Template Parts"));const h=function(e,t){switch(e){case"site":return 1===t?(0,Zt.__)("This change will affect your whole site."):(0,Zt.__)("These changes will affect your whole site.");case"wp_template":return(0,Zt.__)("This change will affect pages and posts that use this template.");case"page":case"post":return(0,Zt.__)("The following content has been modified.")}}(p,a);return(0,i.createElement)(To.PanelBody,{title:m,initialOpen:!0},h&&(0,i.createElement)(To.PanelRow,null,h),t.map((e=>(0,i.createElement)(Fo,{key:e.key||e.property,record:e,checked:!(0,s.some)(n,(t=>t.kind===e.kind&&t.name===e.name&&t.key===e.key&&t.property===e.property)),onChange:t=>o(e,t),closePanel:r}))))}const Mo={title:(0,Zt.__)("Title"),description:(0,Zt.__)("Tagline"),site_logo:(0,Zt.__)("Logo"),site_icon:(0,Zt.__)("Icon"),show_on_front:(0,Zt.__)("Show on front"),page_on_front:(0,Zt.__)("Page on front")},zo=[{kind:"postType",name:"wp_navigation"}];function Vo(e){let{close:t}=e;const n=(0,i.useRef)(),{dirtyEntityRecords:o}=(0,l.useSelect)((e=>{const t=e(c.store).__experimentalGetDirtyEntityRecords().filter((e=>!("root"===e.kind&&"site"===e.name))),n=e(c.store).getEntityRecordEdits("root","site"),o=[];for(const e in n)o.push({kind:"root",name:"site",title:Mo[e]||e,property:e});return{dirtyEntityRecords:[...t,...o]}}),[]),{editEntityRecord:a,saveEditedEntityRecord:d,__experimentalSaveSpecifiedEntityEdits:m}=(0,l.useDispatch)(c.store),{__unstableMarkLastChangeAsPersistent:h}=(0,l.useDispatch)(p.store),{createSuccessNotice:g,createErrorNotice:f}=(0,l.useDispatch)(qt.store),v=(0,s.groupBy)(o,"name"),{site:_,wp_template:y,wp_template_part:b,...E}=v,w=[_,y,b,...Object.values(E)].filter(Array.isArray),[S,P]=(0,i.useState)([]),k=(e,t)=>{let{kind:n,name:o,key:r,property:i}=e;P(t?S.filter((e=>e.kind!==n||e.name!==o||e.key!==r||e.property!==i)):[...S,{kind:n,name:o,key:r,property:i}])},C=(0,i.useCallback)((()=>t()),[t]),[T,x]=(0,u.__experimentalUseDialog)({onClose:()=>C()});return(0,i.createElement)("div",r({ref:T},x,{className:"entities-saved-states__panel"}),(0,i.createElement)(To.Flex,{className:"entities-saved-states__panel-header",gap:2},(0,i.createElement)(To.FlexItem,{isBlock:!0,as:To.Button,ref:n,variant:"primary",disabled:o.length-S.length==0,onClick:()=>{const e=o.filter((e=>{let{kind:t,name:n,key:o,property:r}=e;return!(0,s.some)(S,(e=>e.kind===t&&e.name===n&&e.key===o&&e.property===r))}));t(e);const n=[],r=[];e.forEach((e=>{let{kind:t,name:o,key:i,property:s}=e;"root"===t&&"site"===o?n.push(s):(zo.some((e=>e.kind===t&&e.name===o))&&a(t,o,i,{status:"publish"}),r.push(d(t,o,i)))})),n.length&&r.push(m("root","site",void 0,n)),h(),Promise.all(r).then((e=>{e.some((e=>void 0===e))?f((0,Zt.__)("Saving failed.")):g((0,Zt.__)("Site updated."),{type:"snackbar"})})).catch((e=>f(`${(0,Zt.__)("Saving failed.")} ${e}`)))},className:"editor-entities-saved-states__save-button"},(0,Zt.__)("Save")),(0,i.createElement)(To.FlexItem,{isBlock:!0,as:To.Button,variant:"secondary",onClick:C},(0,Zt.__)("Cancel"))),(0,i.createElement)("div",{className:"entities-saved-states__text-prompt"},(0,i.createElement)("strong",null,(0,Zt.__)("Are you ready to save?")),(0,i.createElement)("p",null,(0,Zt.__)("The following changes have been made to your site, templates, and content."))),w.map((e=>(0,i.createElement)(Uo,{key:e[0].name,list:e,closePanel:C,unselectedEntities:S,setUnselectedEntities:k}))))}function Ho(e){let{text:t,children:n}=e;const o=(0,u.useCopyToClipboard)(t);return(0,i.createElement)(To.Button,{variant:"secondary",ref:o},n)}class Wo extends i.Component{constructor(){super(...arguments),this.reboot=this.reboot.bind(this),this.getContent=this.getContent.bind(this),this.state={error:null}}componentDidCatch(e){this.setState({error:e})}reboot(){this.props.onError()}getContent(){try{return(0,l.select)(oo).getEditedPostContent()}catch(e){}}render(){const{error:e}=this.state;return e?(0,i.createElement)(p.Warning,{className:"editor-error-boundary",actions:[(0,i.createElement)(To.Button,{key:"recovery",onClick:this.reboot,variant:"secondary"},(0,Zt.__)("Attempt Recovery")),(0,i.createElement)(Ho,{key:"copy-post",text:this.getContent},(0,Zt.__)("Copy Post Text")),(0,i.createElement)(Ho,{key:"copy-error",text:e.stack},(0,Zt.__)("Copy Error"))]},(0,Zt.__)("The editor has encountered an unexpected error.")):this.props.children}}var Go=Wo;const jo=window.requestIdleCallback?window.requestIdleCallback:window.requestAnimationFrame,$o=(0,s.once)((()=>{try{return window.sessionStorage.setItem("__wpEditorTestSessionStorage",""),window.sessionStorage.removeItem("__wpEditorTestSessionStorage"),!0}catch(e){return!1}}));function Ko(){const{postId:e,isEditedPostNew:t,hasRemoteAutosave:n}=(0,l.useSelect)((e=>({postId:e(oo).getCurrentPostId(),isEditedPostNew:e(oo).isEditedPostNew(),hasRemoteAutosave:!!e(oo).getEditorSettings().autosave})),[]),{getEditedPostAttribute:o}=(0,l.useSelect)(oo),{createWarningNotice:r,removeNotice:c}=(0,l.useDispatch)(qt.store),{editPost:u,resetEditorBlocks:d}=(0,l.useDispatch)(oo);(0,i.useEffect)((()=>{let i=function(e,t){return window.sessionStorage.getItem(Qt(e,t))}(e,t);if(!i)return;try{i=JSON.parse(i)}catch(e){return}const{post_title:l,content:p,excerpt:m}=i,h={title:l,content:p,excerpt:m};if(!Object.keys(h).some((e=>h[e]!==o(e))))return void Xt(e,t);if(n)return;const g=(0,s.uniqueId)("wpEditorAutosaveRestore");r((0,Zt.__)("The backup of this post in your browser is different from the version below."),{id:g,actions:[{label:(0,Zt.__)("Restore the backup"),onClick(){u((0,s.omit)(h,["content"])),d((0,a.parse)(h.content)),c(g)}}]})}),[t,e])}var qo=(0,u.ifCondition)($o)((function(){const{autosave:e}=(0,l.useDispatch)(oo),t=(0,i.useCallback)((()=>{jo((()=>e({local:!0})))}),[]);Ko(),function(){const{postId:e,isEditedPostNew:t,isDirty:n,isAutosaving:o,didError:r}=(0,l.useSelect)((e=>({postId:e(oo).getCurrentPostId(),isEditedPostNew:e(oo).isEditedPostNew(),isDirty:e(oo).isEditedPostDirty(),isAutosaving:e(oo).isAutosavingPost(),didError:e(oo).didPostSaveRequestFail()})),[]),s=(0,i.useRef)(n),a=(0,i.useRef)(o);(0,i.useEffect)((()=>{!r&&(a.current&&!o||s.current&&!n)&&Xt(e,t),s.current=n,a.current=o}),[n,o,r]);const c=(0,u.usePrevious)(t),d=(0,u.usePrevious)(e);(0,i.useEffect)((()=>{d===e&&c&&!t&&Xt(e,!0)}),[t,e])}();const{localAutosaveInterval:n}=(0,l.useSelect)((e=>({localAutosaveInterval:e(oo).getEditorSettings().localAutosaveInterval})),[]);return(0,i.createElement)(lo,{interval:n,autosave:t})}));var Qo=function(e){let{children:t}=e;const n=(0,l.useSelect)((e=>{const{getEditedPostAttribute:t}=e(oo),{getPostType:n}=e(c.store);return n(t("type"))}),[]);return(0,s.get)(n,["supports","page-attributes"],!1)?t:null};var Yo=(0,l.withSelect)((e=>{const{getEditedPostAttribute:t}=e(oo),{getPostType:n}=e(c.store);return{postType:n(t("type"))}}))((function(e){let{postType:t,children:n,supportKeys:o}=e,r=!0;return t&&(r=(0,s.some)((0,s.castArray)(o),(e=>!!t.supports[e]))),r?n:null}));const Xo=e=>{let{onUpdateOrder:t,order:n=0}=e;const[o,r]=(0,i.useState)(null),a=null===o?n:o;return(0,i.createElement)(To.TextControl,{className:"editor-page-attributes__order",type:"number",label:(0,Zt.__)("Order"),value:a,onChange:e=>{r(e);const n=Number(e);Number.isInteger(n)&&""!==(0,s.invoke)(e,["trim"])&&t(Number(e))},size:6,onBlur:()=>{r(null)}})};var Zo=(0,u.compose)([(0,l.withSelect)((e=>({order:e(oo).getEditedPostAttribute("menu_order")}))),(0,l.withDispatch)((e=>({onUpdateOrder(t){e(oo).editPost({menu_order:t})}})))])((function(e){return(0,i.createElement)(Yo,{supportKeys:"page-attributes"},(0,i.createElement)(Xo,e))}));function Jo(e){const t=e.map((e=>({children:[],parent:null,...e}))),n=(0,s.groupBy)(t,"parent");if(n.null&&n.null.length)return t;const o=e=>e.map((e=>{const t=n[e.id];return{...e,children:t&&t.length?o(t):[]}}));return o(n[0]||[])}const er=e=>(0,s.unescape)(e.replace("&#039;","'")),tr=e=>({...e,name:er(e.name)}),nr=e=>(0,s.map)(e,tr);function or(e){var t;return null!=e&&null!==(t=e.title)&&void 0!==t&&t.rendered?(0,Oo.decodeEntities)(e.title.rendered):`#${e.id} (${(0,Zt.__)("no title")})`}const rr=(e,t)=>{const n=(0,s.deburr)(e).toLowerCase(),o=(0,s.deburr)(t).toLowerCase();return n===o?0:n.startsWith(o)?n.length:1/0};var ir=function(){const{editPost:e}=(0,l.useDispatch)(oo),[t,n]=(0,i.useState)(!1),{parentPost:o,parentPostId:r,items:a,postType:u}=(0,l.useSelect)((e=>{const{getPostType:n,getEntityRecords:o,getEntityRecord:r}=e(c.store),{getCurrentPostId:i,getEditedPostAttribute:a}=e(oo),l=a("type"),u=a("parent"),d=n(l),p=i(),m=(0,s.get)(d,["hierarchical"],!1),h={per_page:100,exclude:p,parent_exclude:p,orderby:"menu_order",order:"asc",_fields:"id,title,parent"};return t&&(h.search=t),{parentPostId:u,parentPost:u?r("postType",l,u):null,items:m?o("postType",l,h):[],postType:d}}),[t]),d=(0,s.get)(u,["hierarchical"],!1),p=(0,s.get)(u,["labels","parent_item_colon"]),m=a||[],h=(0,i.useMemo)((()=>{const e=function(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const r=n.map((t=>[{value:t.id,label:(0,s.repeat)("— ",o)+(0,s.unescape)(t.name),rawName:t.name},...e(t.children||[],o+1)])),i=r.sort(((e,n)=>{let[o]=e,[r]=n;return rr(o.rawName,t)>=rr(r.rawName,t)?1:-1}));return(0,s.flatten)(i)};let n=m.map((e=>({id:e.id,parent:e.parent,name:or(e)})));t||(n=Jo(n));const i=e(n),a=(0,s.find)(i,(e=>e.value===r));return o&&!a&&i.unshift({value:r,label:or(o)}),i}),[m,t]);return d&&p?(0,i.createElement)(To.ComboboxControl,{className:"editor-page-attributes__parent",label:p,value:r,options:h,onFilterValueChange:(0,s.debounce)((e=>{n(e)}),300),onChange:t=>{e({parent:t})}}):null};var sr=function(e){let{}=e;const{availableTemplates:t,selectedTemplate:n,isViewable:o}=(0,l.useSelect)((e=>{var t,n;const{getEditedPostAttribute:o,getEditorSettings:r,getCurrentPostType:i}=e(oo),{getPostType:s}=e(c.store);return{selectedTemplate:o("template"),availableTemplates:r().availableTemplates,isViewable:null!==(t=null===(n=s(i()))||void 0===n?void 0:n.viewable)&&void 0!==t&&t}}),[]),{editPost:r}=(0,l.useDispatch)(oo);return!o||(0,s.isEmpty)(t)?null:(0,i.createElement)(To.SelectControl,{label:(0,Zt.__)("Template:"),value:n,onChange:e=>{r({template:e||""})},options:(0,s.map)(t,((e,t)=>({value:t,label:e})))})};const ar={who:"authors",per_page:50,_fields:"id,name",context:"view"};var lr=function(){const[e,t]=(0,i.useState)(),{authorId:n,isLoading:o,authors:r,postAuthor:a}=(0,l.useSelect)((t=>{const{getUser:n,getUsers:o,isResolving:r}=t(c.store),{getEditedPostAttribute:i}=t(oo),s=n(i("author"),{context:"view"}),a={...ar};return e&&(a.search=e),{authorId:i("author"),postAuthor:s,authors:o(a),isLoading:r("core","getUsers",[a])}}),[e]),{editPost:u}=(0,l.useDispatch)(oo),d=(0,i.useMemo)((()=>{const e=(null!=r?r:[]).map((e=>({value:e.id,label:(0,Oo.decodeEntities)(e.name)})));return e.findIndex((e=>{let{value:t}=e;return(null==a?void 0:a.id)===t}))<0&&a?[{value:a.id,label:(0,Oo.decodeEntities)(a.name)},...e]:e}),[r,a]);return a?(0,i.createElement)(To.ComboboxControl,{label:(0,Zt.__)("Author"),options:d,value:n,onFilterValueChange:(0,s.debounce)((e=>{t(e)}),300),onChange:e=>{e&&u({author:e})},isLoading:o,allowReset:!1}):null};var cr=function(){const{editPost:e}=(0,l.useDispatch)(oo),{postAuthor:t,authors:n}=(0,l.useSelect)((e=>({postAuthor:e(oo).getEditedPostAttribute("author"),authors:e(c.store).getUsers(ar)})),[]),o=(0,i.useMemo)((()=>(null!=n?n:[]).map((e=>({value:e.id,label:(0,Oo.decodeEntities)(e.name)})))),[n]);return(0,i.createElement)(To.SelectControl,{className:"post-author-selector",label:(0,Zt.__)("Author"),options:o,onChange:t=>{const n=Number(t);e({author:n})},value:t})};var ur=function(){return(0,l.useSelect)((e=>{const t=e(c.store).getUsers(ar);return(null==t?void 0:t.length)>=25}),[])?(0,i.createElement)(lr,null):(0,i.createElement)(cr,null)};function dr(e){let{children:t}=e;const{hasAssignAuthorAction:n,hasAuthors: <AUTHORS>