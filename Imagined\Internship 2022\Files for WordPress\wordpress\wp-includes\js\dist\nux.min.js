/*! This file is auto-generated */
!function(){"use strict";var e={n:function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,{a:t}),t},d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{DotTip:function(){return A},store:function(){return E}});var t={};e.r(t),e.d(t,{disableTips:function(){return f},dismissTip:function(){return p},enableTips:function(){return w},triggerGuide:function(){return d}});var r={};e.r(r),e.d(r,{areTipsEnabled:function(){return I},getAssociatedGuide:function(){return b},isTipVisible:function(){return m}});var i=window.wp.deprecated,s=e.n(i),o=window.wp.data;const u=(0,o.combineReducers)({areTipsEnabled:function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1?arguments[1]:void 0;switch(n.type){case"DISABLE_TIPS":return!1;case"ENABLE_TIPS":return!0}return e},dismissedTips:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;switch(n.type){case"DISMISS_TIP":return{...e,[n.id]:!0};case"ENABLE_TIPS":return{}}return e}});var a,c,l=(0,o.combineReducers)({guides:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;return"TRIGGER_GUIDE"===n.type?[...e,n.tipIds]:e},preferences:u});function d(e){return{type:"TRIGGER_GUIDE",tipIds:e}}function p(e){return{type:"DISMISS_TIP",id:e}}function f(){return{type:"DISABLE_TIPS"}}function w(){return{type:"ENABLE_TIPS"}}function h(e){return[e]}function v(){var e={clear:function(){e.head=null}};return e}function g(e,n,t){var r;if(e.length!==n.length)return!1;for(r=t;r<e.length;r++)if(e[r]!==n[r])return!1;return!0}a={},c="undefined"!=typeof WeakMap;var T=window.lodash;const b=function(e,n){var t,r;function i(){t=c?new WeakMap:v()}function s(){var t,i,s,o,u,a=arguments.length;for(o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];for(u=n.apply(null,o),(t=r(u)).isUniqueByDependants||(t.lastDependants&&!g(u,t.lastDependants,0)&&t.clear(),t.lastDependants=u),i=t.head;i;){if(g(i.args,o,1))return i!==t.head&&(i.prev.next=i.next,i.next&&(i.next.prev=i.prev),i.next=t.head,i.prev=null,t.head.prev=i,t.head=i),i.val;i=i.next}return i={val:e.apply(null,o)},o[0]=null,i.args=o,t.head&&(t.head.prev=i,i.next=t.head),t.head=i,i.val}return n||(n=h),r=c?function(e){var n,r,i,s,o,u=t,c=!0;for(n=0;n<e.length;n++){if(r=e[n],!(o=r)||"object"!=typeof o){c=!1;break}u.has(r)?u=u.get(r):(i=new WeakMap,u.set(r,i),u=i)}return u.has(a)||((s=v()).isUniqueByDependants=c,u.set(a,s)),u.get(a)}:function(){return t},s.getDependants=n,s.clear=i,i(),s}(((e,n)=>{for(const t of e.guides)if((0,T.includes)(t,n)){const n=(0,T.difference)(t,(0,T.keys)(e.preferences.dismissedTips)),[r=null,i=null]=n;return{tipIds:t,currentTipId:r,nextTipId:i}}return null}),(e=>[e.guides,e.preferences.dismissedTips]));function m(e,n){if(!e.preferences.areTipsEnabled)return!1;if((0,T.has)(e.preferences.dismissedTips,[n]))return!1;const t=b(e,n);return!t||t.currentTipId===n}function I(e){return e.preferences.areTipsEnabled}const y="core/nux",E=(0,o.createReduxStore)(y,{reducer:l,actions:t,selectors:r,persist:["preferences"]});(0,o.registerStore)(y,{reducer:l,actions:t,selectors:r,persist:["preferences"]});var S=window.wp.element,_=window.wp.compose,x=window.wp.components,D=window.wp.i18n,P=window.wp.primitives;var G=(0,S.createElement)(P.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,S.createElement)(P.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));function k(e){e.stopPropagation()}var A=(0,_.compose)((0,o.withSelect)(((e,n)=>{let{tipId:t}=n;const{isTipVisible:r,getAssociatedGuide:i}=e(E),s=i(t);return{isVisible:r(t),hasNextTip:!(!s||!s.nextTipId)}})),(0,o.withDispatch)(((e,n)=>{let{tipId:t}=n;const{dismissTip:r,disableTips:i}=e(E);return{onDismiss(){r(t)},onDisable(){i()}}})))((function(e){let{position:n="middle right",children:t,isVisible:r,hasNextTip:i,onDismiss:s,onDisable:o}=e;const u=(0,S.useRef)(null),a=(0,S.useCallback)((e=>{u.current&&(u.current.contains(e.relatedTarget)||o())}),[o,u]);return r?(0,S.createElement)(x.Popover,{className:"nux-dot-tip",position:n,noArrow:!0,focusOnMount:"container",shouldAnchorIncludePadding:!0,role:"dialog","aria-label":(0,D.__)("Editor tips"),onClick:k,onFocusOutside:a},(0,S.createElement)("p",null,t),(0,S.createElement)("p",null,(0,S.createElement)(x.Button,{variant:"link",onClick:s},i?(0,D.__)("See next tip"):(0,D.__)("Got it"))),(0,S.createElement)(x.Button,{className:"nux-dot-tip__disable",icon:G,label:(0,D.__)("Disable tips"),onClick:o})):null}));s()("wp.nux",{since:"5.4",hint:"wp.components.Guide can be used to show a user guide.",version:"6.2"}),(window.wp=window.wp||{}).nux=n}();