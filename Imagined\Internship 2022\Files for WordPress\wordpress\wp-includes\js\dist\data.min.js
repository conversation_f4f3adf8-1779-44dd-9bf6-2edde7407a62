/*! This file is auto-generated */
!function(){var e={2167:function(e){"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t){var r=e._map,n=e._arrayTreeMap,o=e._objectTreeMap;if(r.has(t))return r.get(t);for(var i=Object.keys(t).sort(),s=Array.isArray(t)?n:o,u=0;u<i.length;u++){var c=i[u];if(void 0===(s=s.get(c)))return;var a=t[c];if(void 0===(s=s.get(a)))return}var l=s.get("_ekm_value");return l?(r.delete(l[0]),l[0]=t,s.set("_ekm_value",l),r.set(t,l),l):void 0}var o=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.clear(),t instanceof e){var r=[];t.forEach((function(e,t){r.push([t,e])})),t=r}if(null!=t)for(var n=0;n<t.length;n++)this.set(t[n][0],t[n][1])}var o,i,s;return o=e,i=[{key:"set",value:function(r,n){if(null===r||"object"!==t(r))return this._map.set(r,n),this;for(var o=Object.keys(r).sort(),i=[r,n],s=Array.isArray(r)?this._arrayTreeMap:this._objectTreeMap,u=0;u<o.length;u++){var c=o[u];s.has(c)||s.set(c,new e),s=s.get(c);var a=r[c];s.has(a)||s.set(a,new e),s=s.get(a)}var l=s.get("_ekm_value");return l&&this._map.delete(l[0]),s.set("_ekm_value",i),this._map.set(r,i),this}},{key:"get",value:function(e){if(null===e||"object"!==t(e))return this._map.get(e);var r=n(this,e);return r?r[1]:void 0}},{key:"has",value:function(e){return null===e||"object"!==t(e)?this._map.has(e):void 0!==n(this,e)}},{key:"delete",value:function(e){return!!this.has(e)&&(this.set(e,void 0),!0)}},{key:"forEach",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;this._map.forEach((function(o,i){null!==i&&"object"===t(i)&&(o=o[1]),e.call(n,o,i,r)}))}},{key:"clear",value:function(){this._map=new Map,this._arrayTreeMap=new Map,this._objectTreeMap=new Map}},{key:"size",get:function(){return this._map.size}}],i&&r(o.prototype,i),s&&r(o,s),e}();e.exports=o},9125:function(e){e.exports=function(e){var t,r=Object.keys(e);return t=function(){var e,t,n;for(e="return {",t=0;t<r.length;t++)e+=(n=JSON.stringify(r[t]))+":r["+n+"](s["+n+"],a),";return e+="}",new Function("r,s,a",e)}(),function(n,o){var i,s,u;if(void 0===n)return t(e,{},o);for(i=t(e,n,o),s=r.length;s--;)if(n[u=r[s]]!==i[u])return i;return n}}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){"use strict";r.r(n),r.d(n,{AsyncModeProvider:function(){return De},RegistryConsumer:function(){return Ce},RegistryProvider:function(){return Fe},combineReducers:function(){return s()},controls:function(){return j},createReduxStore:function(){return ie},createRegistry:function(){return ce},createRegistryControl:function(){return I},createRegistrySelector:function(){return _},dispatch:function(){return qe},plugins:function(){return o},register:function(){return tt},registerGenericStore:function(){return Ye},registerStore:function(){return Ze},resolveSelect:function(){return $e},select:function(){return Je},subscribe:function(){return Qe},use:function(){return et},useDispatch:function(){return ze},useRegistry:function(){return Ue},useSelect:function(){return He},withDispatch:function(){return Be},withRegistry:function(){return Xe},withSelect:function(){return Ke}});var e={};r.r(e),r.d(e,{getCachedResolvers:function(){return J},getIsResolving:function(){return H},getResolutionError:function(){return X},getResolutionState:function(){return G},hasFinishedResolution:function(){return W},hasResolutionFailed:function(){return B},hasStartedResolution:function(){return K},isResolving:function(){return z}});var t={};r.r(t),r.d(t,{failResolution:function(){return Q},failResolutions:function(){return ee},finishResolution:function(){return q},finishResolutions:function(){return Z},invalidateResolution:function(){return te},invalidateResolutionForStore:function(){return re},invalidateResolutionForStoreSelector:function(){return ne},startResolution:function(){return $},startResolutions:function(){return Y}});var o={};r.r(o),r.d(o,{persistence:function(){return Oe}});var i=r(9125),s=r.n(i),u=window.lodash,c=window.wp.deprecated,a=r.n(c);function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var v="function"==typeof Symbol&&Symbol.observable||"@@observable",g=function(){return Math.random().toString(36).substring(7).split("").join(".")},h={INIT:"@@redux/INIT"+g(),REPLACE:"@@redux/REPLACE"+g(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+g()}};function y(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function S(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(p(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(p(1));return r(S)(e,t)}if("function"!=typeof e)throw new Error(p(2));var o=e,i=t,s=[],u=s,c=!1;function a(){u===s&&(u=s.slice())}function l(){if(c)throw new Error(p(3));return i}function f(e){if("function"!=typeof e)throw new Error(p(4));if(c)throw new Error(p(5));var t=!0;return a(),u.push(e),function(){if(t){if(c)throw new Error(p(6));t=!1,a();var r=u.indexOf(e);u.splice(r,1),s=null}}}function d(e){if(!y(e))throw new Error(p(7));if(void 0===e.type)throw new Error(p(8));if(c)throw new Error(p(9));try{c=!0,i=o(i,e)}finally{c=!1}for(var t=s=u,r=0;r<t.length;r++){(0,t[r])()}return e}function g(e){if("function"!=typeof e)throw new Error(p(10));o=e,d({type:h.REPLACE})}function b(){var e,t=f;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(p(11));function r(){e.next&&e.next(l())}return r(),{unsubscribe:t(r)}}})[v]=function(){return this},e}return d({type:h.INIT}),(n={dispatch:d,subscribe:f,getState:l,replaceReducer:g})[v]=b,n}function b(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function m(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error(p(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=t.map((function(e){return e(o)}));return n=b.apply(void 0,i)(r.dispatch),d(d({},r),{},{dispatch:n})}}}var O=r(2167),w=r.n(O),R=window.wp.reduxRoutine,E=r.n(R);function _(e){const t=function(){return e(t.registry.select)(...arguments)};return t.isRegistrySelector=!0,t}function I(e){return e.isRegistryControl=!0,e}const T="@@data/SELECT",N="@@data/RESOLVE_SELECT",A="@@data/DISPATCH";const j={select:function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];return{type:T,storeKey:(0,u.isObject)(e)?e.name:e,selectorName:t,args:n}},resolveSelect:function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];return{type:N,storeKey:(0,u.isObject)(e)?e.name:e,selectorName:t,args:n}},dispatch:function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];return{type:A,storeKey:(0,u.isObject)(e)?e.name:e,actionName:t,args:n}}},L={[T]:I((e=>t=>{let{storeKey:r,selectorName:n,args:o}=t;return e.select(r)[n](...o)})),[N]:I((e=>t=>{let{storeKey:r,selectorName:n,args:o}=t;const i=e.select(r)[n].hasResolver?"resolveSelect":"select";return e[i](r)[n](...o)})),[A]:I((e=>t=>{let{storeKey:r,actionName:n,args:o}=t;return e.dispatch(r)[n](...o)}))};var P=()=>e=>t=>{return!(r=t)||"object"!=typeof r&&"function"!=typeof r||"function"!=typeof r.then?e(t):t.then((t=>{if(t)return e(t)}));var r};const C={name:"core/data",instantiate(e){const t=t=>function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return e.select(r)[t](...o)},r=t=>function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return e.dispatch(r)[t](...o)};return{getSelectors:()=>Object.fromEntries(["getIsResolving","hasStartedResolution","hasFinishedResolution","isResolving","getCachedResolvers"].map((e=>[e,t(e)]))),getActions:()=>Object.fromEntries(["startResolution","finishResolution","invalidateResolution","invalidateResolutionForStore","invalidateResolutionForStoreSelector"].map((e=>[e,r(e)]))),subscribe:()=>()=>()=>{}}}};var F=C;var U=(e,t)=>()=>r=>n=>{const o=e.select(F).getCachedResolvers(t);return Object.entries(o).forEach((r=>{let[o,i]=r;const s=(0,u.get)(e.stores,[t,"resolvers",o]);s&&s.shouldInvalidate&&i.forEach(((r,i)=>{"finished"!==(null==r?void 0:r.status)&&"error"!==(null==r?void 0:r.status)||!s.shouldInvalidate(n,...i)||e.dispatch(F).invalidateResolution(t,o,i)}))})),r(n)};function k(e){return()=>t=>r=>"function"==typeof r?r(e):t(r)}function x(e){if(null==e)return[];const t=e.length;let r=t;for(;r>0&&void 0===e[r-1];)r--;return r===t?e:e.slice(0,r)}const M=(D="selectorName",e=>function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;const n=r[D];if(void 0===n)return t;const o=e(t[n],r);return o===t[n]?t:{...t,[n]:o}})((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new(w()),t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"START_RESOLUTION":{const r=new(w())(e);return r.set(x(t.args),{status:"resolving"}),r}case"FINISH_RESOLUTION":{const r=new(w())(e);return r.set(x(t.args),{status:"finished"}),r}case"FAIL_RESOLUTION":{const r=new(w())(e);return r.set(x(t.args),{status:"error",error:t.error}),r}case"START_RESOLUTIONS":{const r=new(w())(e);for(const e of t.args)r.set(x(e),{status:"resolving"});return r}case"FINISH_RESOLUTIONS":{const r=new(w())(e);for(const e of t.args)r.set(x(e),{status:"finished"});return r}case"FAIL_RESOLUTIONS":{const r=new(w())(e);return t.args.forEach(((e,n)=>{const o={status:"error",error:void 0},i=t.errors[n];i&&(o.error=i),r.set(x(e),o)})),r}case"INVALIDATE_RESOLUTION":{const r=new(w())(e);return r.delete(x(t.args)),r}}return e}));var D;var V=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"INVALIDATE_RESOLUTION_FOR_STORE":return{};case"INVALIDATE_RESOLUTION_FOR_STORE_SELECTOR":return(0,u.has)(e,[t.selectorName])?(0,u.omit)(e,[t.selectorName]):e;case"START_RESOLUTION":case"FINISH_RESOLUTION":case"FAIL_RESOLUTION":case"START_RESOLUTIONS":case"FINISH_RESOLUTIONS":case"FAIL_RESOLUTIONS":case"INVALIDATE_RESOLUTION":return M(e,t)}return e};function G(e,t,r){const n=(0,u.get)(e,[t]);if(n)return n.get(x(r))}function H(e,t,r){const n=G(e,t,r);return n&&"resolving"===n.status}function K(e,t,r){return void 0!==G(e,t,r)}function W(e,t,r){var n;const o=null===(n=G(e,t,r))||void 0===n?void 0:n.status;return"finished"===o||"error"===o}function B(e,t,r){var n;return"error"===(null===(n=G(e,t,r))||void 0===n?void 0:n.status)}function X(e,t,r){const n=G(e,t,r);return"error"===(null==n?void 0:n.status)?n.error:null}function z(e,t,r){var n;return"resolving"===(null===(n=G(e,t,r))||void 0===n?void 0:n.status)}function J(e){return e}function $(e,t){return{type:"START_RESOLUTION",selectorName:e,args:t}}function q(e,t){return{type:"FINISH_RESOLUTION",selectorName:e,args:t}}function Q(e,t,r){return{type:"FAIL_RESOLUTION",selectorName:e,args:t,error:r}}function Y(e,t){return{type:"START_RESOLUTIONS",selectorName:e,args:t}}function Z(e,t){return{type:"FINISH_RESOLUTIONS",selectorName:e,args:t}}function ee(e,t,r){return{type:"FAIL_RESOLUTIONS",selectorName:e,args:t,errors:r}}function te(e,t){return{type:"INVALIDATE_RESOLUTION",selectorName:e,args:t}}function re(){return{type:"INVALIDATE_RESOLUTION_FOR_STORE"}}function ne(e){return{type:"INVALIDATE_RESOLUTION_FOR_STORE_SELECTOR",selectorName:e}}const oe=e=>{const t=[...e];for(let e=t.length-1;e>=0;e--)void 0===t[e]&&t.splice(e,1);return t};function ie(r,n){return{name:r,instantiate:o=>{const i=n.reducer,c=function(e,t,r,n){const o={...t.controls,...L},i=(0,u.mapValues)(o,(e=>e.isRegistryControl?e(r):e)),c=[m(U(r,e),P,E()(i),k(n))];"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&c.push(window.__REDUX_DEVTOOLS_EXTENSION__({name:e,instanceId:e}));const{reducer:a,initialState:l}=t;return S(s()({metadata:V,root:a}),{root:l},(0,u.flowRight)(c))}(r,n,o,{registry:o,get dispatch(){return Object.assign((e=>c.dispatch(e)),g())},get select(){return Object.assign((e=>e(c.__unstableOriginalGetState())),v())},get resolveSelect(){return h()}}),a=function(){const e={};return{isRunning:(t,r)=>e[t]&&e[t].get(oe(r)),clear(t,r){e[t]&&e[t].delete(oe(r))},markAsRunning(t,r){e[t]||(e[t]=new(w())),e[t].set(oe(r),!0)}}}();let l;const f=function(e,t){const r=e=>function(){return Promise.resolve(t.dispatch(e(...arguments)))};return(0,u.mapValues)(e,r)}({...t,...n.actions},c);let d=function(e,t){const r=e=>{const r=function(){const r=arguments.length,n=new Array(r+1);n[0]=t.__unstableOriginalGetState();for(let e=0;e<r;e++)n[e+1]=arguments[e];return e(...n)};return r.hasResolver=!1,r};return(0,u.mapValues)(e,r)}({...(0,u.mapValues)(e,(e=>function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return e(t.metadata,...n)})),...(0,u.mapValues)(n.selectors,(e=>(e.isRegistrySelector&&(e.registry=o),function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return e(t.root,...n)})))},c);if(n.resolvers){const e=function(e,t,r,n){const o=(0,u.mapValues)(e,(e=>e.fulfill?e:{...e,fulfill:e})),i=(t,i)=>{const s=e[i];if(!s)return t.hasResolver=!1,t;const u=function(){for(var e=arguments.length,u=new Array(e),c=0;c<e;c++)u[c]=arguments[c];async function a(){const e=r.getState();if(n.isRunning(i,u)||"function"==typeof s.isFulfilled&&s.isFulfilled(e,...u))return;const{metadata:t}=r.__unstableOriginalGetState();K(t,i,u)||(n.markAsRunning(i,u),setTimeout((async()=>{n.clear(i,u),r.dispatch($(i,u));try{await se(r,o,i,...u),r.dispatch(q(i,u))}catch(e){r.dispatch(Q(i,u,e))}})))}return a(...u),t(...u)};return u.hasResolver=!0,u};return{resolvers:o,selectors:(0,u.mapValues)(t,i)}}(n.resolvers,d,c,a);l=e.resolvers,d=e.selectors}const p=function(e,t){const r=(0,u.omit)(e,["getIsResolving","hasStartedResolution","hasFinishedResolution","hasResolutionFailed","isResolving","getCachedResolvers","getResolutionState","getResolutionError"]);return(0,u.mapValues)(r,((r,n)=>r.hasResolver?function(){for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return new Promise(((o,s)=>{const u=()=>e.hasFinishedResolution(n,i),c=t=>{if(e.hasResolutionFailed(n,i)){const t=e.getResolutionError(n,i);s(t)}else o(t)},a=()=>r.apply(null,i),l=a();if(u())return c(l);const f=t.subscribe((()=>{u()&&(f(),c(a()))}))}))}:async function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.apply(null,t)}))}(d,c),v=()=>d,g=()=>f,h=()=>p;c.__unstableOriginalGetState=c.getState,c.getState=()=>c.__unstableOriginalGetState().root;const y=c&&(e=>{let t=c.__unstableOriginalGetState();return c.subscribe((()=>{const r=c.__unstableOriginalGetState(),n=r!==t;t=r,n&&e()}))});return{reducer:i,store:c,actions:f,selectors:d,resolvers:l,getSelectors:v,getResolveSelectors:h,getActions:g,subscribe:y}}}}async function se(e,t,r){const n=(0,u.get)(t,[r]);if(!n)return;for(var o=arguments.length,i=new Array(o>3?o-3:0),s=3;s<o;s++)i[s-3]=arguments[s];const c=n.fulfill(...i);c&&await e.dispatch(c)}function ue(){let e=!1,t=!1;const r=new Set,n=()=>Array.from(r).forEach((e=>e()));return{get isPaused(){return e},subscribe:e=>(r.add(e),()=>r.delete(e)),pause(){e=!0},resume(){e=!1,t&&(t=!1,n())},emit(){e?t=!0:n()}}}function ce(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const r={},n=ue(),o=new Set;function i(){n.emit()}const s=e=>n.subscribe(e);function c(e){const n=(0,u.isObject)(e)?e.name:e;o.add(n);const i=r[n];return i?i.getSelectors():t&&t.select(n)}function l(e,t){o.clear();const r=e.call(this);return t.current=Array.from(o),r}function f(e){const n=(0,u.isObject)(e)?e.name:e;o.add(n);const i=r[n];return i?i.getResolveSelectors():t&&t.resolveSelect(n)}function d(e){const n=(0,u.isObject)(e)?e.name:e,o=r[n];return o?o.getActions():t&&t.dispatch(n)}function p(e){return(0,u.mapValues)(e,((e,t)=>"function"!=typeof e?e:function(){return m[t].apply(null,arguments)}))}function v(e,t){if("function"!=typeof t.getSelectors)throw new TypeError("store.getSelectors must be a function");if("function"!=typeof t.getActions)throw new TypeError("store.getActions must be a function");if("function"!=typeof t.subscribe)throw new TypeError("store.subscribe must be a function");t.emitter=ue();const n=t.subscribe;t.subscribe=e=>{const r=t.emitter.subscribe(e),o=n((()=>{t.emitter.isPaused?t.emitter.emit():e()}));return()=>{null==o||o(),null==r||r()}},r[e]=t,t.subscribe(i)}function g(e){v(e.name,e.instantiate(m))}function h(e,t){a()("wp.data.registerGenericStore",{since:"5.9",alternative:"wp.data.register( storeDescriptor )"}),v(e,t)}function y(e,t){if(!t.reducer)throw new TypeError("Must specify store reducer");const r=ie(e,t).instantiate(m);return v(e,r),r.store}function S(e,n){return e in r?r[e].subscribe(n):t?t.__unstableSubscribeStore(e,n):s(n)}function b(e){n.pause(),(0,u.forEach)(r,(e=>e.emitter.pause())),e(),n.resume(),(0,u.forEach)(r,(e=>e.emitter.resume()))}let m={batch:b,stores:r,namespaces:r,subscribe:s,select:c,resolveSelect:f,dispatch:d,use:O,register:g,registerGenericStore:h,registerStore:y,__unstableMarkListeningStores:l,__unstableSubscribeStore:S};function O(e,t){if(e)return m={...m,...e(m,t)},m}m.register(F);for(const[t,r]of Object.entries(e))m.register(ie(t,r));return t&&t.subscribe(i),p(m)}var ae=ce();let le;const fe={getItem:e=>le&&le[e]?le[e]:null,setItem(e,t){le||fe.clear(),le[e]=String(t)},clear(){le=Object.create(null)}};var de=fe;let pe;try{pe=window.localStorage,pe.setItem("__wpDataTestLocalStorage",""),pe.removeItem("__wpDataTestLocalStorage")}catch(e){pe=de}const ve=pe,ge="WP_DATA";function he(e){const{storage:t=ve,storageKey:r=ge}=e;let n;return{get:function(){if(void 0===n){const e=t.getItem(r);if(null===e)n={};else try{n=JSON.parse(e)}catch(e){n={}}}return n},set:function(e,o){n={...n,[e]:o},t.setItem(r,JSON.stringify(n))}}}function ye(e,t){const r=he(t);return{registerStore(t,n){if(!n.persist)return e.registerStore(t,n);const o=r.get()[t];if(void 0!==o){let e=n.reducer(n.initialState,{type:"@@WP/PERSISTENCE_RESTORE"});e=(0,u.isPlainObject)(e)&&(0,u.isPlainObject)(o)?(0,u.merge)({},e,o):o,n={...n,initialState:e}}const i=e.registerStore(t,n);return i.subscribe(function(e,t,n){let o;if(Array.isArray(n)){const e=n.reduce(((e,t)=>Object.assign(e,{[t]:(e,r)=>r.nextState[t]})),{});i=s()(e),o=(e,t)=>t.nextState===e?e:i(e,t)}else o=(e,t)=>t.nextState;var i;let u=o(void 0,{nextState:e()});return()=>{const n=o(u,{nextState:e()});n!==u&&(r.set(t,n),u=n)}}(i.getState,t,n.persist)),i}}}function Se(e,t){var r,n,o,i,s;const u="core/preferences",c="core/interface",a=e.get(),l=null===(r=a["core/interface"])||void 0===r||null===(n=r.preferences)||void 0===n||null===(o=n.features)||void 0===o?void 0:o[t],f=null===(i=a[t])||void 0===i||null===(s=i.preferences)||void 0===s?void 0:s.features,d=l||f;if(d){var p;const r=null===(p=a["core/preferences"])||void 0===p?void 0:p.preferences;if(null==r||!r[t]){if(e.set(u,{preferences:{...r,[t]:d}}),l){var v,g;const r=a["core/interface"],n=null===(v=a["core/interface"])||void 0===v||null===(g=v.preferences)||void 0===g?void 0:g.features;e.set(c,{...r,preferences:{features:{...n,[t]:void 0}}})}if(f){var h;const r=a[t],n=null===(h=a[t])||void 0===h?void 0:h.preferences;e.set(t,{...r,preferences:{...n,features:void 0}})}}}}function be(e,t,r){var n,o,i,s,c,a,l,f,d;let{from:p,scope:v}=t,g=arguments.length>3&&void 0!==arguments[3]?arguments[3]:u.identity;const h="core/preferences",y=e.get(),S=null===(n=y[p])||void 0===n||null===(o=n.preferences)||void 0===o?void 0:o[r];if(void 0===S)return;const b=null===(i=y[h])||void 0===i||null===(s=i.preferences)||void 0===s||null===(c=s[v])||void 0===c?void 0:c[r];if(b)return;const m=null===(a=y[h])||void 0===a?void 0:a.preferences,O=null===(l=y[h])||void 0===l||null===(f=l.preferences)||void 0===f?void 0:f[v],w=g({[r]:S});e.set(h,{preferences:{...m,[v]:{...O,...w}}});const R=y[p],E=null===(d=y[p])||void 0===d?void 0:d.preferences;e.set(p,{...R,preferences:{...E,[r]:void 0}})}function me(e){var t;const r=null!==(t=null==e?void 0:e.panels)&&void 0!==t?t:{};return Object.keys(r).reduce(((e,t)=>{const n=r[t];return!1===(null==n?void 0:n.enabled)&&e.inactivePanels.push(t),!0===(null==n?void 0:n.opened)&&e.openPanels.push(t),e}),{inactivePanels:[],openPanels:[]})}ye.__unstableMigrate=e=>{const t=he(e);Se(t,"core/edit-widgets"),Se(t,"core/customize-widgets"),Se(t,"core/edit-post"),Se(t,"core/edit-site"),function(e){var t,r;const n="core/interface",o="core/preferences";let i=e.get();const s=null===(t=i["core/interface"])||void 0===t||null===(r=t.preferences)||void 0===r?void 0:r.features;for(const t in s){var u,c,a;if(t.startsWith("core"))continue;const r=s[t];if(!r)continue;const l=null===(u=i["core/preferences"])||void 0===u?void 0:u.preferences;e.set(o,{preferences:{...l,[t]:r}}),i=e.get();const f=i["core/interface"],d=null===(c=i["core/interface"])||void 0===c||null===(a=c.preferences)||void 0===a?void 0:a.features;e.set(n,{...f,preferences:{features:{...d,[t]:void 0}}})}}(t),be(t,{from:"core/edit-post",scope:"core/edit-post"},"hiddenBlockTypes"),be(t,{from:"core/edit-post",scope:"core/edit-post"},"editorMode"),be(t,{from:"core/edit-post",scope:"core/edit-post"},"preferredStyleVariations"),be(t,{from:"core/edit-post",scope:"core/edit-post"},"panels",me),be(t,{from:"core/editor",scope:"core/edit-post"},"isPublishSidebarEnabled"),be(t,{from:"core/edit-site",scope:"core/edit-site"},"editorMode"),function(e){var t,r,n,o,i,s,u;const c="core/interface",a="core/preferences",l=e.get(),f=null===(t=l["core/interface"])||void 0===t?void 0:t.enableItems;if(!f)return;const d=null!==(r=null===(n=l["core/preferences"])||void 0===n?void 0:n.preferences)&&void 0!==r?r:{},p=null!==(o=null==f||null===(i=f.singleEnableItems)||void 0===i?void 0:i.complementaryArea)&&void 0!==o?o:{},v=Object.keys(p).reduce(((e,t)=>{var r;const n=p[t];return null!==(r=e[t])&&void 0!==r&&r.complementaryArea?e:{...e,[t]:{...e[t],complementaryArea:n}}}),d),g=null!==(s=null==f||null===(u=f.multipleEnableItems)||void 0===u?void 0:u.pinnedItems)&&void 0!==s?s:{},h=Object.keys(g).reduce(((e,t)=>{var r;const n=g[t];return null!==(r=e[t])&&void 0!==r&&r.pinnedItems?e:{...e,[t]:{...e[t],pinnedItems:n}}}),v);e.set(a,{preferences:h});const y=l["core/interface"];e.set(c,{...y,enableItems:void 0})}(t)};var Oe=ye;function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},we.apply(this,arguments)}var Re=window.wp.element,Ee=window.wp.compose,_e=window.React;function Ie(e,t){var r=(0,_e.useState)((function(){return{inputs:t,result:e()}}))[0],n=(0,_e.useRef)(!0),o=(0,_e.useRef)(r),i=n.current||Boolean(t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,o.current.inputs))?o.current:{inputs:t,result:e()};return(0,_e.useEffect)((function(){n.current=!1,o.current=i}),[i]),i.result}var Te=window.wp.priorityQueue,Ne=window.wp.isShallowEqual,Ae=r.n(Ne);const je=(0,Re.createContext)(ae),{Consumer:Le,Provider:Pe}=je,Ce=Le;var Fe=Pe;function Ue(){return(0,Re.useContext)(je)}const ke=(0,Re.createContext)(!1),{Consumer:xe,Provider:Me}=ke;var De=Me;const Ve=()=>{},Ge=(0,Te.createQueue)();function He(e,t){const r="function"==typeof e;r||(t=[]);const n=(0,Re.useCallback)(r?e:Ve,t),o=r?n:null,i=Ue(),s=(0,Re.useContext)(ke),u=Ie((()=>({queue:!0})),[i]),[,c]=(0,Re.useReducer)((e=>e+1),0),a=(0,Re.useRef)(),l=(0,Re.useRef)(s),f=(0,Re.useRef)(),d=(0,Re.useRef)(),p=(0,Re.useRef)(),v=(0,Re.useRef)([]),g=(0,Re.useCallback)((e=>i.__unstableMarkListeningStores((()=>e(i.select,i)),v)),[i]),h=(0,Re.useMemo)((()=>({})),t||[]);let y;if(o){y=f.current;const e=a.current!==o,t=!!d.current;if(e||t)try{y=g(o)}catch(e){let t=`An error occurred while running 'mapSelect': ${e.message}`;d.current&&(t+="\nThe error may be correlated with this previous error:\n",t+=`${d.current.stack}\n\n`,t+="Original stack trace:"),console.error(t)}}return(0,Ee.useIsomorphicLayoutEffect)((()=>{r&&(a.current=o,f.current=y,d.current=void 0,p.current=!0,l.current!==s&&(l.current=s,Ge.flush(u)))})),(0,Ee.useIsomorphicLayoutEffect)((()=>{if(!r)return;const e=()=>{if(p.current){try{const e=g(a.current);if(Ae()(f.current,e))return;f.current=e}catch(e){d.current=e}c()}},t=()=>{l.current?Ge.add(u,e):e()};t();const n=v.current.map((e=>i.__unstableSubscribeStore(e,t)));return()=>{p.current=!1,n.forEach((e=>null==e?void 0:e())),Ge.flush(u)}}),[i,g,r,h]),r?y:i.select(e)}var Ke=e=>(0,Ee.createHigherOrderComponent)((t=>(0,Ee.pure)((r=>{const n=He(((t,n)=>e(t,r,n)));return(0,Re.createElement)(t,we({},r,n))}))),"withSelect");var We=(e,t)=>{const r=Ue(),n=(0,Re.useRef)(e);return(0,Ee.useIsomorphicLayoutEffect)((()=>{n.current=e})),(0,Re.useMemo)((()=>{const e=n.current(r.dispatch,r);return(0,u.mapValues)(e,((e,t)=>("function"!=typeof e&&console.warn(`Property ${t} returned from dispatchMap in useDispatchWithMap must be a function.`),function(){return n.current(r.dispatch,r)[t](...arguments)})))}),[r,...t])};var Be=e=>(0,Ee.createHigherOrderComponent)((t=>r=>{const n=We(((t,n)=>e(t,r,n)),[]);return(0,Re.createElement)(t,we({},r,n))}),"withDispatch");var Xe=(0,Ee.createHigherOrderComponent)((e=>t=>(0,Re.createElement)(Ce,null,(r=>(0,Re.createElement)(e,we({},t,{registry:r}))))),"withRegistry");var ze=e=>{const{dispatch:t}=Ue();return void 0===e?t:t(e)};const Je=ae.select,$e=ae.resolveSelect,qe=ae.dispatch,Qe=ae.subscribe,Ye=ae.registerGenericStore,Ze=ae.registerStore,et=ae.use,tt=ae.register}(),(window.wp=window.wp||{}).data=n}();