<?php
/**
 * `TRUNCATE` statement.
 */

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\SqlParser\Statements;

use Php<PERSON>yAdmin\SqlParser\Components\Expression;
use Php<PERSON>y<PERSON>dmin\SqlParser\Statement;

/**
 * `TRUNCATE` statement.
 */
class TruncateStatement extends Statement
{
    /**
     * Options for `TRUNCATE` statements.
     *
     * @var array
     */
    public static $OPTIONS = ['TABLE' => 1];

    /**
     * The name of the truncated table.
     *
     * @var Expression
     */
    public $table;

    /**
     * Special build method for truncate statement as Statement::build would return empty string.
     *
     * @return string
     */
    public function build()
    {
        return 'TRUNCATE TABLE ' . $this->table . ';';
    }
}
