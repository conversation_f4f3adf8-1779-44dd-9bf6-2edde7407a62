/******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "ALT": function() { return /* binding */ ALT; },
  "BACKSPACE": function() { return /* binding */ BACKSPACE; },
  "COMMAND": function() { return /* binding */ COMMAND; },
  "CTRL": function() { return /* binding */ CTRL; },
  "DELETE": function() { return /* binding */ DELETE; },
  "DOWN": function() { return /* binding */ DOWN; },
  "END": function() { return /* binding */ END; },
  "ENTER": function() { return /* binding */ ENTER; },
  "ESCAPE": function() { return /* binding */ ESCAPE; },
  "F10": function() { return /* binding */ F10; },
  "HOME": function() { return /* binding */ HOME; },
  "LEFT": function() { return /* binding */ LEFT; },
  "PAGEDOWN": function() { return /* binding */ PAGEDOWN; },
  "PAGEUP": function() { return /* binding */ PAGEUP; },
  "RIGHT": function() { return /* binding */ RIGHT; },
  "SHIFT": function() { return /* binding */ SHIFT; },
  "SPACE": function() { return /* binding */ SPACE; },
  "TAB": function() { return /* binding */ TAB; },
  "UP": function() { return /* binding */ UP; },
  "ZERO": function() { return /* binding */ ZERO; },
  "displayShortcut": function() { return /* binding */ displayShortcut; },
  "displayShortcutList": function() { return /* binding */ displayShortcutList; },
  "isKeyboardEvent": function() { return /* binding */ isKeyboardEvent; },
  "modifiers": function() { return /* binding */ modifiers; },
  "rawShortcut": function() { return /* binding */ rawShortcut; },
  "shortcutAriaLabel": function() { return /* binding */ shortcutAriaLabel; }
});

;// CONCATENATED MODULE: external "lodash"
var external_lodash_namespaceObject = window["lodash"];
;// CONCATENATED MODULE: external ["wp","i18n"]
var external_wp_i18n_namespaceObject = window["wp"]["i18n"];
;// CONCATENATED MODULE: ./node_modules/@wordpress/keycodes/build-module/platform.js
/**
 * External dependencies
 */

/**
 * Return true if platform is MacOS.
 *
 * @param {Window?} _window window object by default; used for DI testing.
 *
 * @return {boolean} True if MacOS; false otherwise.
 */

function isAppleOS() {
  let _window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;

  if (!_window) {
    if (typeof window === 'undefined') {
      return false;
    }

    _window = window;
  }

  const {
    platform
  } = _window.navigator;
  return platform.indexOf('Mac') !== -1 || (0,external_lodash_namespaceObject.includes)(['iPad', 'iPhone'], platform);
}

;// CONCATENATED MODULE: ./node_modules/@wordpress/keycodes/build-module/index.js
/**
 * Note: The order of the modifier keys in many of the [foo]Shortcut()
 * functions in this file are intentional and should not be changed. They're
 * designed to fit with the standard menu keyboard shortcuts shown in the
 * user's platform.
 *
 * For example, on MacOS menu shortcuts will place Shift before Command, but
 * on Windows Control will usually come first. So don't provide your own
 * shortcut combos directly to keyboardShortcut().
 */

/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */


/**
 * Internal dependencies
 */


/** @typedef {typeof ALT | CTRL | COMMAND | SHIFT } WPModifierPart */

/** @typedef {'primary' | 'primaryShift' | 'primaryAlt' | 'secondary' | 'access' | 'ctrl' | 'alt' | 'ctrlShift' | 'shift' | 'shiftAlt' | 'undefined'} WPKeycodeModifier */

/**
 * An object of handler functions for each of the possible modifier
 * combinations. A handler will return a value for a given key.
 *
 * @template T
 *
 * @typedef {Record<WPKeycodeModifier, T>} WPModifierHandler
 */

/**
 * @template T
 *
 * @typedef {(character: string, isApple?: () => boolean) => T} WPKeyHandler
 */

/** @typedef {(event: KeyboardEvent, character: string, isApple?: () => boolean) => boolean} WPEventKeyHandler */

/**
 * Keycode for BACKSPACE key.
 */

const BACKSPACE = 8;
/**
 * Keycode for TAB key.
 */

const TAB = 9;
/**
 * Keycode for ENTER key.
 */

const ENTER = 13;
/**
 * Keycode for ESCAPE key.
 */

const ESCAPE = 27;
/**
 * Keycode for SPACE key.
 */

const SPACE = 32;
/**
 * Keycode for PAGEUP key.
 */

const PAGEUP = 33;
/**
 * Keycode for PAGEDOWN key.
 */

const PAGEDOWN = 34;
/**
 * Keycode for END key.
 */

const END = 35;
/**
 * Keycode for HOME key.
 */

const HOME = 36;
/**
 * Keycode for LEFT key.
 */

const LEFT = 37;
/**
 * Keycode for UP key.
 */

const UP = 38;
/**
 * Keycode for RIGHT key.
 */

const RIGHT = 39;
/**
 * Keycode for DOWN key.
 */

const DOWN = 40;
/**
 * Keycode for DELETE key.
 */

const DELETE = 46;
/**
 * Keycode for F10 key.
 */

const F10 = 121;
/**
 * Keycode for ALT key.
 */

const ALT = 'alt';
/**
 * Keycode for CTRL key.
 */

const CTRL = 'ctrl';
/**
 * Keycode for COMMAND/META key.
 */

const COMMAND = 'meta';
/**
 * Keycode for SHIFT key.
 */

const SHIFT = 'shift';
/**
 * Keycode for ZERO key.
 */

const ZERO = 48;
/**
 * Object that contains functions that return the available modifier
 * depending on platform.
 *
 * @type {WPModifierHandler< ( isApple: () => boolean ) => WPModifierPart[]>}
 */

const modifiers = {
  primary: _isApple => _isApple() ? [COMMAND] : [CTRL],
  primaryShift: _isApple => _isApple() ? [SHIFT, COMMAND] : [CTRL, SHIFT],
  primaryAlt: _isApple => _isApple() ? [ALT, COMMAND] : [CTRL, ALT],
  secondary: _isApple => _isApple() ? [SHIFT, ALT, COMMAND] : [CTRL, SHIFT, ALT],
  access: _isApple => _isApple() ? [CTRL, ALT] : [SHIFT, ALT],
  ctrl: () => [CTRL],
  alt: () => [ALT],
  ctrlShift: () => [CTRL, SHIFT],
  shift: () => [SHIFT],
  shiftAlt: () => [SHIFT, ALT],
  undefined: () => []
};
/**
 * An object that contains functions to get raw shortcuts.
 *
 * These are intended for user with the KeyboardShortcuts.
 *
 * @example
 * ```js
 * // Assuming macOS:
 * rawShortcut.primary( 'm' )
 * // "meta+m""
 * ```
 *
 * @type {WPModifierHandler<WPKeyHandler<string>>} Keyed map of functions to raw
 *                                                 shortcuts.
 */

const rawShortcut = (0,external_lodash_namespaceObject.mapValues)(modifiers, modifier => {
  return (
    /** @type {WPKeyHandler<string>} */
    function (character) {
      let _isApple = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : isAppleOS;

      return [...modifier(_isApple), character.toLowerCase()].join('+');
    }
  );
});
/**
 * Return an array of the parts of a keyboard shortcut chord for display.
 *
 * @example
 * ```js
 * // Assuming macOS:
 * displayShortcutList.primary( 'm' );
 * // [ "⌘", "M" ]
 * ```
 *
 * @type {WPModifierHandler<WPKeyHandler<string[]>>} Keyed map of functions to
 *                                                   shortcut sequences.
 */

const displayShortcutList = (0,external_lodash_namespaceObject.mapValues)(modifiers, modifier => {
  return (
    /** @type {WPKeyHandler<string[]>} */
    function (character) {
      let _isApple = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : isAppleOS;

      const isApple = _isApple();

      const replacementKeyMap = {
        [ALT]: isApple ? '⌥' : 'Alt',
        [CTRL]: isApple ? '⌃' : 'Ctrl',
        // Make sure ⌃ is the U+2303 UP ARROWHEAD unicode character and not the caret character.
        [COMMAND]: '⌘',
        [SHIFT]: isApple ? '⇧' : 'Shift'
      };
      const modifierKeys = modifier(_isApple).reduce((accumulator, key) => {
        const replacementKey = (0,external_lodash_namespaceObject.get)(replacementKeyMap, key, key); // If on the Mac, adhere to platform convention and don't show plus between keys.

        if (isApple) {
          return [...accumulator, replacementKey];
        }

        return [...accumulator, replacementKey, '+'];
      },
      /** @type {string[]} */
      []);
      const capitalizedCharacter = (0,external_lodash_namespaceObject.capitalize)(character);
      return [...modifierKeys, capitalizedCharacter];
    }
  );
});
/**
 * An object that contains functions to display shortcuts.
 *
 * @example
 * ```js
 * // Assuming macOS:
 * displayShortcut.primary( 'm' );
 * // "⌘M"
 * ```
 *
 * @type {WPModifierHandler<WPKeyHandler<string>>} Keyed map of functions to
 *                                                 display shortcuts.
 */

const displayShortcut = (0,external_lodash_namespaceObject.mapValues)(displayShortcutList, shortcutList => {
  return (
    /** @type {WPKeyHandler<string>} */
    function (character) {
      let _isApple = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : isAppleOS;

      return shortcutList(character, _isApple).join('');
    }
  );
});
/**
 * An object that contains functions to return an aria label for a keyboard
 * shortcut.
 *
 * @example
 * ```js
 * // Assuming macOS:
 * shortcutAriaLabel.primary( '.' );
 * // "Command + Period"
 * ```
 *
 * @type {WPModifierHandler<WPKeyHandler<string>>} Keyed map of functions to
 *                                                 shortcut ARIA labels.
 */

const shortcutAriaLabel = (0,external_lodash_namespaceObject.mapValues)(modifiers, modifier => {
  return (
    /** @type {WPKeyHandler<string>} */
    function (character) {
      let _isApple = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : isAppleOS;

      const isApple = _isApple();

      const replacementKeyMap = {
        [SHIFT]: 'Shift',
        [COMMAND]: isApple ? 'Command' : 'Control',
        [CTRL]: 'Control',
        [ALT]: isApple ? 'Option' : 'Alt',

        /* translators: comma as in the character ',' */
        ',': (0,external_wp_i18n_namespaceObject.__)('Comma'),

        /* translators: period as in the character '.' */
        '.': (0,external_wp_i18n_namespaceObject.__)('Period'),

        /* translators: backtick as in the character '`' */
        '`': (0,external_wp_i18n_namespaceObject.__)('Backtick')
      };
      return [...modifier(_isApple), character].map(key => (0,external_lodash_namespaceObject.capitalize)((0,external_lodash_namespaceObject.get)(replacementKeyMap, key, key))).join(isApple ? ' ' : ' + ');
    }
  );
});
/**
 * From a given KeyboardEvent, returns an array of active modifier constants for
 * the event.
 *
 * @param {KeyboardEvent} event Keyboard event.
 *
 * @return {Array<WPModifierPart>} Active modifier constants.
 */

function getEventModifiers(event) {
  return (
    /** @type {WPModifierPart[]} */
    [ALT, CTRL, COMMAND, SHIFT].filter(key => event[
    /** @type {'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'} */
    `${key}Key`])
  );
}
/**
 * An object that contains functions to check if a keyboard event matches a
 * predefined shortcut combination.
 *
 * @example
 * ```js
 * // Assuming an event for ⌘M key press:
 * isKeyboardEvent.primary( event, 'm' );
 * // true
 * ```
 *
 * @type {WPModifierHandler<WPEventKeyHandler>} Keyed map of functions
 *                                                       to match events.
 */


const isKeyboardEvent = (0,external_lodash_namespaceObject.mapValues)(modifiers, getModifiers => {
  return (
    /** @type {WPEventKeyHandler} */
    function (event, character) {
      let _isApple = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : isAppleOS;

      const mods = getModifiers(_isApple);
      const eventMods = getEventModifiers(event);

      if ((0,external_lodash_namespaceObject.xor)(mods, eventMods).length) {
        return false;
      }

      let key = event.key.toLowerCase();

      if (!character) {
        return (0,external_lodash_namespaceObject.includes)(mods, key);
      }

      if (event.altKey && character.length === 1) {
        key = String.fromCharCode(event.keyCode).toLowerCase();
      } // For backwards compatibility.


      if (character === 'del') {
        character = 'delete';
      }

      return key === character.toLowerCase();
    }
  );
});

(window.wp = window.wp || {}).keycodes = __webpack_exports__;
/******/ })()
;