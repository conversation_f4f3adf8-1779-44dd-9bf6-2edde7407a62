/*------------------------------------------------------------------------------
 TinyMCE and Quicklinks toolbars
------------------------------------------------------------------------------*/

/* TinyMCE widgets/containers */

.mce-tinymce {
	box-shadow: none;
}

.mce-container,
.mce-container *,
.mce-widget,
.mce-widget * {
	color: inherit;
	font-family: inherit;
}

.mce-container .mce-monospace,
.mce-widget .mce-monospace {
	font-family: Consolas, Monaco, monospace;
	font-size: 13px;
	line-height: 150%;
}

/* TinyMCE windows */
#mce-modal-block,
#mce-modal-block.mce-fade {
	opacity: 0.7;
	filter: alpha(opacity=70);
	transition: none;
	background: #000;
}

.mce-window {
	border-radius: 0;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
	-webkit-font-smoothing: subpixel-antialiased;
	transition: none;
}

.mce-window .mce-container-body.mce-abs-layout {
	overflow: visible;
}

.mce-window .mce-window-head {
	background: #fff;
	border-bottom: 1px solid #dcdcde;
	padding: 0;
	min-height: 36px;
}

.mce-window .mce-window-head .mce-title {
	color: #3c434a;
	font-size: 18px;
	font-weight: 600;
	line-height: 36px;
	margin: 0;
	padding: 0 36px 0 16px;
}

.mce-window .mce-window-head .mce-close,
.mce-window-head .mce-close .mce-i-remove {
	color: transparent;
	top: 0;
	right: 0;
	width: 36px;
	height: 36px;
	padding: 0;
	line-height: 36px;
	text-align: center;
}

.mce-window-head .mce-close .mce-i-remove:before {
	font: normal 20px/36px dashicons;
	text-align: center;
	color: #646970;
	width: 36px;
	height: 36px;
	display: block;
}

.mce-window-head .mce-close:hover .mce-i-remove:before,
.mce-window-head .mce-close:focus .mce-i-remove:before {
	color: #135e96;
}

.mce-window-head .mce-close:focus .mce-i-remove,
div.mce-tab:focus {
	box-shadow: 0 0 0 1px #4f94d4,
		0 0 2px 1px rgba(79, 148, 212, 0.8);
}

.mce-window .mce-window-head .mce-dragh {
	width: calc( 100% - 36px );
}

.mce-window .mce-foot {
	border-top: 1px solid #dcdcde;
}

.mce-textbox,
.mce-checkbox i.mce-i-checkbox,
#wp-link .query-results {
	border: 1px solid #dcdcde;
	border-radius: 0;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
	transition: .05s all ease-in-out;
}

.mce-textbox:focus,
.mce-textbox.mce-focus,
.mce-checkbox:focus i.mce-i-checkbox,
#wp-link .query-results:focus {
	border-color: #4f94d4;
	box-shadow: 0 0 2px rgba(79, 148, 212, 0.8);
}

.mce-window .mce-wp-help {
	height: 360px;
	width: 460px;
	overflow: auto;
}

.mce-window .mce-wp-help * {
	box-sizing: border-box;
}

.mce-window .mce-wp-help > .mce-container-body {
	width: auto !important;
}

.mce-window .wp-editor-help {
	padding: 10px 10px 0 20px;
}

.mce-window .wp-editor-help h2,
.mce-window .wp-editor-help p {
	margin: 8px 0;
	white-space: normal;
	font-size: 14px;
	font-weight: 400;
}

.mce-window .wp-editor-help table {
	width: 100%;
	margin-bottom: 20px;
}

.mce-window .wp-editor-help table.wp-help-single {
	margin: 0 8px 20px;
}

.mce-window .wp-editor-help table.fixed {
	table-layout: fixed;
}

.mce-window .wp-editor-help table.fixed th:nth-child(odd),
.mce-window .wp-editor-help table.fixed td:nth-child(odd) {
	width: 12%;
}

.mce-window .wp-editor-help table.fixed th:nth-child(even),
.mce-window .wp-editor-help table.fixed td:nth-child(even) {
	width: 38%;
}

.mce-window .wp-editor-help table.fixed th:nth-child(odd) {
	padding: 5px 0 0;
}

.mce-window .wp-editor-help td,
.mce-window .wp-editor-help th {
	font-size: 13px;
	padding: 5px;
	vertical-align: middle;
	word-wrap: break-word;
	white-space: normal;
}

.mce-window .wp-editor-help th {
	font-weight: 600;
	padding-bottom: 0;
}

.mce-window .wp-editor-help kbd {
	font-family: monospace;
	padding: 2px 7px 3px;
	font-weight: 600;
	margin: 0;
	background: #f0f0f1;
	background: rgba(0, 0, 0, 0.08);
}

.mce-window .wp-help-th-center td:nth-child(odd),
.mce-window .wp-help-th-center th:nth-child(odd) {
	text-align: center;
}

/* TinyMCE menus */
.mce-menu,
.mce-floatpanel.mce-popover {
	border-color: rgba(0, 0, 0, 0.15);
	border-radius: 0;
	box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

.mce-menu,
.mce-floatpanel.mce-popover.mce-bottom {
	margin-top: 2px;
}

.mce-floatpanel .mce-arrow {
	display: none;
}

.mce-menu .mce-container-body {
	min-width: 160px;
}

.mce-menu-item {
	border: none;
	margin-bottom: 2px;
	padding: 6px 15px 6px 12px;
}

.mce-menu-has-icons i.mce-ico {
	line-height: 20px;
}

/* TinyMCE panel */
div.mce-panel {
	border: 0;
	background: #fff;
}

.mce-panel.mce-menu {
	border: 1px solid #dcdcde;
}

div.mce-tab {
	line-height: 13px;
}

/* TinyMCE toolbars */
div.mce-toolbar-grp {
	border-bottom: 1px solid #dcdcde;
	background: #f6f7f7;
	padding: 0;
	position: relative;
}

div.mce-inline-toolbar-grp {
	border: 1px solid #a7aaad;
	border-radius: 2px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
	box-sizing: border-box;
	margin-bottom: 8px;
	position: absolute;
	-webkit-user-select: none;
	user-select: none;
	max-width: 98%;
	z-index: 100100; /* Same as the other TinyMCE "panels" */
}

div.mce-inline-toolbar-grp > div.mce-stack-layout {
	padding: 1px;
}

div.mce-inline-toolbar-grp.mce-arrow-up {
	margin-bottom: 0;
	margin-top: 8px;
}

div.mce-inline-toolbar-grp:before,
div.mce-inline-toolbar-grp:after {
	position: absolute;
	left: 50%;
	display: block;
	width: 0;
	height: 0;
	border-style: solid;
	border-color: transparent;
	content: "";
}

div.mce-inline-toolbar-grp.mce-arrow-up:before {
	top: -9px;
	border-bottom-color: #a7aaad;
	border-width: 0 9px 9px;
	margin-left: -9px;
}

div.mce-inline-toolbar-grp.mce-arrow-down:before {
	bottom: -9px;
	border-top-color: #a7aaad;
	border-width: 9px 9px 0;
	margin-left: -9px;
}

div.mce-inline-toolbar-grp.mce-arrow-up:after {
	top: -8px;
	border-bottom-color: #f6f7f7;
	border-width: 0 8px 8px;
	margin-left: -8px;
}

div.mce-inline-toolbar-grp.mce-arrow-down:after {
	bottom: -8px;
	border-top-color: #f6f7f7;
	border-width: 8px 8px 0;
	margin-left: -8px;
}

div.mce-inline-toolbar-grp.mce-arrow-left:before,
div.mce-inline-toolbar-grp.mce-arrow-left:after {
	margin: 0;
}

div.mce-inline-toolbar-grp.mce-arrow-left:before {
	left: 20px;
}
div.mce-inline-toolbar-grp.mce-arrow-left:after {
	left: 21px;
}

div.mce-inline-toolbar-grp.mce-arrow-right:before,
div.mce-inline-toolbar-grp.mce-arrow-right:after {
	left: auto;
	margin: 0;
}

div.mce-inline-toolbar-grp.mce-arrow-right:before {
	right: 20px;
}

div.mce-inline-toolbar-grp.mce-arrow-right:after {
	right: 21px;
}

div.mce-inline-toolbar-grp.mce-arrow-full {
	right: 0;
}

div.mce-inline-toolbar-grp.mce-arrow-full > div {
	width: 100%;
	overflow-x: auto;
}

div.mce-toolbar-grp > div {
	padding: 3px;
}

.has-dfw div.mce-toolbar-grp .mce-toolbar.mce-first {
	padding-right: 32px;
}

.mce-toolbar .mce-btn-group {
	margin: 0;
}

/* Classic block hide/show toolbars */
.block-library-classic__toolbar .mce-toolbar-grp .mce-toolbar:not(:first-child) {
	display: none;
}

.block-library-classic__toolbar.has-advanced-toolbar .mce-toolbar-grp .mce-toolbar {
	display: block;
}

div.mce-statusbar {
	border-top: 1px solid #dcdcde;
}

div.mce-path {
	padding: 2px 10px;
	margin: 0;
}

.mce-path,
.mce-path-item,
.mce-path .mce-divider {
	font-size: 12px;
}

.mce-toolbar .mce-btn,
.qt-dfw {
	border-color: transparent;
	background: transparent;
	box-shadow: none;
	text-shadow: none;
	cursor: pointer;
}

.mce-btn .mce-txt {
	direction: inherit;
	text-align: inherit;
}

.mce-toolbar .mce-btn-group .mce-btn,
.qt-dfw {
	border: 1px solid transparent;
	margin: 2px;
	border-radius: 2px;
}

.mce-toolbar .mce-btn-group .mce-btn:hover,
.mce-toolbar .mce-btn-group .mce-btn:focus,
.qt-dfw:hover,
.qt-dfw:focus {
	background: #f6f7f7;
	border-color: #50575e;
	color: #1d2327;
	box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
	outline: none;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-active,
.mce-toolbar .mce-btn-group .mce-btn:active,
.qt-dfw.active {
	background: #f0f0f1;
	border-color: #50575e;
	box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.3);
}

.mce-btn.mce-active,
.mce-btn.mce-active button,
.mce-btn.mce-active:hover button,
.mce-btn.mce-active i,
.mce-btn.mce-active:hover i {
	color: inherit;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-active:hover,
.mce-toolbar .mce-btn-group .mce-btn.mce-active:focus {
	border-color: #1d2327;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-disabled:hover,
.mce-toolbar .mce-btn-group .mce-btn.mce-disabled:focus {
	color: #a7aaad;
	background: none;
	border-color: #dcdcde;
	text-shadow: 0 1px 0 #fff;
	box-shadow: none;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-disabled:focus {
	border-color: #50575e;
}

.mce-toolbar .mce-btn-group .mce-first,
.mce-toolbar .mce-btn-group .mce-last {
	border-color: transparent;
}

.mce-toolbar .mce-btn button,
.qt-dfw {
	padding: 2px 3px;
	line-height: normal;
}

.mce-toolbar .mce-listbox button {
	font-size: 13px;
	line-height: 1.53846153;
	padding-left: 6px;
	padding-right: 20px;
}

.mce-toolbar .mce-btn i {
	text-shadow: none;
}

.mce-toolbar .mce-btn-group > div {
	white-space: normal;
}

.mce-toolbar .mce-colorbutton .mce-open {
	border-right: 0;
}

.mce-toolbar .mce-colorbutton .mce-preview {
	margin: 0;
	padding: 0;
	top: auto;
	bottom: 2px;
	left: 3px;
	height: 3px;
	width: 20px;
	background: #50575e;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-primary {
	min-width: 0;
	background: #3582c4;
	border-color: #2271b1 #135e96 #135e96;
	box-shadow: 0 1px 0 #135e96;
	color: #fff;
	text-decoration: none;
	text-shadow: none;
}

/* Compensate for the extra box shadow at the bottom of .mce-btn.mce-primary */
.mce-toolbar .mce-btn-group .mce-btn.mce-primary button {
	padding: 2px 3px 1px;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-primary .mce-ico {
	color: #fff;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-primary:hover,
.mce-toolbar .mce-btn-group .mce-btn.mce-primary:focus {
	background: #4f94d4;
	border-color: #135e96;
	color: #fff;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-primary:focus {
	box-shadow: 0 0 1px 1px #72aee6;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-primary:active {
	background: #2271b1;
	border-color: #135e96;
	box-shadow: inset 0 2px 0 #135e96;
}

/* mce listbox */
.mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
	border-radius: 0;
	direction: ltr;
	background: #fff;
	border: 1px solid #dcdcde;
	box-shadow: inset 0 1px 1px -1px rgba(0, 0, 0, 0.2);
}

.mce-toolbar .mce-btn-group .mce-btn.mce-listbox:hover,
.mce-toolbar .mce-btn-group .mce-btn.mce-listbox:focus {
	border-color: #c3c4c7;
}

.mce-panel .mce-btn i.mce-caret {
	border-top: 6px solid #50575e;
	margin-left: 2px;
	margin-right: 2px;
}

.mce-listbox i.mce-caret {
	right: 4px;
}

.mce-panel .mce-btn:hover i.mce-caret,
.mce-panel .mce-btn:focus i.mce-caret {
	border-top-color: #1d2327;
}

.mce-panel .mce-active i.mce-caret {
	border-top: 0;
	border-bottom: 6px solid #1d2327;
	margin-top: 7px;
}

.mce-listbox.mce-active i.mce-caret {
	margin-top: -3px;
}

.mce-toolbar .mce-splitbtn:hover .mce-open {
	border-right-color: transparent;
}

.mce-toolbar .mce-splitbtn .mce-open.mce-active {
	background: transparent;
	outline: none;
}

.mce-menu .mce-menu-item:hover,
.mce-menu .mce-menu-item.mce-selected,
.mce-menu .mce-menu-item:focus,
.mce-menu .mce-menu-item.mce-active.mce-menu-item-normal,
.mce-menu .mce-menu-item.mce-active.mce-menu-item-preview {
	background: #2271b1; /* See color scheme. */
	color: #fff;
}

.mce-menu .mce-menu-item:hover .mce-caret,
.mce-menu .mce-menu-item:focus .mce-caret,
.mce-menu .mce-menu-item.mce-selected .mce-caret {
	border-left-color: #fff;
}

/* rtl:ignore */
.rtl .mce-menu .mce-menu-item:hover .mce-caret,
.rtl .mce-menu .mce-menu-item:focus .mce-caret,
.rtl .mce-menu .mce-menu-item.mce-selected .mce-caret {
	border-left-color: inherit;
	border-right-color: #fff;
}

.mce-menu .mce-menu-item:hover .mce-text,
.mce-menu .mce-menu-item:focus .mce-text,
.mce-menu .mce-menu-item:hover .mce-ico,
.mce-menu .mce-menu-item:focus .mce-ico,
.mce-menu .mce-menu-item.mce-selected .mce-text,
.mce-menu .mce-menu-item.mce-selected .mce-ico,
.mce-menu .mce-menu-item:hover .mce-menu-shortcut,
.mce-menu .mce-menu-item:focus .mce-menu-shortcut,
.mce-menu .mce-menu-item.mce-active .mce-menu-shortcut,
.mce-menu .mce-menu-item.mce-disabled:hover .mce-text,
.mce-menu .mce-menu-item.mce-disabled:hover .mce-ico {
	color: inherit;
}

.mce-menu .mce-menu-item.mce-disabled {
	cursor: default;
}

.mce-menu .mce-menu-item.mce-disabled:hover {
	background: #c3c4c7;
}

/* Menubar */
div.mce-menubar {
	border-color: #dcdcde;
	background: #fff;
	border-width: 0 0 1px;
}

.mce-menubar .mce-menubtn:hover,
.mce-menubar .mce-menubtn.mce-active,
.mce-menubar .mce-menubtn:focus {
	border-color: transparent;
	background: transparent;
}

.mce-menubar .mce-menubtn:focus {
	color: #043959;
	box-shadow:
		0 0 0 1px #4f94d4,
		0 0 2px 1px rgba(79, 148, 212, 0.8);
}

div.mce-menu .mce-menu-item-sep,
.mce-menu-item-sep:hover {
	border-bottom: 1px solid #dcdcde;
	height: 0;
	margin: 5px 0;
}

.mce-menubtn span {
	margin-right: 0;
	padding-left: 3px;
}

.mce-menu-has-icons i.mce-ico:before {
	margin-left: -2px;
}

/* Keyboard shortcuts position */
.mce-menu.mce-menu-align .mce-menu-item-normal {
	position: relative;
}

.mce-menu.mce-menu-align .mce-menu-shortcut {
	bottom: 0.6em;
	font-size: 0.9em;
}

/* Buttons in modals */
.mce-primary button,
.mce-primary button i {
	text-align: center;
	color: #fff;
	text-shadow: none;
	padding: 0;
	line-height: 1.85714285;
}

.mce-window .mce-btn {
	color: #50575e;
	background: #f6f7f7;
	text-decoration: none;
	font-size: 13px;
	line-height: 26px;
	height: 28px;
	margin: 0;
	padding: 0;
	cursor: pointer;
	border: 1px solid #c3c4c7;
	-webkit-appearance: none;
	border-radius: 3px;
	white-space: nowrap;
	box-shadow: 0 1px 0 #c3c4c7;
}

/* Remove the dotted border on :focus and the extra padding in Firefox */
.mce-window .mce-btn::-moz-focus-inner {
	border-width: 0;
	border-style: none;
	padding: 0;
}

.mce-window .mce-btn:hover,
.mce-window .mce-btn:focus {
	background: #f6f7f7;
	border-color: #8c8f94;
	color: #1d2327;
}

.mce-window .mce-btn:focus {
	border-color: #4f94d4;
	box-shadow: 0 0 3px rgba(34, 113, 177, 0.8);
}

.mce-window .mce-btn:active {
	background: #f0f0f1;
	border-color: #8c8f94;
	box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
	transform: translateY(1px);
}

.mce-window .mce-btn.mce-disabled {
	color: #a7aaad !important;
	border-color: #dcdcde !important;
	background: #f6f7f7 !important;
	box-shadow: none !important;
	text-shadow: 0 1px 0 #fff !important;
	cursor: default;
	transform: none !important;
}

.mce-window .mce-btn.mce-primary {
	background: #3582c4;
	border-color: #2271b1 #135e96 #135e96;
	box-shadow: 0 1px 0 #135e96;
	color: #fff;
	text-decoration: none;
	text-shadow: 0 -1px 1px #135e96,
		1px 0 1px #135e96,
		0 1px 1px #135e96,
		-1px 0 1px #135e96;
}

.mce-window .mce-btn.mce-primary:hover,
.mce-window .mce-btn.mce-primary:focus {
	background: #4f94d4;
	border-color: #135e96;
	color: #fff;
}

.mce-window .mce-btn.mce-primary:focus {
	box-shadow: 0 1px 0 #2271b1,
		0 0 2px 1px #72aee6;
}

.mce-window .mce-btn.mce-primary:active {
	background: #2271b1;
	border-color: #135e96;
	box-shadow: inset 0 2px 0 #135e96;
	vertical-align: top;
}

.mce-window .mce-btn.mce-primary.mce-disabled {
	color: #9ec2e6 !important;
	background: #4f94d4 !important;
	border-color: #3582c4 !important;
	box-shadow: none !important;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
	cursor: default;
}

.mce-menubtn.mce-fixed-width span {
	overflow-x: hidden;
	text-overflow: ellipsis;
	width: 82px;
}

/* Charmap modal */
.mce-charmap {
	margin: 3px;
}

.mce-charmap td {
	padding: 0;
	border-color: #dcdcde;
	cursor: pointer;
}

.mce-charmap td:hover {
	background: #f6f7f7;
}

.mce-charmap td div {
	width: 18px;
	height: 22px;
	line-height: 1.57142857;
}

/* TinyMCE tooltips */
.mce-tooltip {
	margin-top: 2px;
}

.mce-tooltip-inner {
	border-radius: 3px;
	box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
	color: #fff;
	font-size: 12px;
}

/* TinyMCE icons */
.mce-ico {
	font-family: tinymce, Arial;
}

.mce-btn-small .mce-ico {
	font-family: tinymce-small, Arial;
}

.mce-toolbar .mce-ico {
	color: #50575e;
	line-height: 1;
	width: 20px;
	height: 20px;
	text-align: center;
	text-shadow: none;
	margin: 0;
	padding: 0;
}

.qt-dfw {
	color: #50575e;
	line-height: 1;
	width: 28px;
	height: 26px;
	text-align: center;
	text-shadow: none;
}

.mce-toolbar .mce-btn .mce-open {
	line-height: 20px;
}

.mce-toolbar .mce-btn:hover .mce-open,
.mce-toolbar .mce-btn:focus .mce-open,
.mce-toolbar .mce-btn.mce-active .mce-open {
	border-left-color: #1d2327;
}

div.mce-notification {
	left: 10% !important;
	right: 10%;
}

.mce-notification button.mce-close {
	right: 6px;
	top: 3px;
	font-weight: 400;
	color: #50575e;
}

.mce-notification button.mce-close:hover,
.mce-notification button.mce-close:focus {
	color: #000;
}

i.mce-i-bold,
i.mce-i-italic,
i.mce-i-bullist,
i.mce-i-numlist,
i.mce-i-blockquote,
i.mce-i-alignleft,
i.mce-i-aligncenter,
i.mce-i-alignright,
i.mce-i-link,
i.mce-i-unlink,
i.mce-i-wp_more,
i.mce-i-strikethrough,
i.mce-i-spellchecker,
i.mce-i-fullscreen,
i.mce-i-wp_fullscreen,
i.mce-i-dfw,
i.mce-i-wp_adv,
i.mce-i-underline,
i.mce-i-alignjustify,
i.mce-i-forecolor,
i.mce-i-backcolor,
i.mce-i-pastetext,
i.mce-i-pasteword,
i.mce-i-removeformat,
i.mce-i-charmap,
i.mce-i-outdent,
i.mce-i-indent,
i.mce-i-undo,
i.mce-i-redo,
i.mce-i-help,
i.mce-i-wp_help,
i.mce-i-wp-media-library,
i.mce-i-ltr,
i.mce-i-wp_page,
i.mce-i-hr,
i.mce-i-wp_code,
i.mce-i-dashicon,
i.mce-i-remove {
	font: normal 20px/1 dashicons;
	padding: 0;
	vertical-align: top;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	margin-left: -2px;
	padding-right: 2px;
}

.qt-dfw {
	font: normal 20px/1 dashicons;
	vertical-align: top;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

i.mce-i-bold:before {
	content: "\f200";
}

i.mce-i-italic:before {
	content: "\f201";
}

i.mce-i-bullist:before {
	content: "\f203";
}

i.mce-i-numlist:before {
	content: "\f204";
}

i.mce-i-blockquote:before {
	content: "\f205";
}

i.mce-i-alignleft:before {
	content: "\f206";
}

i.mce-i-aligncenter:before {
	content: "\f207";
}

i.mce-i-alignright:before {
	content: "\f208";
}

i.mce-i-link:before {
	content: "\f103";
}

i.mce-i-unlink:before {
	content: "\f225";
}

i.mce-i-wp_more:before {
	content: "\f209";
}

i.mce-i-strikethrough:before {
	content: "\f224";
}

i.mce-i-spellchecker:before {
	content: "\f210";
}

i.mce-i-fullscreen:before,
i.mce-i-wp_fullscreen:before,
i.mce-i-dfw:before,
.qt-dfw:before {
	content: "\f211";
}

i.mce-i-wp_adv:before {
	content: "\f212";
}

i.mce-i-underline:before {
	content: "\f213";
}

i.mce-i-alignjustify:before {
	content: "\f214";
}

i.mce-i-forecolor:before,
i.mce-i-backcolor:before {
	content: "\f215";
}

i.mce-i-pastetext:before {
	content: "\f217";
}

i.mce-i-removeformat:before {
	content: "\f218";
}

i.mce-i-charmap:before {
	content: "\f220";
}

i.mce-i-outdent:before {
	content: "\f221";
}

i.mce-i-indent:before {
	content: "\f222";
}

i.mce-i-undo:before {
	content: "\f171";
}

i.mce-i-redo:before {
	content: "\f172";
}

i.mce-i-help:before,
i.mce-i-wp_help:before {
	content: "\f223";
}

i.mce-i-wp-media-library:before {
	content: "\f104";
}

i.mce-i-ltr:before {
	content: "\f320";
}

i.mce-i-wp_page:before {
	content: "\f105";
}

i.mce-i-hr:before {
	content: "\f460";
}

i.mce-i-remove:before {
	content: "\f158";
}

i.mce-i-wp_code:before {
	content: "\f475";
}

/* RTL button icons */
.rtl i.mce-i-outdent:before {
	content: "\f222";
}

.rtl i.mce-i-indent:before {
	content: "\f221";
}

/* Editors */
.wp-editor-wrap {
	position: relative;
}

.wp-editor-tools {
	position: relative;
	z-index: 1;
}

.wp-editor-tools:after {
	clear: both;
	content: "";
	display: table;
}

.wp-editor-container {
	clear: both;
	border: 1px solid #dcdcde;
}

.wp-editor-area {
	font-family: Consolas, Monaco, monospace;
	font-size: 13px;
	padding: 10px;
	margin: 1px 0 0;
	line-height: 150%;
	border: 0;
	outline: none;
	display: block;
	resize: vertical;
	box-sizing: border-box;
}

.rtl .wp-editor-area {
	font-family: Tahoma, Monaco, monospace;
}

.locale-he-il .wp-editor-area {
	font-family: Arial, Monaco, monospace;
}

.wp-editor-container textarea.wp-editor-area {
	width: 100%;
	margin: 0;
	box-shadow: none;
}

.wp-editor-tabs {
	float: right;
}

.wp-switch-editor {
	float: left;
	box-sizing: content-box;
	position: relative;
	top: 1px;
	background: #f0f0f1;
	color: #646970;
	cursor: pointer;
	font-size: 13px;
	line-height: 1.46153846;
	height: 20px;
	margin: 5px 0 0 5px;
	padding: 3px 8px 4px;
	border: 1px solid #dcdcde;
}

.wp-switch-editor:focus {
	box-shadow:
		0 0 0 1px #4f94d4,
		0 0 2px 1px rgba(79, 148, 212, 0.8);
	outline: none;
	color: #1d2327;
}

.wp-switch-editor:active,
.html-active .switch-html:focus,
.tmce-active .switch-tmce:focus {
	box-shadow: none;
}

.wp-switch-editor:active {
	background-color: #f6f7f7;
	box-shadow: none;
}

.js .tmce-active .wp-editor-area {
	color: #fff;
}

.tmce-active .quicktags-toolbar {
	display: none;
}

.tmce-active .switch-tmce,
.html-active .switch-html {
	background: #f6f7f7;
	color: #50575e;
	border-bottom-color: #f6f7f7;
}

.wp-media-buttons {
	float: left;
}

.wp-media-buttons .button {
	margin-right: 5px;
	margin-bottom: 4px;
	padding-left: 7px;
	padding-right: 7px;
}

.wp-media-buttons .button:active {
	position: relative;
	top: 1px;
	margin-top: -1px;
	margin-bottom: 1px;
}

.wp-media-buttons .insert-media {
	padding-left: 5px;
}

.wp-media-buttons a {
	text-decoration: none;
	color: #3c434a;
	font-size: 12px;
}

.wp-media-buttons img {
	padding: 0 4px;
	vertical-align: middle;
}

.wp-media-buttons span.wp-media-buttons-icon {
	display: inline-block;
	width: 20px;
	height: 20px;
	line-height: 1;
	vertical-align: middle;
	margin: 0 2px;
}

.wp-media-buttons .add_media span.wp-media-buttons-icon {
	background: none;
}

.wp-media-buttons .add_media span.wp-media-buttons-icon:before {
	font: normal 18px/1 dashicons;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wp-media-buttons .add_media span.wp-media-buttons-icon:before {
	content: "\f104";
}

.mce-content-body dl.wp-caption {
	max-width: 100%;
}

/* Quicktags */
.quicktags-toolbar {
	padding: 3px;
	position: relative;
	border-bottom: 1px solid #dcdcde;
	background: #f6f7f7;
	min-height: 30px;
}

.has-dfw .quicktags-toolbar {
	padding-right: 35px;
}

.wp-core-ui .quicktags-toolbar input.button.button-small {
	margin: 2px;
}

.quicktags-toolbar input[value="link"] {
	text-decoration: underline;
}

.quicktags-toolbar input[value="del"] {
	text-decoration: line-through;
}

.quicktags-toolbar input[value="i"] {
	font-style: italic;
}

.quicktags-toolbar input[value="b"] {
	font-weight: 600;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-wp-dfw,
.qt-dfw {
	position: absolute;
	top: 0;
	right: 0;
}

.mce-toolbar .mce-btn-group .mce-btn.mce-wp-dfw {
	margin: 7px 7px 0 0;
}

.qt-dfw {
	margin: 5px 5px 0 0;
}

.qt-fullscreen {
	position: static;
	margin: 2px;
}

@media screen and (max-width: 782px) {
	.mce-toolbar .mce-btn button,
	.qt-dfw {
		padding: 6px 7px;
	}

	/* Compensate for the extra box shadow at the bottom of .mce-btn.mce-primary */
	.mce-toolbar .mce-btn-group .mce-btn.mce-primary button {
		padding: 6px 7px 5px;
	}

	.mce-toolbar .mce-btn-group .mce-btn {
		margin: 1px;
	}

	.qt-dfw {
		width: 36px;
		height: 34px;
	}

	.mce-toolbar .mce-btn-group .mce-btn.mce-wp-dfw {
		margin: 4px 4px 0 0;
	}

	.mce-toolbar .mce-colorbutton .mce-preview {
		left: 8px;
		bottom: 6px;
	}

	.mce-window .mce-btn {
		padding: 2px 0;
	}

	.has-dfw div.mce-toolbar-grp .mce-toolbar.mce-first,
	.has-dfw .quicktags-toolbar {
		padding-right: 40px;
	}
}

@media screen and (min-width: 782px) {
	.wp-core-ui .quicktags-toolbar input.button.button-small {
		/* .button-small is normally 11px, but a bit too small for these buttons. */
		font-size: 12px;
		min-height: 26px;
		line-height: 2;
	}
}

#wp_editbtns,
#wp_gallerybtns {
	padding: 2px;
	position: absolute;
	display: none;
	z-index: 100020;
}

#wp_editimgbtn,
#wp_delimgbtn,
#wp_editgallery,
#wp_delgallery {
	background-color: #f0f0f1;
	margin: 2px;
	padding: 2px;
	border: 1px solid #8c8f94;
	border-radius: 3px;
}

#wp_editimgbtn:hover,
#wp_delimgbtn:hover,
#wp_editgallery:hover,
#wp_delgallery:hover {
	border-color: #50575e;
	background-color: #c3c4c7;
}

/*------------------------------------------------------------------------------
 wp-link
------------------------------------------------------------------------------*/

#wp-link-wrap {
	display: none;
	background-color: #fff;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
	width: 500px;
	overflow: hidden;
	margin-left: -250px;
	margin-top: -125px;
	position: fixed;
	top: 50%;
	left: 50%;
	z-index: 100105;
	transition: height 0.2s, margin-top 0.2s;
}

#wp-link-backdrop {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	min-height: 360px;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 100100;
}

#wp-link {
	position: relative;
	height: 100%;
}

#wp-link-wrap {
	height: 500px;
	margin-top: -250px;
}

#wp-link-wrap .wp-link-text-field {
	display: none;
}

#wp-link-wrap.has-text-field .wp-link-text-field {
	display: block;
}

#link-modal-title {
	background: #fff;
	border-bottom: 1px solid #dcdcde;
	height: 36px;
	font-size: 18px;
	font-weight: 600;
	line-height: 2;
	margin: 0;
	padding: 0 36px 0 16px;
}

#wp-link-close {
	color: #646970;
	padding: 0;
	position: absolute;
	top: 0;
	right: 0;
	width: 36px;
	height: 36px;
	text-align: center;
	background: none;
	border: none;
	cursor: pointer;
}

#wp-link-close:before {
	font: normal 20px/36px dashicons;
	vertical-align: top;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	width: 36px;
	height: 36px;
	content: "\f158";
}

#wp-link-close:hover,
#wp-link-close:focus {
	color: #135e96;
}

#wp-link-close:focus {
	outline: none;
	box-shadow:
		0 0 0 1px #4f94d4,
		0 0 2px 1px rgba(79, 148, 212, 0.8);
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	outline-offset: -2px;
}

#wp-link-wrap #link-selector {
	-webkit-overflow-scrolling: touch;
	padding: 0 16px;
	position: absolute;
	top: 37px;
	left: 0;
	right: 0;
	bottom: 44px;
}

#wp-link ol,
#wp-link ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

#wp-link input[type="text"] {
	box-sizing: border-box;
}

#wp-link #link-options {
	padding: 8px 0 12px;
}

#wp-link p.howto {
	margin: 3px 0;
}

#wp-link p.howto a {
	text-decoration: none;
	color: inherit;
}

#wp-link label input[type="text"] {
	margin-top: 5px;
	width: 70%;
}

#wp-link #link-options label span,
#wp-link #search-panel label span.search-label {
	display: inline-block;
	width: 80px;
	text-align: right;
	padding-right: 5px;
	max-width: 24%;
	vertical-align: middle;
	word-wrap: break-word;
}

#wp-link .link-search-field {
	width: 250px;
	max-width: 70%;
}

#wp-link .link-search-wrapper {
	margin: 5px 0 9px;
	display: block;
	overflow: hidden;
}

#wp-link .link-search-wrapper .spinner {
	float: none;
	margin: -3px 0 0 4px;
}

#wp-link .link-target {
	padding: 3px 0 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

#wp-link .link-target label {
	max-width: 70%;
}

#wp-link .query-results {
	border: 1px #dcdcde solid;
	margin: 0 0 12px;
	background: #fff;
	overflow: auto;
	position: absolute;
	left: 16px;
	right: 16px;
	bottom: 0;
	top: 166px;
}

.has-text-field #wp-link .query-results {
	top: 210px;
}

#wp-link li {
	clear: both;
	margin-bottom: 0;
	border-bottom: 1px solid #f0f0f1;
	color: #2c3338;
	padding: 4px 6px 4px 10px;
	cursor: pointer;
	position: relative;
}

#wp-link .query-notice {
	padding: 0;
	border-bottom: 1px solid #dcdcde;
	background-color: #fff;
	color: #000;
}

#wp-link .query-notice .query-notice-default,
#wp-link .query-notice .query-notice-hint {
	display: block;
	padding: 6px;
	border-left: 4px solid #72aee6;
}

#wp-link .unselectable.no-matches-found {
	padding: 0;
	border-bottom: 1px solid #dcdcde;
	background-color: #f6f7f7;
}

#wp-link .no-matches-found .item-title {
	display: block;
	padding: 6px;
	border-left: 4px solid #d63638;
}

#wp-link .query-results em {
	font-style: normal;
}

#wp-link li:hover {
	background: #f0f6fc;
	color: #101517;
}

#wp-link li.unselectable {
	border-bottom: 1px solid #dcdcde;
}

#wp-link li.unselectable:hover {
	background: #fff;
	cursor: auto;
	color: #2c3338;
}

#wp-link li.selected {
	background: #dcdcde;
	color: #2c3338;
}

#wp-link li.selected .item-title {
	font-weight: 600;
}

#wp-link li:last-child {
	border: none;
}

#wp-link .item-title {
	display: inline-block;
	width: 80%;
	width: calc(100% - 68px);
	word-wrap: break-word;
}

#wp-link .item-info {
	text-transform: uppercase;
	color: #646970;
	font-size: 11px;
	position: absolute;
	right: 5px;
	top: 5px;
}

#wp-link .river-waiting {
	display: none;
	padding: 10px 0;
}

#wp-link .submitbox {
	padding: 8px 16px;
	background: #fff;
	border-top: 1px solid #dcdcde;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
}

#wp-link-cancel {
	line-height: 1.92307692;
	float: left;
}

#wp-link-update {
	line-height: 1.76923076;
	float: right;
}

#wp-link-submit {
	float: right;
}

@media screen and (max-width: 782px) {
	#wp-link-wrap {
		margin-top: -140px;
	}

	#wp-link-wrap .query-results {
		top: 195px;
	}

	#wp-link-wrap.has-text-field .query-results {
		top: 235px;
	}

	#link-selector {
		padding: 0 16px 60px;
	}

	#wp-link-wrap #link-selector {
		bottom: 52px;
	}

	#wp-link-cancel {
		line-height: 2.46153846;
	}

	#wp-link .link-target {
		padding-top: 10px;
	}

	#wp-link .submitbox .button {
		margin-bottom: 0;
	}
}

@media screen and (max-width: 520px) {
	#wp-link-wrap {
		width: auto;
		margin-left: 0;
		left: 10px;
		right: 10px;
		max-width: 500px;
	}
}

@media screen and (max-height: 520px) {
	#wp-link-wrap {
		transition: none;
		height: auto;
		margin-top: 0;
		top: 10px;
		bottom: 10px;
	}

	#link-selector {
		overflow: auto;
	}

	#search-panel .query-results {
		position: static;
	}
}

@media screen and (max-height: 290px) {
	#wp-link-wrap {
		height: auto;
		margin-top: 0;
		top: 10px;
		bottom: 10px;
	}

	#link-selector {
		overflow: auto;
		height: calc(100% - 92px);
		padding-bottom: 2px;
	}

	#search-panel .query-results {
		position: static;
	}
}

div.wp-link-preview {
	float: left;
	margin: 5px;
	max-width: 694px;
	overflow: hidden;
	text-overflow: ellipsis;
}

div.wp-link-preview a {
	color: #2271b1;
	text-decoration: underline;
	transition-property: border, background, color;
	transition-duration: .05s;
	transition-timing-function: ease-in-out;
	cursor: pointer;
}

div.wp-link-preview a.wplink-url-error {
	color: #d63638;
}

div.wp-link-input {
	float: left;
	margin: 2px;
	max-width: 694px;
}

div.wp-link-input input {
	width: 300px;
	padding: 3px;
	box-sizing: border-box;
	line-height: 1.28571429; /* 18px */
	/* Override value inherited from default input fields. */
	min-height: 26px;
}

.mce-toolbar div.wp-link-preview ~ .mce-btn,
.mce-toolbar div.wp-link-input ~ .mce-btn {
	margin: 2px 1px;
}

.mce-inline-toolbar-grp .mce-btn-group .mce-btn:last-child {
	margin-right: 2px;
}

.ui-autocomplete.wplink-autocomplete {
	z-index: 100110;
	max-height: 200px;
	overflow-y: auto;
	padding: 0;
	margin: 0;
	list-style: none;
	position: absolute;
	border: 1px solid #4f94d4;
	box-shadow: 0 1px 2px rgba(79, 148, 212, 0.8);
	background-color: #fff;
}

.ui-autocomplete.wplink-autocomplete li {
	margin-bottom: 0;
	padding: 4px 10px;
	clear: both;
	white-space: normal;
	text-align: left;
}

.ui-autocomplete.wplink-autocomplete li .wp-editor-float-right {
	float: right;
}

.ui-autocomplete.wplink-autocomplete li.ui-state-focus {
	background-color: #dcdcde;
	cursor: pointer;
}

@media screen and (max-width: 782px) {
	div.wp-link-preview,
	div.wp-link-input {
		max-width: 70%;
		max-width: calc(100% - 86px);
	}

	div.wp-link-preview {
		margin: 8px 0 8px 5px;
	}

	div.wp-link-input {
		width: 300px;
	}

	div.wp-link-input input {
		width: 100%;
		font-size: 16px;
		padding: 5px;
	}
}

/* =Overlay Body
-------------------------------------------------------------- */

.mce-fullscreen {
	z-index: 100010;
}

/* =Localization
-------------------------------------------------------------- */
.rtl .wp-switch-editor,
.rtl .quicktags-toolbar input {
	font-family: Tahoma, sans-serif;
}

/* rtl:ignore */
.mce-rtl .mce-flow-layout .mce-flow-layout-item > div {
	direction: rtl;
}

/* rtl:ignore */
.mce-rtl .mce-listbox i.mce-caret {
	left: 6px;
}

html:lang(he-il) .rtl .wp-switch-editor,
html:lang(he-il) .rtl .quicktags-toolbar input {
	font-family: Arial, sans-serif;
}

/* HiDPI */
@media print,
  (-webkit-min-device-pixel-ratio: 1.25),
  (min-resolution: 120dpi) {
	.wp-media-buttons .add_media span.wp-media-buttons-icon {
		background: none;
	}
}
