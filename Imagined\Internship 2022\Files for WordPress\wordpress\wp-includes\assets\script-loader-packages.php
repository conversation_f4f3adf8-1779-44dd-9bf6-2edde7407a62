<?php return array('a11y.js' => array('dependencies' => array('wp-dom-ready', 'wp-i18n', 'wp-polyfill'), 'version' => 'a38319d7ba46c6e60f7f9d4c371222c5'), 'annotations.js' => array('dependencies' => array('lodash', 'wp-data', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-rich-text'), 'version' => 'e103c345829d2b4da838b701a4dff236'), 'api-fetch.js' => array('dependencies' => array('wp-i18n', 'wp-polyfill', 'wp-url'), 'version' => '63050163caffa6aac54e9ebf21fe0472'), 'autop.js' => array('dependencies' => array('wp-polyfill'), 'version' => '21d1d6c005241b908b592f52ad684a28'), 'blob.js' => array('dependencies' => array('wp-polyfill'), 'version' => '87cf2365cd719a6954f1e2bb8bcc692a'), 'block-directory.js' => array('dependencies' => array('lodash', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-edit-post', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-plugins', 'wp-polyfill', 'wp-primitives', 'wp-url'), 'version' => '7cf0ebaf9ad71b48f8e259da09c04a96'), 'block-editor.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-polyfill', 'wp-primitives', 'wp-rich-text', 'wp-shortcode', 'wp-token-list', 'wp-url', 'wp-warning', 'wp-wordcount'), 'version' => 'dd2732876a0044113826cb7f80522e70'), 'block-library.js' => array('dependencies' => array('lodash', 'moment', 'wp-a11y', 'wp-api-fetch', 'wp-autop', 'wp-blob', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keycodes', 'wp-notices', 'wp-polyfill', 'wp-primitives', 'wp-reusable-blocks', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-viewport'), 'version' => 'dcc9ded9544ed588a6ecc6aee441005c'), 'block-serialization-default-parser.js' => array('dependencies' => array('wp-polyfill'), 'version' => '8ee151736a1e51db2bafbb61ddd60634'), 'blocks.js' => array('dependencies' => array('lodash', 'wp-autop', 'wp-blob', 'wp-block-serialization-default-parser', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-shortcode'), 'version' => '658a51e7220626e26a92a46af5c2e489'), 'components.js' => array('dependencies' => array('lodash', 'moment', 'react', 'react-dom', 'wp-a11y', 'wp-compose', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-escape-html', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-polyfill', 'wp-primitives', 'wp-rich-text', 'wp-warning'), 'version' => '0e6d68fb5b3f61d537b3d3411bddd6fc'), 'compose.js' => array('dependencies' => array('lodash', 'react', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-polyfill', 'wp-priority-queue'), 'version' => 'e52c48958a19b766c6a9d28c02d53575'), 'core-data.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-blocks', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-url'), 'version' => '95df951bbac4c9f2fd2e6da80561595d'), 'customize-widgets.js' => array('dependencies' => array('lodash', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-widgets'), 'version' => '498aa94909e53672986d64a11b67d88b'), 'data.js' => array('dependencies' => array('lodash', 'react', 'wp-compose', 'wp-deprecated', 'wp-element', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-priority-queue', 'wp-redux-routine'), 'version' => '6c1ab5799c4b061254d313d2d8d9fb87'), 'data-controls.js' => array('dependencies' => array('wp-api-fetch', 'wp-data', 'wp-deprecated', 'wp-polyfill'), 'version' => '6a75067d86cf9ab901a4646595575446'), 'date.js' => array('dependencies' => array('moment', 'wp-polyfill'), 'version' => 'e0e02b3a1c7037e6cb893486d4680631'), 'deprecated.js' => array('dependencies' => array('wp-hooks', 'wp-polyfill'), 'version' => '96593d5d272d008fbcb6912fa0b86778'), 'dom.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => '3c10edc1abf3fbbc79f17fd7d1d332eb'), 'dom-ready.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'd996b53411d1533a84951212ab6ac4ff'), 'edit-post.js' => array('dependencies' => array('lodash', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-url', 'wp-viewport', 'wp-warning'), 'version' => '51ebcd5cd745a9866ab9a430d8318189'), 'edit-site.js' => array('dependencies' => array('lodash', 'react', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-reusable-blocks', 'wp-url', 'wp-viewport'), 'version' => '3497072223e20a22d15e7212dd6da2db'), 'edit-widgets.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-reusable-blocks', 'wp-url', 'wp-viewport', 'wp-widgets'), 'version' => 'd81f8f3ae104f6157df90b9c8d26cd39'), 'editor.js' => array('dependencies' => array('lodash', 'react', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-reusable-blocks', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-wordcount'), 'version' => '5ed697cafca349e71af1c7f8842fcd44'), 'element.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-escape-html', 'wp-polyfill'), 'version' => '3dfdc75a0abf30f057df44e9a39abe5b'), 'escape-html.js' => array('dependencies' => array('wp-polyfill'), 'version' => '00a5735837e9efe13da1d979f16a7105'), 'format-library.js' => array('dependencies' => array('lodash', 'wp-a11y', 'wp-block-editor', 'wp-components', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-polyfill', 'wp-primitives', 'wp-rich-text', 'wp-url'), 'version' => '98e32e5276b4b59e7b283addf7cdbd8c'), 'hooks.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'c6d64f2cb8f5c6bb49caca37f8828ce3'), 'html-entities.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'c6385fb7cd9fdada1cf8892a545f8a26'), 'i18n.js' => array('dependencies' => array('wp-hooks', 'wp-polyfill'), 'version' => 'ebee46757c6a411e38fd079a7ac71d94'), 'is-shallow-equal.js' => array('dependencies' => array('wp-polyfill'), 'version' => '649feec00389556f8015a6b97efc1cb1'), 'keyboard-shortcuts.js' => array('dependencies' => array('lodash', 'wp-data', 'wp-element', 'wp-keycodes', 'wp-polyfill'), 'version' => '37fdd241f3be3126e9248060e363e7c9'), 'keycodes.js' => array('dependencies' => array('lodash', 'wp-i18n', 'wp-polyfill'), 'version' => '84a0e6bbcf0b9e1ea0184c3f2bf28022'), 'list-reusable-blocks.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-element', 'wp-i18n', 'wp-polyfill'), 'version' => '2b04eb3e5628488fe2cb534f02806022'), 'media-utils.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-blob', 'wp-element', 'wp-i18n', 'wp-polyfill'), 'version' => 'ee812d25ec8504c5b419a1aa3cae974d'), 'notices.js' => array('dependencies' => array('lodash', 'wp-data', 'wp-polyfill'), 'version' => 'e44820c667bf205cacdfc48cbeb3c2e6'), 'nux.js' => array('dependencies' => array('lodash', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-primitives'), 'version' => '48ab41273bfd9022769c430fcd3c23cf'), 'plugins.js' => array('dependencies' => array('lodash', 'wp-compose', 'wp-element', 'wp-hooks', 'wp-polyfill', 'wp-primitives'), 'version' => 'f885533aefb501e1eedcbd4a9d04ca70'), 'preferences.js' => array('dependencies' => array('wp-a11y', 'wp-components', 'wp-data', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-primitives'), 'version' => 'a9b6f95c63bb642d0be7c3d1d208b302'), 'primitives.js' => array('dependencies' => array('wp-element', 'wp-polyfill'), 'version' => 'cadf5cfaabdb15c8c8fc440547afe919'), 'priority-queue.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'efad6460ae6b28406d39866cb10731e0'), 'redux-routine.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => '5156478c032ea85a2bbdceeb7a43b0c1'), 'reusable-blocks.js' => array('dependencies' => array('lodash', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-polyfill', 'wp-primitives', 'wp-url'), 'version' => '7387bed6e19d1b3aec4b6fda683a0768'), 'rich-text.js' => array('dependencies' => array('lodash', 'wp-a11y', 'wp-compose', 'wp-data', 'wp-element', 'wp-escape-html', 'wp-i18n', 'wp-keycodes', 'wp-polyfill'), 'version' => 'e7d57502b62ec4756783a0cd79238841'), 'server-side-render.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url'), 'version' => '9aa1118308e1a7ca5fbe4b842b1ba63e'), 'shortcode.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => 'd6964e945049b6190adc8770cda168c4'), 'token-list.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => '4ebce6423dfff99d40033fd33ce52cc9'), 'url.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => '16385e4d69da65c7283790971de6b297'), 'viewport.js' => array('dependencies' => array('lodash', 'wp-compose', 'wp-data', 'wp-element', 'wp-polyfill'), 'version' => '7ee74cd3ed0dfcfd22b233b8750d1025'), 'warning.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'b9190af8fc6a3a48c580473c6f337b88'), 'widgets.js' => array('dependencies' => array('lodash', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-polyfill', 'wp-primitives'), 'version' => '80e98954519d1dad7a91a2248dbc2cc9'), 'wordcount.js' => array('dependencies' => array('lodash', 'wp-polyfill'), 'version' => 'fb8056c75aa0a0569f7ea3ceae97fbc6'));