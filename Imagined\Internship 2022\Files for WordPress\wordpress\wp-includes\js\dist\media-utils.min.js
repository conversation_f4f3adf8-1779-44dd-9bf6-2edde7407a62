/*! This file is auto-generated */
!function(){"use strict";var e={n:function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,{a:i}),i},d:function(t,i){for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MediaUpload:function(){return c},uploadMedia:function(){return g}});var i=window.lodash,o=window.wp.element,a=window.wp.i18n;const{wp:l}=window,s=[],r=()=>l.media.view.MediaFrame.Select.extend({featuredImageToolbar(e){this.createSelectToolbar(e,{text:l.media.view.l10n.setFeaturedImage,state:this.options.state})},editState(){const e=this.state("featured-image").get("selection"),t=new l.media.view.EditImage({model:e.single(),controller:this}).render();this.content.set(t),t.loadEditor()},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new l.media.controller.FeaturedImage,new l.media.controller.EditImage({model:this.options.editImage})])}}),n=()=>l.media.view.MediaFrame.Post.extend({galleryToolbar(){const e=this.state().get("editing");this.toolbar.set(new l.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:e?l.media.view.l10n.updateGallery:l.media.view.l10n.insertGallery,priority:80,requires:{library:!0},click(){const e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},editState(){const e=this.state("gallery").get("selection"),t=new l.media.view.EditImage({model:e.single(),controller:this}).render();this.content.set(t),t.loadEditor()},createStates:function(){this.on("toolbar:create:main-gallery",this.galleryToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new l.media.controller.Library({id:"gallery",title:l.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:l.media.query((0,i.defaults)({type:"image"},this.options.library))}),new l.media.controller.EditImage({model:this.options.editImage}),new l.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new l.media.controller.GalleryAdd])}}),d=e=>(0,i.pick)(e,["sizes","mime","type","subtype","id","url","alt","link","caption"]),p=e=>l.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"});class m extends o.Component{constructor(e){let{allowedTypes:t,gallery:i=!1,unstableFeaturedImageFlow:o=!1,modalClass:s,multiple:r=!1,title:n=(0,a.__)("Select or Upload Media")}=e;if(super(...arguments),this.openModal=this.openModal.bind(this),this.onOpen=this.onOpen.bind(this),this.onSelect=this.onSelect.bind(this),this.onUpdate=this.onUpdate.bind(this),this.onClose=this.onClose.bind(this),i)this.buildAndSetGalleryFrame();else{const e={title:n,multiple:r};t&&(e.library={type:t}),this.frame=l.media(e)}s&&this.frame.$el.addClass(s),o&&this.buildAndSetFeatureImageFrame(),this.initializeListeners()}initializeListeners(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}buildAndSetGalleryFrame(){const{addToGallery:e=!1,allowedTypes:t,multiple:i=!1,value:o=s}=this.props;if(o===this.lastGalleryValue)return;let a;this.lastGalleryValue=o,this.frame&&this.frame.remove(),a=e?"gallery-library":o&&o.length?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=n());const r=p(o),d=new l.media.model.Selection(r.models,{props:r.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:t,state:a,multiple:i,selection:d,editing:!(!o||!o.length)}),l.media.frame=this.frame,this.initializeListeners()}buildAndSetFeatureImageFrame(){const e=r(),t=p(this.props.value),i=new l.media.model.Selection(t.models,{props:t.props.toJSON()});this.frame=new e({mimeType:this.props.allowedTypes,state:"featured-image",multiple:this.props.multiple,selection:i,editing:!!this.props.value}),l.media.frame=this.frame}componentWillUnmount(){this.frame.remove()}onUpdate(e){const{onSelect:t,multiple:i=!1}=this.props,o=this.frame.state(),a=e||o.get("selection");a&&a.models.length&&t(i?a.models.map((e=>d(e.toJSON()))):d(a.models[0].toJSON()))}onSelect(){const{onSelect:e,multiple:t=!1}=this.props,i=this.frame.state().get("selection").toJSON();e(t?i:i[0])}onOpen(){var e;this.updateCollection();if(!(Array.isArray(this.props.value)?!(null===(e=this.props.value)||void 0===e||!e.length):!!this.props.value))return;const t=this.props.gallery,o=this.frame.state().get("selection");t||(0,i.castArray)(this.props.value).forEach((e=>{o.add(l.media.attachment(e))}));const a=p((0,i.castArray)(this.props.value));a.more().done((function(){var e;t&&null!=a&&null!==(e=a.models)&&void 0!==e&&e.length&&o.add(a.models)}))}onClose(){const{onClose:e}=this.props;e&&e()}updateCollection(){const e=this.frame.content.get();if(e&&e.collection){const t=e.collection;t.toArray().forEach((e=>e.trigger("destroy",e))),t.mirroring._hasMore=!0,t.more()}}openModal(){this.props.gallery&&this.buildAndSetGalleryFrame(),this.frame.open()}render(){return this.props.render({open:this.openModal})}}var c=m,h=window.wp.apiFetch,u=e.n(h),y=window.wp.blob;async function g(e){let{allowedTypes:t,additionalData:l={},filesList:s,maxUploadFileSize:r,onError:n=i.noop,onFileChange:d,wpAllowedMimeTypes:p=null}=e;const m=[...s],c=[],h=(e,t)=>{(0,y.revokeBlobURL)((0,i.get)(c,[e,"url"])),c[e]=t,d((0,i.compact)(c))},u=e=>!t||(0,i.some)(t,(t=>(0,i.includes)(t,"/")?t===e:(0,i.startsWith)(e,`${t}/`))),g=(w=p)?(0,i.flatMap)(w,((e,t)=>{const[o]=e.split("/"),a=t.split("|");return[e,...(0,i.map)(a,(e=>`${o}/${e}`))]})):w;var w;const b=e=>{e.message=[(0,o.createElement)("strong",{key:"filename"},e.file.name),": ",e.message],n(e)},S=[];for(const e of m)g&&e.type&&(_=e.type,!(0,i.includes)(g,_))?b({code:"MIME_TYPE_NOT_ALLOWED_FOR_USER",message:(0,a.__)("Sorry, you are not allowed to upload this file type."),file:e}):!e.type||u(e.type)?r&&e.size>r?b({code:"SIZE_ABOVE_LIMIT",message:(0,a.__)("This file exceeds the maximum upload size for this site."),file:e}):e.size<=0?b({code:"EMPTY_FILE",message:(0,a.__)("This file is empty."),file:e}):(S.push(e),c.push({url:(0,y.createBlobURL)(e)}),d(c)):b({code:"MIME_TYPE_NOT_SUPPORTED",message:(0,a.__)("Sorry, this file type is not supported here."),file:e});var _;for(let e=0;e<S.length;++e){const t=S[e];try{const o=await f(t,l);h(e,{...(0,i.omit)(o,["alt_text","source_url"]),alt:o.alt_text,caption:(0,i.get)(o,["caption","raw"],""),title:o.title.raw,url:o.source_url})}catch(o){let l;h(e,null),l=(0,i.has)(o,["message"])?(0,i.get)(o,["message"]):(0,a.sprintf)((0,a.__)("Error while uploading file %s to the media library."),t.name),n({code:"GENERAL",message:l,file:t})}}}function f(e,t){const o=new window.FormData;return o.append("file",e,e.name||e.type.replace("/",".")),(0,i.forEach)(t,((e,t)=>o.append(t,e))),u()({path:"/wp/v2/media",body:o,method:"POST"})}(window.wp=window.wp||{}).mediaUtils=t}();