{"name": "phpmyadmin/sql-parser", "description": "A validating SQL lexer and parser with a focus on MySQL dialect.", "license": "GPL-2.0-or-later", "keywords": ["sql", "lexer", "parser", "analysis"], "homepage": "https://github.com/phpmyadmin/sql-parser", "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "support": {"issues": "https://github.com/phpmyadmin/sql-parser/issues", "source": "https://github.com/phpmyadmin/sql-parser"}, "require": {"php": "^7.1 || ^8.0", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpmyadmin/coding-standard": "^3.0", "phpmyadmin/motranslator": "^4.0 || ^5.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.2", "phpstan/phpstan-phpunit": "^1.0", "phpunit/php-code-coverage": "*", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.11", "zumba/json-serializer": "^3.0"}, "conflict": {"phpmyadmin/motranslator": "<3.0"}, "suggest": {"ext-mbstring": "For best performance", "phpmyadmin/motranslator": "Translate messages to your favorite locale"}, "bin": ["bin/highlight-query", "bin/lint-query", "bin/tokenize-query"], "autoload": {"psr-4": {"PhpMyAdmin\\SqlParser\\": "src"}}, "autoload-dev": {"psr-4": {"PhpMyAdmin\\SqlParser\\Tests\\": "tests"}}, "config": {"sort-packages": true}}