/*! This file is auto-generated */
!function(p,c,g){var m=p(document),h=c.i18n.__,i=c.i18n._x,l=c.i18n._n,o=c.i18n._nx,r=c.i18n.sprintf;(c=c||{}).updates={},c.updates.l10n={searchResults:"",searchResultsLabel:"",noPlugins:"",noItemsSelected:"",updating:"",pluginUpdated:"",themeUpdated:"",update:"",updateNow:"",pluginUpdateNowLabel:"",updateFailedShort:"",updateFailed:"",pluginUpdatingLabel:"",pluginUpdatedLabel:"",pluginUpdateFailedLabel:"",updatingMsg:"",updatedMsg:"",updateCancel:"",beforeunload:"",installNow:"",pluginInstallNowLabel:"",installing:"",pluginInstalled:"",themeInstalled:"",installFailedShort:"",installFailed:"",pluginInstallingLabel:"",themeInstallingLabel:"",pluginInstalledLabel:"",themeInstalledLabel:"",pluginInstallFailedLabel:"",themeInstallFailedLabel:"",installingMsg:"",installedMsg:"",importerInstalledMsg:"",aysDelete:"",aysDeleteUninstall:"",aysBulkDelete:"",aysBulkDeleteThemes:"",deleting:"",deleteFailed:"",pluginDeleted:"",themeDeleted:"",livePreview:"",activatePlugin:"",activateTheme:"",activatePluginLabel:"",activateThemeLabel:"",activateImporter:"",activateImporterLabel:"",unknownError:"",connectionError:"",nonceError:"",pluginsFound:"",noPluginsFound:"",autoUpdatesEnable:"",autoUpdatesEnabling:"",autoUpdatesEnabled:"",autoUpdatesDisable:"",autoUpdatesDisabling:"",autoUpdatesDisabled:"",autoUpdatesError:""},c.updates.l10n=window.wp.deprecateL10nObject("wp.updates.l10n",c.updates.l10n,"5.5.0"),c.updates.ajaxNonce=g.ajax_nonce,c.updates.searchTerm="",c.updates.shouldRequestFilesystemCredentials=!1,c.updates.filesystemCredentials={ftp:{host:"",username:"",password:"",connectionType:""},ssh:{publicKey:"",privateKey:""},fsNonce:"",available:!1},c.updates.ajaxLocked=!1,c.updates.adminNotice=c.template("wp-updates-admin-notice"),c.updates.queue=[],c.updates.$elToReturnFocusToFromCredentialsModal=void 0,c.updates.addAdminNotice=function(e){var t,a=p(e.selector),s=p(".wp-header-end");delete e.selector,t=c.updates.adminNotice(e),(a=a.length?a:p("#"+e.id)).length?a.replaceWith(t):s.length?s.after(t):"customize"===pagenow?p(".customize-themes-notifications").append(t):p(".wrap").find("> h1").after(t),m.trigger("wp-updates-notice-added")},c.updates.ajax=function(e,t){var a={};return c.updates.ajaxLocked?(c.updates.queue.push({action:e,data:t}),p.Deferred()):(c.updates.ajaxLocked=!0,t.success&&(a.success=t.success,delete t.success),t.error&&(a.error=t.error,delete t.error),a.data=_.extend(t,{action:e,_ajax_nonce:c.updates.ajaxNonce,_fs_nonce:c.updates.filesystemCredentials.fsNonce,username:c.updates.filesystemCredentials.ftp.username,password:c.updates.filesystemCredentials.ftp.password,hostname:c.updates.filesystemCredentials.ftp.hostname,connection_type:c.updates.filesystemCredentials.ftp.connectionType,public_key:c.updates.filesystemCredentials.ssh.publicKey,private_key:c.updates.filesystemCredentials.ssh.privateKey}),c.ajax.send(a).always(c.updates.ajaxAlways))},c.updates.ajaxAlways=function(e){e.errorCode&&"unable_to_connect_to_filesystem"===e.errorCode||(c.updates.ajaxLocked=!1,c.updates.queueChecker()),void 0!==e.debug&&window.console&&window.console.log&&_.map(e.debug,function(e){window.console.log(c.sanitize.stripTagsAndEncodeText(e))})},c.updates.refreshCount=function(){var e,t=p("#wp-admin-bar-updates"),a=p('a[href="update-core.php"] .update-plugins'),s=p('a[href="plugins.php"] .update-plugins'),n=p('a[href="themes.php"] .update-plugins');t.find(".ab-label").text(g.totals.counts.total),t.find(".updates-available-text").text(r(l("%s update available","%s updates available",g.totals.counts.total),g.totals.counts.total)),0===g.totals.counts.total&&t.find(".ab-label").parents("li").remove(),a.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+g.totals.counts.total)}),0<g.totals.counts.total?a.find(".update-count").text(g.totals.counts.total):a.remove(),s.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+g.totals.counts.plugins)}),0<g.totals.counts.total?s.find(".plugin-count").text(g.totals.counts.plugins):s.remove(),n.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+g.totals.counts.themes)}),0<g.totals.counts.total?n.find(".theme-count").text(g.totals.counts.themes):n.remove(),"plugins"===pagenow||"plugins-network"===pagenow?e=g.totals.counts.plugins:"themes"!==pagenow&&"themes-network"!==pagenow||(e=g.totals.counts.themes),0<e?p(".subsubsub .upgrade .count").text("("+e+")"):(p(".subsubsub .upgrade").remove(),p(".subsubsub li:last").html(function(){return p(this).children()}))},c.updates.decrementCount=function(e){g.totals.counts.total=Math.max(--g.totals.counts.total,0),"plugin"===e?g.totals.counts.plugins=Math.max(--g.totals.counts.plugins,0):"theme"===e&&(g.totals.counts.themes=Math.max(--g.totals.counts.themes,0)),c.updates.refreshCount(e)},c.updates.updatePlugin=function(e){var t,a,s,n=p("#wp-admin-bar-updates");return e=_.extend({success:c.updates.updatePluginSuccess,error:c.updates.updatePluginError},e),"plugins"===pagenow||"plugins-network"===pagenow?(a=(s=p('tr[data-plugin="'+e.plugin+'"]')).find(".update-message").removeClass("notice-error").addClass("updating-message notice-warning").find("p"),s=r(i("Updating %s...","plugin"),s.find(".plugin-title strong").text())):"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||(a=(t=p(".plugin-card-"+e.slug)).find(".update-now").addClass("updating-message"),s=r(i("Updating %s...","plugin"),a.data("name")),t.removeClass("plugin-card-update-failed").find(".notice.notice-error").remove()),n.addClass("spin"),a.html()!==h("Updating...")&&a.data("originaltext",a.html()),a.attr("aria-label",s).text(h("Updating...")),m.trigger("wp-plugin-updating",e),c.updates.ajax("update-plugin",e)},c.updates.updatePluginSuccess=function(e){var t,a,s,n=p("#wp-admin-bar-updates");"plugins"===pagenow||"plugins-network"===pagenow?(a=(t=p('tr[data-plugin="'+e.plugin+'"]').removeClass("update").addClass("updated")).find(".update-message").removeClass("updating-message notice-warning").addClass("updated-message notice-success").find("p"),s=t.find(".plugin-version-author-uri").html().replace(e.oldVersion,e.newVersion),t.find(".plugin-version-author-uri").html(s),t.find(".auto-update-time").empty()):"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||(a=p(".plugin-card-"+e.slug).find(".update-now").removeClass("updating-message").addClass("button-disabled updated-message")),n.removeClass("spin"),a.attr("aria-label",r(i("%s updated!","plugin"),e.pluginName)).text(i("Updated!","plugin")),c.a11y.speak(h("Update completed successfully.")),c.updates.decrementCount("plugin"),m.trigger("wp-plugin-update-success",e)},c.updates.updatePluginError=function(e){var t,a,s,n=p("#wp-admin-bar-updates");c.updates.isValidResponse(e,"update")&&!c.updates.maybeHandleCredentialError(e,"update-plugin")&&(s=r(h("Update failed: %s"),e.errorMessage),"plugins"===pagenow||"plugins-network"===pagenow?((a=(e.plugin?p('tr[data-plugin="'+e.plugin+'"]'):p('tr[data-slug="'+e.slug+'"]')).find(".update-message")).removeClass("updating-message notice-warning").addClass("notice-error").find("p").html(s),e.pluginName?a.find("p").attr("aria-label",r(i("%s update failed.","plugin"),e.pluginName)):a.find("p").removeAttr("aria-label")):"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||((t=p(".plugin-card-"+e.slug).addClass("plugin-card-update-failed").append(c.updates.adminNotice({className:"update-message notice-error notice-alt is-dismissible",message:s}))).find(".update-now").text(h("Update failed.")).removeClass("updating-message"),e.pluginName?t.find(".update-now").attr("aria-label",r(i("%s update failed.","plugin"),e.pluginName)):t.find(".update-now").removeAttr("aria-label"),t.on("click",".notice.is-dismissible .notice-dismiss",function(){setTimeout(function(){t.removeClass("plugin-card-update-failed").find(".column-name a").trigger("focus"),t.find(".update-now").attr("aria-label",!1).text(h("Update Now"))},200)})),n.removeClass("spin"),c.a11y.speak(s,"assertive"),m.trigger("wp-plugin-update-error",e))},c.updates.installPlugin=function(e){var t=p(".plugin-card-"+e.slug),a=t.find(".install-now");return e=_.extend({success:c.updates.installPluginSuccess,error:c.updates.installPluginError},e),(a="import"===pagenow?p('[data-slug="'+e.slug+'"]'):a).html()!==h("Installing...")&&a.data("originaltext",a.html()),a.addClass("updating-message").attr("aria-label",r(i("Installing %s...","plugin"),a.data("name"))).text(h("Installing...")),c.a11y.speak(h("Installing... please wait.")),t.removeClass("plugin-card-install-failed").find(".notice.notice-error").remove(),m.trigger("wp-plugin-installing",e),c.updates.ajax("install-plugin",e)},c.updates.installPluginSuccess=function(e){var t=p(".plugin-card-"+e.slug).find(".install-now");t.removeClass("updating-message").addClass("updated-message installed button-disabled").attr("aria-label",r(i("%s installed!","plugin"),e.pluginName)).text(i("Installed!","plugin")),c.a11y.speak(h("Installation completed successfully.")),m.trigger("wp-plugin-install-success",e),e.activateUrl&&setTimeout(function(){t.removeClass("install-now installed button-disabled updated-message").addClass("activate-now button-primary").attr("href",e.activateUrl),"plugins-network"===pagenow?t.attr("aria-label",r(i("Network Activate %s","plugin"),e.pluginName)).text(h("Network Activate")):t.attr("aria-label",r(i("Activate %s","plugin"),e.pluginName)).text(h("Activate"))},1e3)},c.updates.installPluginError=function(e){var t,a=p(".plugin-card-"+e.slug),s=a.find(".install-now");c.updates.isValidResponse(e,"install")&&!c.updates.maybeHandleCredentialError(e,"install-plugin")&&(t=r(h("Installation failed: %s"),e.errorMessage),a.addClass("plugin-card-update-failed").append('<div class="notice notice-error notice-alt is-dismissible"><p>'+t+"</p></div>"),a.on("click",".notice.is-dismissible .notice-dismiss",function(){setTimeout(function(){a.removeClass("plugin-card-update-failed").find(".column-name a").trigger("focus")},200)}),s.removeClass("updating-message").addClass("button-disabled").attr("aria-label",r(i("%s installation failed","plugin"),s.data("name"))).text(h("Installation failed.")),c.a11y.speak(t,"assertive"),m.trigger("wp-plugin-install-error",e))},c.updates.installImporterSuccess=function(e){c.updates.addAdminNotice({id:"install-success",className:"notice-success is-dismissible",message:r(h('Importer installed successfully. <a href="%s">Run importer</a>'),e.activateUrl+"&from=import")}),p('[data-slug="'+e.slug+'"]').removeClass("install-now updating-message").addClass("activate-now").attr({href:e.activateUrl+"&from=import","aria-label":r(h("Run %s"),e.pluginName)}).text(h("Run Importer")),c.a11y.speak(h("Installation completed successfully.")),m.trigger("wp-importer-install-success",e)},c.updates.installImporterError=function(e){var t=r(h("Installation failed: %s"),e.errorMessage),a=p('[data-slug="'+e.slug+'"]'),s=a.data("name");c.updates.isValidResponse(e,"install")&&!c.updates.maybeHandleCredentialError(e,"install-plugin")&&(c.updates.addAdminNotice({id:e.errorCode,className:"notice-error is-dismissible",message:t}),a.removeClass("updating-message").attr("aria-label",r(i("Install %s now","plugin"),s)).text(h("Install Now")),c.a11y.speak(t,"assertive"),m.trigger("wp-importer-install-error",e))},c.updates.deletePlugin=function(e){var t=p('[data-plugin="'+e.plugin+'"]').find(".row-actions a.delete");return e=_.extend({success:c.updates.deletePluginSuccess,error:c.updates.deletePluginError},e),t.html()!==h("Deleting...")&&t.data("originaltext",t.html()).text(h("Deleting...")),c.a11y.speak(h("Deleting...")),m.trigger("wp-plugin-deleting",e),c.updates.ajax("delete-plugin",e)},c.updates.deletePluginSuccess=function(u){p('[data-plugin="'+u.plugin+'"]').css({backgroundColor:"#faafaa"}).fadeOut(350,function(){var e=p("#bulk-action-form"),t=p(".subsubsub"),a=p(this),s=t.find('[aria-current="page"]'),n=p(".displaying-num"),l=e.find("thead th:not(.hidden), thead td").length,i=c.template("item-deleted-row"),d=g.plugins;a.hasClass("plugin-update-tr")||a.after(i({slug:u.slug,plugin:u.plugin,colspan:l,name:u.pluginName})),a.remove(),-1!==_.indexOf(d.upgrade,u.plugin)&&(d.upgrade=_.without(d.upgrade,u.plugin),c.updates.decrementCount("plugin")),-1!==_.indexOf(d.inactive,u.plugin)&&(d.inactive=_.without(d.inactive,u.plugin),d.inactive.length?t.find(".inactive .count").text("("+d.inactive.length+")"):t.find(".inactive").remove()),-1!==_.indexOf(d.active,u.plugin)&&(d.active=_.without(d.active,u.plugin),d.active.length?t.find(".active .count").text("("+d.active.length+")"):t.find(".active").remove()),-1!==_.indexOf(d.recently_activated,u.plugin)&&(d.recently_activated=_.without(d.recently_activated,u.plugin),d.recently_activated.length?t.find(".recently_activated .count").text("("+d.recently_activated.length+")"):t.find(".recently_activated").remove()),-1!==_.indexOf(d["auto-update-enabled"],u.plugin)&&(d["auto-update-enabled"]=_.without(d["auto-update-enabled"],u.plugin),d["auto-update-enabled"].length?t.find(".auto-update-enabled .count").text("("+d["auto-update-enabled"].length+")"):t.find(".auto-update-enabled").remove()),-1!==_.indexOf(d["auto-update-disabled"],u.plugin)&&(d["auto-update-disabled"]=_.without(d["auto-update-disabled"],u.plugin),d["auto-update-disabled"].length?t.find(".auto-update-disabled .count").text("("+d["auto-update-disabled"].length+")"):t.find(".auto-update-disabled").remove()),d.all=_.without(d.all,u.plugin),d.all.length?t.find(".all .count").text("("+d.all.length+")"):(e.find(".tablenav").css({visibility:"hidden"}),t.find(".all").remove(),e.find("tr.no-items").length||e.find("#the-list").append('<tr class="no-items"><td class="colspanchange" colspan="'+l+'">'+h("No plugins are currently available.")+"</td></tr>")),n.length&&s.length&&(i=d[s.parent("li").attr("class")].length,n.text(r(o("%s item","%s items","plugin/plugins",i),i)))}),c.a11y.speak(i("Deleted!","plugin")),m.trigger("wp-plugin-delete-success",u)},c.updates.deletePluginError=function(e){var t,a=c.template("item-update-row"),s=c.updates.adminNotice({className:"update-message notice-error notice-alt",message:e.errorMessage}),n=e.plugin?(t=p('tr.inactive[data-plugin="'+e.plugin+'"]')).siblings('[data-plugin="'+e.plugin+'"]'):(t=p('tr.inactive[data-slug="'+e.slug+'"]')).siblings('[data-slug="'+e.slug+'"]');c.updates.isValidResponse(e,"delete")&&!c.updates.maybeHandleCredentialError(e,"delete-plugin")&&(n.length?(n.find(".notice-error").remove(),n.find(".plugin-update").append(s)):t.addClass("update").after(a({slug:e.slug,plugin:e.plugin||e.slug,colspan:p("#bulk-action-form").find("thead th:not(.hidden), thead td").length,content:s})),m.trigger("wp-plugin-delete-error",e))},c.updates.updateTheme=function(e){var t;return e=_.extend({success:c.updates.updateThemeSuccess,error:c.updates.updateThemeError},e),(t="themes-network"===pagenow?p('[data-slug="'+e.slug+'"]').find(".update-message").removeClass("notice-error").addClass("updating-message notice-warning").find("p"):"customize"===pagenow?((t=p('[data-slug="'+e.slug+'"].notice').removeClass("notice-large")).find("h3").remove(),(t=t.add(p("#customize-control-installed_theme_"+e.slug).find(".update-message"))).addClass("updating-message").find("p")):((t=p("#update-theme").closest(".notice").removeClass("notice-large")).find("h3").remove(),(t=t.add(p('[data-slug="'+e.slug+'"]').find(".update-message"))).addClass("updating-message").find("p"))).html()!==h("Updating...")&&t.data("originaltext",t.html()),c.a11y.speak(h("Updating... please wait.")),t.text(h("Updating...")),m.trigger("wp-theme-updating",e),c.updates.ajax("update-theme",e)},c.updates.updateThemeSuccess=function(e){var t,a,s=p("body.modal-open").length,n=p('[data-slug="'+e.slug+'"]'),l={className:"updated-message notice-success notice-alt",message:i("Updated!","theme")};"customize"===pagenow?((n=p(".updating-message").siblings(".theme-name")).length&&(a=n.html().replace(e.oldVersion,e.newVersion),n.html(a)),t=p(".theme-info .notice").add(c.customize.control("installed_theme_"+e.slug).container.find(".theme").find(".update-message"))):"themes-network"===pagenow?(t=n.find(".update-message"),a=n.find(".theme-version-author-uri").html().replace(e.oldVersion,e.newVersion),n.find(".theme-version-author-uri").html(a),n.find(".auto-update-time").empty()):(t=p(".theme-info .notice").add(n.find(".update-message")),s?(p(".load-customize:visible").trigger("focus"),p(".theme-info .theme-autoupdate").find(".auto-update-time").empty()):n.find(".load-customize").trigger("focus")),c.updates.addAdminNotice(_.extend({selector:t},l)),c.a11y.speak(h("Update completed successfully.")),c.updates.decrementCount("theme"),m.trigger("wp-theme-update-success",e),s&&"customize"!==pagenow&&p(".theme-info .theme-author").after(c.updates.adminNotice(l))},c.updates.updateThemeError=function(e){var t,a=p('[data-slug="'+e.slug+'"]'),s=r(h("Update failed: %s"),e.errorMessage);c.updates.isValidResponse(e,"update")&&!c.updates.maybeHandleCredentialError(e,"update-theme")&&("customize"===pagenow&&(a=c.customize.control("installed_theme_"+e.slug).container.find(".theme")),"themes-network"===pagenow?t=a.find(".update-message "):(t=p(".theme-info .notice").add(a.find(".notice")),(p("body.modal-open").length?p(".load-customize:visible"):a.find(".load-customize")).trigger("focus")),c.updates.addAdminNotice({selector:t,className:"update-message notice-error notice-alt is-dismissible",message:s}),c.a11y.speak(s),m.trigger("wp-theme-update-error",e))},c.updates.installTheme=function(e){var t=p('.theme-install[data-slug="'+e.slug+'"]');return e=_.extend({success:c.updates.installThemeSuccess,error:c.updates.installThemeError},e),t.addClass("updating-message"),t.parents(".theme").addClass("focus"),t.html()!==h("Installing...")&&t.data("originaltext",t.html()),t.attr("aria-label",r(i("Installing %s...","theme"),t.data("name"))).text(h("Installing...")),c.a11y.speak(h("Installing... please wait.")),p('.install-theme-info, [data-slug="'+e.slug+'"]').removeClass("theme-install-failed").find(".notice.notice-error").remove(),m.trigger("wp-theme-installing",e),c.updates.ajax("install-theme",e)},c.updates.installThemeSuccess=function(e){var t,a=p(".wp-full-overlay-header, [data-slug="+e.slug+"]");m.trigger("wp-theme-install-success",e),t=a.find(".button-primary").removeClass("updating-message").addClass("updated-message disabled").attr("aria-label",r(i("%s installed!","theme"),e.themeName)).text(i("Installed!","theme")),c.a11y.speak(h("Installation completed successfully.")),setTimeout(function(){e.activateUrl&&(t.attr("href",e.activateUrl).removeClass("theme-install updated-message disabled").addClass("activate"),"themes-network"===pagenow?t.attr("aria-label",r(i("Network Activate %s","theme"),e.themeName)).text(h("Network Enable")):t.attr("aria-label",r(i("Activate %s","theme"),e.themeName)).text(h("Activate"))),e.customizeUrl&&t.siblings(".preview").replaceWith(function(){return p("<a>").attr("href",e.customizeUrl).addClass("button load-customize").text(h("Live Preview"))})},1e3)},c.updates.installThemeError=function(e){var t,a=r(h("Installation failed: %s"),e.errorMessage),s=c.updates.adminNotice({className:"update-message notice-error notice-alt",message:a});c.updates.isValidResponse(e,"install")&&!c.updates.maybeHandleCredentialError(e,"install-theme")&&("customize"===pagenow?(m.find("body").hasClass("modal-open")?(t=p('.theme-install[data-slug="'+e.slug+'"]'),p(".theme-overlay .theme-info").prepend(s)):(t=p('.theme-install[data-slug="'+e.slug+'"]')).closest(".theme").addClass("theme-install-failed").append(s),c.customize.notifications.remove("theme_installing")):m.find("body").hasClass("full-overlay-active")?(t=p('.theme-install[data-slug="'+e.slug+'"]'),p(".install-theme-info").prepend(s)):t=p('[data-slug="'+e.slug+'"]').removeClass("focus").addClass("theme-install-failed").append(s).find(".theme-install"),t.removeClass("updating-message").attr("aria-label",r(i("%s installation failed","theme"),t.data("name"))).text(h("Installation failed.")),c.a11y.speak(a,"assertive"),m.trigger("wp-theme-install-error",e))},c.updates.deleteTheme=function(e){var t;return"themes"===pagenow?t=p(".theme-actions .delete-theme"):"themes-network"===pagenow&&(t=p('[data-slug="'+e.slug+'"]').find(".row-actions a.delete")),e=_.extend({success:c.updates.deleteThemeSuccess,error:c.updates.deleteThemeError},e),t&&t.html()!==h("Deleting...")&&t.data("originaltext",t.html()).text(h("Deleting...")),c.a11y.speak(h("Deleting...")),p(".theme-info .update-message").remove(),m.trigger("wp-theme-deleting",e),c.updates.ajax("delete-theme",e)},c.updates.deleteThemeSuccess=function(n){var e=p('[data-slug="'+n.slug+'"]');"themes-network"===pagenow&&e.css({backgroundColor:"#faafaa"}).fadeOut(350,function(){var e=p(".subsubsub"),t=p(this),a=g.themes,s=c.template("item-deleted-row");t.hasClass("plugin-update-tr")||t.after(s({slug:n.slug,colspan:p("#bulk-action-form").find("thead th:not(.hidden), thead td").length,name:t.find(".theme-title strong").text()})),t.remove(),-1!==_.indexOf(a.upgrade,n.slug)&&(a.upgrade=_.without(a.upgrade,n.slug),c.updates.decrementCount("theme")),-1!==_.indexOf(a.disabled,n.slug)&&(a.disabled=_.without(a.disabled,n.slug),a.disabled.length?e.find(".disabled .count").text("("+a.disabled.length+")"):e.find(".disabled").remove()),-1!==_.indexOf(a["auto-update-enabled"],n.slug)&&(a["auto-update-enabled"]=_.without(a["auto-update-enabled"],n.slug),a["auto-update-enabled"].length?e.find(".auto-update-enabled .count").text("("+a["auto-update-enabled"].length+")"):e.find(".auto-update-enabled").remove()),-1!==_.indexOf(a["auto-update-disabled"],n.slug)&&(a["auto-update-disabled"]=_.without(a["auto-update-disabled"],n.slug),a["auto-update-disabled"].length?e.find(".auto-update-disabled .count").text("("+a["auto-update-disabled"].length+")"):e.find(".auto-update-disabled").remove()),a.all=_.without(a.all,n.slug),e.find(".all .count").text("("+a.all.length+")")}),c.a11y.speak(i("Deleted!","theme")),m.trigger("wp-theme-delete-success",n)},c.updates.deleteThemeError=function(e){var t=p('tr.inactive[data-slug="'+e.slug+'"]'),a=p(".theme-actions .delete-theme"),s=c.template("item-update-row"),n=t.siblings("#"+e.slug+"-update"),l=r(h("Deletion failed: %s"),e.errorMessage),i=c.updates.adminNotice({className:"update-message notice-error notice-alt",message:l});c.updates.maybeHandleCredentialError(e,"delete-theme")||("themes-network"===pagenow?n.length?(n.find(".notice-error").remove(),n.find(".plugin-update").append(i)):t.addClass("update").after(s({slug:e.slug,colspan:p("#bulk-action-form").find("thead th:not(.hidden), thead td").length,content:i})):p(".theme-info .theme-description").before(i),a.html(a.data("originaltext")),c.a11y.speak(l,"assertive"),m.trigger("wp-theme-delete-error",e))},c.updates._addCallbacks=function(e,t){return"import"===pagenow&&"install-plugin"===t&&(e.success=c.updates.installImporterSuccess,e.error=c.updates.installImporterError),e},c.updates.queueChecker=function(){var e;if(!c.updates.ajaxLocked&&c.updates.queue.length)switch((e=c.updates.queue.shift()).action){case"install-plugin":c.updates.installPlugin(e.data);break;case"update-plugin":c.updates.updatePlugin(e.data);break;case"delete-plugin":c.updates.deletePlugin(e.data);break;case"install-theme":c.updates.installTheme(e.data);break;case"update-theme":c.updates.updateTheme(e.data);break;case"delete-theme":c.updates.deleteTheme(e.data)}},c.updates.requestFilesystemCredentials=function(e){!1===c.updates.filesystemCredentials.available&&(e&&!c.updates.$elToReturnFocusToFromCredentialsModal&&(c.updates.$elToReturnFocusToFromCredentialsModal=p(e.target)),c.updates.ajaxLocked=!0,c.updates.requestForCredentialsModalOpen())},c.updates.maybeRequestFilesystemCredentials=function(e){c.updates.shouldRequestFilesystemCredentials&&!c.updates.ajaxLocked&&c.updates.requestFilesystemCredentials(e)},c.updates.keydown=function(e){27===e.keyCode?c.updates.requestForCredentialsModalCancel():9===e.keyCode&&("upgrade"!==e.target.id||e.shiftKey?"hostname"===e.target.id&&e.shiftKey&&(p("#upgrade").trigger("focus"),e.preventDefault()):(p("#hostname").trigger("focus"),e.preventDefault()))},c.updates.requestForCredentialsModalOpen=function(){var e=p("#request-filesystem-credentials-dialog");p("body").addClass("modal-open"),e.show(),e.find("input:enabled:first").trigger("focus"),e.on("keydown",c.updates.keydown)},c.updates.requestForCredentialsModalClose=function(){p("#request-filesystem-credentials-dialog").hide(),p("body").removeClass("modal-open"),c.updates.$elToReturnFocusToFromCredentialsModal&&c.updates.$elToReturnFocusToFromCredentialsModal.trigger("focus")},c.updates.requestForCredentialsModalCancel=function(){(c.updates.ajaxLocked||c.updates.queue.length)&&(_.each(c.updates.queue,function(e){m.trigger("credential-modal-cancel",e)}),c.updates.ajaxLocked=!1,c.updates.queue=[],c.updates.requestForCredentialsModalClose())},c.updates.showErrorInCredentialsForm=function(e){var t=p("#request-filesystem-credentials-form");t.find(".notice").remove(),t.find("#request-filesystem-credentials-title").after('<div class="notice notice-alt notice-error"><p>'+e+"</p></div>")},c.updates.credentialError=function(e,t){e=c.updates._addCallbacks(e,t),c.updates.queue.unshift({action:t,data:e}),c.updates.filesystemCredentials.available=!1,c.updates.showErrorInCredentialsForm(e.errorMessage),c.updates.requestFilesystemCredentials()},c.updates.maybeHandleCredentialError=function(e,t){return!(!c.updates.shouldRequestFilesystemCredentials||!e.errorCode||"unable_to_connect_to_filesystem"!==e.errorCode)&&(c.updates.credentialError(e,t),!0)},c.updates.isValidResponse=function(e,t){var a,s=h("Something went wrong.");if(_.isObject(e)&&!_.isFunction(e.always))return!0;switch(_.isString(e)&&"-1"===e?s=h("An error has occurred. Please reload the page and try again."):_.isString(e)?s=e:void 0!==e.readyState&&0===e.readyState?s=h("Connection lost or the server is busy. Please try again later."):_.isString(e.responseText)&&""!==e.responseText?s=e.responseText:_.isString(e.statusText)&&(s=e.statusText),t){case"update":a=h("Update failed: %s");break;case"install":a=h("Installation failed: %s");break;case"delete":a=h("Deletion failed: %s")}return s=s.replace(/<[\/a-z][^<>]*>/gi,""),a=a.replace("%s",s),c.updates.addAdminNotice({id:"unknown_error",className:"notice-error is-dismissible",message:_.escape(a)}),c.updates.ajaxLocked=!1,c.updates.queue=[],p(".button.updating-message").removeClass("updating-message").removeAttr("aria-label").prop("disabled",!0).text(h("Update failed.")),p(".updating-message:not(.button):not(.thickbox)").removeClass("updating-message notice-warning").addClass("notice-error").find("p").removeAttr("aria-label").text(a),c.a11y.speak(a,"assertive"),!1},c.updates.beforeunload=function(){if(c.updates.ajaxLocked)return h("Updates may not complete if you navigate away from this page.")},p(function(){var l=p("#plugin-filter"),o=p("#bulk-action-form"),e=p("#request-filesystem-credentials-form"),t=p("#request-filesystem-credentials-dialog"),a=p(".plugins-php .wp-filter-search"),s=p(".plugin-install-php .wp-filter-search");(g=_.extend(g,window._wpUpdatesItemCounts||{})).totals&&c.updates.refreshCount(),c.updates.shouldRequestFilesystemCredentials=0<t.length,t.on("submit","form",function(e){e.preventDefault(),c.updates.filesystemCredentials.ftp.hostname=p("#hostname").val(),c.updates.filesystemCredentials.ftp.username=p("#username").val(),c.updates.filesystemCredentials.ftp.password=p("#password").val(),c.updates.filesystemCredentials.ftp.connectionType=p('input[name="connection_type"]:checked').val(),c.updates.filesystemCredentials.ssh.publicKey=p("#public_key").val(),c.updates.filesystemCredentials.ssh.privateKey=p("#private_key").val(),c.updates.filesystemCredentials.fsNonce=p("#_fs_nonce").val(),c.updates.filesystemCredentials.available=!0,c.updates.ajaxLocked=!1,c.updates.queueChecker(),c.updates.requestForCredentialsModalClose()}),t.on("click",'[data-js-action="close"], .notification-dialog-background',c.updates.requestForCredentialsModalCancel),e.on("change",'input[name="connection_type"]',function(){p("#ssh-keys").toggleClass("hidden","ssh"!==p(this).val())}).trigger("change"),m.on("credential-modal-cancel",function(e,t){var a,s=p(".updating-message");"import"===pagenow?s.removeClass("updating-message"):"plugins"===pagenow||"plugins-network"===pagenow?"update-plugin"===t.action?a=p('tr[data-plugin="'+t.data.plugin+'"]').find(".update-message"):"delete-plugin"===t.action&&(a=p('[data-plugin="'+t.data.plugin+'"]').find(".row-actions a.delete")):"themes"===pagenow||"themes-network"===pagenow?"update-theme"===t.action?a=p('[data-slug="'+t.data.slug+'"]').find(".update-message"):"delete-theme"===t.action&&"themes-network"===pagenow?a=p('[data-slug="'+t.data.slug+'"]').find(".row-actions a.delete"):"delete-theme"===t.action&&"themes"===pagenow&&(a=p(".theme-actions .delete-theme")):a=s,a&&a.hasClass("updating-message")&&(void 0===(s=a.data("originaltext"))&&(s=p("<p>").html(a.find("p").data("originaltext"))),a.removeClass("updating-message").html(s),"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||("update-plugin"===t.action?a.attr("aria-label",r(i("Update %s now","plugin"),a.data("name"))):"install-plugin"===t.action&&a.attr("aria-label",r(i("Install %s now","plugin"),a.data("name"))))),c.a11y.speak(h("Update canceled."))}),o.on("click","[data-plugin] .update-link",function(e){var t=p(e.target),a=t.parents("tr");e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(c.updates.maybeRequestFilesystemCredentials(e),c.updates.$elToReturnFocusToFromCredentialsModal=a.find(".check-column input"),c.updates.updatePlugin({plugin:a.data("plugin"),slug:a.data("slug")}))}),l.on("click",".update-now",function(e){var t=p(e.target);e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(c.updates.maybeRequestFilesystemCredentials(e),c.updates.updatePlugin({plugin:t.data("plugin"),slug:t.data("slug")}))}),l.on("click",".install-now",function(e){var t=p(e.target);e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(c.updates.shouldRequestFilesystemCredentials&&!c.updates.ajaxLocked&&(c.updates.requestFilesystemCredentials(e),m.on("credential-modal-cancel",function(){p(".install-now.updating-message").removeClass("updating-message").text(h("Install Now")),c.a11y.speak(h("Update canceled."))})),c.updates.installPlugin({slug:t.data("slug")}))}),m.on("click",".importer-item .install-now",function(e){var t=p(e.target),a=p(this).data("name");e.preventDefault(),t.hasClass("updating-message")||(c.updates.shouldRequestFilesystemCredentials&&!c.updates.ajaxLocked&&(c.updates.requestFilesystemCredentials(e),m.on("credential-modal-cancel",function(){t.removeClass("updating-message").attr("aria-label",r(i("Install %s now","plugin"),a)).text(h("Install Now")),c.a11y.speak(h("Update canceled."))})),c.updates.installPlugin({slug:t.data("slug"),pagenow:pagenow,success:c.updates.installImporterSuccess,error:c.updates.installImporterError}))}),o.on("click","[data-plugin] a.delete",function(e){var t=p(e.target).parents("tr"),a=t.hasClass("is-uninstallable")?r(h("Are you sure you want to delete %s and its data?"),t.find(".plugin-title strong").text()):r(h("Are you sure you want to delete %s?"),t.find(".plugin-title strong").text());e.preventDefault(),window.confirm(a)&&(c.updates.maybeRequestFilesystemCredentials(e),c.updates.deletePlugin({plugin:t.data("plugin"),slug:t.data("slug")}))}),m.on("click",".themes-php.network-admin .update-link",function(e){var t=p(e.target),a=t.parents("tr");e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(c.updates.maybeRequestFilesystemCredentials(e),c.updates.$elToReturnFocusToFromCredentialsModal=a.find(".check-column input"),c.updates.updateTheme({slug:a.data("slug")}))}),m.on("click",".themes-php.network-admin a.delete",function(e){var t=p(e.target).parents("tr"),a=r(h("Are you sure you want to delete %s?"),t.find(".theme-title strong").text());e.preventDefault(),window.confirm(a)&&(c.updates.maybeRequestFilesystemCredentials(e),c.updates.deleteTheme({slug:t.data("slug")}))}),o.on("click",'[type="submit"]:not([name="clear-recent-list"])',function(e){var t,s,n=p(e.target).siblings("select").val(),a=o.find('input[name="checked[]"]:checked'),l=0,i=0,d=[];switch(pagenow){case"plugins":case"plugins-network":t="plugin";break;case"themes-network":t="theme";break;default:return}if(!a.length)return e.preventDefault(),p("html, body").animate({scrollTop:0}),c.updates.addAdminNotice({id:"no-items-selected",className:"notice-error is-dismissible",message:h("Please select at least one item to perform this action on.")});switch(n){case"update-selected":s=n.replace("selected",t);break;case"delete-selected":var u=h("plugin"===t?"Are you sure you want to delete the selected plugins and their data?":"Caution: These themes may be active on other sites in the network. Are you sure you want to proceed?");if(!window.confirm(u))return void e.preventDefault();s=n.replace("selected",t);break;default:return}c.updates.maybeRequestFilesystemCredentials(e),e.preventDefault(),o.find('.manage-column [type="checkbox"]').prop("checked",!1),m.trigger("wp-"+t+"-bulk-"+n,a),a.each(function(e,t){var t=p(t),a=t.parents("tr");"update-selected"!==n||a.hasClass("update")&&!a.find("notice-error").length?c.updates.queue.push({action:s,data:{plugin:a.data("plugin"),slug:a.data("slug")}}):t.prop("checked",!1)}),m.on("wp-plugin-update-success wp-plugin-update-error wp-theme-update-success wp-theme-update-error",function(e,t){var a,s=p('[data-slug="'+t.slug+'"]');"wp-"+t.update+"-update-success"===e.type?l++:(e=t.pluginName||s.find(".column-primary strong").text(),i++,d.push(e+": "+t.errorMessage)),s.find('input[name="checked[]"]:checked').prop("checked",!1),c.updates.adminNotice=c.template("wp-bulk-updates-admin-notice"),c.updates.addAdminNotice({id:"bulk-action-notice",className:"bulk-action-notice",successes:l,errors:i,errorMessages:d,type:t.update}),a=p("#bulk-action-notice").on("click","button",function(){p(this).toggleClass("bulk-action-errors-collapsed").attr("aria-expanded",!p(this).hasClass("bulk-action-errors-collapsed")),a.find(".bulk-action-errors").toggleClass("hidden")}),0<i&&!c.updates.queue.length&&p("html, body").animate({scrollTop:0})}),m.on("wp-updates-notice-added",function(){c.updates.adminNotice=c.template("wp-updates-admin-notice")}),c.updates.queueChecker()}),s.length&&s.attr("aria-describedby","live-search-desc"),s.on("keyup input",_.debounce(function(e,t){var a=p(".plugin-install-search"),s={_ajax_nonce:c.updates.ajaxNonce,s:e.target.value,tab:"search",type:p("#typeselector").val(),pagenow:pagenow},n=location.href.split("?")[0]+"?"+p.param(_.omit(s,["_ajax_nonce","pagenow"]));"keyup"===e.type&&27===e.which&&(e.target.value=""),c.updates.searchTerm===s.s&&"typechange"!==t||(l.empty(),c.updates.searchTerm=s.s,window.history&&window.history.replaceState&&window.history.replaceState(null,"",n),a.length||(a=p('<li class="plugin-install-search" />').append(p("<a />",{class:"current",href:n,text:h("Search Results")})),p(".wp-filter .filter-links .current").removeClass("current").parents(".filter-links").prepend(a),l.prev("p").remove(),p(".plugins-popular-tags-wrapper").remove()),void 0!==c.updates.searchRequest&&c.updates.searchRequest.abort(),p("body").addClass("loading-content"),c.updates.searchRequest=c.ajax.post("search-install-plugins",s).done(function(e){p("body").removeClass("loading-content"),l.append(e.items),delete c.updates.searchRequest,0===e.count?c.a11y.speak(h("You do not appear to have any plugins available at this time.")):c.a11y.speak(r(h("Number of plugins found: %d"),e.count))}))},1e3)),a.length&&a.attr("aria-describedby","live-search-desc"),a.on("keyup input",_.debounce(function(e){var s={_ajax_nonce:c.updates.ajaxNonce,s:e.target.value,pagenow:pagenow,plugin_status:"all"};"keyup"===e.type&&27===e.which&&(e.target.value=""),c.updates.searchTerm!==s.s&&(c.updates.searchTerm=s.s,e=_.object(_.compact(_.map(location.search.slice(1).split("&"),function(e){if(e)return e.split("=")}))),s.plugin_status=e.plugin_status||"all",window.history&&window.history.replaceState&&window.history.replaceState(null,"",location.href.split("?")[0]+"?s="+s.s+"&plugin_status="+s.plugin_status),void 0!==c.updates.searchRequest&&c.updates.searchRequest.abort(),o.empty(),p("body").addClass("loading-content"),p(".subsubsub .current").removeClass("current"),c.updates.searchRequest=c.ajax.post("search-plugins",s).done(function(e){var t=p("<span />").addClass("subtitle").html(r(h("Search results for: %s"),"<strong>"+_.escape(s.s)+"</strong>")),a=p(".wrap .subtitle");s.s.length?a.length?a.replaceWith(t):p(".wp-header-end").before(t):(a.remove(),p(".subsubsub ."+s.plugin_status+" a").addClass("current")),p("body").removeClass("loading-content"),o.append(e.items),delete c.updates.searchRequest,0===e.count?c.a11y.speak(h("No plugins found. Try a different search.")):c.a11y.speak(r(h("Number of plugins found: %d"),e.count))}))},500)),m.on("submit",".search-plugins",function(e){e.preventDefault(),p("input.wp-filter-search").trigger("input")}),m.on("click",".try-again",function(e){e.preventDefault(),s.trigger("input")}),p("#typeselector").on("change",function(){var e=p('input[name="s"]');e.val().length&&e.trigger("input","typechange")}),p("#plugin_update_from_iframe").on("click",function(e){var t=window.parent===window?null:window.parent;p.support.postMessage=!!window.postMessage,!1!==p.support.postMessage&&null!==t&&-1===window.parent.location.pathname.indexOf("update-core.php")&&(e.preventDefault(),e={action:"update-plugin",data:{plugin:p(this).data("plugin"),slug:p(this).data("slug")}},t.postMessage(JSON.stringify(e),window.location.origin))}),p("#plugin_install_from_iframe").on("click",function(e){var t=window.parent===window?null:window.parent;p.support.postMessage=!!window.postMessage,!1!==p.support.postMessage&&null!==t&&-1===window.parent.location.pathname.indexOf("index.php")&&(e.preventDefault(),e={action:"install-plugin",data:{slug:p(this).data("slug")}},t.postMessage(JSON.stringify(e),window.location.origin))}),p(window).on("message",function(e){var t,e=e.originalEvent,a=document.location.protocol+"//"+document.location.host;if(e.origin===a){try{t=JSON.parse(e.data)}catch(e){return}if(t&&void 0!==t.action)switch(t.action){case"decrementUpdateCount":c.updates.decrementCount(t.upgradeType);break;case"install-plugin":case"update-plugin":window.tb_remove(),t.data=c.updates._addCallbacks(t.data,t.action),c.updates.queue.push(t),c.updates.queueChecker()}}}),p(window).on("beforeunload",c.updates.beforeunload),m.on("keydown",".column-auto-updates .toggle-auto-update, .theme-overlay .toggle-auto-update",function(e){32===e.which&&e.preventDefault()}),m.on("click keyup",".column-auto-updates .toggle-auto-update, .theme-overlay .toggle-auto-update",function(e){var l,i,d,u=p(this),o=u.attr("data-wp-action"),r=u.find(".label");if(("keyup"!==e.type||32===e.which)&&(d="themes"!==pagenow?u.closest(".column-auto-updates"):u.closest(".theme-autoupdate"),e.preventDefault(),"yes"!==u.attr("data-doing-ajax"))){switch(u.attr("data-doing-ajax","yes"),pagenow){case"plugins":case"plugins-network":i="plugin",l=u.closest("tr").attr("data-plugin");break;case"themes-network":i="theme",l=u.closest("tr").attr("data-slug");break;case"themes":i="theme",l=u.attr("data-slug")}d.find(".notice.notice-error").addClass("hidden"),"enable"===o?r.text(h("Enabling...")):r.text(h("Disabling...")),u.find(".dashicons-update").removeClass("hidden"),e={action:"toggle-auto-updates",_ajax_nonce:g.ajax_nonce,state:o,type:i,asset:l},p.post(window.ajaxurl,e).done(function(e){var t,a,s,n=u.attr("href");if(!e.success)return e=e.data&&e.data.error?e.data.error:h("The request could not be completed."),d.find(".notice.notice-error").removeClass("hidden").find("p").text(e),void c.a11y.speak(e,"assertive");if("themes"!==pagenow){switch(e=p(".auto-update-enabled span"),t=p(".auto-update-disabled span"),a=parseInt(e.text().replace(/[^\d]+/g,""),10)||0,s=parseInt(t.text().replace(/[^\d]+/g,""),10)||0,o){case"enable":++a,--s;break;case"disable":--a,++s}a=Math.max(0,a),s=Math.max(0,s),e.text("("+a+")"),t.text("("+s+")")}"enable"===o?(u[0].hasAttribute("href")&&(n=n.replace("action=enable-auto-update","action=disable-auto-update"),u.attr("href",n)),u.attr("data-wp-action","disable"),r.text(h("Disable auto-updates")),d.find(".auto-update-time").removeClass("hidden"),c.a11y.speak(h("Auto-updates enabled"))):(u[0].hasAttribute("href")&&(n=n.replace("action=disable-auto-update","action=enable-auto-update"),u.attr("href",n)),u.attr("data-wp-action","enable"),r.text(h("Enable auto-updates")),d.find(".auto-update-time").addClass("hidden"),c.a11y.speak(h("Auto-updates disabled"))),m.trigger("wp-auto-update-setting-changed",{state:o,type:i,asset:l})}).fail(function(){d.find(".notice.notice-error").removeClass("hidden").find("p").text(h("The request could not be completed.")),c.a11y.speak(h("The request could not be completed."),"assertive")}).always(function(){u.removeAttr("data-doing-ajax").find(".dashicons-update").addClass("hidden")})}})})}(jQuery,window.wp,window._wpUpdatesSettings);