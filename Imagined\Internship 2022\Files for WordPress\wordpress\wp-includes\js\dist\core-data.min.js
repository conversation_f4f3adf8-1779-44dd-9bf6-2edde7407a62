/*! This file is auto-generated */
!function(){var e={2167:function(e){"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t){var n=e._map,r=e._arrayTreeMap,i=e._objectTreeMap;if(n.has(t))return n.get(t);for(var o=Object.keys(t).sort(),s=Array.isArray(t)?r:i,a=0;a<o.length;a++){var u=o[a];if(void 0===(s=s.get(u)))return;var c=t[u];if(void 0===(s=s.get(c)))return}var l=s.get("_ekm_value");return l?(n.delete(l[0]),l[0]=t,s.set("_ekm_value",l),n.set(t,l),l):void 0}var i=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.clear(),t instanceof e){var n=[];t.forEach((function(e,t){n.push([t,e])})),t=n}if(null!=t)for(var r=0;r<t.length;r++)this.set(t[r][0],t[r][1])}var i,o,s;return i=e,o=[{key:"set",value:function(n,r){if(null===n||"object"!==t(n))return this._map.set(n,r),this;for(var i=Object.keys(n).sort(),o=[n,r],s=Array.isArray(n)?this._arrayTreeMap:this._objectTreeMap,a=0;a<i.length;a++){var u=i[a];s.has(u)||s.set(u,new e),s=s.get(u);var c=n[u];s.has(c)||s.set(c,new e),s=s.get(c)}var l=s.get("_ekm_value");return l&&this._map.delete(l[0]),s.set("_ekm_value",o),this._map.set(n,o),this}},{key:"get",value:function(e){if(null===e||"object"!==t(e))return this._map.get(e);var n=r(this,e);return n?n[1]:void 0}},{key:"has",value:function(e){return null===e||"object"!==t(e)?this._map.has(e):void 0!==r(this,e)}},{key:"delete",value:function(e){return!!this.has(e)&&(this.set(e,void 0),!0)}},{key:"forEach",value:function(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;this._map.forEach((function(i,o){null!==o&&"object"===t(o)&&(i=i[1]),e.call(r,i,o,n)}))}},{key:"clear",value:function(){this._map=new Map,this._arrayTreeMap=new Map,this._objectTreeMap=new Map}},{key:"size",get:function(){return this._map.size}}],o&&n(i.prototype,o),s&&n(i,s),e}();e.exports=i},9756:function(e){e.exports=function(e,t){var n,r,i=0;function o(){var o,s,a=n,u=arguments.length;e:for(;a;){if(a.args.length===arguments.length){for(s=0;s<u;s++)if(a.args[s]!==arguments[s]){a=a.next;continue e}return a!==n&&(a===r&&(r=a.prev),a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=n,a.prev=null,n.prev=a,n=a),a.val}a=a.next}for(o=new Array(u),s=0;s<u;s++)o[s]=arguments[s];return a={args:o,val:e.apply(null,o)},n?(n.prev=a,a.next=n):r=a,i===t.maxSize?(r=r.prev).next=null:i++,n=a,a.val}return t=t||{},o.clear=function(){n=null,r=null,i=0},o}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){"use strict";n.r(r),n.d(r,{EntityProvider:function(){return rn},__experimentalFetchLinkSuggestions:function(){return En},__experimentalFetchUrlData:function(){return mn},__experimentalUseEntityRecord:function(){return vn},__experimentalUseEntityRecords:function(){return yn},store:function(){return wn},useEntityBlockEditor:function(){return an},useEntityId:function(){return on},useEntityProp:function(){return sn}});var e={};n.r(e),n.d(e,{__experimentalBatch:function(){return X},__experimentalReceiveCurrentGlobalStylesId:function(){return M},__experimentalReceiveThemeBaseGlobalStyles:function(){return G},__experimentalReceiveThemeGlobalStyleVariations:function(){return B},__experimentalSaveSpecifiedEntityEdits:function(){return Z},__unstableCreateUndoLevel:function(){return W},addEntities:function(){return N},deleteEntityRecord:function(){return Q},editEntityRecord:function(){return Y},receiveAutosaves:function(){return ne},receiveCurrentTheme:function(){return q},receiveCurrentUser:function(){return D},receiveEmbedPreview:function(){return $},receiveEntityRecords:function(){return j},receiveThemeSupports:function(){return F},receiveUploadPermissions:function(){return ee},receiveUserPermission:function(){return te},receiveUserQuery:function(){return V},redo:function(){return H},saveEditedEntityRecord:function(){return J},saveEntityRecord:function(){return z},undo:function(){return K}});var t={};n.r(t),n.d(t,{__experimentalGetCurrentGlobalStylesId:function(){return ct},__experimentalGetCurrentThemeBaseGlobalStyles:function(){return _t},__experimentalGetCurrentThemeGlobalStylesVariations:function(){return bt},__experimentalGetDirtyEntityRecords:function(){return Ye},__experimentalGetEntitiesBeingSaved:function(){return Ke},__experimentalGetEntityRecordNoResolver:function(){return Be},__experimentalGetTemplateForLink:function(){return mt},canUser:function(){return pt},canUserEditEntityRecord:function(){return vt},getAuthors: <AUTHORS>