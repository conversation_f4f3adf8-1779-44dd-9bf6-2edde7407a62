/*! This file is auto-generated */
!function(){"use strict";var t={d:function(n,e){for(var r in e)t.o(e,r)&&!t.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:e[r]})},o:function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},n={};function e(t){try{return new URL(t),!0}catch{return!1}}t.r(n),t.d(n,{addQueryArgs:function(){return b},buildQueryString:function(){return p},cleanForSlug:function(){return P},filterURLForDisplay:function(){return R},getAuthority:function(){return c},getFilename:function(){return x},getFragment:function(){return h},getPath:function(){return f},getPathAndQueryString:function(){return d},getProtocol:function(){return u},getQueryArg:function(){return w},getQueryArgs:function(){return y},getQueryString:function(){return a},hasQueryArg:function(){return j},isEmail:function(){return o},isURL:function(){return e},isValidAuthority:function(){return s},isValidFragment:function(){return m},isValidPath:function(){return l},isValidProtocol:function(){return i},isValidQueryString:function(){return g},normalizePath:function(){return C},prependHTTP:function(){return O},removeQueryArgs:function(){return A},safeDecodeURI:function(){return S},safeDecodeURIComponent:function(){return U}});const r=/^(mailto:)?[a-z0-9._%+-]+@[a-z0-9][a-z0-9.-]*\.[a-z]{2,63}$/i;function o(t){return r.test(t)}function u(t){const n=/^([^\s:]+:)/.exec(t);if(n)return n[1]}function i(t){return!!t&&/^[a-z\-.\+]+[0-9]*:$/i.test(t)}function c(t){const n=/^[^\/\s:]+:(?:\/\/)?\/?([^\/\s#?]+)[\/#?]{0,1}\S*$/.exec(t);if(n)return n[1]}function s(t){return!!t&&/^[^\s#?]+$/.test(t)}function f(t){const n=/^[^\/\s:]+:(?:\/\/)?[^\/\s#?]+[\/]([^\s#?]+)[#?]{0,1}\S*$/.exec(t);if(n)return n[1]}function l(t){return!!t&&/^[^\s#?]+$/.test(t)}function a(t){let n;try{n=new URL(t,"http://example.com").search.substring(1)}catch(t){}if(n)return n}function p(t){let n="";const e=Object.entries(t);let r;for(;r=e.shift();){let[t,o]=r;if(Array.isArray(o)||o&&o.constructor===Object){const n=Object.entries(o).reverse();for(const[r,o]of n)e.unshift([`${t}[${r}]`,o])}else void 0!==o&&(null===o&&(o=""),n+="&"+[t,o].map(encodeURIComponent).join("="))}return n.substr(1)}function g(t){return!!t&&/^[^\s#?\/]+$/.test(t)}function d(t){const n=f(t),e=a(t);let r="/";return n&&(r+=n),e&&(r+=`?${e}`),r}function h(t){const n=/^\S+?(#[^\s\?]*)/.exec(t);if(n)return n[1]}function m(t){return!!t&&/^#[^\s#?\/]*$/.test(t)}function y(t){return(a(t)||"").replace(/\+/g,"%20").split("&").reduce(((t,n)=>{const[e,r=""]=n.split("=").filter(Boolean).map(decodeURIComponent);if(e){!function(t,n,e){const r=n.length,o=r-1;for(let u=0;u<r;u++){let r=n[u];!r&&Array.isArray(t)&&(r=t.length.toString()),r=["__proto__","constructor","prototype"].includes(r)?r.toUpperCase():r;const i=!isNaN(Number(n[u+1]));t[r]=u===o?e:t[r]||(i?[]:{}),Array.isArray(t[r])&&!i&&(t[r]={...t[r]}),t=t[r]}}(t,e.replace(/\]/g,"").split("["),r)}return t}),Object.create(null))}function b(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1?arguments[1]:void 0;if(!n||!Object.keys(n).length)return t;let e=t;const r=t.indexOf("?");return-1!==r&&(n=Object.assign(y(t),n),e=e.substr(0,r)),e+"?"+p(n)}function w(t,n){return y(t)[n]}function j(t,n){return void 0!==w(t,n)}function A(t){const n=t.indexOf("?");if(-1===n)return t;const e=y(t),r=t.substr(0,n);for(var o=arguments.length,u=new Array(o>1?o-1:0),i=1;i<o;i++)u[i-1]=arguments[i];u.forEach((t=>delete e[t]));const c=p(e);return c?r+"?"+c:r}const v=/^(?:[a-z]+:|#|\?|\.|\/)/i;function O(t){return t?(t=t.trim(),v.test(t)||o(t)?t:"http://"+t):t}function S(t){try{return decodeURI(t)}catch(n){return t}}function U(t){try{return decodeURIComponent(t)}catch(n){return t}}function R(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,e=t.replace(/^(?:https?:)\/\/(?:www\.)?/,"");e.match(/^[^\/]+\/$/)&&(e=e.replace("/",""));const r=/([\w|:])*\.(?:jpg|jpeg|gif|png|svg)/;if(!n||e.length<=n||!e.match(r))return e;e=e.split("?")[0];const o=e.split("/"),u=o[o.length-1];if(u.length<=n)return"…"+e.slice(-n);const i=u.lastIndexOf("."),[c,s]=[u.slice(0,i),u.slice(i+1)],f=c.slice(-3)+"."+s;return u.slice(0,n-f.length-1)+"…"+f}var $=window.lodash;function P(t){return t?(0,$.trim)((0,$.deburr)(t).replace(/[\s\./]+/g,"-").replace(/[^\p{L}\p{N}_-]+/gu,"").toLowerCase(),"-"):""}function x(t){let n;try{n=new URL(t,"http://example.com").pathname.split("/").pop()}catch(t){}if(n)return n}function C(t){const n=t.split("?"),e=n[1],r=n[0];return e?r+"?"+e.split("&").map((t=>t.split("="))).map((t=>t.map(decodeURIComponent))).sort(((t,n)=>t[0].localeCompare(n[0]))).map((t=>t.map(encodeURIComponent))).map((t=>t.join("="))).join("&"):r}(window.wp=window.wp||{}).url=n}();