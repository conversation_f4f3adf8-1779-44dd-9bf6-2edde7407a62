/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
:root {
  --wp-admin-theme-color: #007cba;
  --wp-admin-theme-color--rgb: 0, 124, 186;
  --wp-admin-theme-color-darker-10: #006ba1;
  --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
  --wp-admin-theme-color-darker-20: #005a87;
  --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  :root {
    --wp-admin-border-width-focus: 1.5px;
  }
}

#customize-theme-controls .customize-pane-child.accordion-section-content.customize-widgets-layout__inspector {
  background: #fff;
  box-sizing: border-box;
}
#customize-theme-controls .customize-pane-child.accordion-section-content.customize-widgets-layout__inspector * {
  box-sizing: inherit;
}
#customize-theme-controls .customize-pane-child.accordion-section-content.customize-widgets-layout__inspector .block-editor-block-inspector {
  margin: -12px;
}
#customize-theme-controls .customize-pane-child.accordion-section-content.customize-widgets-layout__inspector .block-editor-block-inspector h3 {
  margin-bottom: 0;
}

#customize-theme-controls .customize-pane-child.control-section-sidebar.is-sub-section-open {
  transform: translateX(-100%);
}

.customize-widgets-header {
  display: flex;
  justify-content: flex-end;
  margin: -15px -12px 0 -12px;
  background: #f0f0f1;
  border-bottom: 1px solid #e0e0e0;
  z-index: 8;
}
@media (min-width: 782px) {
  .customize-widgets-header {
    margin-bottom: 44px;
  }
}
.customize-widgets-header.is-fixed-toolbar-active {
  margin-bottom: 0;
}

.customize-widgets-header-toolbar {
  display: flex;
  border: none;
  width: 100%;
  align-items: center;
}
.customize-widgets-header-toolbar .customize-widgets-header-toolbar__inserter-toggle.components-button.has-icon {
  border-radius: 2px;
  color: #fff;
  padding: 0;
  min-width: 24px;
  height: 24px;
  margin: 12px 0 12px auto;
}
.customize-widgets-header-toolbar .customize-widgets-header-toolbar__inserter-toggle.components-button.has-icon::before {
  content: none;
}
.customize-widgets-header-toolbar .customize-widgets-header-toolbar__inserter-toggle.components-button.has-icon.is-pressed {
  background: #1e1e1e;
}
.customize-widgets-header-toolbar .components-button.has-icon.customize-widgets-editor-history-button.redo-button {
  margin-left: -12px;
}

#customize-sidebar-outer-content {
  width: auto;
  min-width: 100%;
}

#customize-outer-theme-controls .widgets-inserter {
  padding: 0;
}
#customize-outer-theme-controls .widgets-inserter .customize-section-description-container {
  display: none;
}

.customize-widgets-layout__inserter-panel {
  background: #fff;
}

.customize-widgets-layout__inserter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  height: 46px;
  box-sizing: border-box;
  border-bottom: 1px solid #ddd;
}
.customize-widgets-layout__inserter-panel-header .customize-widgets-layout__inserter-panel-header-title {
  margin: 0;
}

.block-editor-inserter__quick-inserter .block-editor-inserter__panel-content {
  background: #fff;
}

.customize-widgets-keyboard-shortcut-help-modal__section {
  margin: 0 0 2rem 0;
}
.customize-widgets-keyboard-shortcut-help-modal__main-shortcuts .customize-widgets-keyboard-shortcut-help-modal__shortcut-list {
  margin-top: -25px;
}
.customize-widgets-keyboard-shortcut-help-modal__section-title {
  font-size: 0.9rem;
  font-weight: 600;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut {
  display: flex;
  align-items: baseline;
  padding: 0.6rem 0;
  border-top: 1px solid #ddd;
  margin-bottom: 0;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut:last-child {
  border-bottom: 1px solid #ddd;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut:empty {
  display: none;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-term {
  font-weight: 600;
  margin: 0 0 0 1rem;
  text-align: right;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-description {
  flex: 1;
  margin: 0;
  flex-basis: auto;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination {
  display: block;
  background: none;
  margin: 0;
  padding: 0;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination + .customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination {
  margin-top: 10px;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-key {
  padding: 0.25rem 0.5rem;
  border-radius: 8%;
  margin: 0 0.2rem 0 0.2rem;
}
.customize-widgets-keyboard-shortcut-help-modal__shortcut-key:last-child {
  margin: 0 0 0 0.2rem;
}

.block-editor-block-contextual-toolbar.is-fixed {
  top: 0;
  margin-left: -12px;
  margin-right: -12px;
  width: calc(100% + 24px);
  overflow-y: auto;
  z-index: 7;
}

.customize-control-sidebar_block_editor .block-editor-block-list__block-popover {
  position: fixed;
  z-index: 7;
}

.customize-widgets-welcome-guide__image__wrapper {
  background: #00a0d2;
  text-align: center;
  margin-bottom: 8px;
}
.customize-widgets-welcome-guide__image {
  height: auto;
}
.wrap .customize-widgets-welcome-guide__heading {
  font-size: 18px;
  font-weight: 600;
}
.customize-widgets-welcome-guide__text {
  line-height: 1.7;
}
.customize-widgets-welcome-guide__button {
  justify-content: center;
  margin: 1em 0;
  width: 100%;
}
.customize-widgets-welcome-guide__separator {
  margin: 1em 0;
}
.customize-widgets-welcome-guide__more-info {
  line-height: 1.4;
}

#customize-theme-controls .customize-pane-child.customize-widgets__sidebar-section {
  min-height: 100%;
  background-color: #fff;
  padding-top: 12px !important;
}
#customize-theme-controls .customize-pane-child.customize-widgets__sidebar-section.open {
  overflow: unset;
}
#customize-theme-controls .customize-pane-child.customize-widgets__sidebar-section .customize-section-title {
  position: static !important;
  margin-top: -12px !important;
  width: unset !important;
}

.components-modal__screen-overlay {
  z-index: 999999;
}

.customize-control-sidebar_block_editor,
.customize-widgets-layout__inspector,
.customize-widgets-popover {
  box-sizing: border-box;
}
.customize-control-sidebar_block_editor *,
.customize-control-sidebar_block_editor *::before,
.customize-control-sidebar_block_editor *::after,
.customize-widgets-layout__inspector *,
.customize-widgets-layout__inspector *::before,
.customize-widgets-layout__inspector *::after,
.customize-widgets-popover *,
.customize-widgets-popover *::before,
.customize-widgets-popover *::after {
  box-sizing: inherit;
}