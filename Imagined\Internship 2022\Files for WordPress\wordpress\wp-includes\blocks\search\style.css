/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-search__button {
  background: #f7f7f7;
  border: 1px solid #ccc;
  padding: 0.375em 0.625em;
  color: #32373c;
  margin-left: 0.625em;
  word-break: normal;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.wp-block-search__button.has-icon {
  line-height: 0;
}
.wp-block-search__button svg {
  min-width: 1.5em;
  min-height: 1.5em;
  fill: currentColor;
}

.wp-block-search__inside-wrapper {
  display: flex;
  flex: auto;
  flex-wrap: nowrap;
  max-width: 100%;
}

.wp-block-search__label {
  width: 100%;
}

.wp-block-search__input {
  padding: 8px;
  flex-grow: 1;
  min-width: 3em;
  border: 1px solid #949494;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.wp-block-search.wp-block-search__button-only .wp-block-search__button {
  margin-left: 0;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 4px;
  border: 1px solid #949494;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input {
  border-radius: 0;
  border: none;
  padding: 0 0 0 0.25em;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input:focus {
  outline: none;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__button {
  padding: 0.125em 0.5em;
}

.wp-block-search.aligncenter .wp-block-search__inside-wrapper {
  margin: auto;
}