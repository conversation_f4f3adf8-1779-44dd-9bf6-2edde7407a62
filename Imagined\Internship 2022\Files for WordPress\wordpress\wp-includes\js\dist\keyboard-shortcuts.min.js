/*! This file is auto-generated */
!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ShortcutProvider:function(){return _},__unstableUseShortcutEventMatch:function(){return D},store:function(){return E},useShortcut:function(){return j}});var n={};e.r(n),e.d(n,{registerShortcut:function(){return s},unregisterShortcut:function(){return l}});var r={};e.r(r),e.d(r,{getAllShortcutKeyCombinations:function(){return C},getAllShortcutRawKeyCombinations:function(){return O},getCategoryShortcuts:function(){return T},getShortcutAliases:function(){return R},getShortcutDescription:function(){return m},getShortcutKeyCombination:function(){return b},getShortcutRepresentation:function(){return w}});var o=window.wp.data,a=window.lodash;var i,u,c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REGISTER_SHORTCUT":return{...e,[t.name]:{category:t.category,keyCombination:t.keyCombination,aliases:t.aliases,description:t.description}};case"UNREGISTER_SHORTCUT":return(0,a.omit)(e,t.name)}return e};function s(e){let{name:t,category:n,description:r,keyCombination:o,aliases:a}=e;return{type:"REGISTER_SHORTCUT",name:t,category:n,keyCombination:o,aliases:a,description:r}}function l(e){return{type:"UNREGISTER_SHORTCUT",name:e}}function f(e){return[e]}function d(){var e={clear:function(){e.head=null}};return e}function p(e,t,n){var r;if(e.length!==t.length)return!1;for(r=n;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function h(e,t){var n,r;function o(){n=u?new WeakMap:d()}function a(){var n,o,a,i,u,c=arguments.length;for(i=new Array(c),a=0;a<c;a++)i[a]=arguments[a];for(u=t.apply(null,i),(n=r(u)).isUniqueByDependants||(n.lastDependants&&!p(u,n.lastDependants,0)&&n.clear(),n.lastDependants=u),o=n.head;o;){if(p(o.args,i,1))return o!==n.head&&(o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=n.head,o.prev=null,n.head.prev=o,n.head=o),o.val;o=o.next}return o={val:e.apply(null,i)},i[0]=null,o.args=i,n.head&&(n.head.prev=o,o.next=n.head),n.head=o,o.val}return t||(t=f),r=u?function(e){var t,r,o,a,u,c=n,s=!0;for(t=0;t<e.length;t++){if(r=e[t],!(u=r)||"object"!=typeof u){s=!1;break}c.has(r)?c=c.get(r):(o=new WeakMap,c.set(r,o),c=o)}return c.has(i)||((a=d()).isUniqueByDependants=s,c.set(i,a)),c.get(i)}:function(){return n},a.getDependants=t,a.clear=o,o(),a}i={},u="undefined"!=typeof WeakMap;var y=window.wp.keycodes;const g=[],v={display:y.displayShortcut,raw:y.rawShortcut,ariaLabel:y.shortcutAriaLabel};function S(e,t){return e?e.modifier?v[t][e.modifier](e.character):e.character:null}function b(e,t){return e[t]?e[t].keyCombination:null}function w(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"display";const r=b(e,t);return S(r,n)}function m(e,t){return e[t]?e[t].description:null}function R(e,t){return e[t]&&e[t].aliases?e[t].aliases:g}const C=h(((e,t)=>(0,a.compact)([b(e,t),...R(e,t)])),((e,t)=>[e[t]])),O=h(((e,t)=>C(e,t).map((e=>S(e,"raw")))),((e,t)=>[e[t]])),T=h(((e,t)=>Object.entries(e).filter((e=>{let[,n]=e;return n.category===t})).map((e=>{let[t]=e;return t}))),(e=>[e])),E=(0,o.createReduxStore)("core/keyboard-shortcuts",{reducer:c,actions:n,selectors:r});(0,o.register)(E);var k=window.wp.element;function D(){const{getAllShortcutKeyCombinations:e}=(0,o.useSelect)(E);return function(t,n){return e(t).some((e=>{let{modifier:t,character:r}=e;return y.isKeyboardEvent[t](n,r)}))}}const x=(0,k.createContext)();function j(e,t){let{isDisabled:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=(0,k.useContext)(x),o=D(),a=(0,k.useRef)();a.current=t,(0,k.useEffect)((()=>{if(!n)return r.current.add(t),()=>{r.current.delete(t)};function t(t){o(e,t)&&a.current(t)}}),[e,n])}function U(){return U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},U.apply(this,arguments)}const{Provider:K}=x;function _(e){const t=(0,k.useRef)(new Set);return(0,k.createElement)(K,{value:t},(0,k.createElement)("div",U({},e,{onKeyDown:function(n){e.onKeyDown&&e.onKeyDown(n);for(const e of t.current)e(n)}})))}(window.wp=window.wp||{}).keyboardShortcuts=t}();