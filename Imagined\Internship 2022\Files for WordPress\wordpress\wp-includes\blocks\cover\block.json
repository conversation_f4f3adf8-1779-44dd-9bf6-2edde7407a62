{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/cover", "title": "Cover", "category": "media", "description": "Add an image or video with a text overlay — great for headers.", "textdomain": "default", "attributes": {"url": {"type": "string"}, "useFeaturedImage": {"type": "boolean", "default": false}, "id": {"type": "number"}, "alt": {"type": "string", "source": "attribute", "selector": "img", "attribute": "alt", "default": ""}, "hasParallax": {"type": "boolean", "default": false}, "isRepeated": {"type": "boolean", "default": false}, "dimRatio": {"type": "number", "default": 100}, "overlayColor": {"type": "string"}, "customOverlayColor": {"type": "string"}, "backgroundType": {"type": "string", "default": "image"}, "focalPoint": {"type": "object"}, "minHeight": {"type": "number"}, "minHeightUnit": {"type": "string"}, "gradient": {"type": "string"}, "customGradient": {"type": "string"}, "contentPosition": {"type": "string"}, "isDark": {"type": "boolean", "default": true}, "allowedBlocks": {"type": "array"}, "templateLock": {"type": ["string", "boolean"], "enum": ["all", "insert", false]}}, "usesContext": ["postId", "postType"], "supports": {"anchor": true, "align": true, "html": false, "spacing": {"padding": true, "__experimentalDefaultControls": {"padding": true}}, "color": {"__experimentalDuotone": "> .wp-block-cover__image-background, > .wp-block-cover__video-background", "text": false, "background": false}}, "editorStyle": "wp-block-cover-editor", "style": "wp-block-cover"}