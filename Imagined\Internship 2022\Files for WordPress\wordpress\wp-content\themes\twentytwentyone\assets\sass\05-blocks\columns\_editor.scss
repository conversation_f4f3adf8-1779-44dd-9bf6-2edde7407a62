.wp-block-columns {

	&:not(.alignwide):not(.alignfull) {
		clear: both;
	}

	.wp-block,
	.wp-block-column {
		// Allow <PERSON><PERSON><PERSON> to set the width of a block that lives inside the columns block.
		max-width: inherit;
	}

	@include innerblock-margin-clear(".wp-block-column");

	&.is-style-twentytwentyone-columns-overlap {

		@include media(laptop) {

			.wp-block-column:nth-child(2n) {
				margin-left: calc(-2 * var(--global--spacing-horizontal));
				margin-top: calc(2.5 * var(--global--spacing-horizontal));
				z-index: 2;

				// Provide text-based child blocks with a default background color to ensure they're readable.
				> p,
				> h1,
				> h2,
				> h3,
				> h4,
				> h5,
				> h6,
				> ul,
				> ol,
				> pre {

					&:not(.has-background) {
						background-color: var(--global--color-background);
						padding: var(--global--spacing-unit);
					}
				}

				// Lists should still have their usual left padding.
				> ul:not(.has-background),
				> ol:not(.has-background) {
					padding-left: calc(2 * var(--global--spacing-horizontal));
				}

				&.is-vertically-aligned-center {
					margin-top: 0;
				}
			}
		}
	}

	.wp-block[data-align="full"] > & {

		p:not(.has-background),
		h1:not(.has-background),
		h2:not(.has-background),
		h3:not(.has-background),
		h4:not(.has-background),
		h5:not(.has-background),
		h6:not(.has-background) {
			padding-left: var(--global--spacing-unit);
			padding-right: var(--global--spacing-unit);
		}
	}
}
