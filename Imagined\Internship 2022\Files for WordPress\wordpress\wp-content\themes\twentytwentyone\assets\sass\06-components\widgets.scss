.widget-area {
	@extend %responsive-alignwide-width;
	margin-top: calc(6 * var(--global--spacing-vertical));
	padding-bottom: calc(var(--global--spacing-vertical) / 3);
	color: var(--footer--color-text);
	font-size: var(--footer--font-size);
	font-family: var(--footer--font-family);

	@include media(laptop) {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		column-gap: calc(2 * var(--global--spacing-horizontal));
	}

	@include media(wide) {
		grid-template-columns: repeat(3, 1fr);
	}

	@include media(mobile-only) {
		margin-top: calc(3 * var(--global--spacing-vertical));
	}

	.wp-block-social-links {

		&.alignright {
			margin-top: var(--global--spacing-vertical);
			justify-content: flex-end;
		}

		&.alignleft {
			margin-top: var(--global--spacing-vertical);
		}
	}

	&:after {
		content: "";
		display: table;
		clear: both;
	}
}

.widget {

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		font-weight: var(--widget--font-weight-title);
		line-height: var(--widget--line-height-title);
	}

	h1 {
		font-size: var(--global--font-size-md);
	}

	h2 {
		font-size: var(--global--font-size-sm);
	}

	h3 {
		font-size: var(--global--font-size-xs);
	}

	h4 {
		font-size: var(--global--font-size-xs);
	}

	h5 {
		font-size: var(--global--font-size-xs);
	}

	h6 {
		font-size: var(--global--font-size-xs);
	}

	ul {
		list-style-type: none;
		padding: 0;

		li {
			line-height: var(--widget--line-height-list);
		}

		&.sub-menu,
		&.children {
			margin-left: var(--widget--spacing-menu);
		}

		.sub-menu-toggle {
			display: none;
		}
	}

	a {
		color: var(--footer--color-link);
		text-decoration: underline;
		text-decoration-style: solid;
		text-decoration-color: currentColor;

		&:link,
		&:visited,
		&:active {
			color: var(--footer--color-link);
		}

		&:hover {
			color: var(--footer--color-link-hover);
			text-decoration-style: dotted;
		}
	}
}

// Search widget styles
.search-form {
	display: flex;
	flex-wrap: wrap;
	margin: auto;
	max-width: var(--responsive--aligndefault-width);

	> label {
		width: 100%;
		margin-bottom: 0;
		font-weight: var(--form--label-weight);
	}

	.search-field {
		flex-grow: 1;
		max-width: inherit;
		margin-top: calc(var(--global--spacing-vertical) / 3);
		margin-right: calc(0.66 * var(--global--spacing-horizontal));
	}

	.search-submit {
		margin-top: calc(var(--global--spacing-vertical) / 3);
		margin-left: 10px;
	}
}

.widget_search > .search-form {

	.search-field {
		margin-right: calc(-1 * var(--button--border-width));
		-webkit-appearance: none;
		margin-bottom: calc(0.5 * var(--global--spacing-vertical));
	}

	.search-submit {
		margin-left: 0;
		margin-bottom: calc(0.5 * var(--global--spacing-vertical));
	}
}

.widget_rss a.rsswidget .rss-widget-icon {
	display: none;
}
