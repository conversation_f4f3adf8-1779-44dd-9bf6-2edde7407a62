/*! This file is auto-generated */
!function(){var e={4403:function(e,t){var r;
/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var s={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var o=n.apply(null,r);o&&e.push(o)}}else if("object"===i)if(r.toString===Object.prototype.toString)for(var a in r)s.call(r,a)&&r[a]&&e.push(a);else e.push(r.toString())}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var i=t[s]={exports:{}};return e[s](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};!function(){"use strict";r.r(s),r.d(s,{initialize:function(){return Ge}});var e={};r.r(e),r.d(e,{__experimentalGetInsertionPoint:function(){return T},isInserterOpened:function(){return N}});var t={};r.r(t),r.d(t,{setIsInserterOpened:function(){return P}});var n={};r.r(n),r.d(n,{disableComplementaryArea:function(){return $},enableComplementaryArea:function(){return U},pinItem:function(){return j},setFeatureDefaults:function(){return J},setFeatureValue:function(){return Y},toggleFeature:function(){return K},unpinItem:function(){return q}});var i={};r.r(i),r.d(i,{getActiveComplementaryArea:function(){return Q},isFeatureActive:function(){return Z},isItemPinned:function(){return X}});var o=window.wp.element,a=window.wp.blockLibrary,c=window.wp.widgets,l=window.wp.blocks,d=window.wp.data,u=window.wp.preferences,m=window.wp.components,h=window.wp.keyboardShortcuts,p=window.wp.i18n,g=window.wp.blockEditor,b=window.wp.compose;function w(e){let{text:t,children:r}=e;const s=(0,b.useCopyToClipboard)(t);return(0,o.createElement)(m.Button,{variant:"secondary",ref:s},r)}class f extends o.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){this.setState({error:e})}render(){const{error:e}=this.state;return e?(0,o.createElement)(g.Warning,{className:"customize-widgets-error-boundary",actions:[(0,o.createElement)(w,{key:"copy-error",text:e.stack},(0,p.__)("Copy Error"))]},(0,p.__)("The editor has encountered an unexpected error.")):this.props.children}}var _=window.lodash,y=window.wp.coreData,v=window.wp.mediaUtils;function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},E.apply(this,arguments)}var k=function(e){let{inspector:t,closeMenu:r,...s}=e;const n=(0,d.useSelect)((e=>e(g.store).getSelectedBlockClientId()),[]),i=(0,o.useMemo)((()=>document.getElementById(`block-${n}`)),[n]);return(0,o.createElement)(m.MenuItem,E({onClick:()=>{t.open({returnFocusWhenClose:i}),r()}},s),(0,p.__)("Show more settings"))},C=r(4403),S=r.n(C),I=window.wp.keycodes,x=window.wp.primitives;var z=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"}));var W=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"}));var B=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));var A=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"}));var M=(0,d.combineReducers)({blockInserterPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;return"SET_IS_INSERTER_OPENED"===t.type?t.value:e}});function N(e){return!!e.blockInserterPanel}function T(e){const{rootClientId:t,insertionIndex:r}=e.blockInserterPanel;return{rootClientId:t,insertionIndex:r}}function P(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}const F={reducer:M,selectors:e,actions:t},O=(0,d.createReduxStore)("core/customize-widgets",F);(0,d.register)(O);var D=function e(t){let{setIsOpened:r}=t;const s=(0,b.useInstanceId)(e,"customize-widget-layout__inserter-panel-title"),n=(0,d.useSelect)((e=>e(O).__experimentalGetInsertionPoint()),[]);return(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel","aria-labelledby":s},(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel-header"},(0,o.createElement)("h2",{id:s,className:"customize-widgets-layout__inserter-panel-header-title"},(0,p.__)("Add a block")),(0,o.createElement)(m.Button,{className:"customize-widgets-layout__inserter-panel-header-close-button",icon:A,onClick:()=>r(!1),"aria-label":(0,p.__)("Close inserter")})),(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel-content"},(0,o.createElement)(g.__experimentalLibrary,{rootClientId:n.rootClientId,__experimentalInsertionIndex:n.insertionIndex,showInserterHelpPanel:!0,onSelect:()=>r(!1)})))};var L=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z"}));var R=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function H(e){let{as:t=m.DropdownMenu,className:r,label:s=(0,p.__)("Options"),popoverProps:n,toggleProps:i,children:a}=e;return(0,o.createElement)(t,{className:S()("interface-more-menu-dropdown",r),icon:R,label:s,popoverProps:{position:"bottom left",...n,className:S()("interface-more-menu-dropdown__content",null==n?void 0:n.className)},toggleProps:{tooltipPosition:"bottom",...i}},(e=>a(e)))}var G=window.wp.deprecated,V=r.n(G);const U=(e,t)=>r=>{let{registry:s}=r;t&&s.dispatch(u.store).set(e,"complementaryArea",t)},$=e=>t=>{let{registry:r}=t;r.dispatch(u.store).set(e,"complementaryArea",null)},j=(e,t)=>r=>{let{registry:s}=r;if(!t)return;const n=s.select(u.store).get(e,"pinnedItems");!0!==(null==n?void 0:n[t])&&s.dispatch(u.store).set(e,"pinnedItems",{...n,[t]:!0})},q=(e,t)=>r=>{let{registry:s}=r;if(!t)return;const n=s.select(u.store).get(e,"pinnedItems");s.dispatch(u.store).set(e,"pinnedItems",{...n,[t]:!1})};function K(e,t){return function(r){let{registry:s}=r;V()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),s.dispatch(u.store).toggle(e,t)}}function Y(e,t,r){return function(s){let{registry:n}=s;V()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),n.dispatch(u.store).set(e,t,!!r)}}function J(e,t){return function(r){let{registry:s}=r;V()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),s.dispatch(u.store).setDefaults(e,t)}}const Q=(0,d.createRegistrySelector)((e=>(t,r)=>e(u.store).get(r,"complementaryArea"))),X=(0,d.createRegistrySelector)((e=>(t,r,s)=>{var n;const i=e(u.store).get(r,"pinnedItems");return null===(n=null==i?void 0:i[s])||void 0===n||n})),Z=(0,d.createRegistrySelector)((e=>(t,r,s)=>(V()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(u.store).get(r,s)))),ee=(0,d.createReduxStore)("core/interface",{reducer:()=>{},actions:n,selectors:i});(0,d.register)(ee);const te=[{keyCombination:{modifier:"primary",character:"b"},description:(0,p.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,p.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,p.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,p.__)("Remove a link.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,p.__)("Underline the selected text.")}];function re(e){let{keyCombination:t,forceAriaLabel:r}=e;const s=t.modifier?I.displayShortcutList[t.modifier](t.character):t.character,n=t.modifier?I.shortcutAriaLabel[t.modifier](t.character):t.character;return(0,o.createElement)("kbd",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":r||n},(0,_.castArray)(s).map(((e,t)=>"+"===e?(0,o.createElement)(o.Fragment,{key:t},e):(0,o.createElement)("kbd",{key:t,className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key"},e))))}var se=function(e){let{description:t,keyCombination:r,aliases:s=[],ariaLabel:n}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-description"},t),(0,o.createElement)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-term"},(0,o.createElement)(re,{keyCombination:r,forceAriaLabel:n}),s.map(((e,t)=>(0,o.createElement)(re,{keyCombination:e,forceAriaLabel:n,key:t})))))};var ne=function(e){let{name:t}=e;const{keyCombination:r,description:s,aliases:n}=(0,d.useSelect)((e=>{const{getShortcutKeyCombination:r,getShortcutDescription:s,getShortcutAliases:n}=e(h.store);return{keyCombination:r(t),aliases:n(t),description:s(t)}}),[t]);return r?(0,o.createElement)(se,{keyCombination:r,description:s,aliases:n}):null};const ie=e=>{let{shortcuts:t}=e;return(0,o.createElement)("ul",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list"},t.map(((e,t)=>(0,o.createElement)("li",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut",key:t},(0,_.isString)(e)?(0,o.createElement)(ne,{name:e}):(0,o.createElement)(se,e)))))},oe=e=>{let{title:t,shortcuts:r,className:s}=e;return(0,o.createElement)("section",{className:S()("customize-widgets-keyboard-shortcut-help-modal__section",s)},!!t&&(0,o.createElement)("h2",{className:"customize-widgets-keyboard-shortcut-help-modal__section-title"},t),(0,o.createElement)(ie,{shortcuts:r}))},ae=e=>{let{title:t,categoryName:r,additionalShortcuts:s=[]}=e;const n=(0,d.useSelect)((e=>e(h.store).getCategoryShortcuts(r)),[r]);return(0,o.createElement)(oe,{title:t,shortcuts:n.concat(s)})};function ce(e){let{isModalActive:t,toggleModal:r}=e;const{registerShortcut:s}=(0,d.useDispatch)(h.store);return s({name:"core/customize-widgets/keyboard-shortcuts",category:"main",description:(0,p.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),(0,h.useShortcut)("core/customize-widgets/keyboard-shortcuts",r),t?(0,o.createElement)(m.Modal,{className:"customize-widgets-keyboard-shortcut-help-modal",title:(0,p.__)("Keyboard shortcuts"),closeLabel:(0,p.__)("Close"),onRequestClose:r},(0,o.createElement)(oe,{className:"customize-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/customize-widgets/keyboard-shortcuts"]}),(0,o.createElement)(ae,{title:(0,p.__)("Global shortcuts"),categoryName:"global"}),(0,o.createElement)(ae,{title:(0,p.__)("Selection shortcuts"),categoryName:"selection"}),(0,o.createElement)(ae,{title:(0,p.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,p.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,p.__)("Forward-slash")}]}),(0,o.createElement)(oe,{title:(0,p.__)("Text formatting"),shortcuts:te})):null}function le(){const[e,t]=(0,o.useState)(!1),r=()=>t(!e);return(0,h.useShortcut)("core/customize-widgets/keyboard-shortcuts",r),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(H,{as:m.ToolbarDropdownMenu},(()=>(0,o.createElement)(o.Fragment,null,(0,o.createElement)(m.MenuGroup,{label:(0,p._x)("View","noun")},(0,o.createElement)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"fixedToolbar",label:(0,p.__)("Top toolbar"),info:(0,p.__)("Access all block and document tools in a single place"),messageActivated:(0,p.__)("Top toolbar activated"),messageDeactivated:(0,p.__)("Top toolbar deactivated")})),(0,o.createElement)(m.MenuGroup,{label:(0,p.__)("Tools")},(0,o.createElement)(m.MenuItem,{onClick:()=>{t(!0)},shortcut:I.displayShortcut.access("h")},(0,p.__)("Keyboard shortcuts")),(0,o.createElement)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"welcomeGuide",label:(0,p.__)("Welcome Guide")}),(0,o.createElement)(m.MenuItem,{role:"menuitem",icon:L,href:(0,p.__)("https://wordpress.org/support/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,p.__)("Help"),(0,o.createElement)(m.VisuallyHidden,{as:"span"},(0,p.__)("(opens in a new tab)")))),(0,o.createElement)(m.MenuGroup,{label:(0,p.__)("Preferences")},(0,o.createElement)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"keepCaretInsideBlock",label:(0,p.__)("Contain text cursor inside block"),info:(0,p.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,p.__)("Contain text cursor inside block activated"),messageDeactivated:(0,p.__)("Contain text cursor inside block deactivated")}))))),(0,o.createElement)(ce,{isModalActive:e,toggleModal:r}))}var de=function(e){let{sidebar:t,inserter:r,isInserterOpened:s,setIsInserterOpened:n,isFixedToolbarActive:i}=e;const[[a,c],l]=(0,o.useState)([t.hasUndo(),t.hasRedo()]);return(0,o.useEffect)((()=>t.subscribeHistory((()=>{l([t.hasUndo(),t.hasRedo()])}))),[t]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:S()("customize-widgets-header",{"is-fixed-toolbar-active":i})},(0,o.createElement)(g.NavigableToolbar,{className:"customize-widgets-header-toolbar","aria-label":(0,p.__)("Document tools")},(0,o.createElement)(m.ToolbarButton,{icon:(0,p.isRTL)()?W:z,label:(0,p.__)("Undo"),shortcut:I.displayShortcut.primary("z"),"aria-disabled":!a,onClick:t.undo,className:"customize-widgets-editor-history-button undo-button"}),(0,o.createElement)(m.ToolbarButton,{icon:(0,p.isRTL)()?z:W,label:(0,p.__)("Redo"),shortcut:I.displayShortcut.primaryShift("z"),"aria-disabled":!c,onClick:t.redo,className:"customize-widgets-editor-history-button redo-button"}),(0,o.createElement)(m.ToolbarButton,{className:"customize-widgets-header-toolbar__inserter-toggle",isPressed:s,variant:"primary",icon:B,label:(0,p._x)("Add block","Generic label for block inserter button"),onClick:()=>{n((e=>!e))}}),(0,o.createElement)(le,null))),(0,o.createPortal)((0,o.createElement)(D,{setIsOpened:n}),r.contentContainer[0]))};var ue=window.wp.isShallowEqual,me=r.n(ue);function he(e){const t=e.match(/^widget_(.+)(?:\[(\d+)\])$/);if(t){return`${t[1]}-${parseInt(t[2],10)}`}return e}function pe(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const s="core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance);if(s)if(e.attributes.id)t={id:e.attributes.id};else{const{encoded:s,hash:n,raw:i,...o}=e.attributes.instance;t={idBase:e.attributes.idBase,instance:{...null==r?void 0:r.instance,is_widget_customizer_js_value:!0,encoded_serialized_instance:s,instance_hash_key:n,raw_instance:i,...o}}}else{t={idBase:"block",widgetClass:"WP_Widget_Block",instance:{raw_instance:{content:(0,l.serialize)(e)}}}}return{...(0,_.omit)(r,["form","rendered"]),...t}}function ge(e){let t,{id:r,idBase:s,number:n,instance:i}=e;const{encoded_serialized_instance:o,instance_hash_key:a,raw_instance:d,...u}=i;if("block"===s){const e=(0,l.parse)(d.content);t=e.length?e[0]:(0,l.createBlock)("core/paragraph",{})}else t=n?(0,l.createBlock)("core/legacy-widget",{idBase:s,instance:{encoded:o,hash:a,raw:d,...u}}):(0,l.createBlock)("core/legacy-widget",{id:r});return(0,c.addWidgetIdToBlock)(t,r)}function be(e){const[t,r]=(0,o.useState)((()=>e.getWidgets().map((e=>ge(e)))));(0,o.useEffect)((()=>e.subscribe(((e,t)=>{r((r=>{const s=new Map(e.map((e=>[e.id,e]))),n=new Map(r.map((e=>[(0,c.getWidgetIdFromBlock)(e),e]))),i=t.map((e=>{const t=s.get(e.id);return t&&t===e?n.get(e.id):ge(e)}));return me()(r,i)?r:i}))}))),[e]);const s=(0,o.useCallback)((t=>{r((r=>{if(me()(r,t))return r;const s=new Map(r.map((e=>[(0,c.getWidgetIdFromBlock)(e),e]))),n=t.map((t=>{const r=(0,c.getWidgetIdFromBlock)(t);if(r&&s.has(r)){const n=s.get(r),i=e.getWidget(r);return(0,_.isEqual)(t,n)&&i?i:pe(t,i)}return pe(t)}));if(me()(e.getWidgets(),n))return r;const i=e.setWidgets(n);return t.reduce(((e,r,s)=>{const n=i[s];return null!==n&&(e===t&&(e=t.slice()),e[s]=(0,c.addWidgetIdToBlock)(r,n)),e}),t)}))}),[e]);return[t,s,s]}const we=(0,o.createContext)();function fe(e){let{api:t,sidebarControls:r,children:s}=e;const[n,i]=(0,o.useState)({current:null}),a=(0,o.useCallback)((e=>{for(const t of r){if(t.setting.get().includes(e)){t.sectionInstance.expand({completeCallback(){i({current:e})}});break}}}),[r]);(0,o.useEffect)((()=>{function e(e){const t=he(e);a(t)}function r(){t.previewer.preview.bind("focus-control-for-setting",e)}return t.previewer.bind("ready",r),()=>{t.previewer.unbind("ready",r),t.previewer.preview.unbind("focus-control-for-setting",e)}}),[t,a]);const c=(0,o.useMemo)((()=>[n,a]),[n,a]);return(0,o.createElement)(we.Provider,{value:c},s)}const _e=()=>(0,o.useContext)(we);function ye(e){let{sidebar:t,settings:r,children:s}=e;const[n,i,a]=be(t);return function(e){const{selectBlock:t}=(0,d.useDispatch)(g.store),[r]=_e(),s=(0,o.useRef)(e);(0,o.useEffect)((()=>{s.current=e}),[e]),(0,o.useEffect)((()=>{if(r.current){const e=s.current.find((e=>(0,c.getWidgetIdFromBlock)(e)===r.current));if(e){t(e.clientId);const r=document.querySelector(`[data-block="${e.clientId}"]`);null==r||r.focus()}}}),[r,t])}(n),(0,o.createElement)(g.BlockEditorProvider,{value:n,onInput:i,onChange:a,settings:r,useSubRegistry:!1},s)}function ve(e){let{sidebar:t}=e;const{toggle:r}=(0,d.useDispatch)(u.store),s=t.getWidgets().every((e=>e.id.startsWith("block-")));return(0,o.createElement)("div",{className:"customize-widgets-welcome-guide"},(0,o.createElement)("div",{className:"customize-widgets-welcome-guide__image__wrapper"},(0,o.createElement)("picture",null,(0,o.createElement)("source",{srcSet:"https://s.w.org/images/block-editor/welcome-editor.svg",media:"(prefers-reduced-motion: reduce)"}),(0,o.createElement)("img",{className:"customize-widgets-welcome-guide__image",src:"https://s.w.org/images/block-editor/welcome-editor.gif",width:"312",height:"240",alt:""}))),(0,o.createElement)("h1",{className:"customize-widgets-welcome-guide__heading"},(0,p.__)("Welcome to block Widgets")),(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__text"},s?(0,p.__)("Your theme provides different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site."):(0,p.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")),(0,o.createElement)(m.Button,{className:"customize-widgets-welcome-guide__button",variant:"primary",onClick:()=>r("core/customize-widgets","welcomeGuide")},(0,p.__)("Got it")),(0,o.createElement)("hr",{className:"customize-widgets-welcome-guide__separator"}),!s&&(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__more-info"},(0,p.__)("Want to stick with the old widgets?"),(0,o.createElement)("br",null),(0,o.createElement)(m.ExternalLink,{href:(0,p.__)("https://wordpress.org/plugins/classic-widgets/")},(0,p.__)("Get the Classic Widgets plugin."))),(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__more-info"},(0,p.__)("New to the block editor?"),(0,o.createElement)("br",null),(0,o.createElement)(m.ExternalLink,{href:(0,p.__)("https://wordpress.org/support/article/wordpress-editor/")},(0,p.__)("Here's a detailed guide."))))}function Ee(e){let{undo:t,redo:r,save:s}=e;return(0,h.useShortcut)("core/customize-widgets/undo",(e=>{t(),e.preventDefault()})),(0,h.useShortcut)("core/customize-widgets/redo",(e=>{r(),e.preventDefault()})),(0,h.useShortcut)("core/customize-widgets/save",(e=>{e.preventDefault(),s()})),null}Ee.Register=function(){const{registerShortcut:e,unregisterShortcut:t}=(0,d.useDispatch)(h.store);return(0,o.useEffect)((()=>(e({name:"core/customize-widgets/undo",category:"global",description:(0,p.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/customize-widgets/redo",category:"global",description:(0,p.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}}),e({name:"core/customize-widgets/save",category:"global",description:(0,p.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),()=>{t("core/customize-widgets/undo"),t("core/customize-widgets/redo"),t("core/customize-widgets/save")})),[e]),null};var ke=Ee;function Ce(e){const t=(0,o.useRef)(),r=(0,d.useSelect)((e=>0===e(g.store).getBlockCount()));return(0,o.useEffect)((()=>{if(r&&t.current){const{ownerDocument:e}=t.current;e.activeElement&&e.activeElement!==e.body||t.current.focus()}}),[r]),(0,o.createElement)(g.ButtonBlockAppender,E({},e,{ref:t}))}function Se(e){let{blockEditorSettings:t,sidebar:r,inserter:s,inspector:n}=e;const[i,a]=function(e){const t=(0,d.useSelect)((e=>e(O).isInserterOpened()),[]),{setIsInserterOpened:r}=(0,d.useDispatch)(O);return(0,o.useEffect)((()=>{t?e.open():e.close()}),[e,t]),[t,(0,o.useCallback)((e=>{let t=e;"function"==typeof e&&(t=e((0,d.select)(O).isInserterOpened())),r(t)}),[r])]}(s),{hasUploadPermissions:c,isFixedToolbarActive:l,keepCaretInsideBlock:m,isWelcomeGuideActive:h}=(0,d.useSelect)((e=>{const{get:t}=e(u.store);return{hasUploadPermissions:(0,_.defaultTo)(e(y.store).canUser("create","media"),!0),isFixedToolbarActive:!!t("core/customize-widgets","fixedToolbar"),keepCaretInsideBlock:!!t("core/customize-widgets","keepCaretInsideBlock"),isWelcomeGuideActive:!!t("core/customize-widgets","welcomeGuide")}}),[]),p=(0,o.useMemo)((()=>{let e;return c&&(e=e=>{let{onError:r,...s}=e;(0,v.uploadMedia)({wpAllowedMimeTypes:t.allowedMimeTypes,onError:e=>{let{message:t}=e;return r(t)},...s})}),{...t,__experimentalSetIsInserterOpened:a,mediaUpload:e,hasFixedToolbar:l,keepCaretInsideBlock:m,__unstableHasCustomAppender:!0}}),[c,t,l,m,a]);return h?(0,o.createElement)(ve,{sidebar:r}):(0,o.createElement)(o.Fragment,null,(0,o.createElement)(g.BlockEditorKeyboardShortcuts.Register,null),(0,o.createElement)(ke.Register,null),(0,o.createElement)(ye,{sidebar:r,settings:p},(0,o.createElement)(ke,{undo:r.undo,redo:r.redo,save:r.save}),(0,o.createElement)(de,{sidebar:r,inserter:s,isInserterOpened:i,setIsInserterOpened:a,isFixedToolbarActive:l}),(0,o.createElement)(g.CopyHandler,null,(0,o.createElement)(g.BlockTools,null,(0,o.createElement)(g.__unstableEditorStyles,{styles:p.defaultEditorStyles}),(0,o.createElement)(g.BlockSelectionClearer,null,(0,o.createElement)(g.WritingFlow,{className:"editor-styles-wrapper"},(0,o.createElement)(g.ObserveTyping,null,(0,o.createElement)(g.BlockList,{renderAppender:Ce})))))),(0,o.createPortal)((0,o.createElement)("form",{onSubmit:e=>e.preventDefault()},(0,o.createElement)(g.BlockInspector,null)),n.contentContainer[0])),(0,o.createElement)(g.__unstableBlockSettingsMenuFirstItem,null,(e=>{let{onClose:t}=e;return(0,o.createElement)(k,{inspector:n,closeMenu:t})})))}const Ie=(0,o.createContext)();function xe(e){let{sidebarControls:t,activeSidebarControl:r,children:s}=e;const n=(0,o.useMemo)((()=>({sidebarControls:t,activeSidebarControl:r})),[t,r]);return(0,o.createElement)(Ie.Provider,{value:n},s)}function ze(e){let{api:t,sidebarControls:r,blockEditorSettings:s}=e;const[n,i]=(0,o.useState)(null),a=document.getElementById("customize-theme-controls"),c=(0,o.useRef)();!function(e,t){const{hasSelectedBlock:r,hasMultiSelection:s}=(0,d.useSelect)(g.store),{clearSelectedBlock:n}=(0,d.useDispatch)(g.store);(0,o.useEffect)((()=>{if(t.current&&e){const i=e.inspector,o=e.container[0],a=o.ownerDocument,c=a.defaultView;function l(e){!r()&&!s()||!e||!a.contains(e)||o.contains(e)||t.current.contains(e)||e.closest('[role="dialog"]')||i.expanded()||n()}function d(e){l(e.target)}function u(){l(a.activeElement)}return a.addEventListener("mousedown",d),c.addEventListener("blur",u),()=>{a.removeEventListener("mousedown",d),c.removeEventListener("blur",u)}}}),[t,e,r,s,n])}(n,c),(0,o.useEffect)((()=>{const e=r.map((e=>e.subscribe((t=>{t&&i(e)}))));return()=>{e.forEach((e=>e()))}}),[r]);const l=n&&(0,o.createPortal)((0,o.createElement)(f,null,(0,o.createElement)(Se,{key:n.id,blockEditorSettings:s,sidebar:n.sidebarAdapter,inserter:n.inserter,inspector:n.inspector})),n.container[0]),u=a&&(0,o.createPortal)((0,o.createElement)("div",{className:"customize-widgets-popover",ref:c},(0,o.createElement)(m.Popover.Slot,null)),a);return(0,o.createElement)(h.ShortcutProvider,null,(0,o.createElement)(m.SlotFillProvider,null,(0,o.createElement)(xe,{sidebarControls:r,activeSidebarControl:n},(0,o.createElement)(fe,{api:t,sidebarControls:r},l,u))))}function We(){const{wp:{customize:e}}=window,t=window.matchMedia("(prefers-reduced-motion: reduce)");let r=t.matches;return t.addEventListener("change",(e=>{r=e.matches})),class extends e.Section{ready(){const t=function(){const{wp:{customize:e}}=window;return class extends e.Section{constructor(e,t){super(e,t),this.parentSection=t.parentSection,this.returnFocusWhenClose=null,this._isOpen=!1}get isOpen(){return this._isOpen}set isOpen(e){this._isOpen=e,this.triggerActiveCallbacks()}ready(){this.contentContainer[0].classList.add("customize-widgets-layout__inspector")}isContextuallyActive(){return this.isOpen}onChangeExpanded(e,t){super.onChangeExpanded(e,t),this.parentSection&&!t.unchanged&&(e?this.parentSection.collapse({manualTransition:!0}):this.parentSection.expand({manualTransition:!0,completeCallback:()=>{this.returnFocusWhenClose&&!this.contentContainer[0].contains(this.returnFocusWhenClose)&&this.returnFocusWhenClose.focus()}}))}open(){let{returnFocusWhenClose:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.isOpen=!0,this.returnFocusWhenClose=e,this.expand({allowMultiple:!0})}close(){this.collapse({allowMultiple:!0})}collapse(e){this.isOpen=!1,super.collapse(e)}triggerActiveCallbacks(){this.active.callbacks.fireWith(this.active,[!1,!0])}}}();this.inspector=new t(`widgets-inspector-${this.id}`,{title:(0,p.__)("Block Settings"),parentSection:this,customizeAction:[(0,p.__)("Customizing"),(0,p.__)("Widgets"),this.params.title].join(" ▸ ")}),e.section.add(this.inspector),this.contentContainer[0].classList.add("customize-widgets__sidebar-section")}hasSubSectionOpened(){return this.inspector.expanded()}onChangeExpanded(e,t){const s=this.controls(),n={...t,completeCallback(){var r;s.forEach((t=>{var r;null===(r=t.onChangeSectionExpanded)||void 0===r||r.call(t,e,n)})),null===(r=t.completeCallback)||void 0===r||r.call(t)}};if(n.manualTransition){e?(this.contentContainer.addClass(["busy","open"]),this.contentContainer.removeClass("is-sub-section-open"),this.contentContainer.closest(".wp-full-overlay").addClass("section-open")):(this.contentContainer.addClass(["busy","is-sub-section-open"]),this.contentContainer.closest(".wp-full-overlay").addClass("section-open"),this.contentContainer.removeClass("open"));const t=()=>{this.contentContainer.removeClass("busy"),n.completeCallback()};r?t():this.contentContainer.one("transitionend",t)}else super.onChangeExpanded(e,n)}}}const{wp:Be}=window;function Ae(e){const t=e.match(/^(.+)-(\d+)$/);return t?{idBase:t[1],number:parseInt(t[2],10)}:{idBase:e}}function Me(e){const{idBase:t,number:r}=Ae(e);return r?`widget_${t}[${r}]`:`widget_${t}`}class Ne{constructor(e,t){this.setting=e,this.api=t,this.locked=!1,this.widgetsCache=new WeakMap,this.subscribers=new Set,this.history=[this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex=0,this.historySubscribers=new Set,this._debounceSetHistory=function(e,t,r){let s,n=!1;function i(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];const c=(n?t:e).apply(this,o);return n=!0,clearTimeout(s),s=setTimeout((()=>{n=!1}),r),c}return i.cancel=()=>{n=!1,clearTimeout(s)},i}(this._pushHistory,this._replaceHistory,1e3),this.setting.bind(this._handleSettingChange.bind(this)),this.api.bind("change",this._handleAllSettingsChange.bind(this)),this.undo=this.undo.bind(this),this.redo=this.redo.bind(this),this.save=this.save.bind(this)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}getWidgets(){return this.history[this.historyIndex]}_emit(){for(const e of this.subscribers)e(...arguments)}_getWidgetIds(){return this.setting.get()}_pushHistory(){this.history=[...this.history.slice(0,this.historyIndex+1),this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex+=1,this.historySubscribers.forEach((e=>e()))}_replaceHistory(){this.history[this.historyIndex]=this._getWidgetIds().map((e=>this.getWidget(e)))}_handleSettingChange(){if(this.locked)return;const e=this.getWidgets();this._pushHistory(),this._emit(e,this.getWidgets())}_handleAllSettingsChange(e){if(this.locked)return;if(!e.id.startsWith("widget_"))return;const t=he(e.id);if(!this.setting.get().includes(t))return;const r=this.getWidgets();this._pushHistory(),this._emit(r,this.getWidgets())}_createWidget(e){const t=Be.customize.Widgets.availableWidgets.findWhere({id_base:e.idBase});let r=e.number;t.get("is_multi")&&!r&&(t.set("multi_number",t.get("multi_number")+1),r=t.get("multi_number"));const s=r?`widget_${e.idBase}[${r}]`:`widget_${e.idBase}`,n={transport:Be.customize.Widgets.data.selectiveRefreshableWidgets[t.get("id_base")]?"postMessage":"refresh",previewer:this.setting.previewer};this.api.create(s,s,"",n).set(e.instance);return he(s)}_removeWidget(e){const t=Me(e.id),r=this.api(t);if(r){const e=r.get();this.widgetsCache.delete(e)}this.api.remove(t)}_updateWidget(e){const t=this.getWidget(e.id);if(t===e)return e.id;if(t.idBase&&e.idBase&&t.idBase===e.idBase){const t=Me(e.id);return this.api(t).set(e.instance),e.id}return this._removeWidget(e),this._createWidget(e)}getWidget(e){if(!e)return null;const{idBase:t,number:r}=Ae(e),s=Me(e),n=this.api(s);if(!n)return null;const i=n.get();if(this.widgetsCache.has(i))return this.widgetsCache.get(i);const o={id:e,idBase:t,number:r,instance:i};return this.widgetsCache.set(i,o),o}_updateWidgets(e){this.locked=!0;const t=[],r=e.map((e=>{if(e.id&&this.getWidget(e.id))return t.push(null),this._updateWidget(e);const r=this._createWidget(e);return t.push(r),r}));return this.getWidgets().filter((e=>!r.includes(e.id))).forEach((e=>this._removeWidget(e))),this.setting.set(r),this.locked=!1,t}setWidgets(e){const t=this._updateWidgets(e);return this._debounceSetHistory(),t}hasUndo(){return this.historyIndex>0}hasRedo(){return this.historyIndex<this.history.length-1}_seek(e){const t=this.getWidgets();this.historyIndex=e;const r=this.history[this.historyIndex];this._updateWidgets(r),this._emit(t,this.getWidgets()),this.historySubscribers.forEach((e=>e())),this._debounceSetHistory.cancel()}undo(){this.hasUndo()&&this._seek(this.historyIndex-1)}redo(){this.hasRedo()&&this._seek(this.historyIndex+1)}subscribeHistory(e){return this.historySubscribers.add(e),()=>{this.historySubscribers.delete(e)}}save(){this.api.previewer.save()}}var Te=window.wp.dom;function Pe(){const{wp:{customize:e}}=window;return class extends e.Control{constructor(){super(...arguments),this.subscribers=new Set}ready(){const t=function(){const{wp:{customize:e}}=window,t=e.OuterSection;return e.OuterSection=class extends t{onChangeExpanded(t,r){return t&&e.section.each((e=>{"outer"===e.params.type&&e.id!==this.id&&e.expanded()&&e.collapse()})),super.onChangeExpanded(t,r)}},e.sectionConstructor.outer=e.OuterSection,class extends e.OuterSection{constructor(){super(...arguments),this.params.type="outer",this.activeElementBeforeExpanded=null,this.contentContainer[0].ownerDocument.defaultView.addEventListener("keydown",(e=>{!this.expanded()||e.keyCode!==I.ESCAPE&&"Escape"!==e.code||e.defaultPrevented||(e.preventDefault(),e.stopPropagation(),(0,d.dispatch)(O).setIsInserterOpened(!1))}),!0),this.contentContainer.addClass("widgets-inserter"),this.isFromInternalAction=!1,this.expanded.bind((()=>{this.isFromInternalAction||(0,d.dispatch)(O).setIsInserterOpened(this.expanded()),this.isFromInternalAction=!1}))}open(){if(!this.expanded()){const e=this.contentContainer[0];this.activeElementBeforeExpanded=e.ownerDocument.activeElement,this.isFromInternalAction=!0,this.expand({completeCallback(){const t=Te.focus.tabbable.find(e)[1];t&&t.focus()}})}}close(){if(this.expanded()){const e=this.contentContainer[0],t=e.ownerDocument.activeElement;this.isFromInternalAction=!0,this.collapse({completeCallback(){e.contains(t)&&this.activeElementBeforeExpanded&&this.activeElementBeforeExpanded.focus()}})}}}}();this.inserter=new t(`widgets-inserter-${this.id}`,{}),e.section.add(this.inserter),this.sectionInstance=e.section(this.section()),this.inspector=this.sectionInstance.inspector,this.sidebarAdapter=new Ne(this.setting,e)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}onChangeSectionExpanded(e,t){t.unchanged||(e||(0,d.dispatch)(O).setIsInserterOpened(!1),this.subscribers.forEach((r=>r(e,t))))}}}var Fe=window.wp.hooks;const Oe=(0,b.createHigherOrderComponent)((e=>t=>{let r=(0,c.getWidgetIdFromBlock)(t);const s=function(){const{sidebarControls:e}=(0,o.useContext)(Ie);return e}(),n=function(){const{activeSidebarControl:e}=(0,o.useContext)(Ie);return e}(),i=(null==s?void 0:s.length)>1,a=t.name,l=t.clientId,u=(0,d.useSelect)((e=>e(g.store).canInsertBlockType(a,"")),[a]),m=(0,d.useSelect)((e=>e(g.store).getBlock(l)),[l]),{removeBlock:h}=(0,d.useDispatch)(g.store),[,p]=_e();return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(e,t),i&&u&&(0,o.createElement)(g.BlockControls,null,(0,o.createElement)(c.MoveToWidgetArea,{widgetAreas:s.map((e=>({id:e.id,name:e.params.label,description:e.params.description}))),currentWidgetAreaId:null==n?void 0:n.id,onSelect:function(e){const t=s.find((t=>t.id===e));if(r){const e=n.setting,s=t.setting;e((0,_.without)(e(),r)),s([...s(),r])}else{const e=t.sidebarAdapter;h(l);const s=e.setWidgets([...e.getWidgets(),pe(m)]);r=s.reverse().find((e=>!!e))}p(r)}})))}),"withMoveToSidebarToolbarItem");(0,Fe.addFilter)("editor.BlockEdit","core/customize-widgets/block-edit",Oe);(0,Fe.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>v.MediaUpload));const{wp:De}=window,Le=(0,b.createHigherOrderComponent)((e=>t=>{var r,s;const{idBase:n}=t.attributes,i=null!==(r=null===(s=De.customize.Widgets.data.availableWidgets.find((e=>e.id_base===n)))||void 0===s?void 0:s.is_wide)&&void 0!==r&&r;return(0,o.createElement)(e,E({},t,{isWide:i}))}),"withWideWidgetDisplay");(0,Fe.addFilter)("editor.BlockEdit","core/customize-widgets/wide-widget-display",Le);const{wp:Re}=window,He=["core/more","core/block","core/freeform","core/template-part"];function Ge(e,t){(0,d.dispatch)(u.store).setDefaults("core/customize-widgets",{fixedToolbar:!1,welcomeGuide:!0}),(0,d.dispatch)(l.store).__experimentalReapplyBlockTypeFilters();const r=(0,a.__experimentalGetCoreBlocks)().filter((e=>!(He.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));(0,a.registerCoreBlocks)(r),(0,c.registerLegacyWidgetBlock)(),(0,c.registerLegacyWidgetVariations)(t),(0,c.registerWidgetGroupBlock)(),(0,l.setFreeformContentHandlerName)("core/html");const s=Pe();Re.customize.sectionConstructor.sidebar=We(),Re.customize.controlConstructor.sidebar_block_editor=s;const n=document.createElement("div");document.body.appendChild(n),Re.customize.bind("ready",(()=>{const e=[];Re.customize.control.each((t=>{t instanceof s&&e.push(t)})),(0,o.render)((0,o.createElement)(ze,{api:Re.customize,sidebarControls:e,blockEditorSettings:t}),n)}))}}(),(window.wp=window.wp||{}).customizeWidgets=s}();