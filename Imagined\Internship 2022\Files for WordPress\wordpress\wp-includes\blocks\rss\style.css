/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
ul.wp-block-rss {
  list-style: none;
  padding: 0;
}
ul.wp-block-rss.wp-block-rss {
  box-sizing: border-box;
}
ul.wp-block-rss.alignleft {
  /*rtl:ignore*/
  margin-right: 2em;
}
ul.wp-block-rss.alignright {
  /*rtl:ignore*/
  margin-left: 2em;
}
ul.wp-block-rss.is-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  list-style: none;
}
ul.wp-block-rss.is-grid li {
  margin: 0 1em 1em 0;
  width: 100%;
}
@media (min-width: 600px) {
  ul.wp-block-rss.columns-2 li {
    width: calc(( 100% / 2 ) - 1em);
  }
  ul.wp-block-rss.columns-3 li {
    width: calc(( 100% / 3 ) - 1em);
  }
  ul.wp-block-rss.columns-4 li {
    width: calc(( 100% / 4 ) - 1em);
  }
  ul.wp-block-rss.columns-5 li {
    width: calc(( 100% / 5 ) - 1em);
  }
  ul.wp-block-rss.columns-6 li {
    width: calc(( 100% / 6 ) - 1em);
  }
}

.wp-block-rss__item-publish-date,
.wp-block-rss__item-author {
  display: block;
  font-size: 0.8125em;
}