name: Psalm

on: [push]

jobs:
  psalm:
    name: <PERSON>salm on PHP ${{ matrix.php-versions }}
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-latest']
        php-versions: ['7.4']
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: psalm:4
          coverage: none

      - name: Install Composer dependencies
        uses: "ramsey/composer-install@v2"
        with:
          composer-options: --no-dev

      - name: Put Psalm config in place
        run: cp psalm-above-3.xml psalm.xml

      - name: Static Analysis
        run: psalm
