.table {
  border-collapse: separate;

  td {
    touch-action: manipulation;
    vertical-align: middle;
  }

  thead th {
    border-bottom: 0;
    background: $th-background;
  }
}

.table-striped {
  tbody tr:nth-of-type(odd) {
    background-color: $bg-one;
  }

  tbody tr:nth-of-type(even) {
    background-color: $bg-two;
  }
}

.table-light {
  --#{$variable-prefix}table-bg: #{$table-bg};
  --#{$variable-prefix}table-accent-bg: #{$table-accent-bg};
  --#{$variable-prefix}table-striped-color: #{$table-striped-color};
  --#{$variable-prefix}table-striped-bg: #{$table-striped-bg};
  --#{$variable-prefix}table-active-color: #{$table-active-color};
  --#{$variable-prefix}table-active-bg: #{$table-active-bg};
  --#{$variable-prefix}table-hover-color: #{$table-hover-color};
  --#{$variable-prefix}table-hover-bg: #{$table-hover-bg};
}

@media only screen and (min-width: 768px) {
  .table th.position-sticky {
    top: 49px;
  }
}
