# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [v1.2.13]

### Changed

- [e96314f](https://github.com/williamdes/mariadb-mysql-kbs/commit/e96314f47373a45c1829a91d1cd5fa574343ea4a) update: [MariaDB] && [MySQL] updates
- [d4cbcc7](https://github.com/williamdes/mariadb-mysql-kbs/commit/d4cbcc7acf033e7cf4a22aeda749f9870e2d1beb) update: [MariaDB] updates
- [6d0aed2](https://github.com/williamdes/mariadb-mysql-kbs/commit/6d0aed2a94f4247655cc1ccbd47a1950abac6eb3) update: [MariaDB] && [MySQL] updates
- [6646d76](https://github.com/williamdes/mariadb-mysql-kbs/commit/6646d764c62136474f0ffaa42ba7bb1a324f3c0f) update: [MySQL] updates
- [be98073](https://github.com/williamdes/mariadb-mysql-kbs/commit/be9807377b13d08ed6998ae185400d27686f4f38) update: [MariaDB] && [MySQL] updates
- [f6fd5f6](https://github.com/williamdes/mariadb-mysql-kbs/commit/f6fd5f6a2f06d4116266833b3f68d7a8a1d10be6) update: [MariaDB] && [MySQL] updates
- [cb8f850](https://github.com/williamdes/mariadb-mysql-kbs/commit/cb8f850bb4cf0b9619986313696d476aad53e8ee) update: [MariaDB] updates
- [05e1883](https://github.com/williamdes/mariadb-mysql-kbs/commit/05e18834cbd44f544d8a131a477f90af9df439f0) update: [MariaDB] updates
- [15deeb9](https://github.com/williamdes/mariadb-mysql-kbs/commit/15deeb9672cb3309adcdd7b2554e5abb5e6ebc56) update: [MySQL] updates
- [1a8bf0a](https://github.com/williamdes/mariadb-mysql-kbs/commit/1a8bf0a2c31013b3bcf11f0862ef0e39f5b9ba74) update: [MariaDB] updates
- [90a0b26](https://github.com/williamdes/mariadb-mysql-kbs/commit/90a0b26638e67e32e230a1a1022878dbe8e7dd35) update: [MariaDB] && [MySQL] updates
- [50ab914](https://github.com/williamdes/mariadb-mysql-kbs/commit/50ab914b6df67ce5a4d63121bd8b1772bcadf872) update: [MariaDB] && [MySQL] updates
- [4b57142](https://github.com/williamdes/mariadb-mysql-kbs/commit/4b57142e4705617280ad3da9729240348b494f90) update: [MySQL] updates
- [daa313e](https://github.com/williamdes/mariadb-mysql-kbs/commit/daa313ea4fc19400ddc6fb5350dc79daf85d1a21) update: [MySQL] updates
- [1f02417](https://github.com/williamdes/mariadb-mysql-kbs/commit/1f024175e0deb70eff34e36d7305b58c0cef68d4) update: [MySQL] updates
- [bc011b7](https://github.com/williamdes/mariadb-mysql-kbs/commit/bc011b7ea4f36b10de81c4f58f55b6ff079ff2e2) update: [MariaDB] updates
- [925f01f](https://github.com/williamdes/mariadb-mysql-kbs/commit/925f01fd8dae8f8e4a72e680bce6223f1ab1084b) update: [MariaDB] updates
- [1eb110b](https://github.com/williamdes/mariadb-mysql-kbs/commit/1eb110b8c9238a88d406f0e232d63d8aa3d18053) update: [MariaDB] && [MySQL] updates
- [886d599](https://github.com/williamdes/mariadb-mysql-kbs/commit/886d599fb4ecd2e886629c177b2f9815c1fae957) update: [MariaDB] && [MySQL] updates
- [fbc1e98](https://github.com/williamdes/mariadb-mysql-kbs/commit/fbc1e98d0c4b98506b03ee30b60ee08fdbbe9440) update: [MySQL] updates
- [4726cd1](https://github.com/williamdes/mariadb-mysql-kbs/commit/4726cd1c799e3dad815f7113f3e62a7f383a7f40) update: [MariaDB] && [MySQL] updates
- [b7bc2db](https://github.com/williamdes/mariadb-mysql-kbs/commit/b7bc2dbf779c55c13f804a1cc4ce459e46f0e3e0) update: [MariaDB] && [MySQL] updates
- [ef773a7](https://github.com/williamdes/mariadb-mysql-kbs/commit/ef773a7a118e275b98b57cf18d0ad6cbfe514def) update: [MariaDB] && [MySQL] updates
- [9837411](https://github.com/williamdes/mariadb-mysql-kbs/commit/9837411eedb53b0cb61047699ad7081480f9c458) update: [MariaDB] && [MySQL] updates
- [1da68a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/1da68a28fcbcf1f9fa9f886aab84526b0408424b) update: [MySQL] updates
- [8b290ce](https://github.com/williamdes/mariadb-mysql-kbs/commit/8b290ce7109586eca5a259f63cda60551da8ab61) update: [MySQL] updates
- [9a30263](https://github.com/williamdes/mariadb-mysql-kbs/commit/9a302637313e79c392467e28c925a39ab573b61d) update: [MySQL] updates
- [a1fbb55](https://github.com/williamdes/mariadb-mysql-kbs/commit/a1fbb55335510afabfe1b544564f24639ad6f668) update: [MySQL] updates

### Fixed

- [a402856](https://github.com/williamdes/mariadb-mysql-kbs/commit/a4028565d062339f18a504f39c61cfa04a962802) fix: exclude merge.php from Doctum
- [c69d529](https://github.com/williamdes/mariadb-mysql-kbs/commit/c69d5296b09e7a5478b23d00ab865e3b3564a0cd) fix: cleanup the accepted values for an enum
- [2104623](https://github.com/williamdes/mariadb-mysql-kbs/commit/21046232efaa4f5ec355477fe50b0dab19a44ca2) fix: MariaDb enum values without a code tag
- [66c6992](https://github.com/williamdes/mariadb-mysql-kbs/commit/66c69927e3945cdc65f7919e7a757bd50dfe75c3) fix: create PR script install in "/usr/local/bin"
- [4f10b0e](https://github.com/williamdes/mariadb-mysql-kbs/commit/4f10b0eb4f2551fa1d2643fead412bb8b6f9dd2d) fix: yarn binary path
- [de8ef42](https://github.com/williamdes/mariadb-mysql-kbs/commit/de8ef42628bcfeb04627aa850783d88d82046ba6) fix: binary path for sudo-bot
- [c1528ea](https://github.com/williamdes/mariadb-mysql-kbs/commit/c1528ea35b0838ceefc18dbbf82141848a8c0213) fix: coding standard migration mistake
- [de8945b](https://github.com/williamdes/mariadb-mysql-kbs/commit/de8945b622f3360245784f7fa7653537c72b592b) fix: data URLs
- [0f31076](https://github.com/williamdes/mariadb-mysql-kbs/commit/0f31076dae1ebe2af5c13521e6a2031bc2381f14) fix: do not try to match another scope than global or session for MariaDB

### Improvements

- [0e7508c](https://github.com/williamdes/mariadb-mysql-kbs/commit/0e7508c01dfc6bb783032345007cab8b2bdbb603) style: stop using old array syntax
- [2784c48](https://github.com/williamdes/mariadb-mysql-kbs/commit/2784c48571fb629758f821bef07e55729efcf98c) style: coding standard fixes and ignore for tests
- [ce5e57b](https://github.com/williamdes/mariadb-mysql-kbs/commit/ce5e57b79cf9fb7e7960dcef6bd0df94c58162c4) style: run prettier
- [daeec8f](https://github.com/williamdes/mariadb-mysql-kbs/commit/daeec8f36ab548854fc035fed214d2887f563a34) style: migrate to wdes/coding-standard and composer json cleanup

### Features

- [1b8f387](https://github.com/williamdes/mariadb-mysql-kbs/commit/1b8f3874c595b9ee5991041c4926ce062e80bef4) feat: pass phpstan v1 max level
- [0273dbf](https://github.com/williamdes/mariadb-mysql-kbs/commit/0273dbfdfa6c7b808954235fd40c9897ad8ad121) feat: simplify PHP 8 composer installs
- [7c6a94e](https://github.com/williamdes/mariadb-mysql-kbs/commit/7c6a94e557d593b2cd168a462c43dbbac2b1b52c) feat: add new S3 data file
- [29960a3](https://github.com/williamdes/mariadb-mysql-kbs/commit/29960a349e49bb6be5e299593f6dd6829cae5a25) feat: add new data sets

### Documentation

- [b0af14e](https://github.com/williamdes/mariadb-mysql-kbs/commit/b0af14ef9e78259da65be9fc3756013fe50dc7b4) docs: update CHANGELOG
- [41631bf](https://github.com/williamdes/mariadb-mysql-kbs/commit/41631bf402f20242aa55e2f2815488dafe9508de) docs: add FUNDING and SECURITY
- [474885d](https://github.com/williamdes/mariadb-mysql-kbs/commit/474885d4eb22a3bab8e81b72dd7cf002a5723c29) docs: update docs submodule

### Others

- [5a708dd](https://github.com/williamdes/mariadb-mysql-kbs/commit/5a708ddd0d444fde62eee557350677ffb538ea23) chore: do not try to change version in composer.json
- [b492531](https://github.com/williamdes/mariadb-mysql-kbs/commit/b492531f6ee0eb067ef6cea95b7039729d8acd8d) ci: set back coverage to xdebug for PHP 7.1
- [095d6bf](https://github.com/williamdes/mariadb-mysql-kbs/commit/095d6bfbd2bd71665959d824f2edfdfa667da6a2) ci: set PHP 8.1 as a normal tested version and nightly as an experimental version
- [848c359](https://github.com/williamdes/mariadb-mysql-kbs/commit/848c359c933f90568ff65ce4a6efb9267430c968) chore: ignore composer.lock
- [7f56820](https://github.com/williamdes/mariadb-mysql-kbs/commit/7f5682015cb6728ab7871da69d92e49a112ed213) chore: remove version field from composer.json
- [ed98920](https://github.com/williamdes/mariadb-mysql-kbs/commit/ed98920339eecdae90c986a6c7dc8e11337add96) chore: upgrade phpstan to ^1.2.0
- [4dc14cd](https://github.com/williamdes/mariadb-mysql-kbs/commit/4dc14cd791e74e929274b6a276d8b8c80015a2a6) ci: do not require SKIP_DOCS_STEPS ENV
- [624b71e](https://github.com/williamdes/mariadb-mysql-kbs/commit/624b71e435557946d45ea387abe4d6bcf32f5a09) chore: update jshint, mocha, prettier
- [af0326c](https://github.com/williamdes/mariadb-mysql-kbs/commit/af0326c04ccefffed1f1bbe26c5f086387985883) ci: add new workflow environment names
- [2e6ae05](https://github.com/williamdes/mariadb-mysql-kbs/commit/2e6ae05b8fa1ba7ba8339ecb10a5baa09a112ae0) chore: update dependencies
- [a5a3627](https://github.com/williamdes/mariadb-mysql-kbs/commit/a5a36270c7a1423d404dbeb21257ed3556af0fc5) ci: fix workflow
- [12cb0c7](https://github.com/williamdes/mariadb-mysql-kbs/commit/12cb0c705abedc4f7eca992fc8e32c41dc68b905) ci: adjust lint and analyse CI config and run tests on PHP 8.1
- [9c72287](https://github.com/williamdes/mariadb-mysql-kbs/commit/9c7228749af60ad40f2f522803a518bb21f54921) ci: test on node 12, 14, 15, 16
- [6954d32](https://github.com/williamdes/mariadb-mysql-kbs/commit/6954d326fdda0b041396997112fe3c45a56541c2) chore: require node 12
- [13bf7a5](https://github.com/williamdes/mariadb-mysql-kbs/commit/13bf7a53327c9b89a32ec95bdc27e2469f478a01) chore: upgrade jshint, mocha, prettier
- [e120770](https://github.com/williamdes/mariadb-mysql-kbs/commit/e12077002cae75b0ac2d3f5515ac6d7d21fca403) ci: remove sudo for pages build
- [8e31e3b](https://github.com/williamdes/mariadb-mysql-kbs/commit/8e31e3b5bdf748cc3454deed0c113fb37815b4df) ci: remove composer arguments
- [2552a4b](https://github.com/williamdes/mariadb-mysql-kbs/commit/2552a4b103ffcf73635c67150fff95f47696d061) ci: use different templates for each workflow
- [2944134](https://github.com/williamdes/mariadb-mysql-kbs/commit/294413447143adf4c4a525eec5327fba67d88124) ci: allow access to secrets
- [ca5bb42](https://github.com/williamdes/mariadb-mysql-kbs/commit/ca5bb42fd68d0ba38b761f75368af6e3c3364618) ci: handle non docs workflow
- [3cc68cb](https://github.com/williamdes/mariadb-mysql-kbs/commit/3cc68cb8c8f2a7365a78d710e8087d704107b5a7) ci: fix NodeJs not expanding ~ of paths
- [852e2c3](https://github.com/williamdes/mariadb-mysql-kbs/commit/852e2c3f7fc13729b7a91800a5718d8725b295a5) ci: fix build process missing files
- [80eb36d](https://github.com/williamdes/mariadb-mysql-kbs/commit/80eb36dcdf817da45c7a392d7a268cb9dcfc0eaa) ci: use non relative paths for some file arguments on sudo-bot
- [61263ca](https://github.com/williamdes/mariadb-mysql-kbs/commit/61263ca257ead278e377748bce45ed9fb8f2d25f) ci: fix secret for sudo-bot GPG passphrase
- [547e15e](https://github.com/williamdes/mariadb-mysql-kbs/commit/547e15ece69560a45556916edc7e7a58d99ce576) ci: remove duplicates lines on the sudo-bot script That simple..., wtf keyboard what did you do /o\
- [a208602](https://github.com/williamdes/mariadb-mysql-kbs/commit/a2086022e8c68c151fcc0cf29753e2aa0430cf1d) ci: add more debugs for sudo-bot script What is wrong ? :/
- [c8255a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/c8255a254c6fc782a86ce8a62050662558914628) ci: fix sudo-bot, use bash instead of sh
- [3f0f318](https://github.com/williamdes/mariadb-mysql-kbs/commit/3f0f3185a346292a16ee71e0f9b9db4279545e0b) ci: debug command line issue
- [fa11e54](https://github.com/williamdes/mariadb-mysql-kbs/commit/fa11e54d410ba435f660afed58ee80d275b4be18) ci: use bash equals for sudo-bot
- [4329113](https://github.com/williamdes/mariadb-mysql-kbs/commit/432911397912e4fd29d513058ed7be9b571c09a3) ci: fix move to root dir for API docs
- [373a22f](https://github.com/williamdes/mariadb-mysql-kbs/commit/373a22febd0253d04a5c6c656633878b49b08987) ci: rename cache to tmp
- [085abed](https://github.com/williamdes/mariadb-mysql-kbs/commit/085abeda4486d41ee274b5ee72b33fa11b14796c) ci: fetch all the repository to have the gh-pages branch available
- [43d7ecd](https://github.com/williamdes/mariadb-mysql-kbs/commit/43d7ecd45999b0720ca7bd9939c1c6727d00332a) ci: fix root dir path for sudo-bot docs
- [5cfd151](https://github.com/williamdes/mariadb-mysql-kbs/commit/5cfd151f09e78643ac42dc2c4029069eadd2ad8c) ci: make sudo-bot script executable
- [44293ad](https://github.com/williamdes/mariadb-mysql-kbs/commit/44293ad4f9bb3377c237e75f0d7591eec59963e6) ci: fix PHP documentation workflow
- [6b0f012](https://github.com/williamdes/mariadb-mysql-kbs/commit/6b0f012cfec344d8b49b7a3fb9e788d3e1f50a5f) ci: drop all the old sudo-bot process and re-build it
- [dcad6c8](https://github.com/williamdes/mariadb-mysql-kbs/commit/dcad6c83674b26f50885281bd03d51c3cade077b) ci: rename GPG_PRIV_PASSWORD ENV to GPG_PASSPHRASE
- [3ba3766](https://github.com/williamdes/mariadb-mysql-kbs/commit/3ba37668d67e29b31ac0f96c860d279464298f47) chore: upgrade chai and mocha
- [e48d0c0](https://github.com/williamdes/mariadb-mysql-kbs/commit/e48d0c01895a397198b6751fafd310420b15e575) ci: Update actions/checkout to v2

## [v1.2.12]

### Changed

- [b78197e](https://github.com/williamdes/mariadb-mysql-kbs/commit/b78197e1c16522c86fab4b23c8b75050efd27dd0) update: [MySQL] updates
- [537d185](https://github.com/williamdes/mariadb-mysql-kbs/commit/537d1853dbe02c175ad917f17bd1fbf852a7ff06) update: [MySQL] updates
- [1f7868b](https://github.com/williamdes/mariadb-mysql-kbs/commit/1f7868baba0df415ec7fb5e4358494127915bafe) update: [MariaDB] updates
- [fce5e6a](https://github.com/williamdes/mariadb-mysql-kbs/commit/fce5e6ab7c105d8e1b9493724ea942ee8aea84c3) update: [MariaDB] && [MySQL] updates
- [8aabb2b](https://github.com/williamdes/mariadb-mysql-kbs/commit/8aabb2b929f601a29645df0c8c55166f18e64a76) update: [MariaDB] && [MySQL] updates
- [deaa820](https://github.com/williamdes/mariadb-mysql-kbs/commit/deaa8204e46a2af4947489d348a58331367d5d94) update: [MariaDB] && [MySQL] updates
- [fd5e5de](https://github.com/williamdes/mariadb-mysql-kbs/commit/fd5e5de7c0019df37d905ae1e28ee4340d16cc51) update: 🤖 Some updates 🤖
- [4278ad9](https://github.com/williamdes/mariadb-mysql-kbs/commit/4278ad9b212e6679d5c19d27d49f516f01f03863) update: [MariaDB] && [MySQL] updates
- [37a7156](https://github.com/williamdes/mariadb-mysql-kbs/commit/37a7156d2027122955fc8087b1d056f54dd1ca74) update: [MariaDB] && [MySQL] updates
- [74c152c](https://github.com/williamdes/mariadb-mysql-kbs/commit/74c152cc366fa0bd9fcdf5e774aa6a4002a52fa8) update: [MariaDB] updates
- [3b86e1f](https://github.com/williamdes/mariadb-mysql-kbs/commit/3b86e1f34a60651c071289b920bd52a6ad0bac4c) update: [MySQL] updates

### Fixed

- [a8a7893](https://github.com/williamdes/mariadb-mysql-kbs/commit/a8a78939ccad4937600d06364f265b79745b1b9a) fix: KBEntry::jsonSerialize phpdoc block
- [4d33f7e](https://github.com/williamdes/mariadb-mysql-kbs/commit/4d33f7eb822920e5336e64d0e8f4930e336257c9) fix: Rename master to source for MySQL replication options

### Features

- [2402554](https://github.com/williamdes/mariadb-mysql-kbs/commit/240255492c4f19a895e0422e8e509caecce27928) feat: improve release script
- [632dc1c](https://github.com/williamdes/mariadb-mysql-kbs/commit/632dc1cc09142588f5443c8d6c419215d233cb70) feat: update documentation submodule
- [c9bcc1c](https://github.com/williamdes/mariadb-mysql-kbs/commit/c9bcc1cacde7f2932e1015499b4fc277f6621e62) feat: improve API docs config

### Others

- [bba46a3](https://github.com/williamdes/mariadb-mysql-kbs/commit/bba46a3ac03b1d999786a4f1f0bb9de3c6f1d59c) ci: use sudo-bot/action-doctum
- [3550bd1](https://github.com/williamdes/mariadb-mysql-kbs/commit/3550bd164152a29e1bce45285f208d97032a2d4d) chore: update dependencies
- [35b9c29](https://github.com/williamdes/mariadb-mysql-kbs/commit/35b9c2998f2f8c2abd2c136836c157e5a0749755) ci: update lint and analyse workflows
- [7c08136](https://github.com/williamdes/mariadb-mysql-kbs/commit/7c08136d1042b62c50a076e4ecc4f2415576de02) chore: change php requirements to "^7.1 || ^8.0"
- [b48b3be](https://github.com/williamdes/mariadb-mysql-kbs/commit/b48b3bedd294dfb873f2992692ff8cf19b3d6924) chore: remove .gitignore from vendor bundles

## [v1.2.11]

### Changed

- [749afd6](https://github.com/williamdes/mariadb-mysql-kbs/commit/749afd6b2dec8e203109628b3ee4d62d3ae5437a) update: [MariaDB] updates
- [238e5ae](https://github.com/williamdes/mariadb-mysql-kbs/commit/238e5ae2aa76868164f2a7f0de9e08dcdebde304) update: [MariaDB] && [MySQL] updates
- [eb62400](https://github.com/williamdes/mariadb-mysql-kbs/commit/eb62400bc18d0e9d2f845434cc367707fe68daa0) update: [MariaDB] && [MySQL] updates
- [bf0405b](https://github.com/williamdes/mariadb-mysql-kbs/commit/bf0405b255e1b79c358175f1391acdd94286f261) update: [MySQL] updates
- [85e3b2b](https://github.com/williamdes/mariadb-mysql-kbs/commit/85e3b2b0402948b01f4d2751d2776fedbee4ced1) update: [MariaDB] && [MySQL] updates
- [5f24772](https://github.com/williamdes/mariadb-mysql-kbs/commit/5f24772774a2e6753183aa14d6381a66fe63f819) update: [MariaDB] updates
- [17e905f](https://github.com/williamdes/mariadb-mysql-kbs/commit/17e905f2ba8cbc42193398c6718169564700627d) update: [MariaDB] updates
- [7fb17c1](https://github.com/williamdes/mariadb-mysql-kbs/commit/7fb17c1a1f7f29d9e623f5702d696b7744356cda) update: [MariaDB] updates
- [2ab4b94](https://github.com/williamdes/mariadb-mysql-kbs/commit/2ab4b9445dda64d4d76206b20d4680802197d06f) update: [MySQL] updates
- [6dca35c](https://github.com/williamdes/mariadb-mysql-kbs/commit/6dca35c3d7e138db1df9fa91cbb3acae045e33ad) update: [MariaDB] updates
- [25cc2f5](https://github.com/williamdes/mariadb-mysql-kbs/commit/25cc2f5c6ae9702d68d07fa7b2673824762b6942) update: [MariaDB] updates
- [a106adc](https://github.com/williamdes/mariadb-mysql-kbs/commit/a106adce4673a781299c05ad71a2f03d5f8f2762) update: [MariaDB] && [MySQL] updates
- [f32aa3c](https://github.com/williamdes/mariadb-mysql-kbs/commit/f32aa3c94886a030a9405d2d64fb32d7665111a9) update: [MySQL] updates
- [1a9712e](https://github.com/williamdes/mariadb-mysql-kbs/commit/1a9712ef486c366e4eab4796da6cc71207a035a0) update: [MariaDB] updates
- [e68823d](https://github.com/williamdes/mariadb-mysql-kbs/commit/e68823d481781ec91642414baba745b5009efd54) update: [MariaDB] && [MySQL] updates
- [25327b8](https://github.com/williamdes/mariadb-mysql-kbs/commit/25327b891ca02d0b5f2639532829e086b2b97755) update: [MariaDB] && [MySQL] updates
- [c70c4d5](https://github.com/williamdes/mariadb-mysql-kbs/commit/c70c4d5d87067e6ce7a3cc7a398bb618a27f31f6) update: [MariaDB] && [MySQL] updates
- [24a7590](https://github.com/williamdes/mariadb-mysql-kbs/commit/24a75905df8b1baad20ff5a6a74a2a200b75d7cb) update: [MySQL] updates
- [86688c6](https://github.com/williamdes/mariadb-mysql-kbs/commit/86688c678cba028750b6678faef3d398bdecf11d) update: [MySQL] updates
- [62e472b](https://github.com/williamdes/mariadb-mysql-kbs/commit/62e472b485c5008fe5c2db1b3569efa8f83735cd) update: [MariaDB] updates
- [8715b00](https://github.com/williamdes/mariadb-mysql-kbs/commit/8715b00fe605af5cf27dec34b2402ecb6ee21271) update: [MySQL] updates

### Fixed

- [438f58b](https://github.com/williamdes/mariadb-mysql-kbs/commit/438f58b093f9e97e6a50f3ad6b8c7ff15fc1636d) fix: use another way to validate variables in MySQL documentation
- [e23b886](https://github.com/williamdes/mariadb-mysql-kbs/commit/e23b8868e34fbe922ba0e83bcf5b55ee0c9b6966) fix: use stdout instead of stderr for phpunit
- [633e667](https://github.com/williamdes/mariadb-mysql-kbs/commit/633e667efd6d05b4497b672af6e2a110234df81e) fix: rename mysql options slave to replica
- [1da576b](https://github.com/williamdes/mariadb-mysql-kbs/commit/1da576b61e661762e41c3bf501b9e163f8a2f298) fix: remove an invalid edge case
- [6cb4b61](https://github.com/williamdes/mariadb-mysql-kbs/commit/6cb4b611d443f038e22e3ea2821dbb7b10e3328c) fix: support bad naming for enums
- [f836f84](https://github.com/williamdes/mariadb-mysql-kbs/commit/f836f84a0ee1d0c4e19cd848af50494fea33a7ae) fix: activate back h3 detection
- [894cce5](https://github.com/williamdes/mariadb-mysql-kbs/commit/894cce551c007c232b41cd696cc447db507d5c63) fix: detect nodes until next header
- [ff6fa4a](https://github.com/williamdes/mariadb-mysql-kbs/commit/ff6fa4a23e0d5735b035f2b66f4cfc20880cb866) fix: ignore non variables headings
- [c4d3a15](https://github.com/williamdes/mariadb-mysql-kbs/commit/c4d3a1506299dc0bc09b4e8d76df78cbefe9f8aa) fix: detect all the nodes until a separation line
- [8dbe3d1](https://github.com/williamdes/mariadb-mysql-kbs/commit/8dbe3d151413070ed35b0451265e503bc054aedd) fix: headers can be td or th elements on some MySQL pages
- [8d5ff2e](https://github.com/williamdes/mariadb-mysql-kbs/commit/8d5ff2eb867ea8e17b6cb2957e66846e0f3e7bb4) fix: update XSD url for phpunit
- [f127419](https://github.com/williamdes/mariadb-mysql-kbs/commit/f1274199744eb6d6ffdbe80a5aafd98a85ea7376) fix: remove incompatible expectExceptionMessage with phpunit 7
- [239fa7f](https://github.com/williamdes/mariadb-mysql-kbs/commit/239fa7f50ea27d7e1b0c90bbe613c2407ea90e61) fix: phpunit test must use expectExceptionMessageMatches intead of expectExceptionMessageRegExp
- [45f5288](https://github.com/williamdes/mariadb-mysql-kbs/commit/45f5288456765d6f91f06251e0cba550304191a8) fix: add target folder to .npmignore

### Features

- [7823f6b](https://github.com/williamdes/mariadb-mysql-kbs/commit/7823f6ba556f393a329028b4e049e2e3a737cd0f) feat: move to phar method to generate docs
- [b0b51b9](https://github.com/williamdes/mariadb-mysql-kbs/commit/b0b51b9473c450fd1ba967d477f700695a5339e9) feat: add a PR template for documentation updates
- [6e0e3c4](https://github.com/williamdes/mariadb-mysql-kbs/commit/6e0e3c4a8fff19ed2832078d974aea52e55291ea) feat: remove sami/sami and use code-lts/doctum
- [00439a0](https://github.com/williamdes/mariadb-mysql-kbs/commit/00439a01b887dd21d22e2d364e73674c0a35cac5) feat: set main as the default branch
- [bb22aac](https://github.com/williamdes/mariadb-mysql-kbs/commit/bb22aac607c5a5f5118b36b358e447877dcc998b) feat: add test cases for the MariaDB extraction script
- [44ca8ce](https://github.com/williamdes/mariadb-mysql-kbs/commit/44ca8ce4f1ae2ce4e5717484d1b047e560434ad7) feat: export MariaDB functions for testing
- [d3e5cab](https://github.com/williamdes/mariadb-mysql-kbs/commit/d3e5cab043426498926d8abde07469f3ffeb5c74) feat: Allow phpunit 9

### Documentation

- [f457e52](https://github.com/williamdes/mariadb-mysql-kbs/commit/f457e52f1274efc3b6f6c813e4f055e1ae6b07bd) docs: Update Repology badge after the merge of Fedora and Debian data

### Others

- [dc34e69](https://github.com/williamdes/mariadb-mysql-kbs/commit/dc34e69cf2358156b79913010afd221337a3f1f8) ci: update some commands
- [c304be5](https://github.com/williamdes/mariadb-mysql-kbs/commit/c304be5bddc74d02b991aa21a001667f66fa39a4) ci: use actions/cache@v2
- [5e0e575](https://github.com/williamdes/mariadb-mysql-kbs/commit/5e0e57542b669cbaf125160e3ea62ce08389a9dd) chore: update phpstan config
- [bd06752](https://github.com/williamdes/mariadb-mysql-kbs/commit/bd067524ca5e0f38fe444d580f38a44f41399d86) chore: upgrade dependabot to v2
- [b945b95](https://github.com/williamdes/mariadb-mysql-kbs/commit/b945b95fc7944800eb960a7f7e09c0d6319ddcc5) chore: upgrade @sudo-bot/sudo-bot to ^1.2.3
- [9d4752e](https://github.com/williamdes/mariadb-mysql-kbs/commit/9d4752ebb4be10870363eb70e105b96693fecbd9) chore: upgrade @sudo-bot/sudo-bot to ^1.2.2
- [ef3e911](https://github.com/williamdes/mariadb-mysql-kbs/commit/ef3e911dba1d68f9877f82ed0f3b9e9ffced1ccf) chore: upgrade @sudo-bot/sudo-bot to ^1.2.1
- [87feee1](https://github.com/williamdes/mariadb-mysql-kbs/commit/87feee104b810e70869495a705111518f871875a) chore: improve doctum config
- [5ae8538](https://github.com/williamdes/mariadb-mysql-kbs/commit/5ae85389ce9fd94992dda42bac6583316a448316) ci: make the script render instead of parse
- [ce383e6](https://github.com/williamdes/mariadb-mysql-kbs/commit/ce383e63f65da54ea1cbfbfef491035a4533c59f) chore: upgrade crawler and mocha
- [2444a8d](https://github.com/williamdes/mariadb-mysql-kbs/commit/2444a8d4a8ed7c8c8778655a488f63f0b6708c86) chore: upgrade @sudo-bot/sudo-bot to ^1.2.0
- [5cb8e6f](https://github.com/williamdes/mariadb-mysql-kbs/commit/5cb8e6f332462831a04ae265bf8371a9f95daa34) ci: upgrade sudo-bot/action-pull-request-merge to 1.1.1
- [390ee9c](https://github.com/williamdes/mariadb-mysql-kbs/commit/390ee9cc96c7137476ea7b4c4a90ca4c39319c94) ci: Ignore php 8.0 because of phpunit
- [7f72d9c](https://github.com/williamdes/mariadb-mysql-kbs/commit/7f72d9c4b39b9ef0190b434801d2cc56d9faeb76) ci: add php 7.4 and 8.0 to the matrix
- [5ca10f0](https://github.com/williamdes/mariadb-mysql-kbs/commit/5ca10f0a9615bae11e22d27f2c37bf9836b9cfa7) ci: remove the need of upload token, upgrade shivammathur/setup-php to v2
- [d37a838](https://github.com/williamdes/mariadb-mysql-kbs/commit/d37a838ff32938de91d94669cfb4370dc121e829) chore: change php versions requirements from ^7.1 to >=7.1
- [0d21cc7](https://github.com/williamdes/mariadb-mysql-kbs/commit/0d21cc7306fc45d87d02fe33bfb372c8817346f5) chore: upgrade some dependencies to require recent versions
- [c4c75df](https://github.com/williamdes/mariadb-mysql-kbs/commit/c4c75df0bd0a2ba4e0d27c18b26b17bedfb1e7f5) chore: upgrade dependencies

## [v1.2.10]

### Changed

- [8b61506](https://github.com/williamdes/mariadb-mysql-kbs/commit/8b61506fe329efcfaaa6b66e969ba23248c52dd6) update: [MariaDB] updates and other changes
- [3826dad](https://github.com/williamdes/mariadb-mysql-kbs/commit/3826dad219b15ff3f6cc0aabd85a4b8f30bf9b17) update: [MariaDB] updates
- [3300c03](https://github.com/williamdes/mariadb-mysql-kbs/commit/3300c03b0a24f742be901960bd87d6e4c984a8e3) update: [MariaDB] updates
- [dd877a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/dd877a282f24f44bd58ccc69424f2181cd1add20) update: [MySQL] updates

### Features

- [969103e](https://github.com/williamdes/mariadb-mysql-kbs/commit/969103e09c5550bead53592755b5bed058a945cb) feat: exclude some files from git archive version of the repo "export-ignore"
- [f564f47](https://github.com/williamdes/mariadb-mysql-kbs/commit/f564f47d03459a2eab97c79aa1290b9d39fa93ac) feat: move all commands to yarn
- [4ac523a](https://github.com/williamdes/mariadb-mysql-kbs/commit/4ac523abc11da0de15cb2defd1639562cea98449) feat: move to yarn

### Others

- [937d0d1](https://github.com/williamdes/mariadb-mysql-kbs/commit/937d0d1904a43e5f1c6bb0412979bf7d53e0fa71) chore: remove codecov npm dependency

## [v1.2.9]

### Added

- [d9742f4](https://github.com/williamdes/mariadb-mysql-kbs/commit/d9742f4bb8e6962c5149c725a7baadf5a79da359) add: Travis CI cron
- [403f751](https://github.com/williamdes/mariadb-mysql-kbs/commit/403f751af50c2a7c5d8c57cffbee552e6c8b7cf1) add: Workflows
- [fad58a0](https://github.com/williamdes/mariadb-mysql-kbs/commit/fad58a0793ac5279de779371cf9429f85a5f12ca) add: get clean type from mixed string
- [58f6ce1](https://github.com/williamdes/mariadb-mysql-kbs/commit/58f6ce174bf326e91345509ee49703b234af503d) add: test case 4 before type detection enhancement

### Changed

- [4d67c9c](https://github.com/williamdes/mariadb-mysql-kbs/commit/4d67c9c891aaa9576faf635a8c2acde506a29826) update: [MySQL] updates and other changes
- [3231ac4](https://github.com/williamdes/mariadb-mysql-kbs/commit/3231ac4cc4a4497c45580686bf2d587141a860e8) update: [MySQL] updates
- [a50744f](https://github.com/williamdes/mariadb-mysql-kbs/commit/a50744f4555c79200e80ae8a104f47e99980fb80) update: [MySQL] updates
- [034b517](https://github.com/williamdes/mariadb-mysql-kbs/commit/034b517927997297f2bc07eb700dc71003fa51f2) update: [MySQL] updates
- [a551a29](https://github.com/williamdes/mariadb-mysql-kbs/commit/a551a299b11b50ab0e282741976bd49adf8eca67) update: [MariaDB] updates
- [af7a10c](https://github.com/williamdes/mariadb-mysql-kbs/commit/af7a10cb89c1df513dff2e697f850fae6af6e113) update: [MariaDB] updates
- [7a979e2](https://github.com/williamdes/mariadb-mysql-kbs/commit/7a979e26722661e4faa78c0ee04b65b37f6ebeeb) update: [MySQL] updates

### Removed

- [fd20335](https://github.com/williamdes/mariadb-mysql-kbs/commit/fd203354b733a73d42d70c360724b0e5d08523fb) remove: release script

### Fixed

- [4c7126f](https://github.com/williamdes/mariadb-mysql-kbs/commit/4c7126ff4ff9c035351549ae7c0808cef3848a45) fix: use new build command and convention for changelog-generator-twig
- [ed7158c](https://github.com/williamdes/mariadb-mysql-kbs/commit/ed7158c890e0b5b7d6b4306520503189e5305348) fix: Improve docs generation script
- [4176e94](https://github.com/williamdes/mariadb-mysql-kbs/commit/4176e9440dd3d556d32d9479142b19565b51062e) fix: phpdoc errors reported by phpstan
- [8369147](https://github.com/williamdes/mariadb-mysql-kbs/commit/8369147444eb5b488b748b598df611c58c56bb21) fix: phpdoc block
- [3faaae7](https://github.com/williamdes/mariadb-mysql-kbs/commit/3faaae71a507438aba3d8c8f2fbdab64bc104c6b) fix: Use version 1.0.5 of sudo-bot/action-pull-request-lock
- [4b06ebe](https://github.com/williamdes/mariadb-mysql-kbs/commit/4b06ebe880fd33e4749cf96b261677f313def6da) fix: test case 4 after adding support for 'type: default, range' in MySQL pages

### Features

- [6e928c7](https://github.com/williamdes/mariadb-mysql-kbs/commit/6e928c70e38780007b145800e240f8f747b1578d) feat: Add a release script
- [03809c5](https://github.com/williamdes/mariadb-mysql-kbs/commit/03809c5af460f1c806826dd2a6308d99719bcb4b) feat: make Swaggest\JsonSchema\Schema optional for Debian packaging vendors
- [22744bb](https://github.com/williamdes/mariadb-mysql-kbs/commit/22744bb96b45aef525119dfc9cef2fcdd9650d98) feat: get rid of the url:: resolver to make the vendor optional
- [ab4a210](https://github.com/williamdes/mariadb-mysql-kbs/commit/ab4a2109604528b40581ac4a036d219f8feef7ce) feat: added sign-release script
- [7e58e57](https://github.com/williamdes/mariadb-mysql-kbs/commit/7e58e571772d4bfff49c628876eaaae7e32f1f99) feat: Improve sudo-bot template file
- [668f1a0](https://github.com/williamdes/mariadb-mysql-kbs/commit/668f1a014870202ea994404baedbb6d60bae1676) feat: Add docs build and PR
- [530f0c1](https://github.com/williamdes/mariadb-mysql-kbs/commit/530f0c148d180430329d01a8788389b51c2334de) feat: Add packaging status badge from repology
- [0e37662](https://github.com/williamdes/mariadb-mysql-kbs/commit/0e37662ab711fd99c2948057d219cf878b2c7980) feat: Add workflow to lock a closed pull-request
- [8c65961](https://github.com/williamdes/mariadb-mysql-kbs/commit/8c659616e3a7fe7c264e234bcc983b06de9a5585) feat: Add script to trigger a workflow
- [74fbc90](https://github.com/williamdes/mariadb-mysql-kbs/commit/74fbc90329639523581617c693a04f4b6a4a797a) feat: support 'type: default, range' in MySQL pages

### Others

- [89c3397](https://github.com/williamdes/mariadb-mysql-kbs/commit/89c339730a3728dd35dbbc8018464fba5688c652) chore: remove /schemas folder for dist archives (composer)
- [976d936](https://github.com/williamdes/mariadb-mysql-kbs/commit/976d936f6dd8a8cc8002e2d254df91315401025a) chore: remove /target folder for dist archives (rust support)
- [7634363](https://github.com/williamdes/mariadb-mysql-kbs/commit/7634363f0ab12eddf80aba61f04483da53c0412c) chore: Add .gitattributes to ignored dir for dist archives
- [73dfae0](https://github.com/williamdes/mariadb-mysql-kbs/commit/73dfae02cf268b03972fc0e5b4c869375e372717) chore: move tests to a more standard place
- [dc37421](https://github.com/williamdes/mariadb-mysql-kbs/commit/dc374211df97b2187317028a40171d5adc39bfa3) chore: simplify phpunit version regex
- [9fc286a](https://github.com/williamdes/mariadb-mysql-kbs/commit/9fc286ab32ca693c5978474495a66b3f737edb6e) ci: Add merge pull-request workflow
- [44d115e](https://github.com/williamdes/mariadb-mysql-kbs/commit/44d115ed3d27355b75f3d6e081411c18003f0049) ci: add --no-interaction and fix docs build
- [a1596a5](https://github.com/williamdes/mariadb-mysql-kbs/commit/a1596a526084da7df3212cf26876950d7560436b) chore: update phpstan/phpstan to 0.12
- [f96a3d1](https://github.com/williamdes/mariadb-mysql-kbs/commit/f96a3d19220bc4444629520a40e2d27785e8f46e) chore: update slevomat/coding-standard to 6.0
- [6248da5](https://github.com/williamdes/mariadb-mysql-kbs/commit/6248da5b030f35b6e6adde216e173d2534024129) chore: Update docs submodule
- [f3ac5ee](https://github.com/williamdes/mariadb-mysql-kbs/commit/f3ac5ee6b54ca2557a1822947776787d00c90a4d) chore: replace badge in README.md
- [42cf92f](https://github.com/williamdes/mariadb-mysql-kbs/commit/42cf92ffc22dc973420586dadf643693312efa23) ci: finish migration to GitHub actions
- [f0cb417](https://github.com/williamdes/mariadb-mysql-kbs/commit/f0cb417661ecdd039dbeccb10873fe2fe722da59) ci: remove all TravisCI files

## [v1.2.8]

### Added

- [8ca6999](https://github.com/williamdes/mariadb-mysql-kbs/commit/8ca6999e1487a9f43846530a3cc241ef7109b5bf) add: .gitattributes file

### Changed

- [b4afbca](https://github.com/williamdes/mariadb-mysql-kbs/commit/b4afbcabc7b40ebfb5b072de568f60063cf86adb) update: dependencies
- [856b39c](https://github.com/williamdes/mariadb-mysql-kbs/commit/856b39cbc7027bfee0f22c661960f694bbf08b32) update: [MySQL] updates
- [9d7b3c3](https://github.com/williamdes/mariadb-mysql-kbs/commit/9d7b3c3b973a3b0fa8fae5adc25f5ef02909203d) update: .gitattributes file
- [9eab800](https://github.com/williamdes/mariadb-mysql-kbs/commit/9eab8008ee3fc91c8817e744fc3755e7815cee47) update: .gitattributes file
- [530c6a6](https://github.com/williamdes/mariadb-mysql-kbs/commit/530c6a68d0f12eca9f1b0f4fd0f7d55f825847ae) update: [MariaDB] updates
- [53bae92](https://github.com/williamdes/mariadb-mysql-kbs/commit/53bae92f735f39271c881858074961fe3bc2e39e) update: [MariaDB] && [MySQL] updates
- [aeb2ffd](https://github.com/williamdes/mariadb-mysql-kbs/commit/aeb2ffd26fb7810d16a92330978f87f06da280a7) update: [MariaDB] && [MySQL] updates
- [fd9ac5e](https://github.com/williamdes/mariadb-mysql-kbs/commit/fd9ac5e27e321e4625150e70dac8cb645fcfbfb6) update: [MariaDB] && [MySQL] updates
- [0860955](https://github.com/williamdes/mariadb-mysql-kbs/commit/0860955d111d44d4e0a6ee3e8392724cbfc32e2a) update: dependencies
- [215fcf3](https://github.com/williamdes/mariadb-mysql-kbs/commit/215fcf3849a0441c55abd64d7f56119428994218) update: [MySQL] updates
- [0434d0a](https://github.com/williamdes/mariadb-mysql-kbs/commit/0434d0a918087e1f0f3679ba007ab12d06b6c00d) update: [MySQL] updates
- [9a65d87](https://github.com/williamdes/mariadb-mysql-kbs/commit/9a65d879cbb67aaf34450d518b6df3d651ee23f5) update: [MariaDB] && [MySQL] updates
- [6f6f19f](https://github.com/williamdes/mariadb-mysql-kbs/commit/6f6f19fd404bee47239fe6648bca607ceb0b97e0) update: dependencies

### Fixed

- [baee0c0](https://github.com/williamdes/mariadb-mysql-kbs/commit/baee0c02d1428d8d1c8d7e0824bdb5463e68cc7f) fix: some MySQL and MariaDB fixes
- [185ebb2](https://github.com/williamdes/mariadb-mysql-kbs/commit/185ebb223ba0904e65395f226a13b3bc708014fb) fix: cleanCli undefined

### Features

- [14d2a95](https://github.com/williamdes/mariadb-mysql-kbs/commit/14d2a95d61e0c1831e82d0a8d2c67132c18de4d8) feat: add tests for MySQL parser
- [228ee4a](https://github.com/williamdes/mariadb-mysql-kbs/commit/228ee4aba365dea063806c059a96c2d7c3c7902a) feat: Add cleaner for default values

## [v1.2.7]

### Changed

- [6416780](https://github.com/williamdes/mariadb-mysql-kbs/commit/64167803686aff4090f72a6d89826364b1d88d7d) update: package version to 1.2.7
- [520d89c](https://github.com/williamdes/mariadb-mysql-kbs/commit/520d89cbe75a088444e3536e7a2f0be31449efba) update: [security] bump lodash from 4.17.11 to 4.17.14
- [cf60c43](https://github.com/williamdes/mariadb-mysql-kbs/commit/cf60c43cc4e7d7c284fa5181707343145f2d88f5) update: [security] bump lodash.merge from 4.6.1 to 4.6.2
- [dfda544](https://github.com/williamdes/mariadb-mysql-kbs/commit/dfda544ecf47bf8cd995e3aebfb445d07f25bf5c) update: [MariaDB] && [MySQL] updates
- [4b31b18](https://github.com/williamdes/mariadb-mysql-kbs/commit/4b31b18c50a25f3e32a19d2b13218ef18166daa8) update: [MariaDB] && [MySQL] updates and other changes

## [v1.2.6]

### Added

- [1c5ccf2](https://github.com/williamdes/mariadb-mysql-kbs/commit/1c5ccf288d2f96e8e18451bf17964de4bc8f38f4) add: SECURITY.md
- [61a933f](https://github.com/williamdes/mariadb-mysql-kbs/commit/61a933f6e0d67f4287ba73243f2d69417b76f609) added: dependencies up to date badge
- [a55aa0c](https://github.com/williamdes/mariadb-mysql-kbs/commit/a55aa0c33ed0f9a70921a5b5ee205f3a79442413) added: php lint to CI and removed apt cache key
- [b3edac6](https://github.com/williamdes/mariadb-mysql-kbs/commit/b3edac617d653623b1840f7dbc89bac9276c3f82) add: .phpunit.result.cache to ignores
- [77830b6](https://github.com/williamdes/mariadb-mysql-kbs/commit/77830b64be3803c7880702e0ba900d5946a11501) add: test command to composer.json
- [0daf1a8](https://github.com/williamdes/mariadb-mysql-kbs/commit/0daf1a866d8ae22033297df652e6795052ab2ebe) added: jshint to dev dependencies
- [3987131](https://github.com/williamdes/mariadb-mysql-kbs/commit/398713113a0387a3b666d52c55766a8dbb8bbad5) added: .jshintignore

### Changed

- [09de4e1](https://github.com/williamdes/mariadb-mysql-kbs/commit/09de4e1288b236fb38686f926c5567b88d2bb661) update: prettier from 1.17.1 to 1.18.2
- [e4a96c9](https://github.com/williamdes/mariadb-mysql-kbs/commit/e4a96c94f85ca7e1fefe3a93761d20895790f857) update: package version to 1.2.6
- [57a2f9c](https://github.com/williamdes/mariadb-mysql-kbs/commit/57a2f9cac2a45792cdc1ae214bc33308614cf9f2) update: [MariaDB] && [MySQL] updates
- [1ba2bdd](https://github.com/williamdes/mariadb-mysql-kbs/commit/1ba2bdd21b634d29aff5dd4dd0620e24b4fee2c7) update: [MariaDB] && [MySQL] updates
- [10e8854](https://github.com/williamdes/mariadb-mysql-kbs/commit/10e88548866862ef2c4313ec591c075ac7d70fa6) update: .travis.yml
- [414e4a6](https://github.com/williamdes/mariadb-mysql-kbs/commit/414e4a670f64056824c9712d6d3f067fcd27c6bc) update: @sudo-bot from 1.1.7 to 1.1.8
- [2f8f42c](https://github.com/williamdes/mariadb-mysql-kbs/commit/2f8f42c8ceeae3c3cfdaeebd93d5ac299fad2c0e) update: @sudo-bot from 1.1.6 to 1.1.7
- [9477c48](https://github.com/williamdes/mariadb-mysql-kbs/commit/9477c4866349d8e997ffd534a53297bd88e4b690) update: @sudo-bot from 1.1.5 to 1.1.6
- [824f4f0](https://github.com/williamdes/mariadb-mysql-kbs/commit/824f4f01517dbf5e0654d486402545001d3f2015) update: [MySQL] data
- [55d6f72](https://github.com/williamdes/mariadb-mysql-kbs/commit/55d6f72913e24c2f32f2c9c3df285ba117f10f8a) update: [MariaDB] data
- [f679d22](https://github.com/williamdes/mariadb-mysql-kbs/commit/f679d2207b5c8d99044471de6e492b3ccd5b4a78) updated: dependencies and package-lock.json
- [46f52d8](https://github.com/williamdes/mariadb-mysql-kbs/commit/46f52d8c91a332480b7f5907d28fea3e391662df) update: [MySQL] data
- [22a609b](https://github.com/williamdes/mariadb-mysql-kbs/commit/22a609b4072296a943102965f79b6e9a2be61fc2) updated: swaggest/json-schema from 0.12.3+ to 0.12.9+ & phpunit command
- [18d2e32](https://github.com/williamdes/mariadb-mysql-kbs/commit/18d2e3237f3ed8a316fe3c4c2683012196b2ac9d) update: [MySQL] data
- [c5cafc6](https://github.com/williamdes/mariadb-mysql-kbs/commit/c5cafc66b59aee56e0db8ca42a0ddb8e0fc14f7e) update: [MariaDB] && [MySQL] updates
- [b2f66ca](https://github.com/williamdes/mariadb-mysql-kbs/commit/b2f66ca3851a5908d491769cacb4cbc726c558bd) updated: dependencies and package-lock.json
- [ac490c2](https://github.com/williamdes/mariadb-mysql-kbs/commit/ac490c2c6f3b7267fb72cb06d7990e547dd435d3) update: @sudo-bot cron script and README.md
- [13b93dc](https://github.com/williamdes/mariadb-mysql-kbs/commit/13b93dc37cb2f2e1d5ebfe6cc990a1065920da7a) update: [MariaDB] && [MySQL] updates
- [a9ddf62](https://github.com/williamdes/mariadb-mysql-kbs/commit/a9ddf62d554500b04a52ca0d425cceab955a656a) updated: prettier and mocha dependencies
- [1df9f60](https://github.com/williamdes/mariadb-mysql-kbs/commit/1df9f6031d72257f4b2e5223ed78d3ded32bfa6a) update: [MariaDB] && [MySQL] updates
- [1862d41](https://github.com/williamdes/mariadb-mysql-kbs/commit/1862d4158b6b285da798b9919af39686de683205) updated: sudo-bot and codecov npm dependencies
- [d2c8b8e](https://github.com/williamdes/mariadb-mysql-kbs/commit/d2c8b8ebdcdc603084c4235e78f93fd4c9f24c8d) update: [MySQL] updates

### Removed

- [f2d43b0](https://github.com/williamdes/mariadb-mysql-kbs/commit/f2d43b099df2442e5599130c86a72200811c4dbf) removed: spy script (useless)

### Fixed

- [4277ed2](https://github.com/williamdes/mariadb-mysql-kbs/commit/4277ed250f2500cb4c47253796f35dfba69d88b2) fix: alert detected by lgtm
- [6d386ce](https://github.com/williamdes/mariadb-mysql-kbs/commit/6d386ce5f1a7f225dd8b38f82f7a0c4aa2f3bdd2) fix: CI @sudo-bot script
- [b8ab559](https://github.com/williamdes/mariadb-mysql-kbs/commit/b8ab5592c5d54a35a6767b3a10c26d7fd53a7689) fix: MariaDB script
- [2362512](https://github.com/williamdes/mariadb-mysql-kbs/commit/2362512aa8da242158303620d4d2509229769e85) fix: move crawler to dev-dependencies
- [f106ca1](https://github.com/williamdes/mariadb-mysql-kbs/commit/f106ca1f2e2e6ba92bbae29bf343de90b0292717) fix: CI and .gitignore and phpstan command
- [ba2631e](https://github.com/williamdes/mariadb-mysql-kbs/commit/ba2631e1fd3265e58764ee06ce1a8bc37ab11813) fix: cleaner and add tests
- [e80c410](https://github.com/williamdes/mariadb-mysql-kbs/commit/e80c410f72cb8f899cab74eb72026db1026e6457) fix: @sudo-bot CI
- [a7cf7bd](https://github.com/williamdes/mariadb-mysql-kbs/commit/a7cf7bdf0a9ce4e49d6f7b56e26a58c71fa79ec0) fix: remove renamed file
- [8fd2d9f](https://github.com/williamdes/mariadb-mysql-kbs/commit/8fd2d9fc7a0507f35a44266a0c1a84f927f1a770) fix: @sudo-bot CI
- [cc7aac7](https://github.com/williamdes/mariadb-mysql-kbs/commit/cc7aac74f2ba0341661b65ab49b65ebd1afd1e41) fixed: CI reporting for non mocha tests
- [b40a61b](https://github.com/williamdes/mariadb-mysql-kbs/commit/b40a61bd0c0d3a2ecf3bdf6532bee02009fc1245) fix: @sudo-bot use 'npm ci' when package-lock.json exists
- [43b8e95](https://github.com/williamdes/mariadb-mysql-kbs/commit/43b8e95d27bfdba9a242b04c7e105e418a7fc816) fix: CI rename style to lint
- [7bb4a5b](https://github.com/williamdes/mariadb-mysql-kbs/commit/7bb4a5b6f6e062e775b6a54ab30a6d86528916a0) fixed: jshint setup
- [9143014](https://github.com/williamdes/mariadb-mysql-kbs/commit/91430143cc7687ca6650761b66f20ea39ff9ebc8) fixed: .npmignore

### Improvements

- [b4d600b](https://github.com/williamdes/mariadb-mysql-kbs/commit/b4d600b77993743c52b2f962b444a54be34b1107) improved: Improved extraction process and fixed bugs
- [7e81a29](https://github.com/williamdes/mariadb-mysql-kbs/commit/7e81a2943dd7bb6bfa369f056548b3239b9fbbaf) improved: move MariaDB and MySQL script to crawler and jquery
- [8190342](https://github.com/williamdes/mariadb-mysql-kbs/commit/8190342314ab1fa767c25b3960d2f8c17660a145) improved: Replace jsdom by crawler
- [be05dea](https://github.com/williamdes/mariadb-mysql-kbs/commit/be05dea67bb8cbc8f0ebb766d6236fdcfd89fea3) improved: CI install of npm and composer packages
- [e4b65db](https://github.com/williamdes/mariadb-mysql-kbs/commit/e4b65dbc851f623564afb3464014dc4ff49ada52) improved: Moved phpcs and phpcbf commands to composer
- [53bd313](https://github.com/williamdes/mariadb-mysql-kbs/commit/53bd31394c5b4a3f42692a54933f083d94347e9b) improved: Use namespace for tests
- [97f6095](https://github.com/williamdes/mariadb-mysql-kbs/commit/97f609580f412cdcd38ff61b20bfa69507661e60) improved: schemas testing
- [ed45a4d](https://github.com/williamdes/mariadb-mysql-kbs/commit/ed45a4db9216193a1e9ef42ea06a11fdd1c089ab) improved: added tests for each .json file format
- [5b51377](https://github.com/williamdes/mariadb-mysql-kbs/commit/5b51377f96821bb8778cf301f05d8573ea873c52) improved: Use phpunit for exception expectations
- [e646cd4](https://github.com/williamdes/mariadb-mysql-kbs/commit/e646cd4118a57131b99bdd8ac3d4d15b700b0ca4) improved: Move spy to cleaner and add tests
- [bc65814](https://github.com/williamdes/mariadb-mysql-kbs/commit/bc658145bde334580b3b495211081aa361099a4f) improved: extract scripts and use Promise and callbacks

## [v1.2.5]

### Added

- [046c3fe](https://github.com/williamdes/mariadb-mysql-kbs/commit/046c3fe15cbf57d89e283b62f5a8b03c576a0337) added: php7.3 and osx php7.3 to test matrix
- [2491c41](https://github.com/williamdes/mariadb-mysql-kbs/commit/2491c415adaa65ab37b47d52cfb322d95fe7767d) added: snyk and dependabot to README.md
- [3fa4313](https://github.com/williamdes/mariadb-mysql-kbs/commit/3fa43131b44f8b5407bb63411589ffa61dcb75b9) added: dependabot config
- [571ccf3](https://github.com/williamdes/mariadb-mysql-kbs/commit/571ccf3edc1b3b5cfa0245d5518063a9434d0835) added: LGTM and fixed changelog
- [a068fde](https://github.com/williamdes/mariadb-mysql-kbs/commit/a068fde4a0421f96d4e1897170b409d638f9aabe) added: merged data and tests to PR template
- [effd148](https://github.com/williamdes/mariadb-mysql-kbs/commit/effd1487a0227d1092455838abd73e145808fd01) added: template for sudo-bot and mocha
- [d17883b](https://github.com/williamdes/mariadb-mysql-kbs/commit/d17883bc5b41d187f9c3132a8577fd470fb3213e) added: sudo-bot

### Changed

- [44264b0](https://github.com/williamdes/mariadb-mysql-kbs/commit/44264b03e02d0908749b696f68721fac99cbd133) update: package version to 1.2.5
- [97ec035](https://github.com/williamdes/mariadb-mysql-kbs/commit/97ec035605cca060e3b7abf92d46ada85465a6d2) update: [MySQL] updates
- [9eb743f](https://github.com/williamdes/mariadb-mysql-kbs/commit/9eb743f65f4a1fbc80abab0247fe6e3896c98930) update: commit message format
- [5366e10](https://github.com/williamdes/mariadb-mysql-kbs/commit/5366e10a0625fa9f95f7eac7a43dfb9af43f72b6) updated: package-lock.json and package.json and changelog
- [2786d1a](https://github.com/williamdes/mariadb-mysql-kbs/commit/2786d1a5c98dd3b93bfca8706cc9d7e92b1770a7) updated: MySQL data
- [9cffbf6](https://github.com/williamdes/mariadb-mysql-kbs/commit/9cffbf64046836229d9c3f83674b213848833036) updated: MariaDB data
- [13d7ba4](https://github.com/williamdes/mariadb-mysql-kbs/commit/13d7ba4510e9957352790a02551eb247f4b17ad6) updated: composer.json && updated: composer.lock
- [7e80648](https://github.com/williamdes/mariadb-mysql-kbs/commit/7e80648f583ce760ab717a0a942bd6f576ede4bc) updated: composer.lock
- [affedaf](https://github.com/williamdes/mariadb-mysql-kbs/commit/affedaf9c5762fc01c5b6084b31f321de5e50140) updated: package-lock.json and dependabot config
- [0f8b1a0](https://github.com/williamdes/mariadb-mysql-kbs/commit/0f8b1a05978fc5d0573c0eacc23a97df17cc032e) updated: changelog 📖
- [e031ee1](https://github.com/williamdes/mariadb-mysql-kbs/commit/e031ee1af193878dfccf7892c2aa553bc76f4f00) updated: package.json & package-lock.json
- [b4675f3](https://github.com/williamdes/mariadb-mysql-kbs/commit/b4675f313ad67ef4bee7a661014d0ffcf9911bd2) updated: sudo-bot
- [dd0ff5c](https://github.com/williamdes/mariadb-mysql-kbs/commit/dd0ff5c9448f65dde6d5ef829589e2115292aa20) updated: [MySQL] & [MariaDB] data
- [852b3a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/852b3a2a527f620310fe2b90877d15ef315a60c9) updated: composer.lock and package-lock.json and updated package.json
- [ef766fd](https://github.com/williamdes/mariadb-mysql-kbs/commit/ef766fd9991e4fd1e80bb7b14abb2a352ecd4689) updated: changelog 📖

### Removed

- [16a3d25](https://github.com/williamdes/mariadb-mysql-kbs/commit/16a3d259263d00eab648fa9bce2249e993be0aeb) removed: composer.lock
- [b0f997d](https://github.com/williamdes/mariadb-mysql-kbs/commit/b0f997d2abc033740a35d14cf16982356b207f16) removed: old changelog script
- [ab83775](https://github.com/williamdes/mariadb-mysql-kbs/commit/ab83775286f122b186526c106079e798525ca307) removed: deploy on tag

### Fixed

- [741b07e](https://github.com/williamdes/mariadb-mysql-kbs/commit/741b07e927b55cf35e918085462e0052153ff8dc) fix: .npmignore .gitignore and composer archive ignore
- [3e5ca71](https://github.com/williamdes/mariadb-mysql-kbs/commit/3e5ca71a782aeedd4e6a726ae1737905ca74d7e6) fix: composer non feature branches and nyc coverage
- [b00a8b0](https://github.com/williamdes/mariadb-mysql-kbs/commit/b00a8b09496f193c3289bf6f3c2ece4cd3a95967) fix: osx CI
- [9ac6e9b](https://github.com/williamdes/mariadb-mysql-kbs/commit/9ac6e9bf898de7760f7f53ddabdc28a2c5a548bb) fixed: changelog
- [127889d](https://github.com/williamdes/mariadb-mysql-kbs/commit/127889df59911291084b8b7815b98519002abd04) fixed: changelog
- [2b23349](https://github.com/williamdes/mariadb-mysql-kbs/commit/2b23349f867450f308a37045486ac6b61eb25a99) fixed: Travis CI
- [5377cec](https://github.com/williamdes/mariadb-mysql-kbs/commit/5377cec845b0cc0888a23a8185e24553f7c2a476) fixed: NPM package name in README.md
- [1b433f8](https://github.com/williamdes/mariadb-mysql-kbs/commit/1b433f8f30c6f1cb4ad42540f869614f13239fbb) fixed: nyc coverage
- [2f24409](https://github.com/williamdes/mariadb-mysql-kbs/commit/2f244099934d2d125d01d55dbe5c2388c09693ff) fixed: Coverage report and ignore files
- [8bee75a](https://github.com/williamdes/mariadb-mysql-kbs/commit/8bee75af85dc02e8be72fae9ff9e29c0670c7b77) fixed: CI coverage
- [ae75ce8](https://github.com/williamdes/mariadb-mysql-kbs/commit/ae75ce8c4fd44cdae42748f3ab769cdd0401868c) fixed: CI & mocha tests installation
- [8fa12ea](https://github.com/williamdes/mariadb-mysql-kbs/commit/8fa12eaab4a917677828a4f12f0b07f936c8a023) fixed: composer namespace
- [ee2c058](https://github.com/williamdes/mariadb-mysql-kbs/commit/ee2c05800e170ed0743b5905db43450359ca10e6) fixed: travis CI and other files
- [a4974c5](https://github.com/williamdes/mariadb-mysql-kbs/commit/a4974c5050132cd38945d2c639fdd614fb080d11) fix: OSX CI
- [a6a9ed8](https://github.com/williamdes/mariadb-mysql-kbs/commit/a6a9ed8d17fc672413bbebc5908928a1ad80a679) fix: OSX on CI
- [4762da9](https://github.com/williamdes/mariadb-mysql-kbs/commit/4762da9723c8da0378d990a3a9062b89ac71f4f6) fixed: npm ignore

### Improvements

- [f4cd7a0](https://github.com/williamdes/mariadb-mysql-kbs/commit/f4cd7a000f32939649796c8880f34dc2c8ceee93) style: prettier
- [40ca56a](https://github.com/williamdes/mariadb-mysql-kbs/commit/40ca56af22aafc5fc314e1c1dee7520ae2cf69b9) style: prettier on changelog.js

## [v1.2.4]

### Added

- [5c00bb6](https://github.com/williamdes/mariadb-mysql-kbs/commit/5c00bb63e8423d092b5e21689e14ee83e9fc918f) added: deploy on tag
- [83b9b19](https://github.com/williamdes/mariadb-mysql-kbs/commit/83b9b19fe47b2a62065c5c3f3f67d1582b6554f1) added: Travis CI labels
- [00a10ea](https://github.com/williamdes/mariadb-mysql-kbs/commit/00a10eab8e00aaa0d073062957de291a02a472e5) added: sudo-bot cron script :factory:
- [071ef1d](https://github.com/williamdes/mariadb-mysql-kbs/commit/071ef1d991a718e972cf551f962e9a5f7b2f51c3) added: CODEOWNERS :lock:
- [4f71020](https://github.com/williamdes/mariadb-mysql-kbs/commit/4f7102047f310df55c85b0967aa872ada8d9fec5) added: [MariaDB] system-versioned-tables

### Changed

- [3092bc0](https://github.com/williamdes/mariadb-mysql-kbs/commit/3092bc0a3d03d580466b86de4036130e644ec94d) updated: composer.json & package.json - version 1.2.4
- [797b1f3](https://github.com/williamdes/mariadb-mysql-kbs/commit/797b1f304443c18076cfa5e910af8c5f23703601) updated: [MySQL] & [MariaDB] data
- [c999377](https://github.com/williamdes/mariadb-mysql-kbs/commit/c9993779f679c7b02161977b6733706b7e793f08) updated: [MySQL] data
- [03d5d6e](https://github.com/williamdes/mariadb-mysql-kbs/commit/03d5d6ea73e29023466449021cb5441b67ffeea6) updated: [MariaDB] data
- [68f2187](https://github.com/williamdes/mariadb-mysql-kbs/commit/68f2187842df8cba506394ccbbb3ddcc7bc401fc) updated: changelog :book:

### Fixed

- [247f98b](https://github.com/williamdes/mariadb-mysql-kbs/commit/247f98b3d21ddd43d336e9f62af5808980bc3806) fixed: OSX tests
- [10e4ff6](https://github.com/williamdes/mariadb-mysql-kbs/commit/10e4ff6ff34c65248815d64c2699e5f2f7847c24) fixed: typo
- [cb792c8](https://github.com/williamdes/mariadb-mysql-kbs/commit/cb792c8133d5c48116d940a3e92657c51f8b4f64) fixed: npm ignore

## [v1.2.3]

### Added

- [03cb762](https://github.com/williamdes/mariadb-mysql-kbs/commit/03cb7629b06f8d64e7b6ebced942a10d3d59c410) added: json schema validation for merged-ultraslim.json
- [fd46f68](https://github.com/williamdes/mariadb-mysql-kbs/commit/fd46f68ba96ffe28c53eeda1c2e41bb9e157d936) added: swaggest/json-schema

### Changed

- [8076644](https://github.com/williamdes/mariadb-mysql-kbs/commit/80766444c4443ba3101e4da6d0b3cfeef3c06351) updated: composer.json & package.json - version 1.2.3
- [9bd0602](https://github.com/williamdes/mariadb-mysql-kbs/commit/9bd06026ca172822f806a7d0625b2ddf85e47e54) updated: api docs :book:
- [fb2f2f6](https://github.com/williamdes/mariadb-mysql-kbs/commit/fb2f2f6f6cdba32d4518529a272cbbf3ed37391f) updated: changelog :book:
- [6696ca7](https://github.com/williamdes/mariadb-mysql-kbs/commit/6696ca745ce172a24d1fb5ae1fefe82ae10b222a) updated: merged data
- [143e1f2](https://github.com/williamdes/mariadb-mysql-kbs/commit/143e1f24f559469f2f13bab7da5366d59d54e2fd) updated: [MariaDB] data
- [c99e2f7](https://github.com/williamdes/mariadb-mysql-kbs/commit/c99e2f7e07faedef1db039c5fc5c7c44f5dc8c52) updated: [MySQL] data
- [c2903a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/c2903a29e2f2f60988276f21dc1f23464133ac4b) updated: composer.lock :lock:
- [5378614](https://github.com/williamdes/mariadb-mysql-kbs/commit/53786144f25bf89d0a040f3be6bc0378a17661f0) updated: changelog :book:

### Removed

- [2ab9b3c](https://github.com/williamdes/mariadb-mysql-kbs/commit/2ab9b3ce7aa43af77c47d0c096b3c7363203a053) removed: validate file
- [84fe040](https://github.com/williamdes/mariadb-mysql-kbs/commit/84fe040254d39f239286bbe77427298d590e5547) removed: testbench file

### Fixed

- [8abb014](https://github.com/williamdes/mariadb-mysql-kbs/commit/8abb01492af0b8c054cb878fa79560b774a637be) fixed: npm ignore

### Improvements

- [c920fd8](https://github.com/williamdes/mariadb-mysql-kbs/commit/c920fd8ea1c9a4be5d01237dd93ab3aef8caaa4e) improved: Tests :rocket: :package:

## [v1.2.2]

### Added

- [e95b8dc](https://github.com/williamdes/mariadb-mysql-kbs/commit/e95b8dc10379bf00236ed0ee24abc0723b6a743a) added: phpcs rules
- [e402e0c](https://github.com/williamdes/mariadb-mysql-kbs/commit/e402e0c03086b8712b68d084bca1f5118ec282af) added: getVariable
- [4e7ae6f](https://github.com/williamdes/mariadb-mysql-kbs/commit/4e7ae6f4e2b349f25f68c028b1e5c3dab171b8b5) added: badges and install intructions :book:
- [9bed254](https://github.com/williamdes/mariadb-mysql-kbs/commit/9bed254abc3ac85b46aa8f6ddd3eb0fd03f32008) added: npm ignore
- [47c5d56](https://github.com/williamdes/mariadb-mysql-kbs/commit/47c5d56b7549050fc7b71da971cc5fc64f5790fb) added: changelog :book:
- [bbeed46](https://github.com/williamdes/mariadb-mysql-kbs/commit/bbeed466908936e25fa7685509e4b0834d2636f6) added: changelog generator :book:
- [5cf8a17](https://github.com/williamdes/mariadb-mysql-kbs/commit/5cf8a17fe58762591da828de2aeb930d55d70027) added: [MariaDB] more documentation

### Changed

- [5b6a4bd](https://github.com/williamdes/mariadb-mysql-kbs/commit/5b6a4bdca787a6204fd8ce9fdfffa3ea7cd43ee7) updated: composer.json & package.json + :lock: - version 1.2.2
- [aa546c7](https://github.com/williamdes/mariadb-mysql-kbs/commit/aa546c73a3cd940d01f0866663c3a0ea99f1be4e) updated: changelog :book:
- [1777f13](https://github.com/williamdes/mariadb-mysql-kbs/commit/1777f13df5866179ce749ecb5b5c00988897f757) updated: prettier
- [e94a9db](https://github.com/williamdes/mariadb-mysql-kbs/commit/e94a9db838d678ccc3db039dccf116eb38586b2a) updated: [MySQL] & [MariaDB] data
- [59e5baa](https://github.com/williamdes/mariadb-mysql-kbs/commit/59e5baa7a31ac72c2ff4a17b5aefec40f98ed6a1) updated: release script
- [5140d06](https://github.com/williamdes/mariadb-mysql-kbs/commit/5140d062bb32ea80fe9155843d99f6a32e178782) updated: api docs
- [2bc0164](https://github.com/williamdes/mariadb-mysql-kbs/commit/2bc016434ed62141a45f3890cf5711bbd0f8755f) updated: composer.lock :lock:
- [c6cb704](https://github.com/williamdes/mariadb-mysql-kbs/commit/c6cb704b8711f2510769494362c60c63cc7cab6f) updated: composer.json & package.json
- [e2c6706](https://github.com/williamdes/mariadb-mysql-kbs/commit/e2c6706c74d9274f63f4185b108e6b335ecff14f) updated: changelog generator :book:
- [fb4cabe](https://github.com/williamdes/mariadb-mysql-kbs/commit/fb4cabe90393f1a7ec863f759be3795cdea36f84) updated: submodule commit hash

### Fixed

- [06737df](https://github.com/williamdes/mariadb-mysql-kbs/commit/06737dfb47665487ce9fb5cf7b490f54614fc0b2) fixed: phpcs rule
- [9acaab8](https://github.com/williamdes/mariadb-mysql-kbs/commit/9acaab87b15b2e9be1a21d9a3703faeb04bcaaf8) fixes: @throws is allowed in phpdoc
- [d4870a6](https://github.com/williamdes/mariadb-mysql-kbs/commit/d4870a6d02ff314001403cc1a1c45b93cce734ea) fixes: dataType bug

### Improvements

- [8af998f](https://github.com/williamdes/mariadb-mysql-kbs/commit/8af998fec0d2e95ddcedb3c586b44bd4df46eead) style: composer.json & package.json
- [5241c13](https://github.com/williamdes/mariadb-mysql-kbs/commit/5241c1338a87293d25ed25413aac50a801c1bcb2) style: phpcs & prettier
- [e5d987c](https://github.com/williamdes/mariadb-mysql-kbs/commit/e5d987c475b64bc002ffac46daab7068a8d7f506) style: phpcs & prettier

## [v1.2.1]

### Added

- [fe488b8](https://github.com/williamdes/mariadb-mysql-kbs/commit/fe488b8008f153ba71e7b4a30432182db9c78e29) added: KBException>Exception
- [016346b](https://github.com/williamdes/mariadb-mysql-kbs/commit/016346bb53487b9761475468b35bffa1d73103f1) added: markdown format :package: :book:
- [8742aa0](https://github.com/williamdes/mariadb-mysql-kbs/commit/8742aa0cb2cb669b6df9e8d3637f18b12fc1fab7) added: markdown format in data :package:
- [5c01220](https://github.com/williamdes/mariadb-mysql-kbs/commit/5c0122006d029180bf358ece53c94194f8b58035) added: API docs :book:

### Changed

- [057b23f](https://github.com/williamdes/mariadb-mysql-kbs/commit/057b23face95ef95e977d7e145e58cb4b68aac3f) updated: composer.json & package.* - version 1.2.1
- [bdaf01a](https://github.com/williamdes/mariadb-mysql-kbs/commit/bdaf01ae451c3b221b65ac7fe6462a17532055cd) updated: prettier ignore & composer archive & phpcs
- [f8786b2](https://github.com/williamdes/mariadb-mysql-kbs/commit/f8786b2d0519aaa1d37d4a74631830e360347975) updated: [MySQL] & [MariaDB] data
- [f64d72c](https://github.com/williamdes/mariadb-mysql-kbs/commit/f64d72c8db530449f3b72e9a71e3819518a5374d) updated: travis config
- [7df7e60](https://github.com/williamdes/mariadb-mysql-kbs/commit/7df7e60acf154fada34f3346669672b12f885732) updated: README :book:
- [bdf4e22](https://github.com/williamdes/mariadb-mysql-kbs/commit/bdf4e22b2d75523a63cc03c91b640485aceed1be) updated: phpcs & phpstan config

### Fixed

- [c16e655](https://github.com/williamdes/mariadb-mysql-kbs/commit/c16e655bd9ed2eebdb0cb8292184911794f3f808) fixes: command line bug in data
- [3637458](https://github.com/williamdes/mariadb-mysql-kbs/commit/3637458d2df6c1642ec323a405e73fc403aa693e) fixes: bug in validValues

### Improvements

- [08a29f0](https://github.com/williamdes/mariadb-mysql-kbs/commit/08a29f07bb2f600a06cf552fdca8426d95f9ba96) style: phpcs fixes

## [v1.2.0]

### Added

- [1928c75](https://github.com/williamdes/mariadb-mysql-kbs/commit/1928c755602b21f6f80b6480216b2defed345950) added: getVariableType in API :rocket:
- [94b083c](https://github.com/williamdes/mariadb-mysql-kbs/commit/94b083cea9e669693d1be369fb454ae002a2a40b) added: variable type & ultraslim php :package:
- [e7368b7](https://github.com/williamdes/mariadb-mysql-kbs/commit/e7368b75acf63b22c87fa311b7bf784ddbf56540) added: spy script :eye:
- [ab2594e](https://github.com/williamdes/mariadb-mysql-kbs/commit/ab2594e07a2c7fdfb246f6a63c7f8a79eca47db8) added: [MySQL] documentation
- [e16eb29](https://github.com/williamdes/mariadb-mysql-kbs/commit/e16eb29a36a1cdc94e18041c6cd0fda1e468bda8) added: [MySQL] documentation
- [4e86741](https://github.com/williamdes/mariadb-mysql-kbs/commit/4e86741f2fc5d91f3011569ad78e6cafb63f4462) added: [MySQL] documentations
- [e156e4f](https://github.com/williamdes/mariadb-mysql-kbs/commit/e156e4f1301a9368e7ce4c63e631a4d6cd57911a) added: [MySQL] documentations
- [58751e1](https://github.com/williamdes/mariadb-mysql-kbs/commit/58751e1ebc7ada1a958886ce6ec9c572848a1572) added: JSON key sorter
- [0a66b68](https://github.com/williamdes/mariadb-mysql-kbs/commit/0a66b680e8ad0eddb88a683dc8dea9a148eb3740) added: [MySQL] data
- [f744088](https://github.com/williamdes/mariadb-mysql-kbs/commit/f744088ae963d7d060e120102465750fc5d7ab06) added: [MySQL] replication options documentation

### Changed

- [bf3be0f](https://github.com/williamdes/mariadb-mysql-kbs/commit/bf3be0f2f91e4299e23665cfe97df9e2489b578c) updated: composer.* & package.* - version 1.2.0
- [599dee7](https://github.com/williamdes/mariadb-mysql-kbs/commit/599dee7e07f72826e339442d00fe0ee1ace42cc0) updated: phpcs & phpstan config
- [0dee19e](https://github.com/williamdes/mariadb-mysql-kbs/commit/0dee19efb1081b196b2fd9100b52b2174bbed786) updated: composer.json archive
- [a7f947e](https://github.com/williamdes/mariadb-mysql-kbs/commit/a7f947efc9ca76126482c052937b1e6bbc332457) updated: merged data
- [414021d](https://github.com/williamdes/mariadb-mysql-kbs/commit/414021daa725c39190cbc402ccee2f0b1740b132) updated: [MySQL] & [MariaDB] data
- [84fe8e7](https://github.com/williamdes/mariadb-mysql-kbs/commit/84fe8e7d46006f7b25e087f953b6db0ed3f5c07c) updated: phpstan config
- [e8eb1d4](https://github.com/williamdes/mariadb-mysql-kbs/commit/e8eb1d4b8400ea54efbc1a82217dc1af26c115df) updated: [MySQL] data
- [9d78bfc](https://github.com/williamdes/mariadb-mysql-kbs/commit/9d78bfc93c86776418591bb66242c867bedca8fb) updated: merged data
- [17370d6](https://github.com/williamdes/mariadb-mysql-kbs/commit/17370d6c621cda7008775bbaa2bfc5be4c9b66b2) updated: [MySQL] data
- [5e57001](https://github.com/williamdes/mariadb-mysql-kbs/commit/5e5700191aab19fc29411b7ecda0b0d597433a84) updated: merged data
- [c6badc1](https://github.com/williamdes/mariadb-mysql-kbs/commit/c6badc1a999df9d8b1b04fa9ba96800023d19348) updated: merged data
- [76281af](https://github.com/williamdes/mariadb-mysql-kbs/commit/76281af259455b8efd2b1a91597fb738afd2d607) updated: merged data
- [89ae61b](https://github.com/williamdes/mariadb-mysql-kbs/commit/89ae61bf3c68f0217167b4002fe0771620330558) updated: [MySQL] data
- [93f47a5](https://github.com/williamdes/mariadb-mysql-kbs/commit/93f47a54eedaef85b4b581ee5062d7eb7139f3f6) updated: [MySQL] & [MariaDB] data
- [9519e64](https://github.com/williamdes/mariadb-mysql-kbs/commit/9519e64c826e823599cbcb8f2054ee7fc19105a0) updated: merged data

### Removed

- [0c37939](https://github.com/williamdes/mariadb-mysql-kbs/commit/0c37939a1ac2fc5b206b0b4954ee48b3cef089d8) removed: :bug: dataType from data, now type

### Fixed

- [d33d2a1](https://github.com/williamdes/mariadb-mysql-kbs/commit/d33d2a1e7a99b77ef9aa2c37b2c06a52c9942c24) fixes: reported errors
- [34a58da](https://github.com/williamdes/mariadb-mysql-kbs/commit/34a58daba6097f2ad7ca4790b6afd51a81fcd3ef) fixes: dataType bug in spy :eye
- [42720e2](https://github.com/williamdes/mariadb-mysql-kbs/commit/42720e214b4821063636822e49d8dc8326d8dfa2) fixes: type bug
- [6e012b3](https://github.com/williamdes/mariadb-mysql-kbs/commit/6e012b3e511de60f2ebfb43e49a166762e0806a5) fixes: not array bug in merge
- [69484ae](https://github.com/williamdes/mariadb-mysql-kbs/commit/69484ae383825aa464895fd15190c057834ea498) fixes: key does not exist

### Improvements

- [0335da9](https://github.com/williamdes/mariadb-mysql-kbs/commit/0335da9b73afa5a8be956efc7766b1687225bb0e) improved: API :rocket:
- [362e136](https://github.com/williamdes/mariadb-mysql-kbs/commit/362e136166d6fbdaee17a4a409f9bcfc2a37920c) improved: [MySQL]  extract script
- [042d762](https://github.com/williamdes/mariadb-mysql-kbs/commit/042d762b739bf8d84790246fedb6f04d1d0d3d2a) style: phpstan & prettier

## [v1.1.0]

### Added

- [1dc3bf7](https://github.com/williamdes/mariadb-mysql-kbs/commit/1dc3bf7a651060398b9b6a28e1cb3b99cdf1f71e) added: prettier
- [797487a](https://github.com/williamdes/mariadb-mysql-kbs/commit/797487abd268ad01f6ecf05b8c3577d147ca5083) added: Search & phpcs & phpstan & travis & phpunit & test
- [ccb19a2](https://github.com/williamdes/mariadb-mysql-kbs/commit/ccb19a2a9a8c9d453ae8fec4c1b3d36771edd370) added: data from data builder
- [5586808](https://github.com/williamdes/mariadb-mysql-kbs/commit/55868083a95b6e7926e1fb4f51cde2cd08129fac) added: data builder

### Changed

- [0a0b691](https://github.com/williamdes/mariadb-mysql-kbs/commit/0a0b6913a40827ca9a9f4c094f7788a16c154436) updated: composer.json - version 1.1.0
- [d5a006d](https://github.com/williamdes/mariadb-mysql-kbs/commit/d5a006dbee72acc89feb60a867a7c8342cff6f61) updated: merge script
- [d891c83](https://github.com/williamdes/mariadb-mysql-kbs/commit/d891c83d44039bd9b74ea9b490b22a0d3c1e24da) updated: [MariaDB] kb url
- [5879b6f](https://github.com/williamdes/mariadb-mysql-kbs/commit/5879b6fa1ee361c7ede73b76f8939bde26033797) updated: .gitignore
- [29523ef](https://github.com/williamdes/mariadb-mysql-kbs/commit/29523efca1f4e65d8df7516a4dc004292511b365) updated: composer.json
- [1d0bcea](https://github.com/williamdes/mariadb-mysql-kbs/commit/1d0bcea1d4df934e7ac019826274e6705cb89eb5) updated: [MariaDB] data
- [39d5440](https://github.com/williamdes/mariadb-mysql-kbs/commit/39d54407d4ee11d76b6f074ba525a49d89466e1c) updated: [MySQL] & [MariaDB] data

### Fixed

- [8adc939](https://github.com/williamdes/mariadb-mysql-kbs/commit/8adc939a8128c20f11e806b97231d083cb5a9af0) fixed: phpcs config
- [e1bd1e6](https://github.com/williamdes/mariadb-mysql-kbs/commit/e1bd1e6a188f37780c988c58a929bae979d6c4ef) fixed: composer.json autoload
- [04865bb](https://github.com/williamdes/mariadb-mysql-kbs/commit/04865bb1f58c16d2faa74ebf516ebda64a4c4623) fixed: scope bug

### Improvements

- [bbd013e](https://github.com/williamdes/mariadb-mysql-kbs/commit/bbd013e4fbf23ea437e0e5cbde05af471906ef49) style: phpstan & prettier

## [v1.0.0]

### Added

- [7a8fb1b](https://github.com/williamdes/mariadb-mysql-kbs/commit/7a8fb1ba9b28671c73b2f1cea54ce52bb3d7048b) added: composer.json
- [9719ee0](https://github.com/williamdes/mariadb-mysql-kbs/commit/9719ee02f688922ce43643035d93d4a93151cea2) added: [MySQL] data
- [d5b9751](https://github.com/williamdes/mariadb-mysql-kbs/commit/d5b975183c319a5ac0a9f341e69afe4eb15cfb8b) added: more MySQL documentations
- [b536a33](https://github.com/williamdes/mariadb-mysql-kbs/commit/b536a33ff4a3d95fa996aa269eb02687a5761c65) added: [MariaDB] data
- [951a927](https://github.com/williamdes/mariadb-mysql-kbs/commit/951a9272f1880f666425e4c1742778ef5bdd4f00) added: more MariaDB system variables
- [c03f2a6](https://github.com/williamdes/mariadb-mysql-kbs/commit/c03f2a63c002b28cae44d8f060ac09d2969e229c) added: [MariaDB] data
- [f45ad15](https://github.com/williamdes/mariadb-mysql-kbs/commit/f45ad15cfff626159a60396abbb5ff085514ccd1) added: [MariaDB] server status variables documentation
- [dc2e800](https://github.com/williamdes/mariadb-mysql-kbs/commit/dc2e800d6d493c30be62bc1e78e84404f994e8d3) added: [MySQL] data
- [4a1e8da](https://github.com/williamdes/mariadb-mysql-kbs/commit/4a1e8da123a15738288bdac1504333ef69df1ebf) added: more documentations for MySQL
- [b35e824](https://github.com/williamdes/mariadb-mysql-kbs/commit/b35e824cc58be1f2b3c7fece095e0a331fb09a9d) added: support for command line
- [6404ab5](https://github.com/williamdes/mariadb-mysql-kbs/commit/6404ab5ad48a2f76702dcbd2fbf40bf7c8976fde) added: [MySQL] data
- [3d7fec8](https://github.com/williamdes/mariadb-mysql-kbs/commit/3d7fec83c1debca9ef636d32c51073e56b354ea6) added: MySQL script
- [cb6e800](https://github.com/williamdes/mariadb-mysql-kbs/commit/cb6e800fd15eb7e2a582d489a17bf86dec079d58) test: Added test for common.js
- [ecccb66](https://github.com/williamdes/mariadb-mysql-kbs/commit/ecccb6694c6b80b3a14b707fdda2318e68c9f5da) added: [MariaDB] data
- [b99b2b5](https://github.com/williamdes/mariadb-mysql-kbs/commit/b99b2b5482c9d089092a6106080299574629443c) added: files
- [e05b05a](https://github.com/williamdes/mariadb-mysql-kbs/commit/e05b05aa96895dee2c3222e93cdddfcb4055d950) added: package.json & :lock:
- [9783044](https://github.com/williamdes/mariadb-mysql-kbs/commit/9783044b051820a9ae893da2d7e488bc4a3f37c6) added: .gitignore & README
- [28e9e01](https://github.com/williamdes/mariadb-mysql-kbs/commit/28e9e010dc027dee17a55f5eedad776dcf983e95) added: LICENSE

### Changed

- [c0c22b9](https://github.com/williamdes/mariadb-mysql-kbs/commit/c0c22b92e6f8bd77addeae4a5e097f56cb4cc88e) updated: data
- [1f61c63](https://github.com/williamdes/mariadb-mysql-kbs/commit/1f61c634686785d7cca3d291368c9a3e737ebff8) updated: data
- [4f7f893](https://github.com/williamdes/mariadb-mysql-kbs/commit/4f7f893870fe295e698b4f6df91b5ba9ae1e88ea) updated: [MariaDB] data
- [0a95903](https://github.com/williamdes/mariadb-mysql-kbs/commit/0a95903495d9f293b419b41e763845ee3146f989) changed: Moved code in common.js

### Fixed

- [dded735](https://github.com/williamdes/mariadb-mysql-kbs/commit/dded735a2f1a16a31973fa623a29000c6e8e0fea) fixed: [MySQL] link
- [d9cd2a8](https://github.com/williamdes/mariadb-mysql-kbs/commit/d9cd2a8123a67d6adfddf0414988b98fcac9f082) fixed: bug in MariaDB script


[v1.2.13]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.12...v1.2.13
[v1.2.12]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.11...v1.2.12
[v1.2.11]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.10...v1.2.11
[v1.2.10]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.9...v1.2.10
[v1.2.9]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.8...v1.2.9
[v1.2.8]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.7...v1.2.8
[v1.2.7]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.6...v1.2.7
[v1.2.6]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.5...v1.2.6
[v1.2.5]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.4...v1.2.5
[v1.2.4]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.3...v1.2.4
[v1.2.3]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.2...v1.2.3
[v1.2.2]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.1...v1.2.2
[v1.2.1]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.2.0...v1.2.1
[v1.2.0]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.1.0...v1.2.0
[v1.1.0]: https://github.com/williamdes/mariadb-mysql-kbs/compare/v1.0.0...v1.1.0
[v1.0.0]: https://github.com/williamdes/mariadb-mysql-kbs/compare/28e9e010dc027dee17a55f5eedad776dcf983e95...v1.0.0

