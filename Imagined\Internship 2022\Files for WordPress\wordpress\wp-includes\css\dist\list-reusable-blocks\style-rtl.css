/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
:root {
  --wp-admin-theme-color: #007cba;
  --wp-admin-theme-color--rgb: 0, 124, 186;
  --wp-admin-theme-color-darker-10: #006ba1;
  --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
  --wp-admin-theme-color-darker-20: #005a87;
  --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  :root {
    --wp-admin-border-width-focus: 1.5px;
  }
}

.list-reusable-blocks-import-dropdown__content .components-popover__content > div {
  padding: 10px;
}

.list-reusable-blocks-import-form__label {
  display: block;
  margin-bottom: 10px;
}

.list-reusable-blocks-import-form__button {
  margin-top: 10px;
  margin-bottom: 10px;
  float: left;
}

.list-reusable-blocks-import-form .components-notice__content {
  margin: 0;
}
.list-reusable-blocks-import-form .components-notice.is-dismissible {
  padding-left: 0;
  margin: 5px 0;
}

.list-reusable-blocks__container {
  display: inline-flex;
  align-items: center;
  position: relative;
  top: -3px;
}
.list-reusable-blocks__container .components-button {
  height: 26px;
}

body.admin-color-light {
  --wp-admin-theme-color: #0085ba;
  --wp-admin-theme-color--rgb: 0, 133, 186;
  --wp-admin-theme-color-darker-10: #0073a1;
  --wp-admin-theme-color-darker-10--rgb: 0, 115, 161;
  --wp-admin-theme-color-darker-20: #006187;
  --wp-admin-theme-color-darker-20--rgb: 0, 97, 135;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-light {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-modern {
  --wp-admin-theme-color: #3858e9;
  --wp-admin-theme-color--rgb: 56, 88, 233;
  --wp-admin-theme-color-darker-10: #2145e6;
  --wp-admin-theme-color-darker-10--rgb: 33, 69, 230;
  --wp-admin-theme-color-darker-20: #183ad6;
  --wp-admin-theme-color-darker-20--rgb: 24, 58, 214;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-modern {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-blue {
  --wp-admin-theme-color: #096484;
  --wp-admin-theme-color--rgb: 9, 100, 132;
  --wp-admin-theme-color-darker-10: #07526c;
  --wp-admin-theme-color-darker-10--rgb: 7, 82, 108;
  --wp-admin-theme-color-darker-20: #064054;
  --wp-admin-theme-color-darker-20--rgb: 6, 64, 84;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-blue {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-coffee {
  --wp-admin-theme-color: #46403c;
  --wp-admin-theme-color--rgb: 70, 64, 60;
  --wp-admin-theme-color-darker-10: #383330;
  --wp-admin-theme-color-darker-10--rgb: 56, 51, 48;
  --wp-admin-theme-color-darker-20: #2b2724;
  --wp-admin-theme-color-darker-20--rgb: 43, 39, 36;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-coffee {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-ectoplasm {
  --wp-admin-theme-color: #523f6d;
  --wp-admin-theme-color--rgb: 82, 63, 109;
  --wp-admin-theme-color-darker-10: #46365d;
  --wp-admin-theme-color-darker-10--rgb: 70, 54, 93;
  --wp-admin-theme-color-darker-20: #3a2c4d;
  --wp-admin-theme-color-darker-20--rgb: 58, 44, 77;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-ectoplasm {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-midnight {
  --wp-admin-theme-color: #e14d43;
  --wp-admin-theme-color--rgb: 225, 77, 67;
  --wp-admin-theme-color-darker-10: #dd382d;
  --wp-admin-theme-color-darker-10--rgb: 221, 56, 45;
  --wp-admin-theme-color-darker-20: #d02c21;
  --wp-admin-theme-color-darker-20--rgb: 208, 44, 33;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-midnight {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-ocean {
  --wp-admin-theme-color: #627c83;
  --wp-admin-theme-color--rgb: 98, 124, 131;
  --wp-admin-theme-color-darker-10: #576e74;
  --wp-admin-theme-color-darker-10--rgb: 87, 110, 116;
  --wp-admin-theme-color-darker-20: #4c6066;
  --wp-admin-theme-color-darker-20--rgb: 76, 96, 102;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-ocean {
    --wp-admin-border-width-focus: 1.5px;
  }
}

body.admin-color-sunrise {
  --wp-admin-theme-color: #dd823b;
  --wp-admin-theme-color--rgb: 221, 130, 59;
  --wp-admin-theme-color-darker-10: #d97426;
  --wp-admin-theme-color-darker-10--rgb: 217, 116, 38;
  --wp-admin-theme-color-darker-20: #c36922;
  --wp-admin-theme-color-darker-20--rgb: 195, 105, 34;
  --wp-admin-border-width-focus: 2px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.admin-color-sunrise {
    --wp-admin-border-width-focus: 1.5px;
  }
}