<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غادة عطوات - السيرة الذاتية</title>
    <link rel="preconnect" href="https://fonts.cdnfonts.com" crossorigin>
	<link rel="stylesheet" href="https://fonts.cdnfonts.com/css/dubai">
	<link href="https://db.onlinewebfonts.com/c/1afcfbf9a48f974a0f4c9f287019e807?family=F%28A%29+<PERSON><PERSON><PERSON>+<PERSON><PERSON><PERSON>+B" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'dubai', sans-serif;
            line-height: 1.7;
            color: #4b2c82;
			background: linear-gradient(135deg, #f8f0fa 0%, #f3eaf7 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            overflow: hidden;
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4a235a 0%, #6c3483 50%, #4a235a 100%);
        }

        .header {
            background: linear-gradient(135deg, #4a235a 0%, #6c3483 100%);
            color: white;
            padding: 40px 40px 35px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.03"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.03"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.03"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.03"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .profile-section {
            display: flex;
            align-items: center;
            gap: 35px;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .photo-container {
            width: 140px;
            height: 175px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .profile-photo {
            width: 140px;
            height: 170px;
            object-fit: cover;
        }

        .profile-info {
            text-align: right;
            position: relative;
            z-index: 2;
            flex: 1;
        }

        .name {
            font-size: 2.4em;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
            letter-spacing: 0.5px;
            line-height: 1.2;
            color: white;
        }

        .title {
            font-size: 1.3em;
            font-weight: 500;
            opacity: 0.95;
            margin-bottom: 10px;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.95);
        }

        .professional-summary {
            font-size: 1.05em;
            line-height: 1.7;
            color: rgba(255, 255, 255, 0.92);
            margin-top: 15px;
            max-width: 700px;
            position: relative;
            padding-right: 10px;
            border-right: 3px solid rgba(255, 255, 255, 0.2);
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 30px;
            position: relative;
            z-index: 2;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95em;
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 20px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .contact-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 40px;
        }

        .left-column {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .right-column {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.5em;
            font-weight: 700;
            color: #4a235a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            right: 0;
            width: 50px;
            height: 3px;
            background: #6c3483;
        }

        .education-item, .experience-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #fbfbfb;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            border-right: 4px solid #6c3483;
            position: relative;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .item-title {
            font-size: 1.2em;
            font-weight: 700;
            color: #2c3e50;
            flex: 1;
            line-height: 1.3;
        }

        .item-date {
            background: #4a235a;
            color: white;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
            margin-left: 15px;
            flex-shrink: 0;
        }

        .item-institution {
            font-weight: 600;
            color: #5a6b77;
            margin-bottom: 12px;
            font-size: 1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .item-institution::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #6c3483;
        }

        .item-description {
            color: #495057;
            line-height: 1.7;
            font-size: 1em;
            padding-right: 15px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 15px;
        }

        .skill-category {
            background: #fbfbfb;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            border-top: 4px solid #6c3483;
            position: relative;
        }

        .skill-category h4 {
            color: #4a235a;
            margin-bottom: 15px;
            font-size: 1.1em;
            font-weight: 700;
        }

        .skill-list {
            list-style: none;
        }

        .skill-list li {
            padding: 8px 0;
            color: #495057;
            position: relative;
            padding-right: 15px;
            font-size: 0.9em;
            line-height: 1.5;
            border-bottom: 1px dashed #e9ecef;
            display: flex;
            align-items: center;
        }

        .skill-list li::before {
            content: '•';
            color: #6c3483;
            position: absolute;
            right: 0;
            font-size: 1.2em;
            top: 8px;
        }

        .skill-list li:last-child {
            border-bottom: none;
        }

        .personal-info {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            background: #fbfbfb;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            border-top: 4px solid #6c3483;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 5px 0;
        }

        .info-label {
            font-weight: 700;
            color: #4a235a;
            min-width: 120px;
            flex-shrink: 0;
        }

        .info-value {
            color: #495057;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .thesis-title {
            font-style: italic;
            color: #5a6b77;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-right: 3px solid #d4af37;
            font-size: 0.95em;
            line-height: 1.6;
            position: relative;
        }

        .subjects-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .subject-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            border-right: 3px solid #6c3483;
            font-size: 0.92em;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subject-item::before {
            content: '✓';
            color: #6c3483;
            font-weight: bold;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            grid-column: 1 / -1;
        }

        .references {
            font-style: italic;
            color: #6c757d;
            font-size: 1em;
        }

        .signature-container {
            text-align: center;
        }

        .signature {
            font-weight: 700;
            color: #4a235a;
            font-size: 1.3em;
            margin-bottom: 5px;
            font-family: "F(A) Arslan Wessam B";
        }

        .proficiency-badge {
            display: inline-block;
            background: #f0e6f5;
            color: #4a235a;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 3px;
            border: 1px solid #d9c3e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="profile-section">
                <div class="photo-container">
                    <img src="gapp1.jpg" alt="غادة" class="profile-photo">
                </div>
                <div class="profile-info">
                    <h1 class="name">غادة أحمد عطوات</h1>
                    <p class="title">ماجستير في علوم الأسرة | أخصائية في الخدمات الاجتماعية والمجتمعية</p>
                    <div class="professional-summary">
					أخصائية في الخدمات الاجتماعية والعمل المجتمعي، تتمتع بخبرة تمتدّ على مدى تسعة عشر عامًا في تصميم وإدارة البرامج الاجتماعية، وتقديم الدعم والإرشاد للأسر والأفراد. تمزج بين التأهيل الأكاديمي المتخصص في علوم الأسرة والإرشاد التربوي، والخبرة الميدانية التي تلامس نبض الواقع، وتُسهم في صياغة مبادرات مجتمعية تُروى من احتياجات الناس وتُبنى على أسس علمية منهجية تلامس واقعهم الإنساني عن قرب.
					</div>
				</div>
            </div>
            
            <div class="contact-info">
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                    </svg>
                    <span dir="ltr">+961 3 698564</span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                    <span dir="ltr"><EMAIL></span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <span>عبرا، صيدا، لبنان</span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    <span>متزوجة وأمّ لابن</span>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="left-column">
                <section class="section">
                    <h2 class="section-title">المعلومات الشخصية</h2>
                    <div class="personal-info">
                        <div class="info-item">
                            <span class="info-label">الجنسية:</span>
                            <span class="info-value">لبنانية</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الولادة:</span>
                            <span class="info-value">٢٠ تشرين الثاني ١٩٧٤</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">اللغات:</span>
                            <span class="info-value">
                                العربية <span class="proficiency-badge">لغة أم</span><br> 
                                الإنجليزية <span class="proficiency-badge">متوسطة</span>
                            </span>
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">المهارات المتخصصة</h2>
                    <div class="skills-grid">
                        <div class="skill-category">
                            <h4>الخدمات الاجتماعية والعمل المجتمعي</h4>
                            <ul class="skill-list">
                                <li>إجراء الزيارات الميدانية وتحليل الحالات <span class="proficiency-badge">متقدم</span></li>
                                <li>تحليل الاحتياجات وتقييم الحالات المجتمعية <span class="proficiency-badge">متقدم</span></li>
                                <li>ضبط برامج التحقّق وتوزيع المساعدات <span class="proficiency-badge">متقدم</span></li>
                                <li>تنسيق الخدمات مع الجهات والجمعيات الخيرية <span class="proficiency-badge">متقدم</span></li>
                                <li>قيادة مشاريع الرعاية الاجتماعية <span class="proficiency-badge">متقدم</span></li>
                                <li>تأسيس وتطوير شبكات الشراكة المجتمعية <span class="proficiency-badge">متقدم</span></li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>الإرشاد والاستشارة الأسرية</h4>
                            <ul class="skill-list">
                                <li>تقديم الإرشاد الأسري والاجتماعي <span class="proficiency-badge">متقدم</span></li>
                                <li>دعم وتمكين الأسر نفسيًا واجتماعيًا <span class="proficiency-badge">متقدم</span></li>
                                <li>حلّ وإدارة النزاعات والخلافات العائلية <span class="proficiency-badge">متقدم</span></li>
                                <li>توجيه المراهقين والشباب <span class="proficiency-badge">متقدم</span></li>
                                <li>التوجيه المهني والدعم المجتمعي <span class="proficiency-badge">متقدم</span></li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>الإدارة والقيادة</h4>
                            <ul class="skill-list">
                                <li>إدارة الأقسام والفرق المتخصصة <span class="proficiency-badge">متقدم</span></li>
                                <li>الإشراف على البرامج الاجتماعية <span class="proficiency-badge">متقدم</span></li>
                                <li>تنسيق وتنظيم المبادرات المجتمعية <span class="proficiency-badge">متقدم</span></li>
                                <li>قيادة وتوحيد الفرق متعددة التخصصات <span class="proficiency-badge">متقدم</span></li>
                                <li>حفظ قواعد البيانات الإلكترونية المتعلقة بالخدمات الاجتماعية <span class="proficiency-badge">متوسط</span></li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>التطوير والتدريب</h4>
                            <ul class="skill-list">
                                <li>تصميم وتطوير البرامج التعليمية والاجتماعية <span class="proficiency-badge">متقدم</span></li>
                                <li>التدريب في مجال العمل الاجتماعي <span class="proficiency-badge">متوسط</span></li>
                                <li>تطبيق أساليب التقييم الحديثة لقياس الأثر الاجتماعي <span class="proficiency-badge">متوسط</span></li>
                                <li>تخطيط وتنسيق الحملات الإعلامية للمبادرات الاجتماعية <span class="proficiency-badge">متوسط</span></li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">التحصيل العلمي</h2>
                    
                    <div class="education-item">
                        <div class="item-header">
                            <h3 class="item-title">ماجستير في علوم الأسرة</h3>
                            <span class="item-date">٢٠١١</span>
                        </div>
                        <div class="item-institution">كلية الدراسات العليا - دبي</div>
                        <div class="item-description">
                            بالتعاون مع جامعة غولدن ستيت - الولايات المتحدة الأمريكية<br>
                        </div>
                        <div class="thesis-title">
                            عنوان الرسالة: «تأخر سن الزواج وعلاقته بالمهور» – دراسة تحليلية في المجتمع اللبناني
                        </div>
                    </div>

                    <div class="education-item">
                        <div class="item-header">
                            <h3 class="item-title">دبلوم في الإرشاد الأسري والتربوي</h3>
                            <span class="item-date">٢٠٠٩</span>
                        </div>
                        <div class="item-institution">كلية الدراسات الحرة - دبي</div>
                        <div class="subjects-list">
                            <div class="subject-item">فن التعامل مع المراهقين</div>
                            <div class="subject-item">التربية الإيجابية</div>
                            <div class="subject-item">أسرار النفس البشرية</div>
                            <div class="subject-item">مهارات الإرشاد العائلي</div>
                            <div class="subject-item">هوية الطفل</div>
                        </div>
                    </div>

                    <div class="education-item">
                        <div class="item-header">
                            <h3 class="item-title">ليسانس في علم النفس التربوي</h3>
                            <span class="item-date">١٩٩٦</span>
                        </div>
                        <div class="item-institution">الجامعة اللبنانية</div>
                        <div class="item-description">
                            فهم السلوك الإنساني والتدخل الاجتماعي المؤثر
                        </div>
                    </div>
                    
                    <div class="education-item">
                        <div class="item-header">
                            <h3 class="item-title">دورة في اللغة الانكليزية (مستوى 5)</h3>
                            <span class="item-date">٢٠٠٧</span>
                        </div>
                        <div class="item-institution">المركز الأمريكي للّغة</div>
                    </div>
                </section>
            </div>

            <div class="right-column">
                <section class="section">
                    <h2 class="section-title">الخبرات المهنية</h2>
                    
                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">مسؤولة التحقق الميداني والخدمات الاجتماعية</h3>
                            <span class="item-date">٢٠١٦–٢٠٢٥</span>
                        </div>
                        <div class="item-institution">صندوق الزكاة - صيدا، لبنان</div>
                        <div class="item-description">
                            <strong>الإنجازات المحورية:</strong>
                            <ul style="margin-top: 10px; padding-right: 20px;">
                                <li><strong>قيادة فريق التحقّق الميداني: </strong> إدارة عمليات تشخيص الاحتياجات الأسرية وتوزيع المساعدات بناءً على أولويات علمية دقيقة</li>
								<li><strong>بناء الشراكات المؤسسية: </strong> التنسيق مع الهيئات الحكومية والجمعيات الأهلية لتوفير دعم متكامل ومستدام للفئات المهمشة</li>
								<li><strong>إدارة الملف الصحّي: </strong>الإشراف على بنك الأدوية، وتنظيم انسياب المستلزمات الطبية والخدمات الاستشفائية بسلاسة وفعالية</li>
								<li><strong>التحوّل الرقمي: </strong> الإشراف العام على تطوير النظام الإلكتروني لإدارة بيانات المستفيدين، وتعزيز كفاءة الاستجابة الاجتماعية</li>
								<li><strong>تعزيز المبادرات المجتمعية: </strong> التنسيق مع لجنة صديقات الصندوق لإطلاق مشاريع نوعية</li>
								<li><strong>التنسيق الإعلامي: </strong>التعاون مع الفريق الإعلامي لصندوق الزكاة، وإبداء الرأي في مسودات المنشورات ومراجعتها</li>
                            </ul>
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">مديرة قسم التعليم المهني</h3>
                            <span class="item-date">٢٠٠٩–٢٠١٥</span>
                        </div>
                        <div class="item-institution">مركز الرحمة لخدمة المجتمع - صيدا، لبنان</div>
                        <div class="item-description">
                            <strong>الإنجازات المحورية:</strong>
                            <ul style="margin-top: 10px; padding-right: 20px;">
                                <li><strong>تصميم برامج التأهيل المجتمعي: </strong>إعداد وتطوير مسارات تأهيل شاملة تستجيب لاحتياجات الشباب والأسر، وتستند إلى منهجيات توجيهية حديثة</li>
                                <li><strong>إدارة التعليم المهني: </strong>الإشراف على المناهج والخطط التدريبية بما يواكب متطلبات سوق العمل المحلي، ويعزز جاهزية الفئات المستفيدة</li>
                                <li><strong>بناء القدرات المؤسسية: </strong>تنفيذ برامج تدريب وتأهيل للكوادر العاملة في المجالات التربوية والاجتماعية، بما يضمن كفاءة الأداء واستدامة التأثير</li>
                                <li><strong>التنمية المجتمعية المستدامة: </strong>تصميم مبادرات وبرامج تتماشى مع أولويات المجتمع المحلي، وتحقق أثرًا تنمويًّا ملموسًا وفق مبادئ التنمية المستدامة</li>
                            </ul>
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">منسّقة برنامج التقوية المدرسية</h3>
                            <span class="item-date">٢٠٠٦–٢٠١٠</span>
                        </div>
                        <div class="item-institution">مركز الرحمة لخدمة المجتمع - صيدا، لبنان</div>
                        <div class="item-description">
                            <strong>الإنجازات المحورية:</strong>
                            <ul style="margin-top: 10px; padding-right: 20px;">
                                <li><strong>تصميم برامج الدعم المتكاملة: </strong>تطوير وتنفيذ برامج دعم شاملة تسهم في رفع مستوى المعيشة لدى الأسر المحتاجة</li>
                                <li><strong>برامج التقوية المدرسية: </strong>تصميم برامج تعليمية متكاملة لدعم التحصيل الأكاديمي للطلاب وتعزيز مهاراتهم التعليمية</li>
                                <li><strong>التنمية المجتمعية الشاملة: </strong>تحقيق أثر ملموس في تحسين الظروف المعيشية وتنمية المهارات الحياتية لدى المستفيدين</li>
                                <li><strong>الشراكات المجتمعية: </strong>التنسيق مع الكوادر المختصة لتشخيص الاحتياجات وتقديم حلول عملية وفعالة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">مشرفة اجتماعية</h3>
                            <span class="item-date">٢٠٠٤–٢٠٠٦</span>
                        </div>
                        <div class="item-institution">مركز الرحمة لخدمة المجتمع - صيدا، لبنان</div>
                        <div class="item-description">
                            تقديم الدعم النفسي والاجتماعي للحالات المستفيدة، ومتابعتها ميدانيًّا لضمان تحسين جودة الحياة وتمكينها من الاعتماد الذاتي. تطوير برامج التوعية في رعاية الطفولة والإرشاد الأسري.
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">مدرّسة تربية إسلامية</h3>
                            <span class="item-date">٢٠٠٢–٢٠١١</span>
                        </div>
                        <div class="item-institution">دار الفتوى – مدرسة متوسطة معروف سعد، صيدا</div>
                        <div class="item-description">
                            فهم الأجيال الناشئة واكتساب مهارات التوجيه التربوي.
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="item-header">
                            <h3 class="item-title">مدرّسة لغة إنكليزية ورياضيات</h3>
                            <span class="item-date">١٩٩٦–١٩٩٧</span>
                        </div>
                        <div class="item-institution">دار رعاية اليتيم – صيدا</div>
						<div class="item-description">
                            تدريس المواد للمرحلة الابتدائية.
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">التطوير المهني</h2>
                    <div class="education-item">
                        <div class="item-header">
                            <h3 class="item-title">دورات تدريبية متخصصة في الإدارة والتربية</h3>
                        </div>
                        <div class="item-description">
                            <strong>أبرز مجالات التطوير:</strong>
                            <ul style="margin-top: 10px; padding-right: 20px;">
                                <li>المشاركة في ورش عمل ودورات تدريبية متخصصة في الإدارة التربوية وأساليب القيادة</li>
                                <li>المتابعة المستمرة لأحدث النظريات والممارسات في مجالي الإرشاد الأسري والتربوي</li>
                            </ul>
                            <em>(التفاصيل متاحة عند الطلب)</em>
                        </div>
                    </div>
                </section>
            </div>

            <div class="signature-section">
                <div class="references">
                    المراجع: متاحة عند الطلب
                </div>
                <div class="signature-container">
                    <div class="signature">غادة عطوات</div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>