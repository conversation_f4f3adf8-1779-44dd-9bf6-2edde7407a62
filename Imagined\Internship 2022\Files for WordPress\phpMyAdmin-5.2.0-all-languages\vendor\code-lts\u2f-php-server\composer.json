{"name": "code-lts/u2f-php-server", "description": "Server side handling class for FIDO U2F registration and authentication", "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/code-lts/U2F-php-server#readme", "authors": [{"name": "<PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "william<PERSON>@wdes.fr"}], "support": {"issues": "https://github.com/code-lts/U2F-php-server/issues", "source": "https://github.com/code-lts/U2F-php-server"}, "scripts": {"phpunit": "./vendor/bin/phpunit"}, "require": {"php": "^7.1 || ^8.0", "ext-openssl": "*"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9"}, "autoload": {"psr-4": {"CodeLts\\U2F\\U2FServer\\": ["src/"]}}, "autoload-dev": {"psr-4": {"CodeLts\\U2F\\U2FServer\\Tests\\": ["test/"]}}, "replace": {"samyoul/u2f-php-server": "*"}}