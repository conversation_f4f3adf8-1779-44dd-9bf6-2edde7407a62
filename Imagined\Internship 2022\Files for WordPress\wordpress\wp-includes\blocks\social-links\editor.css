/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-social-links div.block-editor-url-input {
  display: inline-block;
  margin-left: 8px;
}
.wp-block-social-links.wp-block-social-links {
  background: none;
}

.wp-social-link:hover {
  transform: none;
}

.editor-styles-wrapper .wp-block-social-links {
  padding: 0;
}

.wp-block-social-links__social-placeholder {
  display: flex;
  opacity: 0.8;
  list-style: none;
}
.wp-block-social-links__social-placeholder > .wp-social-link {
  padding-left: 0 !important;
  margin-left: 0 !important;
  padding-right: 0 !important;
  margin-right: 0 !important;
  width: 0 !important;
  visibility: hidden;
}
.wp-block-social-links__social-placeholder > .wp-block-social-links__social-placeholder-icons {
  display: flex;
}
.wp-block-social-links__social-placeholder .wp-social-link {
  padding: 0.25em;
}
.is-style-pill-shape .wp-block-social-links__social-placeholder .wp-social-link {
  padding-left: calc((2/3) * 1em);
  padding-right: calc((2/3) * 1em);
}
.is-style-logos-only .wp-block-social-links__social-placeholder .wp-social-link {
  padding: 0;
}
.wp-block-social-links__social-placeholder .wp-social-link::before {
  content: "";
  display: block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
}
.is-style-logos-only .wp-block-social-links__social-placeholder .wp-social-link::before {
  background: currentColor;
}

.wp-block-social-links .wp-block-social-links__social-prompt {
  min-height: 24px;
  list-style: none;
  order: 2;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  line-height: 24px;
  margin-top: auto;
  margin-bottom: auto;
  cursor: default;
  padding-right: 8px;
}

.wp-block[data-align=center] > .wp-block-social-links {
  justify-content: center;
}

.block-editor-block-preview__content .components-button:disabled {
  opacity: 1;
}

.wp-social-link.wp-social-link__is-incomplete {
  opacity: 0.5;
}
@media (prefers-reduced-motion: reduce) {
  .wp-social-link.wp-social-link__is-incomplete {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}

.wp-block-social-links .is-selected .wp-social-link__is-incomplete,
.wp-social-link.wp-social-link__is-incomplete:hover,
.wp-social-link.wp-social-link__is-incomplete:focus {
  opacity: 1;
}