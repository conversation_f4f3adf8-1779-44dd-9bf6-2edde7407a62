DependencyInjection Component
=============================

The DependencyInjection component allows you to standardize and centralize the
way objects are constructed in your application.

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/dependency_injection.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)
