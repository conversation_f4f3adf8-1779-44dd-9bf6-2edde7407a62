/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.block-library-html__edit .block-library-html__preview-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
}
.block-library-html__edit .block-editor-plain-text {
  font-family: Menlo, Consolas, monaco, monospace !important;
  color: #1e1e1e !important;
  background: #fff !important;
  padding: 12px !important;
  border: 1px solid #1e1e1e !important;
  box-shadow: none !important;
  border-radius: 2px !important;
  max-height: 250px;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  font-size: 16px !important;
}
@media (min-width: 600px) {
  .block-library-html__edit .block-editor-plain-text {
    font-size: 13px !important;
  }
}
.block-library-html__edit .block-editor-plain-text:focus {
  border-color: var(--wp-admin-theme-color) !important;
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color) !important;
  outline: 2px solid transparent !important;
}