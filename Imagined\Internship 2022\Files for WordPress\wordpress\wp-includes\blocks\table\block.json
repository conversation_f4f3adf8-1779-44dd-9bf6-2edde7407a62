{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/table", "title": "Table", "category": "text", "description": "Create structured content in rows and columns to display information.", "textdomain": "default", "attributes": {"hasFixedLayout": {"type": "boolean", "default": false}, "caption": {"type": "string", "source": "html", "selector": "figcaption", "default": ""}, "head": {"type": "array", "default": [], "source": "query", "selector": "thead tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "string", "source": "html"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}}}}}, "body": {"type": "array", "default": [], "source": "query", "selector": "tbody tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "string", "source": "html"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}}}}}, "foot": {"type": "array", "default": [], "source": "query", "selector": "tfoot tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "string", "source": "html"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}}}}}}, "supports": {"anchor": true, "align": true, "color": {"__experimentalSkipSerialization": true, "gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"__experimentalSkipSerialization": true, "color": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "style": true, "width": true}}, "__experimentalSelector": ".wp-block-table > table"}, "styles": [{"name": "regular", "label": "<PERSON><PERSON><PERSON>", "isDefault": true}, {"name": "stripes", "label": "Stripes"}], "editorStyle": "wp-block-table-editor", "style": "wp-block-table"}