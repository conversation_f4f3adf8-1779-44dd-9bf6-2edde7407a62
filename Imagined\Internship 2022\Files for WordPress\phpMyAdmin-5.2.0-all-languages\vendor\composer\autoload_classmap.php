<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'BaconQrCode\\Common\\BitArray' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitArray.php',
    'BaconQrCode\\Common\\BitMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitMatrix.php',
    'BaconQrCode\\Common\\BitUtils' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitUtils.php',
    'BaconQrCode\\Common\\CharacterSetEci' => $vendorDir . '/bacon/bacon-qr-code/src/Common/CharacterSetEci.php',
    'BaconQrCode\\Common\\EcBlock' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlock.php',
    'BaconQrCode\\Common\\EcBlocks' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlocks.php',
    'BaconQrCode\\Common\\ErrorCorrectionLevel' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ErrorCorrectionLevel.php',
    'BaconQrCode\\Common\\FormatInformation' => $vendorDir . '/bacon/bacon-qr-code/src/Common/FormatInformation.php',
    'BaconQrCode\\Common\\Mode' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Mode.php',
    'BaconQrCode\\Common\\ReedSolomonCodec' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ReedSolomonCodec.php',
    'BaconQrCode\\Common\\Version' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Version.php',
    'BaconQrCode\\Encoder\\BlockPair' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/BlockPair.php',
    'BaconQrCode\\Encoder\\ByteMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/ByteMatrix.php',
    'BaconQrCode\\Encoder\\Encoder' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/Encoder.php',
    'BaconQrCode\\Encoder\\MaskUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MaskUtil.php',
    'BaconQrCode\\Encoder\\MatrixUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MatrixUtil.php',
    'BaconQrCode\\Encoder\\QrCode' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/QrCode.php',
    'BaconQrCode\\Exception\\ExceptionInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/ExceptionInterface.php',
    'BaconQrCode\\Exception\\InvalidArgumentException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/InvalidArgumentException.php',
    'BaconQrCode\\Exception\\OutOfBoundsException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/OutOfBoundsException.php',
    'BaconQrCode\\Exception\\RuntimeException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/RuntimeException.php',
    'BaconQrCode\\Exception\\UnexpectedValueException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/UnexpectedValueException.php',
    'BaconQrCode\\Exception\\WriterException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/WriterException.php',
    'BaconQrCode\\Renderer\\Color\\Alpha' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Alpha.php',
    'BaconQrCode\\Renderer\\Color\\Cmyk' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Cmyk.php',
    'BaconQrCode\\Renderer\\Color\\ColorInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/ColorInterface.php',
    'BaconQrCode\\Renderer\\Color\\Gray' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Gray.php',
    'BaconQrCode\\Renderer\\Color\\Rgb' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Rgb.php',
    'BaconQrCode\\Renderer\\Eye\\CompositeEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/CompositeEye.php',
    'BaconQrCode\\Renderer\\Eye\\EyeInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/EyeInterface.php',
    'BaconQrCode\\Renderer\\Eye\\ModuleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/ModuleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SimpleCircleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SimpleCircleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SquareEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SquareEye.php',
    'BaconQrCode\\Renderer\\ImageRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/ImageRenderer.php',
    'BaconQrCode\\Renderer\\Image\\EpsImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/EpsImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\ImageBackEndInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImageBackEndInterface.php',
    'BaconQrCode\\Renderer\\Image\\ImagickImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImagickImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\SvgImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/SvgImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\TransformationMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/TransformationMatrix.php',
    'BaconQrCode\\Renderer\\Module\\DotsModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/DotsModule.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\Edge' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/Edge.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\EdgeIterator' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/EdgeIterator.php',
    'BaconQrCode\\Renderer\\Module\\ModuleInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/ModuleInterface.php',
    'BaconQrCode\\Renderer\\Module\\RoundnessModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/RoundnessModule.php',
    'BaconQrCode\\Renderer\\Module\\SquareModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/SquareModule.php',
    'BaconQrCode\\Renderer\\Path\\Close' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Close.php',
    'BaconQrCode\\Renderer\\Path\\Curve' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Curve.php',
    'BaconQrCode\\Renderer\\Path\\EllipticArc' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/EllipticArc.php',
    'BaconQrCode\\Renderer\\Path\\Line' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Line.php',
    'BaconQrCode\\Renderer\\Path\\Move' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Move.php',
    'BaconQrCode\\Renderer\\Path\\OperationInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/OperationInterface.php',
    'BaconQrCode\\Renderer\\Path\\Path' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Path.php',
    'BaconQrCode\\Renderer\\PlainTextRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/PlainTextRenderer.php',
    'BaconQrCode\\Renderer\\RendererInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererInterface.php',
    'BaconQrCode\\Renderer\\RendererStyle\\EyeFill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/EyeFill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Fill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Fill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Gradient' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Gradient.php',
    'BaconQrCode\\Renderer\\RendererStyle\\GradientType' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/GradientType.php',
    'BaconQrCode\\Renderer\\RendererStyle\\RendererStyle' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/RendererStyle.php',
    'BaconQrCode\\Writer' => $vendorDir . '/bacon/bacon-qr-code/src/Writer.php',
    'CodeLts\\U2F\\U2FServer\\Registration' => $vendorDir . '/code-lts/u2f-php-server/src/Registration.php',
    'CodeLts\\U2F\\U2FServer\\RegistrationRequest' => $vendorDir . '/code-lts/u2f-php-server/src/RegistrationRequest.php',
    'CodeLts\\U2F\\U2FServer\\SignRequest' => $vendorDir . '/code-lts/u2f-php-server/src/SignRequest.php',
    'CodeLts\\U2F\\U2FServer\\U2FException' => $vendorDir . '/code-lts/u2f-php-server/src/U2FException.php',
    'CodeLts\\U2F\\U2FServer\\U2FServer' => $vendorDir . '/code-lts/u2f-php-server/src/U2FServer.php',
    'Composer\\CaBundle\\CaBundle' => $vendorDir . '/composer/ca-bundle/src/CaBundle.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DASPRiD\\Enum\\AbstractEnum' => $vendorDir . '/dasprid/enum/src/AbstractEnum.php',
    'DASPRiD\\Enum\\EnumMap' => $vendorDir . '/dasprid/enum/src/EnumMap.php',
    'DASPRiD\\Enum\\Exception\\CloneNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/CloneNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\ExceptionInterface' => $vendorDir . '/dasprid/enum/src/Exception/ExceptionInterface.php',
    'DASPRiD\\Enum\\Exception\\ExpectationException' => $vendorDir . '/dasprid/enum/src/Exception/ExpectationException.php',
    'DASPRiD\\Enum\\Exception\\IllegalArgumentException' => $vendorDir . '/dasprid/enum/src/Exception/IllegalArgumentException.php',
    'DASPRiD\\Enum\\Exception\\MismatchException' => $vendorDir . '/dasprid/enum/src/Exception/MismatchException.php',
    'DASPRiD\\Enum\\Exception\\SerializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/SerializeNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\UnserializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/UnserializeNotSupportedException.php',
    'DASPRiD\\Enum\\NullValue' => $vendorDir . '/dasprid/enum/src/NullValue.php',
    'Datamatrix' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/datamatrix.php',
    'FastRoute\\BadRouteException' => $vendorDir . '/nikic/fast-route/src/BadRouteException.php',
    'FastRoute\\DataGenerator' => $vendorDir . '/nikic/fast-route/src/DataGenerator.php',
    'FastRoute\\DataGenerator\\CharCountBased' => $vendorDir . '/nikic/fast-route/src/DataGenerator/CharCountBased.php',
    'FastRoute\\DataGenerator\\GroupCountBased' => $vendorDir . '/nikic/fast-route/src/DataGenerator/GroupCountBased.php',
    'FastRoute\\DataGenerator\\GroupPosBased' => $vendorDir . '/nikic/fast-route/src/DataGenerator/GroupPosBased.php',
    'FastRoute\\DataGenerator\\MarkBased' => $vendorDir . '/nikic/fast-route/src/DataGenerator/MarkBased.php',
    'FastRoute\\DataGenerator\\RegexBasedAbstract' => $vendorDir . '/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php',
    'FastRoute\\Dispatcher' => $vendorDir . '/nikic/fast-route/src/Dispatcher.php',
    'FastRoute\\Dispatcher\\CharCountBased' => $vendorDir . '/nikic/fast-route/src/Dispatcher/CharCountBased.php',
    'FastRoute\\Dispatcher\\GroupCountBased' => $vendorDir . '/nikic/fast-route/src/Dispatcher/GroupCountBased.php',
    'FastRoute\\Dispatcher\\GroupPosBased' => $vendorDir . '/nikic/fast-route/src/Dispatcher/GroupPosBased.php',
    'FastRoute\\Dispatcher\\MarkBased' => $vendorDir . '/nikic/fast-route/src/Dispatcher/MarkBased.php',
    'FastRoute\\Dispatcher\\RegexBasedAbstract' => $vendorDir . '/nikic/fast-route/src/Dispatcher/RegexBasedAbstract.php',
    'FastRoute\\Route' => $vendorDir . '/nikic/fast-route/src/Route.php',
    'FastRoute\\RouteCollector' => $vendorDir . '/nikic/fast-route/src/RouteCollector.php',
    'FastRoute\\RouteParser' => $vendorDir . '/nikic/fast-route/src/RouteParser.php',
    'FastRoute\\RouteParser\\Std' => $vendorDir . '/nikic/fast-route/src/RouteParser/Std.php',
    'Fig\\Http\\Message\\RequestMethodInterface' => $vendorDir . '/fig/http-message-util/src/RequestMethodInterface.php',
    'Fig\\Http\\Message\\StatusCodeInterface' => $vendorDir . '/fig/http-message-util/src/StatusCodeInterface.php',
    'JsonException' => $vendorDir . '/symfony/polyfill-php73/Resources/stubs/JsonException.php',
    'PDF417' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/pdf417.php',
    'ParagonIE\\ConstantTime\\Base32' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32.php',
    'ParagonIE\\ConstantTime\\Base32Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32Hex.php',
    'ParagonIE\\ConstantTime\\Base64' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64.php',
    'ParagonIE\\ConstantTime\\Base64DotSlash' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlash.php',
    'ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
    'ParagonIE\\ConstantTime\\Base64UrlSafe' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
    'ParagonIE\\ConstantTime\\Binary' => $vendorDir . '/paragonie/constant_time_encoding/src/Binary.php',
    'ParagonIE\\ConstantTime\\EncoderInterface' => $vendorDir . '/paragonie/constant_time_encoding/src/EncoderInterface.php',
    'ParagonIE\\ConstantTime\\Encoding' => $vendorDir . '/paragonie/constant_time_encoding/src/Encoding.php',
    'ParagonIE\\ConstantTime\\Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Hex.php',
    'ParagonIE\\ConstantTime\\RFC4648' => $vendorDir . '/paragonie/constant_time_encoding/src/RFC4648.php',
    'PhpMyAdmin\\Advisor' => $baseDir . '/libraries/classes/Advisor.php',
    'PhpMyAdmin\\Bookmark' => $baseDir . '/libraries/classes/Bookmark.php',
    'PhpMyAdmin\\BrowseForeigners' => $baseDir . '/libraries/classes/BrowseForeigners.php',
    'PhpMyAdmin\\Cache' => $baseDir . '/libraries/classes/Cache.php',
    'PhpMyAdmin\\Charsets' => $baseDir . '/libraries/classes/Charsets.php',
    'PhpMyAdmin\\Charsets\\Charset' => $baseDir . '/libraries/classes/Charsets/Charset.php',
    'PhpMyAdmin\\Charsets\\Collation' => $baseDir . '/libraries/classes/Charsets/Collation.php',
    'PhpMyAdmin\\CheckUserPrivileges' => $baseDir . '/libraries/classes/CheckUserPrivileges.php',
    'PhpMyAdmin\\Command\\CacheWarmupCommand' => $baseDir . '/libraries/classes/Command/CacheWarmupCommand.php',
    'PhpMyAdmin\\Command\\FixPoTwigCommand' => $baseDir . '/libraries/classes/Command/FixPoTwigCommand.php',
    'PhpMyAdmin\\Command\\SetVersionCommand' => $baseDir . '/libraries/classes/Command/SetVersionCommand.php',
    'PhpMyAdmin\\Command\\TwigLintCommand' => $baseDir . '/libraries/classes/Command/TwigLintCommand.php',
    'PhpMyAdmin\\Command\\WriteGitRevisionCommand' => $baseDir . '/libraries/classes/Command/WriteGitRevisionCommand.php',
    'PhpMyAdmin\\Common' => $baseDir . '/libraries/classes/Common.php',
    'PhpMyAdmin\\Config' => $baseDir . '/libraries/classes/Config.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\BookmarkFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/BookmarkFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\BrowserTransformationFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/BrowserTransformationFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\CentralColumnsFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/CentralColumnsFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\ColumnCommentsFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/ColumnCommentsFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\ConfigurableMenusFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/ConfigurableMenusFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\DatabaseDesignerSettingsFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/DatabaseDesignerSettingsFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\DisplayFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/DisplayFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\ExportTemplatesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/ExportTemplatesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\FavoriteTablesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/FavoriteTablesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\NavigationItemsHidingFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/NavigationItemsHidingFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\PdfFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/PdfFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\RecentlyUsedTablesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/RecentlyUsedTablesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\RelationFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/RelationFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\SavedQueryByExampleSearchesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/SavedQueryByExampleSearchesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\SqlHistoryFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/SqlHistoryFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\TrackingFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/TrackingFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\UiPreferencesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/UiPreferencesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Features\\UserPreferencesFeature' => $baseDir . '/libraries/classes/ConfigStorage/Features/UserPreferencesFeature.php',
    'PhpMyAdmin\\ConfigStorage\\Relation' => $baseDir . '/libraries/classes/ConfigStorage/Relation.php',
    'PhpMyAdmin\\ConfigStorage\\RelationCleanup' => $baseDir . '/libraries/classes/ConfigStorage/RelationCleanup.php',
    'PhpMyAdmin\\ConfigStorage\\RelationParameters' => $baseDir . '/libraries/classes/ConfigStorage/RelationParameters.php',
    'PhpMyAdmin\\ConfigStorage\\UserGroups' => $baseDir . '/libraries/classes/ConfigStorage/UserGroups.php',
    'PhpMyAdmin\\Config\\ConfigFile' => $baseDir . '/libraries/classes/Config/ConfigFile.php',
    'PhpMyAdmin\\Config\\Descriptions' => $baseDir . '/libraries/classes/Config/Descriptions.php',
    'PhpMyAdmin\\Config\\Form' => $baseDir . '/libraries/classes/Config/Form.php',
    'PhpMyAdmin\\Config\\FormDisplay' => $baseDir . '/libraries/classes/Config/FormDisplay.php',
    'PhpMyAdmin\\Config\\FormDisplayTemplate' => $baseDir . '/libraries/classes/Config/FormDisplayTemplate.php',
    'PhpMyAdmin\\Config\\Forms\\BaseForm' => $baseDir . '/libraries/classes/Config/Forms/BaseForm.php',
    'PhpMyAdmin\\Config\\Forms\\BaseFormList' => $baseDir . '/libraries/classes/Config/Forms/BaseFormList.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\BrowseForm' => $baseDir . '/libraries/classes/Config/Forms/Page/BrowseForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\DbStructureForm' => $baseDir . '/libraries/classes/Config/Forms/Page/DbStructureForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\EditForm' => $baseDir . '/libraries/classes/Config/Forms/Page/EditForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\ExportForm' => $baseDir . '/libraries/classes/Config/Forms/Page/ExportForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\ImportForm' => $baseDir . '/libraries/classes/Config/Forms/Page/ImportForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\NaviForm' => $baseDir . '/libraries/classes/Config/Forms/Page/NaviForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\PageFormList' => $baseDir . '/libraries/classes/Config/Forms/Page/PageFormList.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\SqlForm' => $baseDir . '/libraries/classes/Config/Forms/Page/SqlForm.php',
    'PhpMyAdmin\\Config\\Forms\\Page\\TableStructureForm' => $baseDir . '/libraries/classes/Config/Forms/Page/TableStructureForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\ConfigForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/ConfigForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\ExportForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/ExportForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\FeaturesForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/FeaturesForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\ImportForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/ImportForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\MainForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/MainForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\NaviForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/NaviForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\ServersForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/ServersForm.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\SetupFormList' => $baseDir . '/libraries/classes/Config/Forms/Setup/SetupFormList.php',
    'PhpMyAdmin\\Config\\Forms\\Setup\\SqlForm' => $baseDir . '/libraries/classes/Config/Forms/Setup/SqlForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\ExportForm' => $baseDir . '/libraries/classes/Config/Forms/User/ExportForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\FeaturesForm' => $baseDir . '/libraries/classes/Config/Forms/User/FeaturesForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\ImportForm' => $baseDir . '/libraries/classes/Config/Forms/User/ImportForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\MainForm' => $baseDir . '/libraries/classes/Config/Forms/User/MainForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\NaviForm' => $baseDir . '/libraries/classes/Config/Forms/User/NaviForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\SqlForm' => $baseDir . '/libraries/classes/Config/Forms/User/SqlForm.php',
    'PhpMyAdmin\\Config\\Forms\\User\\UserFormList' => $baseDir . '/libraries/classes/Config/Forms/User/UserFormList.php',
    'PhpMyAdmin\\Config\\PageSettings' => $baseDir . '/libraries/classes/Config/PageSettings.php',
    'PhpMyAdmin\\Config\\ServerConfigChecks' => $baseDir . '/libraries/classes/Config/ServerConfigChecks.php',
    'PhpMyAdmin\\Config\\Settings' => $baseDir . '/libraries/classes/Config/Settings.php',
    'PhpMyAdmin\\Config\\Settings\\Console' => $baseDir . '/libraries/classes/Config/Settings/Console.php',
    'PhpMyAdmin\\Config\\Settings\\Debug' => $baseDir . '/libraries/classes/Config/Settings/Debug.php',
    'PhpMyAdmin\\Config\\Settings\\Export' => $baseDir . '/libraries/classes/Config/Settings/Export.php',
    'PhpMyAdmin\\Config\\Settings\\Import' => $baseDir . '/libraries/classes/Config/Settings/Import.php',
    'PhpMyAdmin\\Config\\Settings\\Schema' => $baseDir . '/libraries/classes/Config/Settings/Schema.php',
    'PhpMyAdmin\\Config\\Settings\\Server' => $baseDir . '/libraries/classes/Config/Settings/Server.php',
    'PhpMyAdmin\\Config\\Settings\\SqlQueryBox' => $baseDir . '/libraries/classes/Config/Settings/SqlQueryBox.php',
    'PhpMyAdmin\\Config\\Settings\\Transformations' => $baseDir . '/libraries/classes/Config/Settings/Transformations.php',
    'PhpMyAdmin\\Config\\SpecialSchemaLinks' => $baseDir . '/libraries/classes/Config/SpecialSchemaLinks.php',
    'PhpMyAdmin\\Config\\Validator' => $baseDir . '/libraries/classes/Config/Validator.php',
    'PhpMyAdmin\\Console' => $baseDir . '/libraries/classes/Console.php',
    'PhpMyAdmin\\Controllers\\AbstractController' => $baseDir . '/libraries/classes/Controllers/AbstractController.php',
    'PhpMyAdmin\\Controllers\\BrowseForeignersController' => $baseDir . '/libraries/classes/Controllers/BrowseForeignersController.php',
    'PhpMyAdmin\\Controllers\\ChangeLogController' => $baseDir . '/libraries/classes/Controllers/ChangeLogController.php',
    'PhpMyAdmin\\Controllers\\CheckRelationsController' => $baseDir . '/libraries/classes/Controllers/CheckRelationsController.php',
    'PhpMyAdmin\\Controllers\\CollationConnectionController' => $baseDir . '/libraries/classes/Controllers/CollationConnectionController.php',
    'PhpMyAdmin\\Controllers\\ColumnController' => $baseDir . '/libraries/classes/Controllers/ColumnController.php',
    'PhpMyAdmin\\Controllers\\Config\\GetConfigController' => $baseDir . '/libraries/classes/Controllers/Config/GetConfigController.php',
    'PhpMyAdmin\\Controllers\\Config\\SetConfigController' => $baseDir . '/libraries/classes/Controllers/Config/SetConfigController.php',
    'PhpMyAdmin\\Controllers\\DatabaseController' => $baseDir . '/libraries/classes/Controllers/DatabaseController.php',
    'PhpMyAdmin\\Controllers\\Database\\AbstractController' => $baseDir . '/libraries/classes/Controllers/Database/AbstractController.php',
    'PhpMyAdmin\\Controllers\\Database\\CentralColumnsController' => $baseDir . '/libraries/classes/Controllers/Database/CentralColumnsController.php',
    'PhpMyAdmin\\Controllers\\Database\\CentralColumns\\PopulateColumnsController' => $baseDir . '/libraries/classes/Controllers/Database/CentralColumns/PopulateColumnsController.php',
    'PhpMyAdmin\\Controllers\\Database\\DataDictionaryController' => $baseDir . '/libraries/classes/Controllers/Database/DataDictionaryController.php',
    'PhpMyAdmin\\Controllers\\Database\\DesignerController' => $baseDir . '/libraries/classes/Controllers/Database/DesignerController.php',
    'PhpMyAdmin\\Controllers\\Database\\EventsController' => $baseDir . '/libraries/classes/Controllers/Database/EventsController.php',
    'PhpMyAdmin\\Controllers\\Database\\ExportController' => $baseDir . '/libraries/classes/Controllers/Database/ExportController.php',
    'PhpMyAdmin\\Controllers\\Database\\ImportController' => $baseDir . '/libraries/classes/Controllers/Database/ImportController.php',
    'PhpMyAdmin\\Controllers\\Database\\MultiTableQueryController' => $baseDir . '/libraries/classes/Controllers/Database/MultiTableQueryController.php',
    'PhpMyAdmin\\Controllers\\Database\\MultiTableQuery\\QueryController' => $baseDir . '/libraries/classes/Controllers/Database/MultiTableQuery/QueryController.php',
    'PhpMyAdmin\\Controllers\\Database\\MultiTableQuery\\TablesController' => $baseDir . '/libraries/classes/Controllers/Database/MultiTableQuery/TablesController.php',
    'PhpMyAdmin\\Controllers\\Database\\OperationsController' => $baseDir . '/libraries/classes/Controllers/Database/OperationsController.php',
    'PhpMyAdmin\\Controllers\\Database\\Operations\\CollationController' => $baseDir . '/libraries/classes/Controllers/Database/Operations/CollationController.php',
    'PhpMyAdmin\\Controllers\\Database\\PrivilegesController' => $baseDir . '/libraries/classes/Controllers/Database/PrivilegesController.php',
    'PhpMyAdmin\\Controllers\\Database\\QueryByExampleController' => $baseDir . '/libraries/classes/Controllers/Database/QueryByExampleController.php',
    'PhpMyAdmin\\Controllers\\Database\\RoutinesController' => $baseDir . '/libraries/classes/Controllers/Database/RoutinesController.php',
    'PhpMyAdmin\\Controllers\\Database\\SearchController' => $baseDir . '/libraries/classes/Controllers/Database/SearchController.php',
    'PhpMyAdmin\\Controllers\\Database\\SqlAutoCompleteController' => $baseDir . '/libraries/classes/Controllers/Database/SqlAutoCompleteController.php',
    'PhpMyAdmin\\Controllers\\Database\\SqlController' => $baseDir . '/libraries/classes/Controllers/Database/SqlController.php',
    'PhpMyAdmin\\Controllers\\Database\\SqlFormatController' => $baseDir . '/libraries/classes/Controllers/Database/SqlFormatController.php',
    'PhpMyAdmin\\Controllers\\Database\\StructureController' => $baseDir . '/libraries/classes/Controllers/Database/StructureController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\AddPrefixController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/AddPrefixController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\AddPrefixTableController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/AddPrefixTableController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\AddController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CentralColumns/AddController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\MakeConsistentController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CentralColumns/MakeConsistentController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CentralColumns\\RemoveController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CentralColumns/RemoveController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\ChangePrefixFormController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/ChangePrefixFormController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyFormController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CopyFormController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyTableController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CopyTableController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\CopyTableWithPrefixController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/CopyTableWithPrefixController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\DropFormController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/DropFormController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\DropTableController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/DropTableController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\EmptyFormController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/EmptyFormController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\EmptyTableController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/EmptyTableController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\FavoriteTableController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/FavoriteTableController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\RealRowCountController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/RealRowCountController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\ReplacePrefixController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/ReplacePrefixController.php',
    'PhpMyAdmin\\Controllers\\Database\\Structure\\ShowCreateController' => $baseDir . '/libraries/classes/Controllers/Database/Structure/ShowCreateController.php',
    'PhpMyAdmin\\Controllers\\Database\\TrackingController' => $baseDir . '/libraries/classes/Controllers/Database/TrackingController.php',
    'PhpMyAdmin\\Controllers\\Database\\TriggersController' => $baseDir . '/libraries/classes/Controllers/Database/TriggersController.php',
    'PhpMyAdmin\\Controllers\\ErrorReportController' => $baseDir . '/libraries/classes/Controllers/ErrorReportController.php',
    'PhpMyAdmin\\Controllers\\Export\\CheckTimeOutController' => $baseDir . '/libraries/classes/Controllers/Export/CheckTimeOutController.php',
    'PhpMyAdmin\\Controllers\\Export\\ExportController' => $baseDir . '/libraries/classes/Controllers/Export/ExportController.php',
    'PhpMyAdmin\\Controllers\\Export\\TablesController' => $baseDir . '/libraries/classes/Controllers/Export/TablesController.php',
    'PhpMyAdmin\\Controllers\\Export\\Template\\CreateController' => $baseDir . '/libraries/classes/Controllers/Export/Template/CreateController.php',
    'PhpMyAdmin\\Controllers\\Export\\Template\\DeleteController' => $baseDir . '/libraries/classes/Controllers/Export/Template/DeleteController.php',
    'PhpMyAdmin\\Controllers\\Export\\Template\\LoadController' => $baseDir . '/libraries/classes/Controllers/Export/Template/LoadController.php',
    'PhpMyAdmin\\Controllers\\Export\\Template\\UpdateController' => $baseDir . '/libraries/classes/Controllers/Export/Template/UpdateController.php',
    'PhpMyAdmin\\Controllers\\GisDataEditorController' => $baseDir . '/libraries/classes/Controllers/GisDataEditorController.php',
    'PhpMyAdmin\\Controllers\\GitInfoController' => $baseDir . '/libraries/classes/Controllers/GitInfoController.php',
    'PhpMyAdmin\\Controllers\\HomeController' => $baseDir . '/libraries/classes/Controllers/HomeController.php',
    'PhpMyAdmin\\Controllers\\Import\\ImportController' => $baseDir . '/libraries/classes/Controllers/Import/ImportController.php',
    'PhpMyAdmin\\Controllers\\Import\\SimulateDmlController' => $baseDir . '/libraries/classes/Controllers/Import/SimulateDmlController.php',
    'PhpMyAdmin\\Controllers\\Import\\StatusController' => $baseDir . '/libraries/classes/Controllers/Import/StatusController.php',
    'PhpMyAdmin\\Controllers\\JavaScriptMessagesController' => $baseDir . '/libraries/classes/Controllers/JavaScriptMessagesController.php',
    'PhpMyAdmin\\Controllers\\LicenseController' => $baseDir . '/libraries/classes/Controllers/LicenseController.php',
    'PhpMyAdmin\\Controllers\\LintController' => $baseDir . '/libraries/classes/Controllers/LintController.php',
    'PhpMyAdmin\\Controllers\\LogoutController' => $baseDir . '/libraries/classes/Controllers/LogoutController.php',
    'PhpMyAdmin\\Controllers\\NavigationController' => $baseDir . '/libraries/classes/Controllers/NavigationController.php',
    'PhpMyAdmin\\Controllers\\NormalizationController' => $baseDir . '/libraries/classes/Controllers/NormalizationController.php',
    'PhpMyAdmin\\Controllers\\PhpInfoController' => $baseDir . '/libraries/classes/Controllers/PhpInfoController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\ExportController' => $baseDir . '/libraries/classes/Controllers/Preferences/ExportController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\FeaturesController' => $baseDir . '/libraries/classes/Controllers/Preferences/FeaturesController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\ImportController' => $baseDir . '/libraries/classes/Controllers/Preferences/ImportController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\MainPanelController' => $baseDir . '/libraries/classes/Controllers/Preferences/MainPanelController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\ManageController' => $baseDir . '/libraries/classes/Controllers/Preferences/ManageController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\NavigationController' => $baseDir . '/libraries/classes/Controllers/Preferences/NavigationController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\SqlController' => $baseDir . '/libraries/classes/Controllers/Preferences/SqlController.php',
    'PhpMyAdmin\\Controllers\\Preferences\\TwoFactorController' => $baseDir . '/libraries/classes/Controllers/Preferences/TwoFactorController.php',
    'PhpMyAdmin\\Controllers\\RecentTablesListController' => $baseDir . '/libraries/classes/Controllers/RecentTablesListController.php',
    'PhpMyAdmin\\Controllers\\SchemaExportController' => $baseDir . '/libraries/classes/Controllers/SchemaExportController.php',
    'PhpMyAdmin\\Controllers\\Server\\BinlogController' => $baseDir . '/libraries/classes/Controllers/Server/BinlogController.php',
    'PhpMyAdmin\\Controllers\\Server\\CollationsController' => $baseDir . '/libraries/classes/Controllers/Server/CollationsController.php',
    'PhpMyAdmin\\Controllers\\Server\\DatabasesController' => $baseDir . '/libraries/classes/Controllers/Server/DatabasesController.php',
    'PhpMyAdmin\\Controllers\\Server\\Databases\\CreateController' => $baseDir . '/libraries/classes/Controllers/Server/Databases/CreateController.php',
    'PhpMyAdmin\\Controllers\\Server\\Databases\\DestroyController' => $baseDir . '/libraries/classes/Controllers/Server/Databases/DestroyController.php',
    'PhpMyAdmin\\Controllers\\Server\\EnginesController' => $baseDir . '/libraries/classes/Controllers/Server/EnginesController.php',
    'PhpMyAdmin\\Controllers\\Server\\ExportController' => $baseDir . '/libraries/classes/Controllers/Server/ExportController.php',
    'PhpMyAdmin\\Controllers\\Server\\ImportController' => $baseDir . '/libraries/classes/Controllers/Server/ImportController.php',
    'PhpMyAdmin\\Controllers\\Server\\PluginsController' => $baseDir . '/libraries/classes/Controllers/Server/PluginsController.php',
    'PhpMyAdmin\\Controllers\\Server\\PrivilegesController' => $baseDir . '/libraries/classes/Controllers/Server/PrivilegesController.php',
    'PhpMyAdmin\\Controllers\\Server\\Privileges\\AccountLockController' => $baseDir . '/libraries/classes/Controllers/Server/Privileges/AccountLockController.php',
    'PhpMyAdmin\\Controllers\\Server\\Privileges\\AccountUnlockController' => $baseDir . '/libraries/classes/Controllers/Server/Privileges/AccountUnlockController.php',
    'PhpMyAdmin\\Controllers\\Server\\ReplicationController' => $baseDir . '/libraries/classes/Controllers/Server/ReplicationController.php',
    'PhpMyAdmin\\Controllers\\Server\\ShowEngineController' => $baseDir . '/libraries/classes/Controllers/Server/ShowEngineController.php',
    'PhpMyAdmin\\Controllers\\Server\\SqlController' => $baseDir . '/libraries/classes/Controllers/Server/SqlController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\AbstractController' => $baseDir . '/libraries/classes/Controllers/Server/Status/AbstractController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\AdvisorController' => $baseDir . '/libraries/classes/Controllers/Server/Status/AdvisorController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\MonitorController' => $baseDir . '/libraries/classes/Controllers/Server/Status/MonitorController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\ChartingDataController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Monitor/ChartingDataController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\GeneralLogController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Monitor/GeneralLogController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\LogVarsController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Monitor/LogVarsController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\QueryAnalyzerController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Monitor/QueryAnalyzerController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Monitor\\SlowLogController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Monitor/SlowLogController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\ProcessesController' => $baseDir . '/libraries/classes/Controllers/Server/Status/ProcessesController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Processes\\KillController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Processes/KillController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\Processes\\RefreshController' => $baseDir . '/libraries/classes/Controllers/Server/Status/Processes/RefreshController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\QueriesController' => $baseDir . '/libraries/classes/Controllers/Server/Status/QueriesController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\StatusController' => $baseDir . '/libraries/classes/Controllers/Server/Status/StatusController.php',
    'PhpMyAdmin\\Controllers\\Server\\Status\\VariablesController' => $baseDir . '/libraries/classes/Controllers/Server/Status/VariablesController.php',
    'PhpMyAdmin\\Controllers\\Server\\UserGroupsController' => $baseDir . '/libraries/classes/Controllers/Server/UserGroupsController.php',
    'PhpMyAdmin\\Controllers\\Server\\UserGroupsFormController' => $baseDir . '/libraries/classes/Controllers/Server/UserGroupsFormController.php',
    'PhpMyAdmin\\Controllers\\Server\\VariablesController' => $baseDir . '/libraries/classes/Controllers/Server/VariablesController.php',
    'PhpMyAdmin\\Controllers\\Server\\Variables\\GetVariableController' => $baseDir . '/libraries/classes/Controllers/Server/Variables/GetVariableController.php',
    'PhpMyAdmin\\Controllers\\Server\\Variables\\SetVariableController' => $baseDir . '/libraries/classes/Controllers/Server/Variables/SetVariableController.php',
    'PhpMyAdmin\\Controllers\\Setup\\AbstractController' => $baseDir . '/libraries/classes/Controllers/Setup/AbstractController.php',
    'PhpMyAdmin\\Controllers\\Setup\\ConfigController' => $baseDir . '/libraries/classes/Controllers/Setup/ConfigController.php',
    'PhpMyAdmin\\Controllers\\Setup\\FormController' => $baseDir . '/libraries/classes/Controllers/Setup/FormController.php',
    'PhpMyAdmin\\Controllers\\Setup\\HomeController' => $baseDir . '/libraries/classes/Controllers/Setup/HomeController.php',
    'PhpMyAdmin\\Controllers\\Setup\\ServersController' => $baseDir . '/libraries/classes/Controllers/Setup/ServersController.php',
    'PhpMyAdmin\\Controllers\\Sql\\ColumnPreferencesController' => $baseDir . '/libraries/classes/Controllers/Sql/ColumnPreferencesController.php',
    'PhpMyAdmin\\Controllers\\Sql\\DefaultForeignKeyCheckValueController' => $baseDir . '/libraries/classes/Controllers/Sql/DefaultForeignKeyCheckValueController.php',
    'PhpMyAdmin\\Controllers\\Sql\\EnumValuesController' => $baseDir . '/libraries/classes/Controllers/Sql/EnumValuesController.php',
    'PhpMyAdmin\\Controllers\\Sql\\RelationalValuesController' => $baseDir . '/libraries/classes/Controllers/Sql/RelationalValuesController.php',
    'PhpMyAdmin\\Controllers\\Sql\\SetValuesController' => $baseDir . '/libraries/classes/Controllers/Sql/SetValuesController.php',
    'PhpMyAdmin\\Controllers\\Sql\\SqlController' => $baseDir . '/libraries/classes/Controllers/Sql/SqlController.php',
    'PhpMyAdmin\\Controllers\\TableController' => $baseDir . '/libraries/classes/Controllers/TableController.php',
    'PhpMyAdmin\\Controllers\\Table\\AbstractController' => $baseDir . '/libraries/classes/Controllers/Table/AbstractController.php',
    'PhpMyAdmin\\Controllers\\Table\\AddFieldController' => $baseDir . '/libraries/classes/Controllers/Table/AddFieldController.php',
    'PhpMyAdmin\\Controllers\\Table\\ChangeController' => $baseDir . '/libraries/classes/Controllers/Table/ChangeController.php',
    'PhpMyAdmin\\Controllers\\Table\\ChangeRowsController' => $baseDir . '/libraries/classes/Controllers/Table/ChangeRowsController.php',
    'PhpMyAdmin\\Controllers\\Table\\ChartController' => $baseDir . '/libraries/classes/Controllers/Table/ChartController.php',
    'PhpMyAdmin\\Controllers\\Table\\CreateController' => $baseDir . '/libraries/classes/Controllers/Table/CreateController.php',
    'PhpMyAdmin\\Controllers\\Table\\DeleteConfirmController' => $baseDir . '/libraries/classes/Controllers/Table/DeleteConfirmController.php',
    'PhpMyAdmin\\Controllers\\Table\\DeleteRowsController' => $baseDir . '/libraries/classes/Controllers/Table/DeleteRowsController.php',
    'PhpMyAdmin\\Controllers\\Table\\DropColumnConfirmationController' => $baseDir . '/libraries/classes/Controllers/Table/DropColumnConfirmationController.php',
    'PhpMyAdmin\\Controllers\\Table\\DropColumnController' => $baseDir . '/libraries/classes/Controllers/Table/DropColumnController.php',
    'PhpMyAdmin\\Controllers\\Table\\ExportController' => $baseDir . '/libraries/classes/Controllers/Table/ExportController.php',
    'PhpMyAdmin\\Controllers\\Table\\ExportRowsController' => $baseDir . '/libraries/classes/Controllers/Table/ExportRowsController.php',
    'PhpMyAdmin\\Controllers\\Table\\FindReplaceController' => $baseDir . '/libraries/classes/Controllers/Table/FindReplaceController.php',
    'PhpMyAdmin\\Controllers\\Table\\GetFieldController' => $baseDir . '/libraries/classes/Controllers/Table/GetFieldController.php',
    'PhpMyAdmin\\Controllers\\Table\\GisVisualizationController' => $baseDir . '/libraries/classes/Controllers/Table/GisVisualizationController.php',
    'PhpMyAdmin\\Controllers\\Table\\ImportController' => $baseDir . '/libraries/classes/Controllers/Table/ImportController.php',
    'PhpMyAdmin\\Controllers\\Table\\IndexRenameController' => $baseDir . '/libraries/classes/Controllers/Table/IndexRenameController.php',
    'PhpMyAdmin\\Controllers\\Table\\IndexesController' => $baseDir . '/libraries/classes/Controllers/Table/IndexesController.php',
    'PhpMyAdmin\\Controllers\\Table\\Maintenance\\AnalyzeController' => $baseDir . '/libraries/classes/Controllers/Table/Maintenance/AnalyzeController.php',
    'PhpMyAdmin\\Controllers\\Table\\Maintenance\\CheckController' => $baseDir . '/libraries/classes/Controllers/Table/Maintenance/CheckController.php',
    'PhpMyAdmin\\Controllers\\Table\\Maintenance\\ChecksumController' => $baseDir . '/libraries/classes/Controllers/Table/Maintenance/ChecksumController.php',
    'PhpMyAdmin\\Controllers\\Table\\Maintenance\\OptimizeController' => $baseDir . '/libraries/classes/Controllers/Table/Maintenance/OptimizeController.php',
    'PhpMyAdmin\\Controllers\\Table\\Maintenance\\RepairController' => $baseDir . '/libraries/classes/Controllers/Table/Maintenance/RepairController.php',
    'PhpMyAdmin\\Controllers\\Table\\OperationsController' => $baseDir . '/libraries/classes/Controllers/Table/OperationsController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\AnalyzeController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/AnalyzeController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\CheckController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/CheckController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\DropController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/DropController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\OptimizeController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/OptimizeController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\RebuildController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/RebuildController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\RepairController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/RepairController.php',
    'PhpMyAdmin\\Controllers\\Table\\Partition\\TruncateController' => $baseDir . '/libraries/classes/Controllers/Table/Partition/TruncateController.php',
    'PhpMyAdmin\\Controllers\\Table\\PrivilegesController' => $baseDir . '/libraries/classes/Controllers/Table/PrivilegesController.php',
    'PhpMyAdmin\\Controllers\\Table\\RecentFavoriteController' => $baseDir . '/libraries/classes/Controllers/Table/RecentFavoriteController.php',
    'PhpMyAdmin\\Controllers\\Table\\RelationController' => $baseDir . '/libraries/classes/Controllers/Table/RelationController.php',
    'PhpMyAdmin\\Controllers\\Table\\ReplaceController' => $baseDir . '/libraries/classes/Controllers/Table/ReplaceController.php',
    'PhpMyAdmin\\Controllers\\Table\\SearchController' => $baseDir . '/libraries/classes/Controllers/Table/SearchController.php',
    'PhpMyAdmin\\Controllers\\Table\\SqlController' => $baseDir . '/libraries/classes/Controllers/Table/SqlController.php',
    'PhpMyAdmin\\Controllers\\Table\\StructureController' => $baseDir . '/libraries/classes/Controllers/Table/StructureController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\AddIndexController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/AddIndexController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\AddKeyController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/AddKeyController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\BrowseController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/BrowseController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\CentralColumnsAddController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/CentralColumnsAddController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\CentralColumnsRemoveController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/CentralColumnsRemoveController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\ChangeController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/ChangeController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\FulltextController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/FulltextController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\MoveColumnsController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/MoveColumnsController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\PartitioningController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/PartitioningController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\PrimaryController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/PrimaryController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\ReservedWordCheckController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/ReservedWordCheckController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\SaveController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/SaveController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\SpatialController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/SpatialController.php',
    'PhpMyAdmin\\Controllers\\Table\\Structure\\UniqueController' => $baseDir . '/libraries/classes/Controllers/Table/Structure/UniqueController.php',
    'PhpMyAdmin\\Controllers\\Table\\TrackingController' => $baseDir . '/libraries/classes/Controllers/Table/TrackingController.php',
    'PhpMyAdmin\\Controllers\\Table\\TriggersController' => $baseDir . '/libraries/classes/Controllers/Table/TriggersController.php',
    'PhpMyAdmin\\Controllers\\Table\\ZoomSearchController' => $baseDir . '/libraries/classes/Controllers/Table/ZoomSearchController.php',
    'PhpMyAdmin\\Controllers\\ThemeSetController' => $baseDir . '/libraries/classes/Controllers/ThemeSetController.php',
    'PhpMyAdmin\\Controllers\\ThemesController' => $baseDir . '/libraries/classes/Controllers/ThemesController.php',
    'PhpMyAdmin\\Controllers\\Transformation\\OverviewController' => $baseDir . '/libraries/classes/Controllers/Transformation/OverviewController.php',
    'PhpMyAdmin\\Controllers\\Transformation\\WrapperController' => $baseDir . '/libraries/classes/Controllers/Transformation/WrapperController.php',
    'PhpMyAdmin\\Controllers\\UserPasswordController' => $baseDir . '/libraries/classes/Controllers/UserPasswordController.php',
    'PhpMyAdmin\\Controllers\\VersionCheckController' => $baseDir . '/libraries/classes/Controllers/VersionCheckController.php',
    'PhpMyAdmin\\Controllers\\View\\CreateController' => $baseDir . '/libraries/classes/Controllers/View/CreateController.php',
    'PhpMyAdmin\\Controllers\\View\\OperationsController' => $baseDir . '/libraries/classes/Controllers/View/OperationsController.php',
    'PhpMyAdmin\\Core' => $baseDir . '/libraries/classes/Core.php',
    'PhpMyAdmin\\CreateAddField' => $baseDir . '/libraries/classes/CreateAddField.php',
    'PhpMyAdmin\\Crypto\\Crypto' => $baseDir . '/libraries/classes/Crypto/Crypto.php',
    'PhpMyAdmin\\DatabaseInterface' => $baseDir . '/libraries/classes/DatabaseInterface.php',
    'PhpMyAdmin\\Database\\CentralColumns' => $baseDir . '/libraries/classes/Database/CentralColumns.php',
    'PhpMyAdmin\\Database\\DatabaseList' => $baseDir . '/libraries/classes/Database/DatabaseList.php',
    'PhpMyAdmin\\Database\\Designer' => $baseDir . '/libraries/classes/Database/Designer.php',
    'PhpMyAdmin\\Database\\Designer\\Common' => $baseDir . '/libraries/classes/Database/Designer/Common.php',
    'PhpMyAdmin\\Database\\Designer\\DesignerTable' => $baseDir . '/libraries/classes/Database/Designer/DesignerTable.php',
    'PhpMyAdmin\\Database\\Events' => $baseDir . '/libraries/classes/Database/Events.php',
    'PhpMyAdmin\\Database\\MultiTableQuery' => $baseDir . '/libraries/classes/Database/MultiTableQuery.php',
    'PhpMyAdmin\\Database\\Qbe' => $baseDir . '/libraries/classes/Database/Qbe.php',
    'PhpMyAdmin\\Database\\Routines' => $baseDir . '/libraries/classes/Database/Routines.php',
    'PhpMyAdmin\\Database\\Search' => $baseDir . '/libraries/classes/Database/Search.php',
    'PhpMyAdmin\\Database\\Triggers' => $baseDir . '/libraries/classes/Database/Triggers.php',
    'PhpMyAdmin\\DbTableExists' => $baseDir . '/libraries/classes/DbTableExists.php',
    'PhpMyAdmin\\Dbal\\DatabaseName' => $baseDir . '/libraries/classes/Dbal/DatabaseName.php',
    'PhpMyAdmin\\Dbal\\DbalInterface' => $baseDir . '/libraries/classes/Dbal/DbalInterface.php',
    'PhpMyAdmin\\Dbal\\DbiExtension' => $baseDir . '/libraries/classes/Dbal/DbiExtension.php',
    'PhpMyAdmin\\Dbal\\DbiMysqli' => $baseDir . '/libraries/classes/Dbal/DbiMysqli.php',
    'PhpMyAdmin\\Dbal\\MysqliResult' => $baseDir . '/libraries/classes/Dbal/MysqliResult.php',
    'PhpMyAdmin\\Dbal\\ResultInterface' => $baseDir . '/libraries/classes/Dbal/ResultInterface.php',
    'PhpMyAdmin\\Dbal\\TableName' => $baseDir . '/libraries/classes/Dbal/TableName.php',
    'PhpMyAdmin\\Dbal\\Warning' => $baseDir . '/libraries/classes/Dbal/Warning.php',
    'PhpMyAdmin\\Display\\Results' => $baseDir . '/libraries/classes/Display/Results.php',
    'PhpMyAdmin\\Encoding' => $baseDir . '/libraries/classes/Encoding.php',
    'PhpMyAdmin\\Engines\\Bdb' => $baseDir . '/libraries/classes/Engines/Bdb.php',
    'PhpMyAdmin\\Engines\\Berkeleydb' => $baseDir . '/libraries/classes/Engines/Berkeleydb.php',
    'PhpMyAdmin\\Engines\\Binlog' => $baseDir . '/libraries/classes/Engines/Binlog.php',
    'PhpMyAdmin\\Engines\\Innobase' => $baseDir . '/libraries/classes/Engines/Innobase.php',
    'PhpMyAdmin\\Engines\\Innodb' => $baseDir . '/libraries/classes/Engines/Innodb.php',
    'PhpMyAdmin\\Engines\\Memory' => $baseDir . '/libraries/classes/Engines/Memory.php',
    'PhpMyAdmin\\Engines\\Merge' => $baseDir . '/libraries/classes/Engines/Merge.php',
    'PhpMyAdmin\\Engines\\MrgMyisam' => $baseDir . '/libraries/classes/Engines/MrgMyisam.php',
    'PhpMyAdmin\\Engines\\Myisam' => $baseDir . '/libraries/classes/Engines/Myisam.php',
    'PhpMyAdmin\\Engines\\Ndbcluster' => $baseDir . '/libraries/classes/Engines/Ndbcluster.php',
    'PhpMyAdmin\\Engines\\Pbxt' => $baseDir . '/libraries/classes/Engines/Pbxt.php',
    'PhpMyAdmin\\Engines\\PerformanceSchema' => $baseDir . '/libraries/classes/Engines/PerformanceSchema.php',
    'PhpMyAdmin\\Error' => $baseDir . '/libraries/classes/Error.php',
    'PhpMyAdmin\\ErrorHandler' => $baseDir . '/libraries/classes/ErrorHandler.php',
    'PhpMyAdmin\\ErrorReport' => $baseDir . '/libraries/classes/ErrorReport.php',
    'PhpMyAdmin\\Exceptions\\ExportException' => $baseDir . '/libraries/classes/Exceptions/ExportException.php',
    'PhpMyAdmin\\Export' => $baseDir . '/libraries/classes/Export.php',
    'PhpMyAdmin\\Export\\Options' => $baseDir . '/libraries/classes/Export/Options.php',
    'PhpMyAdmin\\Export\\Template' => $baseDir . '/libraries/classes/Export/Template.php',
    'PhpMyAdmin\\Export\\TemplateModel' => $baseDir . '/libraries/classes/Export/TemplateModel.php',
    'PhpMyAdmin\\FieldMetadata' => $baseDir . '/libraries/classes/FieldMetadata.php',
    'PhpMyAdmin\\File' => $baseDir . '/libraries/classes/File.php',
    'PhpMyAdmin\\FileListing' => $baseDir . '/libraries/classes/FileListing.php',
    'PhpMyAdmin\\FlashMessages' => $baseDir . '/libraries/classes/FlashMessages.php',
    'PhpMyAdmin\\Font' => $baseDir . '/libraries/classes/Font.php',
    'PhpMyAdmin\\Footer' => $baseDir . '/libraries/classes/Footer.php',
    'PhpMyAdmin\\Gis\\GisFactory' => $baseDir . '/libraries/classes/Gis/GisFactory.php',
    'PhpMyAdmin\\Gis\\GisGeometry' => $baseDir . '/libraries/classes/Gis/GisGeometry.php',
    'PhpMyAdmin\\Gis\\GisGeometryCollection' => $baseDir . '/libraries/classes/Gis/GisGeometryCollection.php',
    'PhpMyAdmin\\Gis\\GisLineString' => $baseDir . '/libraries/classes/Gis/GisLineString.php',
    'PhpMyAdmin\\Gis\\GisMultiLineString' => $baseDir . '/libraries/classes/Gis/GisMultiLineString.php',
    'PhpMyAdmin\\Gis\\GisMultiPoint' => $baseDir . '/libraries/classes/Gis/GisMultiPoint.php',
    'PhpMyAdmin\\Gis\\GisMultiPolygon' => $baseDir . '/libraries/classes/Gis/GisMultiPolygon.php',
    'PhpMyAdmin\\Gis\\GisPoint' => $baseDir . '/libraries/classes/Gis/GisPoint.php',
    'PhpMyAdmin\\Gis\\GisPolygon' => $baseDir . '/libraries/classes/Gis/GisPolygon.php',
    'PhpMyAdmin\\Gis\\GisVisualization' => $baseDir . '/libraries/classes/Gis/GisVisualization.php',
    'PhpMyAdmin\\Git' => $baseDir . '/libraries/classes/Git.php',
    'PhpMyAdmin\\Header' => $baseDir . '/libraries/classes/Header.php',
    'PhpMyAdmin\\Html\\Generator' => $baseDir . '/libraries/classes/Html/Generator.php',
    'PhpMyAdmin\\Html\\MySQLDocumentation' => $baseDir . '/libraries/classes/Html/MySQLDocumentation.php',
    'PhpMyAdmin\\Http\\Factory\\ServerRequestFactory' => $baseDir . '/libraries/classes/Http/Factory/ServerRequestFactory.php',
    'PhpMyAdmin\\Http\\ServerRequest' => $baseDir . '/libraries/classes/Http/ServerRequest.php',
    'PhpMyAdmin\\Image\\ImageWrapper' => $baseDir . '/libraries/classes/Image/ImageWrapper.php',
    'PhpMyAdmin\\Import' => $baseDir . '/libraries/classes/Import.php',
    'PhpMyAdmin\\Import\\Ajax' => $baseDir . '/libraries/classes/Import/Ajax.php',
    'PhpMyAdmin\\Import\\SimulateDml' => $baseDir . '/libraries/classes/Import/SimulateDml.php',
    'PhpMyAdmin\\Index' => $baseDir . '/libraries/classes/Index.php',
    'PhpMyAdmin\\IndexColumn' => $baseDir . '/libraries/classes/IndexColumn.php',
    'PhpMyAdmin\\InsertEdit' => $baseDir . '/libraries/classes/InsertEdit.php',
    'PhpMyAdmin\\InternalRelations' => $baseDir . '/libraries/classes/InternalRelations.php',
    'PhpMyAdmin\\IpAllowDeny' => $baseDir . '/libraries/classes/IpAllowDeny.php',
    'PhpMyAdmin\\Language' => $baseDir . '/libraries/classes/Language.php',
    'PhpMyAdmin\\LanguageManager' => $baseDir . '/libraries/classes/LanguageManager.php',
    'PhpMyAdmin\\Linter' => $baseDir . '/libraries/classes/Linter.php',
    'PhpMyAdmin\\ListAbstract' => $baseDir . '/libraries/classes/ListAbstract.php',
    'PhpMyAdmin\\ListDatabase' => $baseDir . '/libraries/classes/ListDatabase.php',
    'PhpMyAdmin\\Logging' => $baseDir . '/libraries/classes/Logging.php',
    'PhpMyAdmin\\Menu' => $baseDir . '/libraries/classes/Menu.php',
    'PhpMyAdmin\\Message' => $baseDir . '/libraries/classes/Message.php',
    'PhpMyAdmin\\Mime' => $baseDir . '/libraries/classes/Mime.php',
    'PhpMyAdmin\\MoTranslator\\CacheException' => $vendorDir . '/phpmyadmin/motranslator/src/CacheException.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\ApcuCache' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/ApcuCache.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\ApcuCacheFactory' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/ApcuCacheFactory.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\CacheFactoryInterface' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/CacheFactoryInterface.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\CacheInterface' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/CacheInterface.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\GetAllInterface' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/GetAllInterface.php',
    'PhpMyAdmin\\MoTranslator\\Cache\\InMemoryCache' => $vendorDir . '/phpmyadmin/motranslator/src/Cache/InMemoryCache.php',
    'PhpMyAdmin\\MoTranslator\\Loader' => $vendorDir . '/phpmyadmin/motranslator/src/Loader.php',
    'PhpMyAdmin\\MoTranslator\\MoParser' => $vendorDir . '/phpmyadmin/motranslator/src/MoParser.php',
    'PhpMyAdmin\\MoTranslator\\ReaderException' => $vendorDir . '/phpmyadmin/motranslator/src/ReaderException.php',
    'PhpMyAdmin\\MoTranslator\\StringReader' => $vendorDir . '/phpmyadmin/motranslator/src/StringReader.php',
    'PhpMyAdmin\\MoTranslator\\Translator' => $vendorDir . '/phpmyadmin/motranslator/src/Translator.php',
    'PhpMyAdmin\\Navigation\\Navigation' => $baseDir . '/libraries/classes/Navigation/Navigation.php',
    'PhpMyAdmin\\Navigation\\NavigationTree' => $baseDir . '/libraries/classes/Navigation/NavigationTree.php',
    'PhpMyAdmin\\Navigation\\NodeFactory' => $baseDir . '/libraries/classes/Navigation/NodeFactory.php',
    'PhpMyAdmin\\Navigation\\Nodes\\Node' => $baseDir . '/libraries/classes/Navigation/Nodes/Node.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeColumn' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeColumn.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeColumnContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeColumnContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabase' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeDatabase.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseChild' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeDatabaseChild.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseChildContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeDatabaseChildContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeDatabaseContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeDatabaseContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeEvent' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeEvent.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeEventContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeEventContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeFunction' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeFunction.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeFunctionContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeFunctionContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeIndex' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeIndex.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeIndexContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeIndexContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeProcedure' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeProcedure.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeProcedureContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeProcedureContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeTable' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeTable.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeTableContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeTableContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeTrigger' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeTrigger.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeTriggerContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeTriggerContainer.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeView' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeView.php',
    'PhpMyAdmin\\Navigation\\Nodes\\NodeViewContainer' => $baseDir . '/libraries/classes/Navigation/Nodes/NodeViewContainer.php',
    'PhpMyAdmin\\Normalization' => $baseDir . '/libraries/classes/Normalization.php',
    'PhpMyAdmin\\OpenDocument' => $baseDir . '/libraries/classes/OpenDocument.php',
    'PhpMyAdmin\\Operations' => $baseDir . '/libraries/classes/Operations.php',
    'PhpMyAdmin\\OutputBuffering' => $baseDir . '/libraries/classes/OutputBuffering.php',
    'PhpMyAdmin\\ParseAnalyze' => $baseDir . '/libraries/classes/ParseAnalyze.php',
    'PhpMyAdmin\\Partitioning\\Maintenance' => $baseDir . '/libraries/classes/Partitioning/Maintenance.php',
    'PhpMyAdmin\\Partitioning\\Partition' => $baseDir . '/libraries/classes/Partitioning/Partition.php',
    'PhpMyAdmin\\Partitioning\\SubPartition' => $baseDir . '/libraries/classes/Partitioning/SubPartition.php',
    'PhpMyAdmin\\Partitioning\\TablePartitionDefinition' => $baseDir . '/libraries/classes/Partitioning/TablePartitionDefinition.php',
    'PhpMyAdmin\\Pdf' => $baseDir . '/libraries/classes/Pdf.php',
    'PhpMyAdmin\\Plugins' => $baseDir . '/libraries/classes/Plugins.php',
    'PhpMyAdmin\\Plugins\\Auth\\AuthenticationConfig' => $baseDir . '/libraries/classes/Plugins/Auth/AuthenticationConfig.php',
    'PhpMyAdmin\\Plugins\\Auth\\AuthenticationCookie' => $baseDir . '/libraries/classes/Plugins/Auth/AuthenticationCookie.php',
    'PhpMyAdmin\\Plugins\\Auth\\AuthenticationHttp' => $baseDir . '/libraries/classes/Plugins/Auth/AuthenticationHttp.php',
    'PhpMyAdmin\\Plugins\\Auth\\AuthenticationSignon' => $baseDir . '/libraries/classes/Plugins/Auth/AuthenticationSignon.php',
    'PhpMyAdmin\\Plugins\\AuthenticationPlugin' => $baseDir . '/libraries/classes/Plugins/AuthenticationPlugin.php',
    'PhpMyAdmin\\Plugins\\ExportPlugin' => $baseDir . '/libraries/classes/Plugins/ExportPlugin.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportCodegen' => $baseDir . '/libraries/classes/Plugins/Export/ExportCodegen.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportCsv' => $baseDir . '/libraries/classes/Plugins/Export/ExportCsv.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportExcel' => $baseDir . '/libraries/classes/Plugins/Export/ExportExcel.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportHtmlword' => $baseDir . '/libraries/classes/Plugins/Export/ExportHtmlword.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportJson' => $baseDir . '/libraries/classes/Plugins/Export/ExportJson.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportLatex' => $baseDir . '/libraries/classes/Plugins/Export/ExportLatex.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportMediawiki' => $baseDir . '/libraries/classes/Plugins/Export/ExportMediawiki.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportOds' => $baseDir . '/libraries/classes/Plugins/Export/ExportOds.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportOdt' => $baseDir . '/libraries/classes/Plugins/Export/ExportOdt.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportPdf' => $baseDir . '/libraries/classes/Plugins/Export/ExportPdf.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportPhparray' => $baseDir . '/libraries/classes/Plugins/Export/ExportPhparray.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportSql' => $baseDir . '/libraries/classes/Plugins/Export/ExportSql.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportTexytext' => $baseDir . '/libraries/classes/Plugins/Export/ExportTexytext.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportXml' => $baseDir . '/libraries/classes/Plugins/Export/ExportXml.php',
    'PhpMyAdmin\\Plugins\\Export\\ExportYaml' => $baseDir . '/libraries/classes/Plugins/Export/ExportYaml.php',
    'PhpMyAdmin\\Plugins\\Export\\Helpers\\Pdf' => $baseDir . '/libraries/classes/Plugins/Export/Helpers/Pdf.php',
    'PhpMyAdmin\\Plugins\\Export\\Helpers\\TableProperty' => $baseDir . '/libraries/classes/Plugins/Export/Helpers/TableProperty.php',
    'PhpMyAdmin\\Plugins\\IOTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/IOTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\ImportPlugin' => $baseDir . '/libraries/classes/Plugins/ImportPlugin.php',
    'PhpMyAdmin\\Plugins\\Import\\AbstractImportCsv' => $baseDir . '/libraries/classes/Plugins/Import/AbstractImportCsv.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportCsv' => $baseDir . '/libraries/classes/Plugins/Import/ImportCsv.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportLdi' => $baseDir . '/libraries/classes/Plugins/Import/ImportLdi.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportMediawiki' => $baseDir . '/libraries/classes/Plugins/Import/ImportMediawiki.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportOds' => $baseDir . '/libraries/classes/Plugins/Import/ImportOds.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportShp' => $baseDir . '/libraries/classes/Plugins/Import/ImportShp.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportSql' => $baseDir . '/libraries/classes/Plugins/Import/ImportSql.php',
    'PhpMyAdmin\\Plugins\\Import\\ImportXml' => $baseDir . '/libraries/classes/Plugins/Import/ImportXml.php',
    'PhpMyAdmin\\Plugins\\Import\\ShapeFileImport' => $baseDir . '/libraries/classes/Plugins/Import/ShapeFileImport.php',
    'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadNoplugin' => $baseDir . '/libraries/classes/Plugins/Import/Upload/UploadNoplugin.php',
    'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadProgress' => $baseDir . '/libraries/classes/Plugins/Import/Upload/UploadProgress.php',
    'PhpMyAdmin\\Plugins\\Import\\Upload\\UploadSession' => $baseDir . '/libraries/classes/Plugins/Import/Upload/UploadSession.php',
    'PhpMyAdmin\\Plugins\\Plugin' => $baseDir . '/libraries/classes/Plugins/Plugin.php',
    'PhpMyAdmin\\Plugins\\SchemaPlugin' => $baseDir . '/libraries/classes/Plugins/SchemaPlugin.php',
    'PhpMyAdmin\\Plugins\\Schema\\Dia\\Dia' => $baseDir . '/libraries/classes/Plugins/Schema/Dia/Dia.php',
    'PhpMyAdmin\\Plugins\\Schema\\Dia\\DiaRelationSchema' => $baseDir . '/libraries/classes/Plugins/Schema/Dia/DiaRelationSchema.php',
    'PhpMyAdmin\\Plugins\\Schema\\Dia\\RelationStatsDia' => $baseDir . '/libraries/classes/Plugins/Schema/Dia/RelationStatsDia.php',
    'PhpMyAdmin\\Plugins\\Schema\\Dia\\TableStatsDia' => $baseDir . '/libraries/classes/Plugins/Schema/Dia/TableStatsDia.php',
    'PhpMyAdmin\\Plugins\\Schema\\Eps\\Eps' => $baseDir . '/libraries/classes/Plugins/Schema/Eps/Eps.php',
    'PhpMyAdmin\\Plugins\\Schema\\Eps\\EpsRelationSchema' => $baseDir . '/libraries/classes/Plugins/Schema/Eps/EpsRelationSchema.php',
    'PhpMyAdmin\\Plugins\\Schema\\Eps\\RelationStatsEps' => $baseDir . '/libraries/classes/Plugins/Schema/Eps/RelationStatsEps.php',
    'PhpMyAdmin\\Plugins\\Schema\\Eps\\TableStatsEps' => $baseDir . '/libraries/classes/Plugins/Schema/Eps/TableStatsEps.php',
    'PhpMyAdmin\\Plugins\\Schema\\ExportRelationSchema' => $baseDir . '/libraries/classes/Plugins/Schema/ExportRelationSchema.php',
    'PhpMyAdmin\\Plugins\\Schema\\Pdf\\Pdf' => $baseDir . '/libraries/classes/Plugins/Schema/Pdf/Pdf.php',
    'PhpMyAdmin\\Plugins\\Schema\\Pdf\\PdfRelationSchema' => $baseDir . '/libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php',
    'PhpMyAdmin\\Plugins\\Schema\\Pdf\\RelationStatsPdf' => $baseDir . '/libraries/classes/Plugins/Schema/Pdf/RelationStatsPdf.php',
    'PhpMyAdmin\\Plugins\\Schema\\Pdf\\TableStatsPdf' => $baseDir . '/libraries/classes/Plugins/Schema/Pdf/TableStatsPdf.php',
    'PhpMyAdmin\\Plugins\\Schema\\RelationStats' => $baseDir . '/libraries/classes/Plugins/Schema/RelationStats.php',
    'PhpMyAdmin\\Plugins\\Schema\\SchemaDia' => $baseDir . '/libraries/classes/Plugins/Schema/SchemaDia.php',
    'PhpMyAdmin\\Plugins\\Schema\\SchemaEps' => $baseDir . '/libraries/classes/Plugins/Schema/SchemaEps.php',
    'PhpMyAdmin\\Plugins\\Schema\\SchemaPdf' => $baseDir . '/libraries/classes/Plugins/Schema/SchemaPdf.php',
    'PhpMyAdmin\\Plugins\\Schema\\SchemaSvg' => $baseDir . '/libraries/classes/Plugins/Schema/SchemaSvg.php',
    'PhpMyAdmin\\Plugins\\Schema\\Svg\\RelationStatsSvg' => $baseDir . '/libraries/classes/Plugins/Schema/Svg/RelationStatsSvg.php',
    'PhpMyAdmin\\Plugins\\Schema\\Svg\\Svg' => $baseDir . '/libraries/classes/Plugins/Schema/Svg/Svg.php',
    'PhpMyAdmin\\Plugins\\Schema\\Svg\\SvgRelationSchema' => $baseDir . '/libraries/classes/Plugins/Schema/Svg/SvgRelationSchema.php',
    'PhpMyAdmin\\Plugins\\Schema\\Svg\\TableStatsSvg' => $baseDir . '/libraries/classes/Plugins/Schema/Svg/TableStatsSvg.php',
    'PhpMyAdmin\\Plugins\\Schema\\TableStats' => $baseDir . '/libraries/classes/Plugins/Schema/TableStats.php',
    'PhpMyAdmin\\Plugins\\TransformationsInterface' => $baseDir . '/libraries/classes/Plugins/TransformationsInterface.php',
    'PhpMyAdmin\\Plugins\\TransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/TransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\Bool2TextTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/Bool2TextTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\CodeMirrorEditorTransformationPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/CodeMirrorEditorTransformationPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\DateFormatTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/DateFormatTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\DownloadTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/DownloadTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ExternalTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/ExternalTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\FormattedTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/FormattedTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\HexTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/HexTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ImageLinkTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/ImageLinkTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\ImageUploadTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/ImageUploadTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\InlineTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/InlineTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\LongToIPv4TransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/LongToIPv4TransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\PreApPendTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/PreApPendTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\RegexValidationTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/RegexValidationTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\SQLTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/SQLTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\SubstringTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/SubstringTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextFileUploadTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/TextFileUploadTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextImageLinkTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/TextImageLinkTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Abs\\TextLinkTransformationsPlugin' => $baseDir . '/libraries/classes/Plugins/Transformations/Abs/TextLinkTransformationsPlugin.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Image_JPEG_Upload' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Image_JPEG_Upload.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_FileUpload' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_FileUpload.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_Iptobinary' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptobinary.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_Iptolong' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptolong.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_JsonEditor' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_JsonEditor.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_RegexValidation' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_RegexValidation.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_SqlEditor' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_SqlEditor.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Input\\Text_Plain_XmlEditor' => $baseDir . '/libraries/classes/Plugins/Transformations/Input/Text_Plain_XmlEditor.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Application_Octetstream_Download' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Application_Octetstream_Download.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Application_Octetstream_Hex' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Application_Octetstream_Hex.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_JPEG_Inline' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Image_JPEG_Inline.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_JPEG_Link' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Image_JPEG_Link.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Image_PNG_Inline' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Image_PNG_Inline.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Octetstream_Sql' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Octetstream_Sql.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Binarytoip' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Binarytoip.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Bool2Text' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Bool2Text.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Dateformat' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Dateformat.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_External' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_External.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Formatted' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Formatted.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Imagelink' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Imagelink.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Json' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Json.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Sql' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Sql.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Output\\Text_Plain_Xml' => $baseDir . '/libraries/classes/Plugins/Transformations/Output/Text_Plain_Xml.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Link' => $baseDir . '/libraries/classes/Plugins/Transformations/Text_Plain_Link.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Longtoipv4' => $baseDir . '/libraries/classes/Plugins/Transformations/Text_Plain_Longtoipv4.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_PreApPend' => $baseDir . '/libraries/classes/Plugins/Transformations/Text_Plain_PreApPend.php',
    'PhpMyAdmin\\Plugins\\Transformations\\Text_Plain_Substring' => $baseDir . '/libraries/classes/Plugins/Transformations/Text_Plain_Substring.php',
    'PhpMyAdmin\\Plugins\\TwoFactorPlugin' => $baseDir . '/libraries/classes/Plugins/TwoFactorPlugin.php',
    'PhpMyAdmin\\Plugins\\TwoFactor\\Application' => $baseDir . '/libraries/classes/Plugins/TwoFactor/Application.php',
    'PhpMyAdmin\\Plugins\\TwoFactor\\Invalid' => $baseDir . '/libraries/classes/Plugins/TwoFactor/Invalid.php',
    'PhpMyAdmin\\Plugins\\TwoFactor\\Key' => $baseDir . '/libraries/classes/Plugins/TwoFactor/Key.php',
    'PhpMyAdmin\\Plugins\\TwoFactor\\Simple' => $baseDir . '/libraries/classes/Plugins/TwoFactor/Simple.php',
    'PhpMyAdmin\\Plugins\\UploadInterface' => $baseDir . '/libraries/classes/Plugins/UploadInterface.php',
    'PhpMyAdmin\\Profiling' => $baseDir . '/libraries/classes/Profiling.php',
    'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertyMainGroup' => $baseDir . '/libraries/classes/Properties/Options/Groups/OptionsPropertyMainGroup.php',
    'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertyRootGroup' => $baseDir . '/libraries/classes/Properties/Options/Groups/OptionsPropertyRootGroup.php',
    'PhpMyAdmin\\Properties\\Options\\Groups\\OptionsPropertySubgroup' => $baseDir . '/libraries/classes/Properties/Options/Groups/OptionsPropertySubgroup.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\BoolPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/BoolPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\DocPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/DocPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\HiddenPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/HiddenPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\MessageOnlyPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/MessageOnlyPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\NumberPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/NumberPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\RadioPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/RadioPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\SelectPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/SelectPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\Items\\TextPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/Items/TextPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\OptionsPropertyGroup' => $baseDir . '/libraries/classes/Properties/Options/OptionsPropertyGroup.php',
    'PhpMyAdmin\\Properties\\Options\\OptionsPropertyItem' => $baseDir . '/libraries/classes/Properties/Options/OptionsPropertyItem.php',
    'PhpMyAdmin\\Properties\\Options\\OptionsPropertyOneItem' => $baseDir . '/libraries/classes/Properties/Options/OptionsPropertyOneItem.php',
    'PhpMyAdmin\\Properties\\Plugins\\ExportPluginProperties' => $baseDir . '/libraries/classes/Properties/Plugins/ExportPluginProperties.php',
    'PhpMyAdmin\\Properties\\Plugins\\ImportPluginProperties' => $baseDir . '/libraries/classes/Properties/Plugins/ImportPluginProperties.php',
    'PhpMyAdmin\\Properties\\Plugins\\PluginPropertyItem' => $baseDir . '/libraries/classes/Properties/Plugins/PluginPropertyItem.php',
    'PhpMyAdmin\\Properties\\Plugins\\SchemaPluginProperties' => $baseDir . '/libraries/classes/Properties/Plugins/SchemaPluginProperties.php',
    'PhpMyAdmin\\Properties\\PropertyItem' => $baseDir . '/libraries/classes/Properties/PropertyItem.php',
    'PhpMyAdmin\\Providers\\ServerVariables\\MariaDbMySqlKbsProvider' => $baseDir . '/libraries/classes/Providers/ServerVariables/MariaDbMySqlKbsProvider.php',
    'PhpMyAdmin\\Providers\\ServerVariables\\ServerVariablesProvider' => $baseDir . '/libraries/classes/Providers/ServerVariables/ServerVariablesProvider.php',
    'PhpMyAdmin\\Providers\\ServerVariables\\ServerVariablesProviderInterface' => $baseDir . '/libraries/classes/Providers/ServerVariables/ServerVariablesProviderInterface.php',
    'PhpMyAdmin\\Providers\\ServerVariables\\VoidProvider' => $baseDir . '/libraries/classes/Providers/ServerVariables/VoidProvider.php',
    'PhpMyAdmin\\Query\\Cache' => $baseDir . '/libraries/classes/Query/Cache.php',
    'PhpMyAdmin\\Query\\Compatibility' => $baseDir . '/libraries/classes/Query/Compatibility.php',
    'PhpMyAdmin\\Query\\Generator' => $baseDir . '/libraries/classes/Query/Generator.php',
    'PhpMyAdmin\\Query\\Utilities' => $baseDir . '/libraries/classes/Query/Utilities.php',
    'PhpMyAdmin\\RecentFavoriteTable' => $baseDir . '/libraries/classes/RecentFavoriteTable.php',
    'PhpMyAdmin\\Replication' => $baseDir . '/libraries/classes/Replication.php',
    'PhpMyAdmin\\ReplicationGui' => $baseDir . '/libraries/classes/ReplicationGui.php',
    'PhpMyAdmin\\ReplicationInfo' => $baseDir . '/libraries/classes/ReplicationInfo.php',
    'PhpMyAdmin\\ResponseRenderer' => $baseDir . '/libraries/classes/ResponseRenderer.php',
    'PhpMyAdmin\\Routing' => $baseDir . '/libraries/classes/Routing.php',
    'PhpMyAdmin\\Sanitize' => $baseDir . '/libraries/classes/Sanitize.php',
    'PhpMyAdmin\\SavedSearches' => $baseDir . '/libraries/classes/SavedSearches.php',
    'PhpMyAdmin\\Scripts' => $baseDir . '/libraries/classes/Scripts.php',
    'PhpMyAdmin\\Server\\Plugin' => $baseDir . '/libraries/classes/Server/Plugin.php',
    'PhpMyAdmin\\Server\\Plugins' => $baseDir . '/libraries/classes/Server/Plugins.php',
    'PhpMyAdmin\\Server\\Privileges' => $baseDir . '/libraries/classes/Server/Privileges.php',
    'PhpMyAdmin\\Server\\Privileges\\AccountLocking' => $baseDir . '/libraries/classes/Server/Privileges/AccountLocking.php',
    'PhpMyAdmin\\Server\\Select' => $baseDir . '/libraries/classes/Server/Select.php',
    'PhpMyAdmin\\Server\\Status\\Data' => $baseDir . '/libraries/classes/Server/Status/Data.php',
    'PhpMyAdmin\\Server\\Status\\Monitor' => $baseDir . '/libraries/classes/Server/Status/Monitor.php',
    'PhpMyAdmin\\Server\\Status\\Processes' => $baseDir . '/libraries/classes/Server/Status/Processes.php',
    'PhpMyAdmin\\Server\\SysInfo\\Base' => $baseDir . '/libraries/classes/Server/SysInfo/Base.php',
    'PhpMyAdmin\\Server\\SysInfo\\Linux' => $baseDir . '/libraries/classes/Server/SysInfo/Linux.php',
    'PhpMyAdmin\\Server\\SysInfo\\SunOs' => $baseDir . '/libraries/classes/Server/SysInfo/SunOs.php',
    'PhpMyAdmin\\Server\\SysInfo\\SysInfo' => $baseDir . '/libraries/classes/Server/SysInfo/SysInfo.php',
    'PhpMyAdmin\\Server\\SysInfo\\WindowsNt' => $baseDir . '/libraries/classes/Server/SysInfo/WindowsNt.php',
    'PhpMyAdmin\\Session' => $baseDir . '/libraries/classes/Session.php',
    'PhpMyAdmin\\Setup\\ConfigGenerator' => $baseDir . '/libraries/classes/Setup/ConfigGenerator.php',
    'PhpMyAdmin\\Setup\\FormProcessing' => $baseDir . '/libraries/classes/Setup/FormProcessing.php',
    'PhpMyAdmin\\Setup\\Index' => $baseDir . '/libraries/classes/Setup/Index.php',
    'PhpMyAdmin\\ShapeFile\\ShapeFile' => $vendorDir . '/phpmyadmin/shapefile/src/ShapeFile.php',
    'PhpMyAdmin\\ShapeFile\\ShapeRecord' => $vendorDir . '/phpmyadmin/shapefile/src/ShapeRecord.php',
    'PhpMyAdmin\\ShapeFile\\Util' => $vendorDir . '/phpmyadmin/shapefile/src/Util.php',
    'PhpMyAdmin\\Sql' => $baseDir . '/libraries/classes/Sql.php',
    'PhpMyAdmin\\SqlParser\\Component' => $vendorDir . '/phpmyadmin/sql-parser/src/Component.php',
    'PhpMyAdmin\\SqlParser\\Components\\AlterOperation' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/AlterOperation.php',
    'PhpMyAdmin\\SqlParser\\Components\\Array2d' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Array2d.php',
    'PhpMyAdmin\\SqlParser\\Components\\ArrayObj' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/ArrayObj.php',
    'PhpMyAdmin\\SqlParser\\Components\\CaseExpression' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/CaseExpression.php',
    'PhpMyAdmin\\SqlParser\\Components\\Condition' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Condition.php',
    'PhpMyAdmin\\SqlParser\\Components\\CreateDefinition' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/CreateDefinition.php',
    'PhpMyAdmin\\SqlParser\\Components\\DataType' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/DataType.php',
    'PhpMyAdmin\\SqlParser\\Components\\Expression' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Expression.php',
    'PhpMyAdmin\\SqlParser\\Components\\ExpressionArray' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/ExpressionArray.php',
    'PhpMyAdmin\\SqlParser\\Components\\FunctionCall' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/FunctionCall.php',
    'PhpMyAdmin\\SqlParser\\Components\\GroupKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/GroupKeyword.php',
    'PhpMyAdmin\\SqlParser\\Components\\IndexHint' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/IndexHint.php',
    'PhpMyAdmin\\SqlParser\\Components\\IntoKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/IntoKeyword.php',
    'PhpMyAdmin\\SqlParser\\Components\\JoinKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/JoinKeyword.php',
    'PhpMyAdmin\\SqlParser\\Components\\Key' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Key.php',
    'PhpMyAdmin\\SqlParser\\Components\\Limit' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Limit.php',
    'PhpMyAdmin\\SqlParser\\Components\\LockExpression' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/LockExpression.php',
    'PhpMyAdmin\\SqlParser\\Components\\OptionsArray' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/OptionsArray.php',
    'PhpMyAdmin\\SqlParser\\Components\\OrderKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/OrderKeyword.php',
    'PhpMyAdmin\\SqlParser\\Components\\ParameterDefinition' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/ParameterDefinition.php',
    'PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/PartitionDefinition.php',
    'PhpMyAdmin\\SqlParser\\Components\\Reference' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/Reference.php',
    'PhpMyAdmin\\SqlParser\\Components\\RenameOperation' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/RenameOperation.php',
    'PhpMyAdmin\\SqlParser\\Components\\SetOperation' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/SetOperation.php',
    'PhpMyAdmin\\SqlParser\\Components\\UnionKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/UnionKeyword.php',
    'PhpMyAdmin\\SqlParser\\Components\\WithKeyword' => $vendorDir . '/phpmyadmin/sql-parser/src/Components/WithKeyword.php',
    'PhpMyAdmin\\SqlParser\\Context' => $vendorDir . '/phpmyadmin/sql-parser/src/Context.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100000' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100000.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100100' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100100.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100200' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100200.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100300' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100300.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100400' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100400.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100500' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100500.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMariaDb100600' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMariaDb100600.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50000' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50000.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50100' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50100.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50500' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50500.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50600' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50600.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql50700' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql50700.php',
    'PhpMyAdmin\\SqlParser\\Contexts\\ContextMySql80000' => $vendorDir . '/phpmyadmin/sql-parser/src/Contexts/ContextMySql80000.php',
    'PhpMyAdmin\\SqlParser\\Core' => $vendorDir . '/phpmyadmin/sql-parser/src/Core.php',
    'PhpMyAdmin\\SqlParser\\Exceptions\\LexerException' => $vendorDir . '/phpmyadmin/sql-parser/src/Exceptions/LexerException.php',
    'PhpMyAdmin\\SqlParser\\Exceptions\\LoaderException' => $vendorDir . '/phpmyadmin/sql-parser/src/Exceptions/LoaderException.php',
    'PhpMyAdmin\\SqlParser\\Exceptions\\ParserException' => $vendorDir . '/phpmyadmin/sql-parser/src/Exceptions/ParserException.php',
    'PhpMyAdmin\\SqlParser\\Lexer' => $vendorDir . '/phpmyadmin/sql-parser/src/Lexer.php',
    'PhpMyAdmin\\SqlParser\\Parser' => $vendorDir . '/phpmyadmin/sql-parser/src/Parser.php',
    'PhpMyAdmin\\SqlParser\\Statement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\AlterStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/AlterStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/AnalyzeStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\BackupStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/BackupStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\CallStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/CallStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\CheckStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/CheckStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/ChecksumStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\CreateStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/CreateStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/DeleteStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\DropStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/DropStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/ExplainStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\InsertStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/InsertStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\LoadStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/LoadStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\LockStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/LockStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\MaintenanceStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/MaintenanceStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\NotImplementedStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/NotImplementedStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/OptimizeStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/PurgeStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\RenameStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/RenameStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\RepairStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/RepairStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/ReplaceStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/RestoreStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\SelectStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/SelectStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\SetStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/SetStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\ShowStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/ShowStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/TransactionStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/TruncateStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/UpdateStatement.php',
    'PhpMyAdmin\\SqlParser\\Statements\\WithStatement' => $vendorDir . '/phpmyadmin/sql-parser/src/Statements/WithStatement.php',
    'PhpMyAdmin\\SqlParser\\Token' => $vendorDir . '/phpmyadmin/sql-parser/src/Token.php',
    'PhpMyAdmin\\SqlParser\\TokensList' => $vendorDir . '/phpmyadmin/sql-parser/src/TokensList.php',
    'PhpMyAdmin\\SqlParser\\Tools\\ContextGenerator' => $vendorDir . '/phpmyadmin/sql-parser/src/Tools/ContextGenerator.php',
    'PhpMyAdmin\\SqlParser\\Tools\\TestGenerator' => $vendorDir . '/phpmyadmin/sql-parser/src/Tools/TestGenerator.php',
    'PhpMyAdmin\\SqlParser\\Translator' => $vendorDir . '/phpmyadmin/sql-parser/src/Translator.php',
    'PhpMyAdmin\\SqlParser\\UtfString' => $vendorDir . '/phpmyadmin/sql-parser/src/UtfString.php',
    'PhpMyAdmin\\SqlParser\\Utils\\BufferedQuery' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/BufferedQuery.php',
    'PhpMyAdmin\\SqlParser\\Utils\\CLI' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/CLI.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Error' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Error.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Formatter' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Formatter.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Misc' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Misc.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Query' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Query.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Routine' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Routine.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Table' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Table.php',
    'PhpMyAdmin\\SqlParser\\Utils\\Tokens' => $vendorDir . '/phpmyadmin/sql-parser/src/Utils/Tokens.php',
    'PhpMyAdmin\\SqlQueryForm' => $baseDir . '/libraries/classes/SqlQueryForm.php',
    'PhpMyAdmin\\StorageEngine' => $baseDir . '/libraries/classes/StorageEngine.php',
    'PhpMyAdmin\\SystemDatabase' => $baseDir . '/libraries/classes/SystemDatabase.php',
    'PhpMyAdmin\\Table' => $baseDir . '/libraries/classes/Table.php',
    'PhpMyAdmin\\Table\\ColumnsDefinition' => $baseDir . '/libraries/classes/Table/ColumnsDefinition.php',
    'PhpMyAdmin\\Table\\Indexes' => $baseDir . '/libraries/classes/Table/Indexes.php',
    'PhpMyAdmin\\Table\\Maintenance' => $baseDir . '/libraries/classes/Table/Maintenance.php',
    'PhpMyAdmin\\Table\\Maintenance\\Message' => $baseDir . '/libraries/classes/Table/Maintenance/Message.php',
    'PhpMyAdmin\\Table\\Search' => $baseDir . '/libraries/classes/Table/Search.php',
    'PhpMyAdmin\\Template' => $baseDir . '/libraries/classes/Template.php',
    'PhpMyAdmin\\Theme' => $baseDir . '/libraries/classes/Theme.php',
    'PhpMyAdmin\\ThemeManager' => $baseDir . '/libraries/classes/ThemeManager.php',
    'PhpMyAdmin\\Tracker' => $baseDir . '/libraries/classes/Tracker.php',
    'PhpMyAdmin\\Tracking' => $baseDir . '/libraries/classes/Tracking.php',
    'PhpMyAdmin\\Transformations' => $baseDir . '/libraries/classes/Transformations.php',
    'PhpMyAdmin\\Twig\\AssetExtension' => $baseDir . '/libraries/classes/Twig/AssetExtension.php',
    'PhpMyAdmin\\Twig\\CoreExtension' => $baseDir . '/libraries/classes/Twig/CoreExtension.php',
    'PhpMyAdmin\\Twig\\Extensions\\I18nExtension' => $vendorDir . '/phpmyadmin/twig-i18n-extension/src/I18nExtension.php',
    'PhpMyAdmin\\Twig\\Extensions\\Node\\TransNode' => $vendorDir . '/phpmyadmin/twig-i18n-extension/src/Node/TransNode.php',
    'PhpMyAdmin\\Twig\\Extensions\\TokenParser\\TransTokenParser' => $vendorDir . '/phpmyadmin/twig-i18n-extension/src/TokenParser/TransTokenParser.php',
    'PhpMyAdmin\\Twig\\FlashMessagesExtension' => $baseDir . '/libraries/classes/Twig/FlashMessagesExtension.php',
    'PhpMyAdmin\\Twig\\I18nExtension' => $baseDir . '/libraries/classes/Twig/I18nExtension.php',
    'PhpMyAdmin\\Twig\\MessageExtension' => $baseDir . '/libraries/classes/Twig/MessageExtension.php',
    'PhpMyAdmin\\Twig\\RelationExtension' => $baseDir . '/libraries/classes/Twig/RelationExtension.php',
    'PhpMyAdmin\\Twig\\SanitizeExtension' => $baseDir . '/libraries/classes/Twig/SanitizeExtension.php',
    'PhpMyAdmin\\Twig\\TableExtension' => $baseDir . '/libraries/classes/Twig/TableExtension.php',
    'PhpMyAdmin\\Twig\\TrackerExtension' => $baseDir . '/libraries/classes/Twig/TrackerExtension.php',
    'PhpMyAdmin\\Twig\\TransformationsExtension' => $baseDir . '/libraries/classes/Twig/TransformationsExtension.php',
    'PhpMyAdmin\\Twig\\UrlExtension' => $baseDir . '/libraries/classes/Twig/UrlExtension.php',
    'PhpMyAdmin\\Twig\\UtilExtension' => $baseDir . '/libraries/classes/Twig/UtilExtension.php',
    'PhpMyAdmin\\TwoFactor' => $baseDir . '/libraries/classes/TwoFactor.php',
    'PhpMyAdmin\\Types' => $baseDir . '/libraries/classes/Types.php',
    'PhpMyAdmin\\Url' => $baseDir . '/libraries/classes/Url.php',
    'PhpMyAdmin\\UrlRedirector' => $baseDir . '/libraries/classes/UrlRedirector.php',
    'PhpMyAdmin\\UserPassword' => $baseDir . '/libraries/classes/UserPassword.php',
    'PhpMyAdmin\\UserPreferences' => $baseDir . '/libraries/classes/UserPreferences.php',
    'PhpMyAdmin\\Util' => $baseDir . '/libraries/classes/Util.php',
    'PhpMyAdmin\\Utils\\ForeignKey' => $baseDir . '/libraries/classes/Utils/ForeignKey.php',
    'PhpMyAdmin\\Utils\\FormatConverter' => $baseDir . '/libraries/classes/Utils/FormatConverter.php',
    'PhpMyAdmin\\Utils\\Gis' => $baseDir . '/libraries/classes/Utils/Gis.php',
    'PhpMyAdmin\\Utils\\HttpRequest' => $baseDir . '/libraries/classes/Utils/HttpRequest.php',
    'PhpMyAdmin\\Utils\\SessionCache' => $baseDir . '/libraries/classes/Utils/SessionCache.php',
    'PhpMyAdmin\\Version' => $baseDir . '/libraries/classes/Version.php',
    'PhpMyAdmin\\VersionInformation' => $baseDir . '/libraries/classes/VersionInformation.php',
    'PhpMyAdmin\\ZipExtension' => $baseDir . '/libraries/classes/ZipExtension.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'PragmaRX\\Google2FAQRCode\\Exceptions\\MissingQrCodeServiceException' => $vendorDir . '/pragmarx/google2fa-qrcode/src/Exceptions/MissingQrCodeServiceException.php',
    'PragmaRX\\Google2FAQRCode\\Google2FA' => $vendorDir . '/pragmarx/google2fa-qrcode/src/Google2FA.php',
    'PragmaRX\\Google2FAQRCode\\QRCode\\Bacon' => $vendorDir . '/pragmarx/google2fa-qrcode/src/QRCode/Bacon.php',
    'PragmaRX\\Google2FAQRCode\\QRCode\\Chillerlan' => $vendorDir . '/pragmarx/google2fa-qrcode/src/QRCode/Chillerlan.php',
    'PragmaRX\\Google2FAQRCode\\QRCode\\QRCodeServiceContract' => $vendorDir . '/pragmarx/google2fa-qrcode/src/QRCode/QRCodeServiceContract.php',
    'PragmaRX\\Google2FAQRCode\\Tests\\Constants' => $vendorDir . '/pragmarx/google2fa-qrcode/tests/Constants.php',
    'PragmaRX\\Google2FAQRCode\\Tests\\Google2FATest' => $vendorDir . '/pragmarx/google2fa-qrcode/tests/Google2FATest.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\Google2FA' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/Google2FA.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\IncompatibleWithGoogleAuthenticator' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/IncompatibleWithGoogleAuthenticator.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidAlgorithm' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidAlgorithm.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidCharacters' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidCharacters.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\SecretKeyTooShort' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/SecretKeyTooShort.php',
    'PragmaRX\\Google2FA\\Exceptions\\Google2FAException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Google2FAException.php',
    'PragmaRX\\Google2FA\\Exceptions\\IncompatibleWithGoogleAuthenticatorException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/IncompatibleWithGoogleAuthenticatorException.php',
    'PragmaRX\\Google2FA\\Exceptions\\InvalidAlgorithmException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/InvalidAlgorithmException.php',
    'PragmaRX\\Google2FA\\Exceptions\\InvalidCharactersException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/InvalidCharactersException.php',
    'PragmaRX\\Google2FA\\Exceptions\\SecretKeyTooShortException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/SecretKeyTooShortException.php',
    'PragmaRX\\Google2FA\\Google2FA' => $vendorDir . '/pragmarx/google2fa/src/Google2FA.php',
    'PragmaRX\\Google2FA\\Support\\Base32' => $vendorDir . '/pragmarx/google2fa/src/Support/Base32.php',
    'PragmaRX\\Google2FA\\Support\\Constants' => $vendorDir . '/pragmarx/google2fa/src/Support/Constants.php',
    'PragmaRX\\Google2FA\\Support\\QRCode' => $vendorDir . '/pragmarx/google2fa/src/Support/QRCode.php',
    'Psr\\Cache\\CacheException' => $vendorDir . '/psr/cache/src/CacheException.php',
    'Psr\\Cache\\CacheItemInterface' => $vendorDir . '/psr/cache/src/CacheItemInterface.php',
    'Psr\\Cache\\CacheItemPoolInterface' => $vendorDir . '/psr/cache/src/CacheItemPoolInterface.php',
    'Psr\\Cache\\InvalidArgumentException' => $vendorDir . '/psr/cache/src/InvalidArgumentException.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/Psr/Log/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/Psr/Log/NullLogger.php',
    'Psr\\Log\\Test\\DummyTest' => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php',
    'Psr\\Log\\Test\\LoggerInterfaceTest' => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'Psr\\Log\\Test\\TestLogger' => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php',
    'QRcode' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/qrcode.php',
    'ReCaptcha\\ReCaptcha' => $vendorDir . '/google/recaptcha/src/ReCaptcha/ReCaptcha.php',
    'ReCaptcha\\RequestMethod' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod.php',
    'ReCaptcha\\RequestMethod\\Curl' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod/Curl.php',
    'ReCaptcha\\RequestMethod\\CurlPost' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod/CurlPost.php',
    'ReCaptcha\\RequestMethod\\Post' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod/Post.php',
    'ReCaptcha\\RequestMethod\\Socket' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod/Socket.php',
    'ReCaptcha\\RequestMethod\\SocketPost' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestMethod/SocketPost.php',
    'ReCaptcha\\RequestParameters' => $vendorDir . '/google/recaptcha/src/ReCaptcha/RequestParameters.php',
    'ReCaptcha\\Response' => $vendorDir . '/google/recaptcha/src/ReCaptcha/Response.php',
    'ReturnTypeWillChange' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    'Slim\\Psr7\\Cookies' => $vendorDir . '/slim/psr7/src/Cookies.php',
    'Slim\\Psr7\\Environment' => $vendorDir . '/slim/psr7/src/Environment.php',
    'Slim\\Psr7\\Factory\\RequestFactory' => $vendorDir . '/slim/psr7/src/Factory/RequestFactory.php',
    'Slim\\Psr7\\Factory\\ResponseFactory' => $vendorDir . '/slim/psr7/src/Factory/ResponseFactory.php',
    'Slim\\Psr7\\Factory\\ServerRequestFactory' => $vendorDir . '/slim/psr7/src/Factory/ServerRequestFactory.php',
    'Slim\\Psr7\\Factory\\StreamFactory' => $vendorDir . '/slim/psr7/src/Factory/StreamFactory.php',
    'Slim\\Psr7\\Factory\\UploadedFileFactory' => $vendorDir . '/slim/psr7/src/Factory/UploadedFileFactory.php',
    'Slim\\Psr7\\Factory\\UriFactory' => $vendorDir . '/slim/psr7/src/Factory/UriFactory.php',
    'Slim\\Psr7\\Header' => $vendorDir . '/slim/psr7/src/Header.php',
    'Slim\\Psr7\\Headers' => $vendorDir . '/slim/psr7/src/Headers.php',
    'Slim\\Psr7\\Interfaces\\HeadersInterface' => $vendorDir . '/slim/psr7/src/Interfaces/HeadersInterface.php',
    'Slim\\Psr7\\Message' => $vendorDir . '/slim/psr7/src/Message.php',
    'Slim\\Psr7\\NonBufferedBody' => $vendorDir . '/slim/psr7/src/NonBufferedBody.php',
    'Slim\\Psr7\\Request' => $vendorDir . '/slim/psr7/src/Request.php',
    'Slim\\Psr7\\Response' => $vendorDir . '/slim/psr7/src/Response.php',
    'Slim\\Psr7\\Stream' => $vendorDir . '/slim/psr7/src/Stream.php',
    'Slim\\Psr7\\UploadedFile' => $vendorDir . '/slim/psr7/src/UploadedFile.php',
    'Slim\\Psr7\\Uri' => $vendorDir . '/slim/psr7/src/Uri.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\Cache\\Adapter\\AbstractAdapter' => $vendorDir . '/symfony/cache/Adapter/AbstractAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\AbstractTagAwareAdapter' => $vendorDir . '/symfony/cache/Adapter/AbstractTagAwareAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\AdapterInterface' => $vendorDir . '/symfony/cache/Adapter/AdapterInterface.php',
    'Symfony\\Component\\Cache\\Adapter\\ApcuAdapter' => $vendorDir . '/symfony/cache/Adapter/ApcuAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\ArrayAdapter' => $vendorDir . '/symfony/cache/Adapter/ArrayAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\ChainAdapter' => $vendorDir . '/symfony/cache/Adapter/ChainAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\CouchbaseBucketAdapter' => $vendorDir . '/symfony/cache/Adapter/CouchbaseBucketAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\CouchbaseCollectionAdapter' => $vendorDir . '/symfony/cache/Adapter/CouchbaseCollectionAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\DoctrineAdapter' => $vendorDir . '/symfony/cache/Adapter/DoctrineAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\DoctrineDbalAdapter' => $vendorDir . '/symfony/cache/Adapter/DoctrineDbalAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\FilesystemAdapter' => $vendorDir . '/symfony/cache/Adapter/FilesystemAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\FilesystemTagAwareAdapter' => $vendorDir . '/symfony/cache/Adapter/FilesystemTagAwareAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\MemcachedAdapter' => $vendorDir . '/symfony/cache/Adapter/MemcachedAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\NullAdapter' => $vendorDir . '/symfony/cache/Adapter/NullAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\ParameterNormalizer' => $vendorDir . '/symfony/cache/Adapter/ParameterNormalizer.php',
    'Symfony\\Component\\Cache\\Adapter\\PdoAdapter' => $vendorDir . '/symfony/cache/Adapter/PdoAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\PhpArrayAdapter' => $vendorDir . '/symfony/cache/Adapter/PhpArrayAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\PhpFilesAdapter' => $vendorDir . '/symfony/cache/Adapter/PhpFilesAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\ProxyAdapter' => $vendorDir . '/symfony/cache/Adapter/ProxyAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\Psr16Adapter' => $vendorDir . '/symfony/cache/Adapter/Psr16Adapter.php',
    'Symfony\\Component\\Cache\\Adapter\\RedisAdapter' => $vendorDir . '/symfony/cache/Adapter/RedisAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\RedisTagAwareAdapter' => $vendorDir . '/symfony/cache/Adapter/RedisTagAwareAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\TagAwareAdapter' => $vendorDir . '/symfony/cache/Adapter/TagAwareAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\TagAwareAdapterInterface' => $vendorDir . '/symfony/cache/Adapter/TagAwareAdapterInterface.php',
    'Symfony\\Component\\Cache\\Adapter\\TraceableAdapter' => $vendorDir . '/symfony/cache/Adapter/TraceableAdapter.php',
    'Symfony\\Component\\Cache\\Adapter\\TraceableTagAwareAdapter' => $vendorDir . '/symfony/cache/Adapter/TraceableTagAwareAdapter.php',
    'Symfony\\Component\\Cache\\CacheItem' => $vendorDir . '/symfony/cache/CacheItem.php',
    'Symfony\\Component\\Cache\\DataCollector\\CacheDataCollector' => $vendorDir . '/symfony/cache/DataCollector/CacheDataCollector.php',
    'Symfony\\Component\\Cache\\DependencyInjection\\CacheCollectorPass' => $vendorDir . '/symfony/cache/DependencyInjection/CacheCollectorPass.php',
    'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolClearerPass' => $vendorDir . '/symfony/cache/DependencyInjection/CachePoolClearerPass.php',
    'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolPass' => $vendorDir . '/symfony/cache/DependencyInjection/CachePoolPass.php',
    'Symfony\\Component\\Cache\\DependencyInjection\\CachePoolPrunerPass' => $vendorDir . '/symfony/cache/DependencyInjection/CachePoolPrunerPass.php',
    'Symfony\\Component\\Cache\\DoctrineProvider' => $vendorDir . '/symfony/cache/DoctrineProvider.php',
    'Symfony\\Component\\Cache\\Exception\\CacheException' => $vendorDir . '/symfony/cache/Exception/CacheException.php',
    'Symfony\\Component\\Cache\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/cache/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Cache\\Exception\\LogicException' => $vendorDir . '/symfony/cache/Exception/LogicException.php',
    'Symfony\\Component\\Cache\\LockRegistry' => $vendorDir . '/symfony/cache/LockRegistry.php',
    'Symfony\\Component\\Cache\\Marshaller\\DefaultMarshaller' => $vendorDir . '/symfony/cache/Marshaller/DefaultMarshaller.php',
    'Symfony\\Component\\Cache\\Marshaller\\DeflateMarshaller' => $vendorDir . '/symfony/cache/Marshaller/DeflateMarshaller.php',
    'Symfony\\Component\\Cache\\Marshaller\\MarshallerInterface' => $vendorDir . '/symfony/cache/Marshaller/MarshallerInterface.php',
    'Symfony\\Component\\Cache\\Marshaller\\SodiumMarshaller' => $vendorDir . '/symfony/cache/Marshaller/SodiumMarshaller.php',
    'Symfony\\Component\\Cache\\Marshaller\\TagAwareMarshaller' => $vendorDir . '/symfony/cache/Marshaller/TagAwareMarshaller.php',
    'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationDispatcher' => $vendorDir . '/symfony/cache/Messenger/EarlyExpirationDispatcher.php',
    'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationHandler' => $vendorDir . '/symfony/cache/Messenger/EarlyExpirationHandler.php',
    'Symfony\\Component\\Cache\\Messenger\\EarlyExpirationMessage' => $vendorDir . '/symfony/cache/Messenger/EarlyExpirationMessage.php',
    'Symfony\\Component\\Cache\\PruneableInterface' => $vendorDir . '/symfony/cache/PruneableInterface.php',
    'Symfony\\Component\\Cache\\Psr16Cache' => $vendorDir . '/symfony/cache/Psr16Cache.php',
    'Symfony\\Component\\Cache\\ResettableInterface' => $vendorDir . '/symfony/cache/ResettableInterface.php',
    'Symfony\\Component\\Cache\\Traits\\AbstractAdapterTrait' => $vendorDir . '/symfony/cache/Traits/AbstractAdapterTrait.php',
    'Symfony\\Component\\Cache\\Traits\\ContractsTrait' => $vendorDir . '/symfony/cache/Traits/ContractsTrait.php',
    'Symfony\\Component\\Cache\\Traits\\FilesystemCommonTrait' => $vendorDir . '/symfony/cache/Traits/FilesystemCommonTrait.php',
    'Symfony\\Component\\Cache\\Traits\\FilesystemTrait' => $vendorDir . '/symfony/cache/Traits/FilesystemTrait.php',
    'Symfony\\Component\\Cache\\Traits\\ProxyTrait' => $vendorDir . '/symfony/cache/Traits/ProxyTrait.php',
    'Symfony\\Component\\Cache\\Traits\\RedisClusterNodeProxy' => $vendorDir . '/symfony/cache/Traits/RedisClusterNodeProxy.php',
    'Symfony\\Component\\Cache\\Traits\\RedisClusterProxy' => $vendorDir . '/symfony/cache/Traits/RedisClusterProxy.php',
    'Symfony\\Component\\Cache\\Traits\\RedisProxy' => $vendorDir . '/symfony/cache/Traits/RedisProxy.php',
    'Symfony\\Component\\Cache\\Traits\\RedisTrait' => $vendorDir . '/symfony/cache/Traits/RedisTrait.php',
    'Symfony\\Component\\Config\\Builder\\ClassBuilder' => $vendorDir . '/symfony/config/Builder/ClassBuilder.php',
    'Symfony\\Component\\Config\\Builder\\ConfigBuilderGenerator' => $vendorDir . '/symfony/config/Builder/ConfigBuilderGenerator.php',
    'Symfony\\Component\\Config\\Builder\\ConfigBuilderGeneratorInterface' => $vendorDir . '/symfony/config/Builder/ConfigBuilderGeneratorInterface.php',
    'Symfony\\Component\\Config\\Builder\\ConfigBuilderInterface' => $vendorDir . '/symfony/config/Builder/ConfigBuilderInterface.php',
    'Symfony\\Component\\Config\\Builder\\Method' => $vendorDir . '/symfony/config/Builder/Method.php',
    'Symfony\\Component\\Config\\Builder\\Property' => $vendorDir . '/symfony/config/Builder/Property.php',
    'Symfony\\Component\\Config\\ConfigCache' => $vendorDir . '/symfony/config/ConfigCache.php',
    'Symfony\\Component\\Config\\ConfigCacheFactory' => $vendorDir . '/symfony/config/ConfigCacheFactory.php',
    'Symfony\\Component\\Config\\ConfigCacheFactoryInterface' => $vendorDir . '/symfony/config/ConfigCacheFactoryInterface.php',
    'Symfony\\Component\\Config\\ConfigCacheInterface' => $vendorDir . '/symfony/config/ConfigCacheInterface.php',
    'Symfony\\Component\\Config\\Definition\\ArrayNode' => $vendorDir . '/symfony/config/Definition/ArrayNode.php',
    'Symfony\\Component\\Config\\Definition\\BaseNode' => $vendorDir . '/symfony/config/Definition/BaseNode.php',
    'Symfony\\Component\\Config\\Definition\\BooleanNode' => $vendorDir . '/symfony/config/Definition/BooleanNode.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\ArrayNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/ArrayNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\BooleanNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/BooleanNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\BuilderAwareInterface' => $vendorDir . '/symfony/config/Definition/Builder/BuilderAwareInterface.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\EnumNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/EnumNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\ExprBuilder' => $vendorDir . '/symfony/config/Definition/Builder/ExprBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\FloatNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/FloatNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\IntegerNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/IntegerNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\MergeBuilder' => $vendorDir . '/symfony/config/Definition/Builder/MergeBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\NodeBuilder' => $vendorDir . '/symfony/config/Definition/Builder/NodeBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\NodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/NodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\NodeParentInterface' => $vendorDir . '/symfony/config/Definition/Builder/NodeParentInterface.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\NormalizationBuilder' => $vendorDir . '/symfony/config/Definition/Builder/NormalizationBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\NumericNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/NumericNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\ParentNodeDefinitionInterface' => $vendorDir . '/symfony/config/Definition/Builder/ParentNodeDefinitionInterface.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\ScalarNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/ScalarNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\TreeBuilder' => $vendorDir . '/symfony/config/Definition/Builder/TreeBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\ValidationBuilder' => $vendorDir . '/symfony/config/Definition/Builder/ValidationBuilder.php',
    'Symfony\\Component\\Config\\Definition\\Builder\\VariableNodeDefinition' => $vendorDir . '/symfony/config/Definition/Builder/VariableNodeDefinition.php',
    'Symfony\\Component\\Config\\Definition\\ConfigurationInterface' => $vendorDir . '/symfony/config/Definition/ConfigurationInterface.php',
    'Symfony\\Component\\Config\\Definition\\Dumper\\XmlReferenceDumper' => $vendorDir . '/symfony/config/Definition/Dumper/XmlReferenceDumper.php',
    'Symfony\\Component\\Config\\Definition\\Dumper\\YamlReferenceDumper' => $vendorDir . '/symfony/config/Definition/Dumper/YamlReferenceDumper.php',
    'Symfony\\Component\\Config\\Definition\\EnumNode' => $vendorDir . '/symfony/config/Definition/EnumNode.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\DuplicateKeyException' => $vendorDir . '/symfony/config/Definition/Exception/DuplicateKeyException.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\Exception' => $vendorDir . '/symfony/config/Definition/Exception/Exception.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\ForbiddenOverwriteException' => $vendorDir . '/symfony/config/Definition/Exception/ForbiddenOverwriteException.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\InvalidConfigurationException' => $vendorDir . '/symfony/config/Definition/Exception/InvalidConfigurationException.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\InvalidDefinitionException' => $vendorDir . '/symfony/config/Definition/Exception/InvalidDefinitionException.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\InvalidTypeException' => $vendorDir . '/symfony/config/Definition/Exception/InvalidTypeException.php',
    'Symfony\\Component\\Config\\Definition\\Exception\\UnsetKeyException' => $vendorDir . '/symfony/config/Definition/Exception/UnsetKeyException.php',
    'Symfony\\Component\\Config\\Definition\\FloatNode' => $vendorDir . '/symfony/config/Definition/FloatNode.php',
    'Symfony\\Component\\Config\\Definition\\IntegerNode' => $vendorDir . '/symfony/config/Definition/IntegerNode.php',
    'Symfony\\Component\\Config\\Definition\\NodeInterface' => $vendorDir . '/symfony/config/Definition/NodeInterface.php',
    'Symfony\\Component\\Config\\Definition\\NumericNode' => $vendorDir . '/symfony/config/Definition/NumericNode.php',
    'Symfony\\Component\\Config\\Definition\\Processor' => $vendorDir . '/symfony/config/Definition/Processor.php',
    'Symfony\\Component\\Config\\Definition\\PrototypeNodeInterface' => $vendorDir . '/symfony/config/Definition/PrototypeNodeInterface.php',
    'Symfony\\Component\\Config\\Definition\\PrototypedArrayNode' => $vendorDir . '/symfony/config/Definition/PrototypedArrayNode.php',
    'Symfony\\Component\\Config\\Definition\\ScalarNode' => $vendorDir . '/symfony/config/Definition/ScalarNode.php',
    'Symfony\\Component\\Config\\Definition\\VariableNode' => $vendorDir . '/symfony/config/Definition/VariableNode.php',
    'Symfony\\Component\\Config\\Exception\\FileLoaderImportCircularReferenceException' => $vendorDir . '/symfony/config/Exception/FileLoaderImportCircularReferenceException.php',
    'Symfony\\Component\\Config\\Exception\\FileLocatorFileNotFoundException' => $vendorDir . '/symfony/config/Exception/FileLocatorFileNotFoundException.php',
    'Symfony\\Component\\Config\\Exception\\LoaderLoadException' => $vendorDir . '/symfony/config/Exception/LoaderLoadException.php',
    'Symfony\\Component\\Config\\FileLocator' => $vendorDir . '/symfony/config/FileLocator.php',
    'Symfony\\Component\\Config\\FileLocatorInterface' => $vendorDir . '/symfony/config/FileLocatorInterface.php',
    'Symfony\\Component\\Config\\Loader\\DelegatingLoader' => $vendorDir . '/symfony/config/Loader/DelegatingLoader.php',
    'Symfony\\Component\\Config\\Loader\\FileLoader' => $vendorDir . '/symfony/config/Loader/FileLoader.php',
    'Symfony\\Component\\Config\\Loader\\GlobFileLoader' => $vendorDir . '/symfony/config/Loader/GlobFileLoader.php',
    'Symfony\\Component\\Config\\Loader\\Loader' => $vendorDir . '/symfony/config/Loader/Loader.php',
    'Symfony\\Component\\Config\\Loader\\LoaderInterface' => $vendorDir . '/symfony/config/Loader/LoaderInterface.php',
    'Symfony\\Component\\Config\\Loader\\LoaderResolver' => $vendorDir . '/symfony/config/Loader/LoaderResolver.php',
    'Symfony\\Component\\Config\\Loader\\LoaderResolverInterface' => $vendorDir . '/symfony/config/Loader/LoaderResolverInterface.php',
    'Symfony\\Component\\Config\\Loader\\ParamConfigurator' => $vendorDir . '/symfony/config/Loader/ParamConfigurator.php',
    'Symfony\\Component\\Config\\ResourceCheckerConfigCache' => $vendorDir . '/symfony/config/ResourceCheckerConfigCache.php',
    'Symfony\\Component\\Config\\ResourceCheckerConfigCacheFactory' => $vendorDir . '/symfony/config/ResourceCheckerConfigCacheFactory.php',
    'Symfony\\Component\\Config\\ResourceCheckerInterface' => $vendorDir . '/symfony/config/ResourceCheckerInterface.php',
    'Symfony\\Component\\Config\\Resource\\ClassExistenceResource' => $vendorDir . '/symfony/config/Resource/ClassExistenceResource.php',
    'Symfony\\Component\\Config\\Resource\\ComposerResource' => $vendorDir . '/symfony/config/Resource/ComposerResource.php',
    'Symfony\\Component\\Config\\Resource\\DirectoryResource' => $vendorDir . '/symfony/config/Resource/DirectoryResource.php',
    'Symfony\\Component\\Config\\Resource\\FileExistenceResource' => $vendorDir . '/symfony/config/Resource/FileExistenceResource.php',
    'Symfony\\Component\\Config\\Resource\\FileResource' => $vendorDir . '/symfony/config/Resource/FileResource.php',
    'Symfony\\Component\\Config\\Resource\\GlobResource' => $vendorDir . '/symfony/config/Resource/GlobResource.php',
    'Symfony\\Component\\Config\\Resource\\ReflectionClassResource' => $vendorDir . '/symfony/config/Resource/ReflectionClassResource.php',
    'Symfony\\Component\\Config\\Resource\\ResourceInterface' => $vendorDir . '/symfony/config/Resource/ResourceInterface.php',
    'Symfony\\Component\\Config\\Resource\\SelfCheckingResourceChecker' => $vendorDir . '/symfony/config/Resource/SelfCheckingResourceChecker.php',
    'Symfony\\Component\\Config\\Resource\\SelfCheckingResourceInterface' => $vendorDir . '/symfony/config/Resource/SelfCheckingResourceInterface.php',
    'Symfony\\Component\\Config\\Util\\Exception\\InvalidXmlException' => $vendorDir . '/symfony/config/Util/Exception/InvalidXmlException.php',
    'Symfony\\Component\\Config\\Util\\Exception\\XmlParsingException' => $vendorDir . '/symfony/config/Util/Exception/XmlParsingException.php',
    'Symfony\\Component\\Config\\Util\\XmlUtils' => $vendorDir . '/symfony/config/Util/XmlUtils.php',
    'Symfony\\Component\\DependencyInjection\\Alias' => $vendorDir . '/symfony/dependency-injection/Alias.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\AbstractArgument' => $vendorDir . '/symfony/dependency-injection/Argument/AbstractArgument.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\ArgumentInterface' => $vendorDir . '/symfony/dependency-injection/Argument/ArgumentInterface.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\BoundArgument' => $vendorDir . '/symfony/dependency-injection/Argument/BoundArgument.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\IteratorArgument' => $vendorDir . '/symfony/dependency-injection/Argument/IteratorArgument.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\ReferenceSetArgumentTrait' => $vendorDir . '/symfony/dependency-injection/Argument/ReferenceSetArgumentTrait.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\RewindableGenerator' => $vendorDir . '/symfony/dependency-injection/Argument/RewindableGenerator.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\ServiceClosureArgument' => $vendorDir . '/symfony/dependency-injection/Argument/ServiceClosureArgument.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\ServiceLocator' => $vendorDir . '/symfony/dependency-injection/Argument/ServiceLocator.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\ServiceLocatorArgument' => $vendorDir . '/symfony/dependency-injection/Argument/ServiceLocatorArgument.php',
    'Symfony\\Component\\DependencyInjection\\Argument\\TaggedIteratorArgument' => $vendorDir . '/symfony/dependency-injection/Argument/TaggedIteratorArgument.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\AsTaggedItem' => $vendorDir . '/symfony/dependency-injection/Attribute/AsTaggedItem.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\Autoconfigure' => $vendorDir . '/symfony/dependency-injection/Attribute/Autoconfigure.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\AutoconfigureTag' => $vendorDir . '/symfony/dependency-injection/Attribute/AutoconfigureTag.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\TaggedIterator' => $vendorDir . '/symfony/dependency-injection/Attribute/TaggedIterator.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\TaggedLocator' => $vendorDir . '/symfony/dependency-injection/Attribute/TaggedLocator.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\Target' => $vendorDir . '/symfony/dependency-injection/Attribute/Target.php',
    'Symfony\\Component\\DependencyInjection\\Attribute\\When' => $vendorDir . '/symfony/dependency-injection/Attribute/When.php',
    'Symfony\\Component\\DependencyInjection\\ChildDefinition' => $vendorDir . '/symfony/dependency-injection/ChildDefinition.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AbstractRecursivePass' => $vendorDir . '/symfony/dependency-injection/Compiler/AbstractRecursivePass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AliasDeprecatedPublicServicesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/AliasDeprecatedPublicServicesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AnalyzeServiceReferencesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/AnalyzeServiceReferencesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AttributeAutoconfigurationPass' => $vendorDir . '/symfony/dependency-injection/Compiler/AttributeAutoconfigurationPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AutoAliasServicePass' => $vendorDir . '/symfony/dependency-injection/Compiler/AutoAliasServicePass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AutowirePass' => $vendorDir . '/symfony/dependency-injection/Compiler/AutowirePass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AutowireRequiredMethodsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/AutowireRequiredMethodsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\AutowireRequiredPropertiesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/AutowireRequiredPropertiesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckArgumentsValidityPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckArgumentsValidityPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckCircularReferencesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckCircularReferencesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckDefinitionValidityPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckDefinitionValidityPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckExceptionOnInvalidReferenceBehaviorPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckExceptionOnInvalidReferenceBehaviorPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckReferenceValidityPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckReferenceValidityPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CheckTypeDeclarationsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/CheckTypeDeclarationsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\Compiler' => $vendorDir . '/symfony/dependency-injection/Compiler/Compiler.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\CompilerPassInterface' => $vendorDir . '/symfony/dependency-injection/Compiler/CompilerPassInterface.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\DecoratorServicePass' => $vendorDir . '/symfony/dependency-injection/Compiler/DecoratorServicePass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\DefinitionErrorExceptionPass' => $vendorDir . '/symfony/dependency-injection/Compiler/DefinitionErrorExceptionPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ExtensionCompilerPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ExtensionCompilerPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\InlineServiceDefinitionsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/InlineServiceDefinitionsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\MergeExtensionConfigurationPass' => $vendorDir . '/symfony/dependency-injection/Compiler/MergeExtensionConfigurationPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\PassConfig' => $vendorDir . '/symfony/dependency-injection/Compiler/PassConfig.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\PriorityTaggedServiceTrait' => $vendorDir . '/symfony/dependency-injection/Compiler/PriorityTaggedServiceTrait.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterAutoconfigureAttributesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RegisterAutoconfigureAttributesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterEnvVarProcessorsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RegisterEnvVarProcessorsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterReverseContainerPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RegisterReverseContainerPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RegisterServiceSubscribersPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RegisterServiceSubscribersPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RemoveAbstractDefinitionsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RemoveAbstractDefinitionsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RemovePrivateAliasesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RemovePrivateAliasesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\RemoveUnusedDefinitionsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/RemoveUnusedDefinitionsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ReplaceAliasByActualDefinitionPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ReplaceAliasByActualDefinitionPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveBindingsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveBindingsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveChildDefinitionsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveChildDefinitionsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveClassPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveClassPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveDecoratorStackPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveDecoratorStackPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveEnvPlaceholdersPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveEnvPlaceholdersPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveFactoryClassPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveFactoryClassPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveHotPathPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveHotPathPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveInstanceofConditionalsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveInstanceofConditionalsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveInvalidReferencesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveInvalidReferencesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveNamedArgumentsPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveNamedArgumentsPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveNoPreloadPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveNoPreloadPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveParameterPlaceHoldersPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveParameterPlaceHoldersPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolvePrivatesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolvePrivatesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveReferencesToAliasesPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveReferencesToAliasesPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveServiceSubscribersPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveServiceSubscribersPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ResolveTaggedIteratorArgumentPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ResolveTaggedIteratorArgumentPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceLocatorTagPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ServiceLocatorTagPass.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraph' => $vendorDir . '/symfony/dependency-injection/Compiler/ServiceReferenceGraph.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraphEdge' => $vendorDir . '/symfony/dependency-injection/Compiler/ServiceReferenceGraphEdge.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ServiceReferenceGraphNode' => $vendorDir . '/symfony/dependency-injection/Compiler/ServiceReferenceGraphNode.php',
    'Symfony\\Component\\DependencyInjection\\Compiler\\ValidateEnvPlaceholdersPass' => $vendorDir . '/symfony/dependency-injection/Compiler/ValidateEnvPlaceholdersPass.php',
    'Symfony\\Component\\DependencyInjection\\Config\\ContainerParametersResource' => $vendorDir . '/symfony/dependency-injection/Config/ContainerParametersResource.php',
    'Symfony\\Component\\DependencyInjection\\Config\\ContainerParametersResourceChecker' => $vendorDir . '/symfony/dependency-injection/Config/ContainerParametersResourceChecker.php',
    'Symfony\\Component\\DependencyInjection\\Container' => $vendorDir . '/symfony/dependency-injection/Container.php',
    'Symfony\\Component\\DependencyInjection\\ContainerAwareInterface' => $vendorDir . '/symfony/dependency-injection/ContainerAwareInterface.php',
    'Symfony\\Component\\DependencyInjection\\ContainerAwareTrait' => $vendorDir . '/symfony/dependency-injection/ContainerAwareTrait.php',
    'Symfony\\Component\\DependencyInjection\\ContainerBuilder' => $vendorDir . '/symfony/dependency-injection/ContainerBuilder.php',
    'Symfony\\Component\\DependencyInjection\\ContainerInterface' => $vendorDir . '/symfony/dependency-injection/ContainerInterface.php',
    'Symfony\\Component\\DependencyInjection\\Definition' => $vendorDir . '/symfony/dependency-injection/Definition.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\Dumper' => $vendorDir . '/symfony/dependency-injection/Dumper/Dumper.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\DumperInterface' => $vendorDir . '/symfony/dependency-injection/Dumper/DumperInterface.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\GraphvizDumper' => $vendorDir . '/symfony/dependency-injection/Dumper/GraphvizDumper.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\PhpDumper' => $vendorDir . '/symfony/dependency-injection/Dumper/PhpDumper.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\Preloader' => $vendorDir . '/symfony/dependency-injection/Dumper/Preloader.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\XmlDumper' => $vendorDir . '/symfony/dependency-injection/Dumper/XmlDumper.php',
    'Symfony\\Component\\DependencyInjection\\Dumper\\YamlDumper' => $vendorDir . '/symfony/dependency-injection/Dumper/YamlDumper.php',
    'Symfony\\Component\\DependencyInjection\\EnvVarLoaderInterface' => $vendorDir . '/symfony/dependency-injection/EnvVarLoaderInterface.php',
    'Symfony\\Component\\DependencyInjection\\EnvVarProcessor' => $vendorDir . '/symfony/dependency-injection/EnvVarProcessor.php',
    'Symfony\\Component\\DependencyInjection\\EnvVarProcessorInterface' => $vendorDir . '/symfony/dependency-injection/EnvVarProcessorInterface.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\AutowiringFailedException' => $vendorDir . '/symfony/dependency-injection/Exception/AutowiringFailedException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\BadMethodCallException' => $vendorDir . '/symfony/dependency-injection/Exception/BadMethodCallException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\EnvNotFoundException' => $vendorDir . '/symfony/dependency-injection/Exception/EnvNotFoundException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\EnvParameterException' => $vendorDir . '/symfony/dependency-injection/Exception/EnvParameterException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/dependency-injection/Exception/ExceptionInterface.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/dependency-injection/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\InvalidParameterTypeException' => $vendorDir . '/symfony/dependency-injection/Exception/InvalidParameterTypeException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\LogicException' => $vendorDir . '/symfony/dependency-injection/Exception/LogicException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\OutOfBoundsException' => $vendorDir . '/symfony/dependency-injection/Exception/OutOfBoundsException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\ParameterCircularReferenceException' => $vendorDir . '/symfony/dependency-injection/Exception/ParameterCircularReferenceException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\ParameterNotFoundException' => $vendorDir . '/symfony/dependency-injection/Exception/ParameterNotFoundException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\RuntimeException' => $vendorDir . '/symfony/dependency-injection/Exception/RuntimeException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\ServiceCircularReferenceException' => $vendorDir . '/symfony/dependency-injection/Exception/ServiceCircularReferenceException.php',
    'Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException' => $vendorDir . '/symfony/dependency-injection/Exception/ServiceNotFoundException.php',
    'Symfony\\Component\\DependencyInjection\\ExpressionLanguage' => $vendorDir . '/symfony/dependency-injection/ExpressionLanguage.php',
    'Symfony\\Component\\DependencyInjection\\ExpressionLanguageProvider' => $vendorDir . '/symfony/dependency-injection/ExpressionLanguageProvider.php',
    'Symfony\\Component\\DependencyInjection\\Extension\\ConfigurationExtensionInterface' => $vendorDir . '/symfony/dependency-injection/Extension/ConfigurationExtensionInterface.php',
    'Symfony\\Component\\DependencyInjection\\Extension\\Extension' => $vendorDir . '/symfony/dependency-injection/Extension/Extension.php',
    'Symfony\\Component\\DependencyInjection\\Extension\\ExtensionInterface' => $vendorDir . '/symfony/dependency-injection/Extension/ExtensionInterface.php',
    'Symfony\\Component\\DependencyInjection\\Extension\\PrependExtensionInterface' => $vendorDir . '/symfony/dependency-injection/Extension/PrependExtensionInterface.php',
    'Symfony\\Component\\DependencyInjection\\LazyProxy\\Instantiator\\InstantiatorInterface' => $vendorDir . '/symfony/dependency-injection/LazyProxy/Instantiator/InstantiatorInterface.php',
    'Symfony\\Component\\DependencyInjection\\LazyProxy\\Instantiator\\RealServiceInstantiator' => $vendorDir . '/symfony/dependency-injection/LazyProxy/Instantiator/RealServiceInstantiator.php',
    'Symfony\\Component\\DependencyInjection\\LazyProxy\\PhpDumper\\DumperInterface' => $vendorDir . '/symfony/dependency-injection/LazyProxy/PhpDumper/DumperInterface.php',
    'Symfony\\Component\\DependencyInjection\\LazyProxy\\PhpDumper\\NullDumper' => $vendorDir . '/symfony/dependency-injection/LazyProxy/PhpDumper/NullDumper.php',
    'Symfony\\Component\\DependencyInjection\\LazyProxy\\ProxyHelper' => $vendorDir . '/symfony/dependency-injection/LazyProxy/ProxyHelper.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\ClosureLoader' => $vendorDir . '/symfony/dependency-injection/Loader/ClosureLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AbstractConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/AbstractConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AbstractServiceConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/AbstractServiceConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\AliasConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/AliasConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ClosureReferenceConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ClosureReferenceConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ContainerConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ContainerConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\DefaultsConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/DefaultsConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\EnvConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/EnvConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\InlineServiceConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/InlineServiceConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\InstanceofConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/InstanceofConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ParametersConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ParametersConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\PrototypeConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/PrototypeConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ReferenceConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ReferenceConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ServiceConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ServiceConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\ServicesConfigurator' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/ServicesConfigurator.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AbstractTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/AbstractTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ArgumentTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/ArgumentTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AutoconfigureTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/AutoconfigureTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\AutowireTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/AutowireTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\BindTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/BindTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\CallTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/CallTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ClassTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/ClassTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ConfiguratorTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/ConfiguratorTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\DecorateTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/DecorateTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\DeprecateTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/DeprecateTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\FactoryTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/FactoryTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\FileTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/FileTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\LazyTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/LazyTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ParentTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/ParentTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\PropertyTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/PropertyTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\PublicTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/PublicTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\ShareTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/ShareTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\SyntheticTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/SyntheticTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\Configurator\\Traits\\TagTrait' => $vendorDir . '/symfony/dependency-injection/Loader/Configurator/Traits/TagTrait.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\DirectoryLoader' => $vendorDir . '/symfony/dependency-injection/Loader/DirectoryLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\FileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/FileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\GlobFileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/GlobFileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\IniFileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/IniFileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\PhpFileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/PhpFileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\XmlFileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/XmlFileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Loader\\YamlFileLoader' => $vendorDir . '/symfony/dependency-injection/Loader/YamlFileLoader.php',
    'Symfony\\Component\\DependencyInjection\\Parameter' => $vendorDir . '/symfony/dependency-injection/Parameter.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBag' => $vendorDir . '/symfony/dependency-injection/ParameterBag/ContainerBag.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBagInterface' => $vendorDir . '/symfony/dependency-injection/ParameterBag/ContainerBagInterface.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\EnvPlaceholderParameterBag' => $vendorDir . '/symfony/dependency-injection/ParameterBag/EnvPlaceholderParameterBag.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\FrozenParameterBag' => $vendorDir . '/symfony/dependency-injection/ParameterBag/FrozenParameterBag.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBag' => $vendorDir . '/symfony/dependency-injection/ParameterBag/ParameterBag.php',
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => $vendorDir . '/symfony/dependency-injection/ParameterBag/ParameterBagInterface.php',
    'Symfony\\Component\\DependencyInjection\\Reference' => $vendorDir . '/symfony/dependency-injection/Reference.php',
    'Symfony\\Component\\DependencyInjection\\ReverseContainer' => $vendorDir . '/symfony/dependency-injection/ReverseContainer.php',
    'Symfony\\Component\\DependencyInjection\\ServiceLocator' => $vendorDir . '/symfony/dependency-injection/ServiceLocator.php',
    'Symfony\\Component\\DependencyInjection\\TaggedContainerInterface' => $vendorDir . '/symfony/dependency-injection/TaggedContainerInterface.php',
    'Symfony\\Component\\DependencyInjection\\TypedReference' => $vendorDir . '/symfony/dependency-injection/TypedReference.php',
    'Symfony\\Component\\DependencyInjection\\Variable' => $vendorDir . '/symfony/dependency-injection/Variable.php',
    'Symfony\\Component\\ExpressionLanguage\\Compiler' => $vendorDir . '/symfony/expression-language/Compiler.php',
    'Symfony\\Component\\ExpressionLanguage\\Expression' => $vendorDir . '/symfony/expression-language/Expression.php',
    'Symfony\\Component\\ExpressionLanguage\\ExpressionFunction' => $vendorDir . '/symfony/expression-language/ExpressionFunction.php',
    'Symfony\\Component\\ExpressionLanguage\\ExpressionFunctionProviderInterface' => $vendorDir . '/symfony/expression-language/ExpressionFunctionProviderInterface.php',
    'Symfony\\Component\\ExpressionLanguage\\ExpressionLanguage' => $vendorDir . '/symfony/expression-language/ExpressionLanguage.php',
    'Symfony\\Component\\ExpressionLanguage\\Lexer' => $vendorDir . '/symfony/expression-language/Lexer.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\ArgumentsNode' => $vendorDir . '/symfony/expression-language/Node/ArgumentsNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\ArrayNode' => $vendorDir . '/symfony/expression-language/Node/ArrayNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\BinaryNode' => $vendorDir . '/symfony/expression-language/Node/BinaryNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\ConditionalNode' => $vendorDir . '/symfony/expression-language/Node/ConditionalNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\ConstantNode' => $vendorDir . '/symfony/expression-language/Node/ConstantNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\FunctionNode' => $vendorDir . '/symfony/expression-language/Node/FunctionNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\GetAttrNode' => $vendorDir . '/symfony/expression-language/Node/GetAttrNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\NameNode' => $vendorDir . '/symfony/expression-language/Node/NameNode.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\Node' => $vendorDir . '/symfony/expression-language/Node/Node.php',
    'Symfony\\Component\\ExpressionLanguage\\Node\\UnaryNode' => $vendorDir . '/symfony/expression-language/Node/UnaryNode.php',
    'Symfony\\Component\\ExpressionLanguage\\ParsedExpression' => $vendorDir . '/symfony/expression-language/ParsedExpression.php',
    'Symfony\\Component\\ExpressionLanguage\\Parser' => $vendorDir . '/symfony/expression-language/Parser.php',
    'Symfony\\Component\\ExpressionLanguage\\SerializedParsedExpression' => $vendorDir . '/symfony/expression-language/SerializedParsedExpression.php',
    'Symfony\\Component\\ExpressionLanguage\\SyntaxError' => $vendorDir . '/symfony/expression-language/SyntaxError.php',
    'Symfony\\Component\\ExpressionLanguage\\Token' => $vendorDir . '/symfony/expression-language/Token.php',
    'Symfony\\Component\\ExpressionLanguage\\TokenStream' => $vendorDir . '/symfony/expression-language/TokenStream.php',
    'Symfony\\Component\\Filesystem\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\FileNotFoundException' => $vendorDir . '/symfony/filesystem/Exception/FileNotFoundException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOException' => $vendorDir . '/symfony/filesystem/Exception/IOException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/IOExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/filesystem/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Filesystem\\Exception\\RuntimeException' => $vendorDir . '/symfony/filesystem/Exception/RuntimeException.php',
    'Symfony\\Component\\Filesystem\\Filesystem' => $vendorDir . '/symfony/filesystem/Filesystem.php',
    'Symfony\\Component\\Filesystem\\Path' => $vendorDir . '/symfony/filesystem/Path.php',
    'Symfony\\Component\\VarExporter\\Exception\\ClassNotFoundException' => $vendorDir . '/symfony/var-exporter/Exception/ClassNotFoundException.php',
    'Symfony\\Component\\VarExporter\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/var-exporter/Exception/ExceptionInterface.php',
    'Symfony\\Component\\VarExporter\\Exception\\NotInstantiableTypeException' => $vendorDir . '/symfony/var-exporter/Exception/NotInstantiableTypeException.php',
    'Symfony\\Component\\VarExporter\\Instantiator' => $vendorDir . '/symfony/var-exporter/Instantiator.php',
    'Symfony\\Component\\VarExporter\\Internal\\Exporter' => $vendorDir . '/symfony/var-exporter/Internal/Exporter.php',
    'Symfony\\Component\\VarExporter\\Internal\\Hydrator' => $vendorDir . '/symfony/var-exporter/Internal/Hydrator.php',
    'Symfony\\Component\\VarExporter\\Internal\\Reference' => $vendorDir . '/symfony/var-exporter/Internal/Reference.php',
    'Symfony\\Component\\VarExporter\\Internal\\Registry' => $vendorDir . '/symfony/var-exporter/Internal/Registry.php',
    'Symfony\\Component\\VarExporter\\Internal\\Values' => $vendorDir . '/symfony/var-exporter/Internal/Values.php',
    'Symfony\\Component\\VarExporter\\VarExporter' => $vendorDir . '/symfony/var-exporter/VarExporter.php',
    'Symfony\\Contracts\\Cache\\CacheInterface' => $vendorDir . '/symfony/cache-contracts/CacheInterface.php',
    'Symfony\\Contracts\\Cache\\CacheTrait' => $vendorDir . '/symfony/cache-contracts/CacheTrait.php',
    'Symfony\\Contracts\\Cache\\CallbackInterface' => $vendorDir . '/symfony/cache-contracts/CallbackInterface.php',
    'Symfony\\Contracts\\Cache\\ItemInterface' => $vendorDir . '/symfony/cache-contracts/ItemInterface.php',
    'Symfony\\Contracts\\Cache\\TagAwareCacheInterface' => $vendorDir . '/symfony/cache-contracts/TagAwareCacheInterface.php',
    'Symfony\\Contracts\\Service\\Attribute\\Required' => $vendorDir . '/symfony/service-contracts/Attribute/Required.php',
    'Symfony\\Contracts\\Service\\Attribute\\SubscribedService' => $vendorDir . '/symfony/service-contracts/Attribute/SubscribedService.php',
    'Symfony\\Contracts\\Service\\ResetInterface' => $vendorDir . '/symfony/service-contracts/ResetInterface.php',
    'Symfony\\Contracts\\Service\\ServiceLocatorTrait' => $vendorDir . '/symfony/service-contracts/ServiceLocatorTrait.php',
    'Symfony\\Contracts\\Service\\ServiceProviderInterface' => $vendorDir . '/symfony/service-contracts/ServiceProviderInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberInterface' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberTrait.php',
    'Symfony\\Contracts\\Service\\Test\\ServiceLocatorTest' => $vendorDir . '/symfony/service-contracts/Test/ServiceLocatorTest.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Symfony\\Polyfill\\Php73\\Php73' => $vendorDir . '/symfony/polyfill-php73/Php73.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'Symfony\\Polyfill\\Php81\\Php81' => $vendorDir . '/symfony/polyfill-php81/Php81.php',
    'TCPDF' => $vendorDir . '/tecnickcom/tcpdf/tcpdf.php',
    'TCPDF2DBarcode' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_barcodes_2d.php',
    'TCPDFBarcode' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_barcodes_1d.php',
    'TCPDF_COLORS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_colors.php',
    'TCPDF_FILTERS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_filters.php',
    'TCPDF_FONTS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_fonts.php',
    'TCPDF_FONT_DATA' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_font_data.php',
    'TCPDF_IMAGES' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_images.php',
    'TCPDF_IMPORT' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_import.php',
    'TCPDF_PARSER' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_parser.php',
    'TCPDF_STATIC' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_static.php',
    'Twig\\Cache\\CacheInterface' => $vendorDir . '/twig/twig/src/Cache/CacheInterface.php',
    'Twig\\Cache\\FilesystemCache' => $vendorDir . '/twig/twig/src/Cache/FilesystemCache.php',
    'Twig\\Cache\\NullCache' => $vendorDir . '/twig/twig/src/Cache/NullCache.php',
    'Twig\\Compiler' => $vendorDir . '/twig/twig/src/Compiler.php',
    'Twig\\Environment' => $vendorDir . '/twig/twig/src/Environment.php',
    'Twig\\Error\\Error' => $vendorDir . '/twig/twig/src/Error/Error.php',
    'Twig\\Error\\LoaderError' => $vendorDir . '/twig/twig/src/Error/LoaderError.php',
    'Twig\\Error\\RuntimeError' => $vendorDir . '/twig/twig/src/Error/RuntimeError.php',
    'Twig\\Error\\SyntaxError' => $vendorDir . '/twig/twig/src/Error/SyntaxError.php',
    'Twig\\ExpressionParser' => $vendorDir . '/twig/twig/src/ExpressionParser.php',
    'Twig\\ExtensionSet' => $vendorDir . '/twig/twig/src/ExtensionSet.php',
    'Twig\\Extension\\AbstractExtension' => $vendorDir . '/twig/twig/src/Extension/AbstractExtension.php',
    'Twig\\Extension\\CoreExtension' => $vendorDir . '/twig/twig/src/Extension/CoreExtension.php',
    'Twig\\Extension\\DebugExtension' => $vendorDir . '/twig/twig/src/Extension/DebugExtension.php',
    'Twig\\Extension\\EscaperExtension' => $vendorDir . '/twig/twig/src/Extension/EscaperExtension.php',
    'Twig\\Extension\\ExtensionInterface' => $vendorDir . '/twig/twig/src/Extension/ExtensionInterface.php',
    'Twig\\Extension\\GlobalsInterface' => $vendorDir . '/twig/twig/src/Extension/GlobalsInterface.php',
    'Twig\\Extension\\OptimizerExtension' => $vendorDir . '/twig/twig/src/Extension/OptimizerExtension.php',
    'Twig\\Extension\\ProfilerExtension' => $vendorDir . '/twig/twig/src/Extension/ProfilerExtension.php',
    'Twig\\Extension\\RuntimeExtensionInterface' => $vendorDir . '/twig/twig/src/Extension/RuntimeExtensionInterface.php',
    'Twig\\Extension\\SandboxExtension' => $vendorDir . '/twig/twig/src/Extension/SandboxExtension.php',
    'Twig\\Extension\\StagingExtension' => $vendorDir . '/twig/twig/src/Extension/StagingExtension.php',
    'Twig\\Extension\\StringLoaderExtension' => $vendorDir . '/twig/twig/src/Extension/StringLoaderExtension.php',
    'Twig\\FileExtensionEscapingStrategy' => $vendorDir . '/twig/twig/src/FileExtensionEscapingStrategy.php',
    'Twig\\Lexer' => $vendorDir . '/twig/twig/src/Lexer.php',
    'Twig\\Loader\\ArrayLoader' => $vendorDir . '/twig/twig/src/Loader/ArrayLoader.php',
    'Twig\\Loader\\ChainLoader' => $vendorDir . '/twig/twig/src/Loader/ChainLoader.php',
    'Twig\\Loader\\FilesystemLoader' => $vendorDir . '/twig/twig/src/Loader/FilesystemLoader.php',
    'Twig\\Loader\\LoaderInterface' => $vendorDir . '/twig/twig/src/Loader/LoaderInterface.php',
    'Twig\\Markup' => $vendorDir . '/twig/twig/src/Markup.php',
    'Twig\\NodeTraverser' => $vendorDir . '/twig/twig/src/NodeTraverser.php',
    'Twig\\NodeVisitor\\AbstractNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/AbstractNodeVisitor.php',
    'Twig\\NodeVisitor\\EscaperNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/EscaperNodeVisitor.php',
    'Twig\\NodeVisitor\\MacroAutoImportNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/MacroAutoImportNodeVisitor.php',
    'Twig\\NodeVisitor\\NodeVisitorInterface' => $vendorDir . '/twig/twig/src/NodeVisitor/NodeVisitorInterface.php',
    'Twig\\NodeVisitor\\OptimizerNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/OptimizerNodeVisitor.php',
    'Twig\\NodeVisitor\\SafeAnalysisNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/SafeAnalysisNodeVisitor.php',
    'Twig\\NodeVisitor\\SandboxNodeVisitor' => $vendorDir . '/twig/twig/src/NodeVisitor/SandboxNodeVisitor.php',
    'Twig\\Node\\AutoEscapeNode' => $vendorDir . '/twig/twig/src/Node/AutoEscapeNode.php',
    'Twig\\Node\\BlockNode' => $vendorDir . '/twig/twig/src/Node/BlockNode.php',
    'Twig\\Node\\BlockReferenceNode' => $vendorDir . '/twig/twig/src/Node/BlockReferenceNode.php',
    'Twig\\Node\\BodyNode' => $vendorDir . '/twig/twig/src/Node/BodyNode.php',
    'Twig\\Node\\CheckSecurityCallNode' => $vendorDir . '/twig/twig/src/Node/CheckSecurityCallNode.php',
    'Twig\\Node\\CheckSecurityNode' => $vendorDir . '/twig/twig/src/Node/CheckSecurityNode.php',
    'Twig\\Node\\CheckToStringNode' => $vendorDir . '/twig/twig/src/Node/CheckToStringNode.php',
    'Twig\\Node\\DeprecatedNode' => $vendorDir . '/twig/twig/src/Node/DeprecatedNode.php',
    'Twig\\Node\\DoNode' => $vendorDir . '/twig/twig/src/Node/DoNode.php',
    'Twig\\Node\\EmbedNode' => $vendorDir . '/twig/twig/src/Node/EmbedNode.php',
    'Twig\\Node\\Expression\\AbstractExpression' => $vendorDir . '/twig/twig/src/Node/Expression/AbstractExpression.php',
    'Twig\\Node\\Expression\\ArrayExpression' => $vendorDir . '/twig/twig/src/Node/Expression/ArrayExpression.php',
    'Twig\\Node\\Expression\\ArrowFunctionExpression' => $vendorDir . '/twig/twig/src/Node/Expression/ArrowFunctionExpression.php',
    'Twig\\Node\\Expression\\AssignNameExpression' => $vendorDir . '/twig/twig/src/Node/Expression/AssignNameExpression.php',
    'Twig\\Node\\Expression\\Binary\\AbstractBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/AbstractBinary.php',
    'Twig\\Node\\Expression\\Binary\\AddBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/AddBinary.php',
    'Twig\\Node\\Expression\\Binary\\AndBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/AndBinary.php',
    'Twig\\Node\\Expression\\Binary\\BitwiseAndBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/BitwiseAndBinary.php',
    'Twig\\Node\\Expression\\Binary\\BitwiseOrBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/BitwiseOrBinary.php',
    'Twig\\Node\\Expression\\Binary\\BitwiseXorBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/BitwiseXorBinary.php',
    'Twig\\Node\\Expression\\Binary\\ConcatBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/ConcatBinary.php',
    'Twig\\Node\\Expression\\Binary\\DivBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/DivBinary.php',
    'Twig\\Node\\Expression\\Binary\\EndsWithBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/EndsWithBinary.php',
    'Twig\\Node\\Expression\\Binary\\EqualBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/EqualBinary.php',
    'Twig\\Node\\Expression\\Binary\\FloorDivBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/FloorDivBinary.php',
    'Twig\\Node\\Expression\\Binary\\GreaterBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/GreaterBinary.php',
    'Twig\\Node\\Expression\\Binary\\GreaterEqualBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/GreaterEqualBinary.php',
    'Twig\\Node\\Expression\\Binary\\InBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/InBinary.php',
    'Twig\\Node\\Expression\\Binary\\LessBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/LessBinary.php',
    'Twig\\Node\\Expression\\Binary\\LessEqualBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/LessEqualBinary.php',
    'Twig\\Node\\Expression\\Binary\\MatchesBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/MatchesBinary.php',
    'Twig\\Node\\Expression\\Binary\\ModBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/ModBinary.php',
    'Twig\\Node\\Expression\\Binary\\MulBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/MulBinary.php',
    'Twig\\Node\\Expression\\Binary\\NotEqualBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/NotEqualBinary.php',
    'Twig\\Node\\Expression\\Binary\\NotInBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/NotInBinary.php',
    'Twig\\Node\\Expression\\Binary\\OrBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/OrBinary.php',
    'Twig\\Node\\Expression\\Binary\\PowerBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/PowerBinary.php',
    'Twig\\Node\\Expression\\Binary\\RangeBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/RangeBinary.php',
    'Twig\\Node\\Expression\\Binary\\SpaceshipBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/SpaceshipBinary.php',
    'Twig\\Node\\Expression\\Binary\\StartsWithBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/StartsWithBinary.php',
    'Twig\\Node\\Expression\\Binary\\SubBinary' => $vendorDir . '/twig/twig/src/Node/Expression/Binary/SubBinary.php',
    'Twig\\Node\\Expression\\BlockReferenceExpression' => $vendorDir . '/twig/twig/src/Node/Expression/BlockReferenceExpression.php',
    'Twig\\Node\\Expression\\CallExpression' => $vendorDir . '/twig/twig/src/Node/Expression/CallExpression.php',
    'Twig\\Node\\Expression\\ConditionalExpression' => $vendorDir . '/twig/twig/src/Node/Expression/ConditionalExpression.php',
    'Twig\\Node\\Expression\\ConstantExpression' => $vendorDir . '/twig/twig/src/Node/Expression/ConstantExpression.php',
    'Twig\\Node\\Expression\\FilterExpression' => $vendorDir . '/twig/twig/src/Node/Expression/FilterExpression.php',
    'Twig\\Node\\Expression\\Filter\\DefaultFilter' => $vendorDir . '/twig/twig/src/Node/Expression/Filter/DefaultFilter.php',
    'Twig\\Node\\Expression\\FunctionExpression' => $vendorDir . '/twig/twig/src/Node/Expression/FunctionExpression.php',
    'Twig\\Node\\Expression\\GetAttrExpression' => $vendorDir . '/twig/twig/src/Node/Expression/GetAttrExpression.php',
    'Twig\\Node\\Expression\\InlinePrint' => $vendorDir . '/twig/twig/src/Node/Expression/InlinePrint.php',
    'Twig\\Node\\Expression\\MethodCallExpression' => $vendorDir . '/twig/twig/src/Node/Expression/MethodCallExpression.php',
    'Twig\\Node\\Expression\\NameExpression' => $vendorDir . '/twig/twig/src/Node/Expression/NameExpression.php',
    'Twig\\Node\\Expression\\NullCoalesceExpression' => $vendorDir . '/twig/twig/src/Node/Expression/NullCoalesceExpression.php',
    'Twig\\Node\\Expression\\ParentExpression' => $vendorDir . '/twig/twig/src/Node/Expression/ParentExpression.php',
    'Twig\\Node\\Expression\\TempNameExpression' => $vendorDir . '/twig/twig/src/Node/Expression/TempNameExpression.php',
    'Twig\\Node\\Expression\\TestExpression' => $vendorDir . '/twig/twig/src/Node/Expression/TestExpression.php',
    'Twig\\Node\\Expression\\Test\\ConstantTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/ConstantTest.php',
    'Twig\\Node\\Expression\\Test\\DefinedTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/DefinedTest.php',
    'Twig\\Node\\Expression\\Test\\DivisiblebyTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/DivisiblebyTest.php',
    'Twig\\Node\\Expression\\Test\\EvenTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/EvenTest.php',
    'Twig\\Node\\Expression\\Test\\NullTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/NullTest.php',
    'Twig\\Node\\Expression\\Test\\OddTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/OddTest.php',
    'Twig\\Node\\Expression\\Test\\SameasTest' => $vendorDir . '/twig/twig/src/Node/Expression/Test/SameasTest.php',
    'Twig\\Node\\Expression\\Unary\\AbstractUnary' => $vendorDir . '/twig/twig/src/Node/Expression/Unary/AbstractUnary.php',
    'Twig\\Node\\Expression\\Unary\\NegUnary' => $vendorDir . '/twig/twig/src/Node/Expression/Unary/NegUnary.php',
    'Twig\\Node\\Expression\\Unary\\NotUnary' => $vendorDir . '/twig/twig/src/Node/Expression/Unary/NotUnary.php',
    'Twig\\Node\\Expression\\Unary\\PosUnary' => $vendorDir . '/twig/twig/src/Node/Expression/Unary/PosUnary.php',
    'Twig\\Node\\Expression\\VariadicExpression' => $vendorDir . '/twig/twig/src/Node/Expression/VariadicExpression.php',
    'Twig\\Node\\FlushNode' => $vendorDir . '/twig/twig/src/Node/FlushNode.php',
    'Twig\\Node\\ForLoopNode' => $vendorDir . '/twig/twig/src/Node/ForLoopNode.php',
    'Twig\\Node\\ForNode' => $vendorDir . '/twig/twig/src/Node/ForNode.php',
    'Twig\\Node\\IfNode' => $vendorDir . '/twig/twig/src/Node/IfNode.php',
    'Twig\\Node\\ImportNode' => $vendorDir . '/twig/twig/src/Node/ImportNode.php',
    'Twig\\Node\\IncludeNode' => $vendorDir . '/twig/twig/src/Node/IncludeNode.php',
    'Twig\\Node\\MacroNode' => $vendorDir . '/twig/twig/src/Node/MacroNode.php',
    'Twig\\Node\\ModuleNode' => $vendorDir . '/twig/twig/src/Node/ModuleNode.php',
    'Twig\\Node\\Node' => $vendorDir . '/twig/twig/src/Node/Node.php',
    'Twig\\Node\\NodeCaptureInterface' => $vendorDir . '/twig/twig/src/Node/NodeCaptureInterface.php',
    'Twig\\Node\\NodeOutputInterface' => $vendorDir . '/twig/twig/src/Node/NodeOutputInterface.php',
    'Twig\\Node\\PrintNode' => $vendorDir . '/twig/twig/src/Node/PrintNode.php',
    'Twig\\Node\\SandboxNode' => $vendorDir . '/twig/twig/src/Node/SandboxNode.php',
    'Twig\\Node\\SetNode' => $vendorDir . '/twig/twig/src/Node/SetNode.php',
    'Twig\\Node\\TextNode' => $vendorDir . '/twig/twig/src/Node/TextNode.php',
    'Twig\\Node\\WithNode' => $vendorDir . '/twig/twig/src/Node/WithNode.php',
    'Twig\\Parser' => $vendorDir . '/twig/twig/src/Parser.php',
    'Twig\\Profiler\\Dumper\\BaseDumper' => $vendorDir . '/twig/twig/src/Profiler/Dumper/BaseDumper.php',
    'Twig\\Profiler\\Dumper\\BlackfireDumper' => $vendorDir . '/twig/twig/src/Profiler/Dumper/BlackfireDumper.php',
    'Twig\\Profiler\\Dumper\\HtmlDumper' => $vendorDir . '/twig/twig/src/Profiler/Dumper/HtmlDumper.php',
    'Twig\\Profiler\\Dumper\\TextDumper' => $vendorDir . '/twig/twig/src/Profiler/Dumper/TextDumper.php',
    'Twig\\Profiler\\NodeVisitor\\ProfilerNodeVisitor' => $vendorDir . '/twig/twig/src/Profiler/NodeVisitor/ProfilerNodeVisitor.php',
    'Twig\\Profiler\\Node\\EnterProfileNode' => $vendorDir . '/twig/twig/src/Profiler/Node/EnterProfileNode.php',
    'Twig\\Profiler\\Node\\LeaveProfileNode' => $vendorDir . '/twig/twig/src/Profiler/Node/LeaveProfileNode.php',
    'Twig\\Profiler\\Profile' => $vendorDir . '/twig/twig/src/Profiler/Profile.php',
    'Twig\\RuntimeLoader\\ContainerRuntimeLoader' => $vendorDir . '/twig/twig/src/RuntimeLoader/ContainerRuntimeLoader.php',
    'Twig\\RuntimeLoader\\FactoryRuntimeLoader' => $vendorDir . '/twig/twig/src/RuntimeLoader/FactoryRuntimeLoader.php',
    'Twig\\RuntimeLoader\\RuntimeLoaderInterface' => $vendorDir . '/twig/twig/src/RuntimeLoader/RuntimeLoaderInterface.php',
    'Twig\\Sandbox\\SecurityError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityError.php',
    'Twig\\Sandbox\\SecurityNotAllowedFilterError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityNotAllowedFilterError.php',
    'Twig\\Sandbox\\SecurityNotAllowedFunctionError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityNotAllowedFunctionError.php',
    'Twig\\Sandbox\\SecurityNotAllowedMethodError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityNotAllowedMethodError.php',
    'Twig\\Sandbox\\SecurityNotAllowedPropertyError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityNotAllowedPropertyError.php',
    'Twig\\Sandbox\\SecurityNotAllowedTagError' => $vendorDir . '/twig/twig/src/Sandbox/SecurityNotAllowedTagError.php',
    'Twig\\Sandbox\\SecurityPolicy' => $vendorDir . '/twig/twig/src/Sandbox/SecurityPolicy.php',
    'Twig\\Sandbox\\SecurityPolicyInterface' => $vendorDir . '/twig/twig/src/Sandbox/SecurityPolicyInterface.php',
    'Twig\\Source' => $vendorDir . '/twig/twig/src/Source.php',
    'Twig\\Template' => $vendorDir . '/twig/twig/src/Template.php',
    'Twig\\TemplateWrapper' => $vendorDir . '/twig/twig/src/TemplateWrapper.php',
    'Twig\\Test\\IntegrationTestCase' => $vendorDir . '/twig/twig/src/Test/IntegrationTestCase.php',
    'Twig\\Test\\NodeTestCase' => $vendorDir . '/twig/twig/src/Test/NodeTestCase.php',
    'Twig\\Token' => $vendorDir . '/twig/twig/src/Token.php',
    'Twig\\TokenParser\\AbstractTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/AbstractTokenParser.php',
    'Twig\\TokenParser\\ApplyTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/ApplyTokenParser.php',
    'Twig\\TokenParser\\AutoEscapeTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/AutoEscapeTokenParser.php',
    'Twig\\TokenParser\\BlockTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/BlockTokenParser.php',
    'Twig\\TokenParser\\DeprecatedTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/DeprecatedTokenParser.php',
    'Twig\\TokenParser\\DoTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/DoTokenParser.php',
    'Twig\\TokenParser\\EmbedTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/EmbedTokenParser.php',
    'Twig\\TokenParser\\ExtendsTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/ExtendsTokenParser.php',
    'Twig\\TokenParser\\FlushTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/FlushTokenParser.php',
    'Twig\\TokenParser\\ForTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/ForTokenParser.php',
    'Twig\\TokenParser\\FromTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/FromTokenParser.php',
    'Twig\\TokenParser\\IfTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/IfTokenParser.php',
    'Twig\\TokenParser\\ImportTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/ImportTokenParser.php',
    'Twig\\TokenParser\\IncludeTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/IncludeTokenParser.php',
    'Twig\\TokenParser\\MacroTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/MacroTokenParser.php',
    'Twig\\TokenParser\\SandboxTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/SandboxTokenParser.php',
    'Twig\\TokenParser\\SetTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/SetTokenParser.php',
    'Twig\\TokenParser\\TokenParserInterface' => $vendorDir . '/twig/twig/src/TokenParser/TokenParserInterface.php',
    'Twig\\TokenParser\\UseTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/UseTokenParser.php',
    'Twig\\TokenParser\\WithTokenParser' => $vendorDir . '/twig/twig/src/TokenParser/WithTokenParser.php',
    'Twig\\TokenStream' => $vendorDir . '/twig/twig/src/TokenStream.php',
    'Twig\\TwigFilter' => $vendorDir . '/twig/twig/src/TwigFilter.php',
    'Twig\\TwigFunction' => $vendorDir . '/twig/twig/src/TwigFunction.php',
    'Twig\\TwigTest' => $vendorDir . '/twig/twig/src/TwigTest.php',
    'Twig\\Util\\DeprecationCollector' => $vendorDir . '/twig/twig/src/Util/DeprecationCollector.php',
    'Twig\\Util\\TemplateDirIterator' => $vendorDir . '/twig/twig/src/Util/TemplateDirIterator.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'Webmozart\\Assert\\Assert' => $vendorDir . '/webmozart/assert/src/Assert.php',
    'Webmozart\\Assert\\InvalidArgumentException' => $vendorDir . '/webmozart/assert/src/InvalidArgumentException.php',
    'Webmozart\\Assert\\Mixin' => $vendorDir . '/webmozart/assert/src/Mixin.php',
    'Williamdes\\MariaDBMySQLKBS\\KBDocumentation' => $vendorDir . '/williamdes/mariadb-mysql-kbs/src/KBDocumentation.php',
    'Williamdes\\MariaDBMySQLKBS\\KBEntry' => $vendorDir . '/williamdes/mariadb-mysql-kbs/src/KBEntry.php',
    'Williamdes\\MariaDBMySQLKBS\\KBException' => $vendorDir . '/williamdes/mariadb-mysql-kbs/src/KBException.php',
    'Williamdes\\MariaDBMySQLKBS\\Search' => $vendorDir . '/williamdes/mariadb-mysql-kbs/src/Search.php',
    'Williamdes\\MariaDBMySQLKBS\\SlimData' => $vendorDir . '/williamdes/mariadb-mysql-kbs/src/SlimData.php',
);
