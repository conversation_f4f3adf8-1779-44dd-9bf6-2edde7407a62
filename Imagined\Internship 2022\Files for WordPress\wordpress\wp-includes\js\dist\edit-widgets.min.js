/*! This file is auto-generated */
!function(){var e={4403:function(e,t){var r;
/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var a=typeof r;if("string"===a||"number"===a)e.push(r);else if(Array.isArray(r)){if(r.length){var o=i.apply(null,r);o&&e.push(o)}}else if("object"===a)if(r.toString===Object.prototype.toString)for(var s in r)n.call(r,s)&&r[s]&&e.push(s);else e.push(r.toString())}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){"use strict";r.r(n),r.d(n,{initialize:function(){return er},reinitializeEditor:function(){return Zt}});var e={};r.r(e),r.d(e,{disableComplementaryArea:function(){return L},enableComplementaryArea:function(){return T},pinItem:function(){return O},setFeatureDefaults:function(){return V},setFeatureValue:function(){return D},toggleFeature:function(){return M},unpinItem:function(){return R}});var t={};r.r(t),r.d(t,{getActiveComplementaryArea:function(){return F},isFeatureActive:function(){return z},isItemPinned:function(){return G}});var i={};r.r(i),r.d(i,{closeGeneralSidebar:function(){return Ie},moveBlockToWidgetArea:function(){return Ae},persistStubPost:function(){return he},saveEditedWidgetAreas:function(){return _e},saveWidgetArea:function(){return fe},saveWidgetAreas:function(){return we},setIsInserterOpened:function(){return ke},setIsListViewOpened:function(){return Se},setIsWidgetAreaOpen:function(){return ye},setWidgetAreasOpenState:function(){return ve},setWidgetIdForClientId:function(){return Ee}});var a={};r.r(a),r.d(a,{getWidgetAreas:function(){return Be},getWidgets:function(){return Ne}});var o={};r.r(o),r.d(o,{__experimentalGetInsertionPoint:function(){return Ve},canInsertBlockInWidgetArea:function(){return Fe},getEditedWidgetAreas:function(){return Le},getIsWidgetAreaOpen:function(){return Me},getParentWidgetAreaBlock:function(){return Te},getReferenceWidgetBlocks:function(){return Oe},getWidget:function(){return xe},getWidgetAreaForWidgetId:function(){return Pe},getWidgetAreas:function(){return We},getWidgets:function(){return Ce},isInserterOpened:function(){return De},isListViewOpened:function(){return Ge},isSavingWidgetAreas:function(){return Re}});var s={};r.r(s),r.d(s,{metadata:function(){return Qe},name:function(){return Je},settings:function(){return Xe}});var l=window.wp.element,c=window.wp.blocks,d=window.wp.data,u=window.wp.blockLibrary,m=window.wp.coreData,g=window.wp.widgets,p=window.wp.preferences,h=window.wp.apiFetch,_=r.n(h);var w=(0,d.combineReducers)({blockInserterPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},listViewPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},widgetAreasOpenState:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;const{type:r}=t;switch(r){case"SET_WIDGET_AREAS_OPEN_STATE":return t.widgetAreasOpenState;case"SET_IS_WIDGET_AREA_OPEN":{const{clientId:r,isOpen:n}=t;return{...e,[r]:n}}default:return e}}}),f=window.wp.i18n,b=window.wp.notices;function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},E.apply(this,arguments)}var v=r(4403),y=r.n(v),k=window.wp.components,S=window.wp.primitives;var I=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"}));var A=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"}));var B=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})),N=window.wp.viewport;var C=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),x=window.lodash,W=window.wp.deprecated,P=r.n(W);const T=(e,t)=>r=>{let{registry:n}=r;t&&n.dispatch(p.store).set(e,"complementaryArea",t)},L=e=>t=>{let{registry:r}=t;r.dispatch(p.store).set(e,"complementaryArea",null)},O=(e,t)=>r=>{let{registry:n}=r;if(!t)return;const i=n.select(p.store).get(e,"pinnedItems");!0!==(null==i?void 0:i[t])&&n.dispatch(p.store).set(e,"pinnedItems",{...i,[t]:!0})},R=(e,t)=>r=>{let{registry:n}=r;if(!t)return;const i=n.select(p.store).get(e,"pinnedItems");n.dispatch(p.store).set(e,"pinnedItems",{...i,[t]:!1})};function M(e,t){return function(r){let{registry:n}=r;P()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),n.dispatch(p.store).toggle(e,t)}}function D(e,t,r){return function(n){let{registry:i}=n;P()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),i.dispatch(p.store).set(e,t,!!r)}}function V(e,t){return function(r){let{registry:n}=r;P()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),n.dispatch(p.store).setDefaults(e,t)}}const F=(0,d.createRegistrySelector)((e=>(t,r)=>e(p.store).get(r,"complementaryArea"))),G=(0,d.createRegistrySelector)((e=>(t,r,n)=>{var i;const a=e(p.store).get(r,"pinnedItems");return null===(i=null==a?void 0:a[n])||void 0===i||i})),z=(0,d.createRegistrySelector)((e=>(t,r,n)=>(P()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(p.store).get(r,n)))),H=(0,d.createReduxStore)("core/interface",{reducer:()=>{},actions:e,selectors:t});(0,d.register)(H);var U=window.wp.plugins,j=(0,U.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`})));var $=j((function(e){let{as:t=k.Button,scope:r,identifier:n,icon:i,selectedIcon:a,...o}=e;const s=t,c=(0,d.useSelect)((e=>e(H).getActiveComplementaryArea(r)===n),[n]),{enableComplementaryArea:u,disableComplementaryArea:m}=(0,d.useDispatch)(H);return(0,l.createElement)(s,E({icon:a&&c?a:i,onClick:()=>{c?m(r):u(r,n)}},(0,x.omit)(o,["name"])))}));var K=e=>{let{smallScreenTitle:t,children:r,className:n,toggleButtonProps:i}=e;const a=(0,l.createElement)($,E({icon:C},i));return(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"components-panel__header interface-complementary-area-header__small"},t&&(0,l.createElement)("span",{className:"interface-complementary-area-header__small-title"},t),a),(0,l.createElement)("div",{className:y()("components-panel__header","interface-complementary-area-header",n),tabIndex:-1},r,a))};function Y(e){let{name:t,as:r=k.Button,onClick:n,...i}=e;return(0,l.createElement)(k.Fill,{name:t},(e=>{let{onClick:t}=e;return(0,l.createElement)(r,E({onClick:n||t?function(){(n||x.noop)(...arguments),(t||x.noop)(...arguments)}:void 0},i))}))}Y.Slot=function(e){let{name:t,as:r=k.ButtonGroup,fillProps:n={},bubblesVirtually:i,...a}=e;return(0,l.createElement)(k.Slot,{name:t,bubblesVirtually:i,fillProps:n},(e=>{if((0,x.isEmpty)(l.Children.toArray(e)))return null;const t=[];l.Children.forEach(e,(e=>{let{props:{__unstableExplicitMenuItem:r,__unstableTarget:n}}=e;n&&r&&t.push(n)}));const n=l.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&t.includes(e.props.__unstableTarget)?null:e));return(0,l.createElement)(r,a,n)}))};var q=Y;const Q=e=>(0,l.createElement)(k.MenuItem,(0,x.omit)(e,["__unstableExplicitMenuItem","__unstableTarget"]));function J(e){let{scope:t,target:r,__unstableExplicitMenuItem:n,...i}=e;return(0,l.createElement)($,E({as:e=>(0,l.createElement)(q,E({__unstableExplicitMenuItem:n,__unstableTarget:`${t}/${r}`,as:Q,name:`${t}/plugin-more-menu`},e)),role:"menuitemcheckbox",selectedIcon:I,name:r,scope:t},i))}function X(e){let{scope:t,...r}=e;return(0,l.createElement)(k.Fill,E({name:`PinnedItems/${t}`},r))}X.Slot=function(e){let{scope:t,className:r,...n}=e;return(0,l.createElement)(k.Slot,E({name:`PinnedItems/${t}`},n),(e=>!(0,x.isEmpty)(e)&&(0,l.createElement)("div",{className:y()(r,"interface-pinned-items")},e)))};var Z=X;function ee(e){let{scope:t,children:r,className:n}=e;return(0,l.createElement)(k.Fill,{name:`ComplementaryArea/${t}`},(0,l.createElement)("div",{className:n},r))}const te=j((function(e){let{children:t,className:r,closeLabel:n=(0,f.__)("Close plugin"),identifier:i,header:a,headerClassName:o,icon:s,isPinnable:c=!0,panelClassName:u,scope:m,name:g,smallScreenTitle:p,title:h,toggleShortcut:_,isActiveByDefault:w,showIconLabels:b=!1}=e;const{isActive:E,isPinned:v,activeArea:S,isSmall:C,isLarge:x}=(0,d.useSelect)((e=>{const{getActiveComplementaryArea:t,isItemPinned:r}=e(H),n=t(m);return{isActive:n===i,isPinned:r(m,i),activeArea:n,isSmall:e(N.store).isViewportMatch("< medium"),isLarge:e(N.store).isViewportMatch("large")}}),[i,m]);!function(e,t,r,n,i){const a=(0,l.useRef)(!1),o=(0,l.useRef)(!1),{enableComplementaryArea:s,disableComplementaryArea:c}=(0,d.useDispatch)(H);(0,l.useEffect)((()=>{n&&i&&!a.current?(c(e),o.current=!0):o.current&&!i&&a.current?(o.current=!1,s(e,t)):o.current&&r&&r!==t&&(o.current=!1),i!==a.current&&(a.current=i)}),[n,i,e,t,r])}(m,i,S,E,C);const{enableComplementaryArea:W,disableComplementaryArea:P,pinItem:T,unpinItem:L}=(0,d.useDispatch)(H);return(0,l.useEffect)((()=>{w&&void 0===S&&!C&&W(m,i)}),[S,w,m,i,C]),(0,l.createElement)(l.Fragment,null,c&&(0,l.createElement)(Z,{scope:m},v&&(0,l.createElement)($,{scope:m,identifier:i,isPressed:E&&(!b||x),"aria-expanded":E,label:h,icon:b?I:s,showTooltip:!b,variant:b?"tertiary":void 0})),g&&c&&(0,l.createElement)(J,{target:g,scope:m,icon:s},h),E&&(0,l.createElement)(ee,{className:y()("interface-complementary-area",r),scope:m},(0,l.createElement)(K,{className:o,closeLabel:n,onClose:()=>P(m),smallScreenTitle:p,toggleButtonProps:{label:n,shortcut:_,scope:m,identifier:i}},a||(0,l.createElement)(l.Fragment,null,(0,l.createElement)("strong",null,h),c&&(0,l.createElement)(k.Button,{className:"interface-complementary-area__pin-unpin-item",icon:v?A:B,label:v?(0,f.__)("Unpin from toolbar"):(0,f.__)("Pin to toolbar"),onClick:()=>(v?L:T)(m,i),isPressed:v,"aria-expanded":v}))),(0,l.createElement)(k.Panel,{className:u},t)))}));te.Slot=function(e){let{scope:t,...r}=e;return(0,l.createElement)(k.Slot,E({name:`ComplementaryArea/${t}`},r))};var re=te,ne=window.wp.compose;var ie=(0,l.forwardRef)((function(e,t){let{footer:r,header:n,sidebar:i,secondarySidebar:a,notices:o,content:s,drawer:c,actions:d,labels:u,className:m,shortcuts:g}=e;const p=(0,k.__unstableUseNavigateRegions)(g);!function(e){(0,l.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const h={...{drawer:(0,f.__)("Drawer"),header:(0,f.__)("Header"),body:(0,f.__)("Content"),secondarySidebar:(0,f.__)("Block Library"),sidebar:(0,f.__)("Settings"),actions:(0,f.__)("Publish"),footer:(0,f.__)("Footer")},...u};return(0,l.createElement)("div",E({},p,{ref:(0,ne.useMergeRefs)([t,p.ref]),className:y()(m,"interface-interface-skeleton",p.className,!!r&&"has-footer")}),!!c&&(0,l.createElement)("div",{className:"interface-interface-skeleton__drawer",role:"region","aria-label":h.drawer,tabIndex:"-1"},c),(0,l.createElement)("div",{className:"interface-interface-skeleton__editor"},!!n&&(0,l.createElement)("div",{className:"interface-interface-skeleton__header",role:"region","aria-label":h.header,tabIndex:"-1"},n),(0,l.createElement)("div",{className:"interface-interface-skeleton__body"},!!a&&(0,l.createElement)("div",{className:"interface-interface-skeleton__secondary-sidebar",role:"region","aria-label":h.secondarySidebar,tabIndex:"-1"},a),!!o&&(0,l.createElement)("div",{className:"interface-interface-skeleton__notices"},o),(0,l.createElement)("div",{className:"interface-interface-skeleton__content",role:"region","aria-label":h.body,tabIndex:"-1"},s),!!i&&(0,l.createElement)("div",{className:"interface-interface-skeleton__sidebar",role:"region","aria-label":h.sidebar,tabIndex:"-1"},i),!!d&&(0,l.createElement)("div",{className:"interface-interface-skeleton__actions",role:"region","aria-label":h.actions,tabIndex:"-1"},d))),!!r&&(0,l.createElement)("div",{className:"interface-interface-skeleton__footer",role:"region","aria-label":h.footer,tabIndex:"-1"},r))}));var ae=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function oe(e){let{as:t=k.DropdownMenu,className:r,label:n=(0,f.__)("Options"),popoverProps:i,toggleProps:a,children:o}=e;return(0,l.createElement)(t,{className:y()("interface-more-menu-dropdown",r),icon:ae,label:n,popoverProps:{position:"bottom left",...i,className:y()("interface-more-menu-dropdown__content",null==i?void 0:i.className)},toggleProps:{tooltipPosition:"bottom",...a}},(e=>o(e)))}var se=window.wp.blockEditor;function le(e){if("block"===e.id_base){const t=(0,c.parse)(e.instance.raw.content);return t.length?(0,g.addWidgetIdToBlock)(t[0],e.id):(0,g.addWidgetIdToBlock)((0,c.createBlock)("core/paragraph",{},[]),e.id)}let t;return t=e._embedded.about[0].is_multi?{idBase:e.id_base,instance:e.instance}:{id:e.id},(0,g.addWidgetIdToBlock)((0,c.createBlock)("core/legacy-widget",t,[]),e.id)}function ce(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n="core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance);var i,a,o;n?t={...r,id:null!==(i=e.attributes.id)&&void 0!==i?i:r.id,id_base:null!==(a=e.attributes.idBase)&&void 0!==a?a:r.id_base,instance:null!==(o=e.attributes.instance)&&void 0!==o?o:r.instance}:t={...r,id_base:"block",instance:{raw:{content:(0,c.serialize)(e)}}};return delete t.rendered,delete t.rendered_form,t}const de="root",ue="sidebar",me="postType",ge=e=>`widget-area-${e}`;const pe="core/edit-widgets",he=(e,t)=>r=>{let{registry:n}=r;const i=((e,t)=>({id:e,slug:e,status:"draft",type:"page",blocks:t,meta:{widgetAreaId:e}}))(e,t);return n.dispatch(m.store).receiveEntityRecords(de,me,i,{id:i.id},!1),i},_e=()=>async e=>{let{select:t,dispatch:r,registry:n}=e;const i=t.getEditedWidgetAreas();if(null!=i&&i.length)try{await r.saveWidgetAreas(i),n.dispatch(b.store).createSuccessNotice((0,f.__)("Widgets saved."),{type:"snackbar"})}catch(e){n.dispatch(b.store).createErrorNotice((0,f.sprintf)((0,f.__)("There was an error. %s"),e.message),{type:"snackbar"})}},we=e=>async t=>{let{dispatch:r,registry:n}=t;try{for(const t of e)await r.saveWidgetArea(t.id)}finally{await n.dispatch(m.store).finishResolution("getEntityRecord",de,ue,{per_page:-1})}},fe=e=>async t=>{let{dispatch:r,select:n,registry:i}=t;const a=n.getWidgets(),o=i.select(m.store).getEditedEntityRecord(de,me,ge(e)),s=Object.values(a).filter((t=>{let{sidebar:r}=t;return r===e})),l=[],c=o.blocks.filter((e=>{const{id:t}=e.attributes;if("core/legacy-widget"===e.name&&t){if(l.includes(t))return!1;l.push(t)}return!0})),d=[];for(const e of s){n.getWidgetAreaForWidgetId(e.id)||d.push(e)}const u=[],p=[],h=[];for(let t=0;t<c.length;t++){const r=c[t],n=(0,g.getWidgetIdFromBlock)(r),o=a[n],s=ce(r,o);if(h.push(n),o){i.dispatch(m.store).editEntityRecord("root","widget",n,{...s,sidebar:e},{undoIgnore:!0});if(!i.select(m.store).hasEditsForEntityRecord("root","widget",n))continue;p.push((e=>{let{saveEditedEntityRecord:t}=e;return t("root","widget",n)}))}else p.push((t=>{let{saveEntityRecord:r}=t;return r("root","widget",{...s,sidebar:e})}));u.push({block:r,position:t,clientId:r.clientId})}for(const e of d)p.push((t=>{let{deleteEntityRecord:r}=t;return r("root","widget",e.id,{force:!0})}));const _=(await i.dispatch(m.store).__experimentalBatch(p)).filter((e=>!e.hasOwnProperty("deleted"))),w=[];for(let e=0;e<_.length;e++){const t=_[e],{block:r,position:n}=u[e];o.blocks[n].attributes.__internalWidgetId=t.id;var b;if(i.select(m.store).getLastEntitySaveError("root","widget",t.id))w.push((null===(b=r.attributes)||void 0===b?void 0:b.name)||(null==r?void 0:r.name));h[n]||(h[n]=t.id)}if(w.length)throw new Error((0,f.sprintf)((0,f.__)("Could not save the following widgets: %s."),w.join(", ")));i.dispatch(m.store).editEntityRecord(de,ue,e,{widgets:h},{undoIgnore:!0}),r(be(e)),i.dispatch(m.store).receiveEntityRecords(de,me,o,void 0)},be=e=>t=>{let{registry:r}=t;r.dispatch(m.store).saveEditedEntityRecord(de,ue,e,{throwOnError:!0})};function Ee(e,t){return{type:"SET_WIDGET_ID_FOR_CLIENT_ID",clientId:e,widgetId:t}}function ve(e){return{type:"SET_WIDGET_AREAS_OPEN_STATE",widgetAreasOpenState:e}}function ye(e,t){return{type:"SET_IS_WIDGET_AREA_OPEN",clientId:e,isOpen:t}}function ke(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function Se(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const Ie=()=>e=>{let{registry:t}=e;t.dispatch(H).disableComplementaryArea(pe)},Ae=(e,t)=>async r=>{let{dispatch:n,select:i,registry:a}=r;const o=a.select(se.store).getBlockRootClientId([e]),s=a.select(se.store).getBlocks().find((e=>{let{attributes:r}=e;return r.id===t})).clientId,l=a.select(se.store).getBlockOrder(s).length;i.getIsWidgetAreaOpen(s)||n.setIsWidgetAreaOpen(s,!0),a.dispatch(se.store).moveBlocksToPosition([e],o,s,l)},Be=()=>async e=>{let{dispatch:t,registry:r}=e;const n={per_page:-1},i=[],a=(await r.resolveSelect(m.store).getEntityRecords(de,ue,n)).sort(((e,t)=>"wp_inactive_widgets"===e.id?1:"wp_inactive_widgets"===t.id?-1:0));for(const e of a)i.push((0,c.createBlock)("core/widget-area",{id:e.id,name:e.name})),e.widgets.length||t(he(ge(e.id),[]));const o={};i.forEach(((e,t)=>{o[e.clientId]=0===t})),t(ve(o)),t(he("widget-areas",i))},Ne=()=>async e=>{let{dispatch:t,registry:r}=e;const n={per_page:-1,_embed:"about"},i=await r.resolveSelect(m.store).getEntityRecords("root","widget",n),a={};for(const e of i){const t=le(e);a[e.sidebar]=a[e.sidebar]||[],a[e.sidebar].push(t)}for(const e in a)a.hasOwnProperty(e)&&t(he(ge(e),a[e]))},Ce=(0,d.createRegistrySelector)((e=>()=>{const t=e(m.store).getEntityRecords("root","widget",{per_page:-1,_embed:"about"});return(0,x.keyBy)(t,"id")})),xe=(0,d.createRegistrySelector)((e=>(t,r)=>e(pe).getWidgets()[r])),We=(0,d.createRegistrySelector)((e=>()=>{const t={per_page:-1};return e(m.store).getEntityRecords(de,ue,t)})),Pe=(0,d.createRegistrySelector)((e=>(t,r)=>e(pe).getWidgetAreas().find((t=>e(m.store).getEditedEntityRecord(de,me,ge(t.id)).blocks.map((e=>(0,g.getWidgetIdFromBlock)(e))).includes(r))))),Te=(0,d.createRegistrySelector)((e=>(t,r)=>{const{getBlock:n,getBlockName:i,getBlockParents:a}=e(se.store);return n(a(r).find((e=>"core/widget-area"===i(e))))})),Le=(0,d.createRegistrySelector)((e=>(t,r)=>{let n=e(pe).getWidgetAreas();return n?(r&&(n=n.filter((e=>{let{id:t}=e;return r.includes(t)}))),n.filter((t=>{let{id:r}=t;return e(m.store).hasEditsForEntityRecord(de,me,ge(r))})).map((t=>{let{id:r}=t;return e(m.store).getEditedEntityRecord(de,ue,r)}))):[]})),Oe=(0,d.createRegistrySelector)((e=>function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=[],i=e(pe).getWidgetAreas();for(const t of i){const i=e(m.store).getEditedEntityRecord(de,me,ge(t.id));for(const e of i.blocks){var a;"core/legacy-widget"!==e.name||r&&(null===(a=e.attributes)||void 0===a?void 0:a.referenceWidgetName)!==r||n.push(e)}}return n})),Re=(0,d.createRegistrySelector)((e=>()=>{var t;const r=null===(t=e(pe).getWidgetAreas())||void 0===t?void 0:t.map((e=>{let{id:t}=e;return t}));if(!r)return!1;for(const t of r){if(e(m.store).isSavingEntityRecord(de,ue,t))return!0}const n=[...Object.keys(e(pe).getWidgets()),void 0];for(const t of n){if(e(m.store).isSavingEntityRecord("root","widget",t))return!0}return!1})),Me=(e,t)=>{const{widgetAreasOpenState:r}=e;return!!r[t]};function De(e){return!!e.blockInserterPanel}function Ve(e){const{rootClientId:t,insertionIndex:r}=e.blockInserterPanel;return{rootClientId:t,insertionIndex:r}}const Fe=(0,d.createRegistrySelector)((e=>(t,r)=>{const n=e(se.store).getBlocks(),[i]=n;return e(se.store).canInsertBlockType(r,i.clientId)}));function Ge(e){return e.listViewPanel}const ze={reducer:w,selectors:o,resolvers:a,actions:i},He=(0,d.createReduxStore)(pe,ze);(0,d.register)(He),_().use((function(e,t){var r;return 0===(null===(r=e.path)||void 0===r?void 0:r.indexOf("/wp/v2/types/widget-area"))?Promise.resolve({}):t(e)}));var Ue=window.wp.hooks;const je=(0,ne.createHigherOrderComponent)((e=>t=>{const{clientId:r,name:n}=t,{widgetAreas:i,currentWidgetAreaId:a,canInsertBlockInWidgetArea:o}=(0,d.useSelect)((e=>{var t;if("core/widget-area"===n)return{};const i=e(He),a=i.getParentWidgetAreaBlock(r);return{widgetAreas:i.getWidgetAreas(),currentWidgetAreaId:null==a||null===(t=a.attributes)||void 0===t?void 0:t.id,canInsertBlockInWidgetArea:i.canInsertBlockInWidgetArea(n)}}),[r,n]),{moveBlockToWidgetArea:s}=(0,d.useDispatch)(He),c=(null==i?void 0:i.length)>1,u="core/widget-area"!==n&&c&&o;return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(e,t),u&&(0,l.createElement)(se.BlockControls,null,(0,l.createElement)(g.MoveToWidgetArea,{widgetAreas:i,currentWidgetAreaId:a,onSelect:e=>{s(t.clientId,e)}})))}),"withMoveToWidgetAreaToolbarItem");(0,Ue.addFilter)("editor.BlockEdit","core/edit-widgets/block-edit",je);var $e=window.wp.mediaUtils;(0,Ue.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>$e.MediaUpload));var Ke=e=>{const[t,r]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{const{ownerDocument:t}=e.current;function n(e){a(e)}function i(){r(!1)}function a(t){e.current.contains(t.target)?r(!0):r(!1)}return t.addEventListener("dragstart",n),t.addEventListener("dragend",i),t.addEventListener("dragenter",a),()=>{t.removeEventListener("dragstart",n),t.removeEventListener("dragend",i),t.removeEventListener("dragenter",a)}}),[]),t};function Ye(e){let{id:t}=e;const[r,n,i]=(0,m.useEntityBlockEditor)("root","postType"),a=(0,l.useRef)(),o=Ke(a),s=(0,se.useInnerBlocksProps)({ref:a},{value:r,onInput:n,onChange:i,templateLock:!1,renderAppender:se.InnerBlocks.ButtonBlockAppender});return(0,l.createElement)("div",{"data-widget-area-id":t,className:y()("wp-block-widget-area__inner-blocks block-editor-inner-blocks editor-styles-wrapper",{"wp-block-widget-area__highlight-drop-zone":o})},(0,l.createElement)("div",s))}const qe=e=>{const[t,r]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{const{ownerDocument:t}=e.current;function n(){r(!0)}function i(){r(!1)}return t.addEventListener("dragstart",n),t.addEventListener("dragend",i),()=>{t.removeEventListener("dragstart",n),t.removeEventListener("dragend",i)}}),[]),t},Qe={name:"core/widget-area",category:"widgets",attributes:{id:{type:"string"},name:{type:"string"}},supports:{html:!1,inserter:!1,customClassName:!1,reusable:!1,__experimentalToolbar:!1,__experimentalParentSelector:!1},editorStyle:"wp-block-widget-area-editor",style:"wp-block-widget-area"},{name:Je}=Qe,Xe={title:(0,f.__)("Widget Area"),description:(0,f.__)("A widget area container."),__experimentalLabel:e=>{let{name:t}=e;return t},edit:function(e){let{clientId:t,className:r,attributes:{id:n,name:i}}=e;const a=(0,d.useSelect)((e=>e(He).getIsWidgetAreaOpen(t)),[t]),{setIsWidgetAreaOpen:o}=(0,d.useDispatch)(He),s=(0,l.useRef)(),c=(0,l.useCallback)((e=>o(t,e)),[t]),u=qe(s),g=Ke(s),[p,h]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{u?g&&!a?(c(!0),h(!0)):!g&&a&&p&&c(!1):h(!1)}),[a,u,g,p]),(0,l.createElement)(k.Panel,{className:r,ref:s},(0,l.createElement)(k.PanelBody,{title:i,opened:a,onToggle:()=>{o(t,!a)},scrollAfterOpen:!u},(e=>{let{opened:t}=e;return(0,l.createElement)(k.__unstableDisclosureContent,{className:"wp-block-widget-area__panel-body-content",visible:t},(0,l.createElement)(m.EntityProvider,{kind:"root",type:"postType",id:`widget-area-${n}`},(0,l.createElement)(Ye,{id:n})))})))}};function Ze(e){let{text:t,children:r}=e;const n=(0,ne.useCopyToClipboard)(t);return(0,l.createElement)(k.Button,{variant:"secondary",ref:n},r)}class et extends l.Component{constructor(){super(...arguments),this.reboot=this.reboot.bind(this),this.state={error:null}}componentDidCatch(e){this.setState({error:e})}reboot(){this.props.onError()}render(){const{error:e}=this.state;return e?(0,l.createElement)(se.Warning,{className:"edit-widgets-error-boundary",actions:[(0,l.createElement)(k.Button,{key:"recovery",onClick:this.reboot,variant:"secondary"},(0,f.__)("Attempt Recovery")),(0,l.createElement)(Ze,{key:"copy-error",text:e.stack},(0,f.__)("Copy Error"))]},(0,f.__)("The editor has encountered an unexpected error.")):this.props.children}}var tt=window.wp.reusableBlocks,rt=window.wp.keyboardShortcuts;function nt(){const{redo:e,undo:t}=(0,d.useDispatch)(m.store),{saveEditedWidgetAreas:r}=(0,d.useDispatch)(He);return(0,rt.useShortcut)("core/edit-widgets/undo",(e=>{t(),e.preventDefault()})),(0,rt.useShortcut)("core/edit-widgets/redo",(t=>{e(),t.preventDefault()})),(0,rt.useShortcut)("core/edit-widgets/save",(e=>{e.preventDefault(),r()})),null}nt.Register=function(){const{registerShortcut:e}=(0,d.useDispatch)(rt.store);return(0,l.useEffect)((()=>{e({name:"core/edit-widgets/undo",category:"global",description:(0,f.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/edit-widgets/redo",category:"global",description:(0,f.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}}),e({name:"core/edit-widgets/save",category:"global",description:(0,f.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/edit-widgets/keyboard-shortcuts",category:"main",description:(0,f.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/edit-widgets/next-region",category:"global",description:(0,f.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/edit-widgets/previous-region",category:"global",description:(0,f.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"}]})}),[e]),null};var it=nt;var at=()=>(0,d.useSelect)((e=>{var t;const{getBlockSelectionEnd:r,getBlockName:n}=e(se.store),i=r();if("core/widget-area"===n(i))return i;const{getParentWidgetAreaBlock:a}=e(He),o=a(i),s=null==o?void 0:o.clientId;if(s)return s;const{getEntityRecord:l}=e(m.store),c=l(de,me,"widget-areas");return null==c||null===(t=c.blocks[0])||void 0===t?void 0:t.clientId}),[]);function ot(e){let{blockEditorSettings:t,children:r,...n}=e;const{hasUploadPermissions:i,reusableBlocks:a,isFixedToolbarActive:o,keepCaretInsideBlock:s}=(0,d.useSelect)((e=>({hasUploadPermissions:(0,x.defaultTo)(e(m.store).canUser("create","media"),!0),widgetAreas:e(He).getWidgetAreas(),widgets:e(He).getWidgets(),reusableBlocks:[],isFixedToolbarActive:!!e(p.store).get("core/edit-widgets","fixedToolbar"),keepCaretInsideBlock:!!e(p.store).get("core/edit-widgets","keepCaretInsideBlock")})),[]),{setIsInserterOpened:c}=(0,d.useDispatch)(He),u=(0,l.useMemo)((()=>{let e;return i&&(e=e=>{let{onError:r,...n}=e;(0,$e.uploadMedia)({wpAllowedMimeTypes:t.allowedMimeTypes,onError:e=>{let{message:t}=e;return r(t)},...n})}),{...t,__experimentalReusableBlocks:a,hasFixedToolbar:o,keepCaretInsideBlock:s,mediaUpload:e,templateLock:"all",__experimentalSetIsInserterOpened:c}}),[t,o,s,i,a,c]),g=at(),[h,_,w]=(0,m.useEntityBlockEditor)(de,me,{id:"widget-areas"});return(0,l.createElement)(rt.ShortcutProvider,null,(0,l.createElement)(se.BlockEditorKeyboardShortcuts.Register,null),(0,l.createElement)(it.Register,null),(0,l.createElement)(k.SlotFillProvider,null,(0,l.createElement)(se.BlockEditorProvider,E({value:h,onInput:_,onChange:w,settings:u,useSubRegistry:!1},n),(0,l.createElement)(se.CopyHandler,null,r),(0,l.createElement)(tt.ReusableBlocksMenuItems,{rootClientId:g}))))}var st=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"}));var lt=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})),ct=window.wp.url,dt=window.wp.dom;function ut(e){let{selectedWidgetAreaId:t}=e;const r=(0,d.useSelect)((e=>e(He).getWidgetAreas()),[]),n=(0,l.useMemo)((()=>t&&(null==r?void 0:r.find((e=>e.id===t)))),[t,r]);let i;return i=n?"wp_inactive_widgets"===t?(0,f.__)("Blocks in this Widget Area will not be displayed in your site."):n.description:(0,f.__)("Widget Areas are global parts in your site’s layout that can accept blocks. These vary by theme, but are typically parts like your Sidebar or Footer."),(0,l.createElement)("div",{className:"edit-widgets-widget-areas"},(0,l.createElement)("div",{className:"edit-widgets-widget-areas__top-container"},(0,l.createElement)(se.BlockIcon,{icon:lt}),(0,l.createElement)("div",null,(0,l.createElement)("p",{dangerouslySetInnerHTML:{__html:(0,dt.safeHTML)(i)}}),0===(null==r?void 0:r.length)&&(0,l.createElement)("p",null,(0,f.__)("Your theme does not contain any Widget Areas.")),!n&&(0,l.createElement)(k.Button,{href:(0,ct.addQueryArgs)("customize.php",{"autofocus[panel]":"widgets",return:window.location.pathname}),variant:"tertiary"},(0,f.__)("Manage with live preview")))))}const mt=l.Platform.select({web:!0,native:!1}),gt="edit-widgets/block-inspector",pt="edit-widgets/block-areas";function ht(e){let{identifier:t,label:r,isActive:n}=e;const{enableComplementaryArea:i}=(0,d.useDispatch)(H);return(0,l.createElement)(k.Button,{onClick:()=>i(He.name,t),className:y()("edit-widgets-sidebar__panel-tab",{"is-active":n}),"aria-label":n?(0,f.sprintf)((0,f.__)("%s (selected)"),r):r,"data-label":r},r)}function _t(){const{enableComplementaryArea:e}=(0,d.useDispatch)(H),{currentArea:t,hasSelectedNonAreaBlock:r,isGeneralSidebarOpen:n,selectedWidgetAreaBlock:i}=(0,d.useSelect)((e=>{const{getSelectedBlock:t,getBlock:r,getBlockParentsByBlockName:n}=e(se.store),{getActiveComplementaryArea:i}=e(H),a=t(),o=i(He.name);let s,l=o;return l||(l=a?gt:pt),a&&(s="core/widget-area"===a.name?a:r(n(a.clientId,"core/widget-area")[0])),{currentArea:l,hasSelectedNonAreaBlock:!(!a||"core/widget-area"===a.name),isGeneralSidebarOpen:!!o,selectedWidgetAreaBlock:s}}),[]);return(0,l.useEffect)((()=>{r&&t===pt&&n&&e("core/edit-widgets",gt),!r&&t===gt&&n&&e("core/edit-widgets",pt)}),[r,e]),(0,l.createElement)(re,{className:"edit-widgets-sidebar",header:(0,l.createElement)("ul",null,(0,l.createElement)("li",null,(0,l.createElement)(ht,{identifier:pt,label:i?i.attributes.name:(0,f.__)("Widget Areas"),isActive:t===pt})),(0,l.createElement)("li",null,(0,l.createElement)(ht,{identifier:gt,label:(0,f.__)("Block"),isActive:t===gt}))),headerClassName:"edit-widgets-sidebar__panel-tabs",title:(0,f.__)("Settings"),closeLabel:(0,f.__)("Close settings"),scope:"core/edit-widgets",identifier:t,icon:st,isActiveByDefault:mt},t===pt&&(0,l.createElement)(ut,{selectedWidgetAreaId:null==i?void 0:i.attributes.id}),t===gt&&(r?(0,l.createElement)(se.BlockInspector,null):(0,l.createElement)("span",{className:"block-editor-block-inspector__no-blocks"},(0,f.__)("No block selected."))))}var wt=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));var ft=(0,l.createElement)(S.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(S.Path,{d:"M13.8 5.2H3v1.5h10.8V5.2zm-3.6 12v1.5H21v-1.5H10.2zm7.2-6H6.6v1.5h10.8v-1.5z"}));var bt=function(){const{hasEditedWidgetAreaIds:e,isSaving:t}=(0,d.useSelect)((e=>{var t;const{getEditedWidgetAreas:r,isSavingWidgetAreas:n}=e(He);return{hasEditedWidgetAreaIds:(null===(t=r())||void 0===t?void 0:t.length)>0,isSaving:n()}}),[]),{saveEditedWidgetAreas:r}=(0,d.useDispatch)(He);return(0,l.createElement)(k.Button,{variant:"primary",isBusy:t,"aria-disabled":t,onClick:t?void 0:r,disabled:!e},t?(0,f.__)("Saving…"):(0,f.__)("Update"))};var Et=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"}));var vt=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})),yt=window.wp.keycodes;function kt(){const e=(0,d.useSelect)((e=>e(m.store).hasUndo()),[]),{undo:t}=(0,d.useDispatch)(m.store);return(0,l.createElement)(k.ToolbarButton,{icon:(0,f.isRTL)()?vt:Et,label:(0,f.__)("Undo"),shortcut:yt.displayShortcut.primary("z"),"aria-disabled":!e,onClick:e?t:void 0})}function St(){const e=(0,d.useSelect)((e=>e(m.store).hasRedo()),[]),{redo:t}=(0,d.useDispatch)(m.store);return(0,l.createElement)(k.ToolbarButton,{icon:(0,f.isRTL)()?Et:vt,label:(0,f.__)("Redo"),shortcut:yt.displayShortcut.primaryShift("z"),"aria-disabled":!e,onClick:e?t:void 0})}var It=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z"}));const At=[{keyCombination:{modifier:"primary",character:"b"},description:(0,f.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,f.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,f.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,f.__)("Remove a link.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,f.__)("Underline the selected text.")}];function Bt(e){let{keyCombination:t,forceAriaLabel:r}=e;const n=t.modifier?yt.displayShortcutList[t.modifier](t.character):t.character,i=t.modifier?yt.shortcutAriaLabel[t.modifier](t.character):t.character;return(0,l.createElement)("kbd",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":r||i},(0,x.castArray)(n).map(((e,t)=>"+"===e?(0,l.createElement)(l.Fragment,{key:t},e):(0,l.createElement)("kbd",{key:t,className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key"},e))))}var Nt=function(e){let{description:t,keyCombination:r,aliases:n=[],ariaLabel:i}=e;return(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-description"},t),(0,l.createElement)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-term"},(0,l.createElement)(Bt,{keyCombination:r,forceAriaLabel:i}),n.map(((e,t)=>(0,l.createElement)(Bt,{keyCombination:e,forceAriaLabel:i,key:t})))))};var Ct=function(e){let{name:t}=e;const{keyCombination:r,description:n,aliases:i}=(0,d.useSelect)((e=>{const{getShortcutKeyCombination:r,getShortcutDescription:n,getShortcutAliases:i}=e(rt.store);return{keyCombination:r(t),aliases:i(t),description:n(t)}}),[t]);return r?(0,l.createElement)(Nt,{keyCombination:r,description:n,aliases:i}):null};const xt=e=>{let{shortcuts:t}=e;return(0,l.createElement)("ul",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list"},t.map(((e,t)=>(0,l.createElement)("li",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut",key:t},(0,x.isString)(e)?(0,l.createElement)(Ct,{name:e}):(0,l.createElement)(Nt,e)))))},Wt=e=>{let{title:t,shortcuts:r,className:n}=e;return(0,l.createElement)("section",{className:y()("edit-widgets-keyboard-shortcut-help-modal__section",n)},!!t&&(0,l.createElement)("h2",{className:"edit-widgets-keyboard-shortcut-help-modal__section-title"},t),(0,l.createElement)(xt,{shortcuts:r}))},Pt=e=>{let{title:t,categoryName:r,additionalShortcuts:n=[]}=e;const i=(0,d.useSelect)((e=>e(rt.store).getCategoryShortcuts(r)),[r]);return(0,l.createElement)(Wt,{title:t,shortcuts:i.concat(n)})};function Tt(e){let{isModalActive:t,toggleModal:r}=e;return(0,rt.useShortcut)("core/edit-widgets/keyboard-shortcuts",r,{bindGlobal:!0}),t?(0,l.createElement)(k.Modal,{className:"edit-widgets-keyboard-shortcut-help-modal",title:(0,f.__)("Keyboard shortcuts"),closeLabel:(0,f.__)("Close"),onRequestClose:r},(0,l.createElement)(Wt,{className:"edit-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/edit-widgets/keyboard-shortcuts"]}),(0,l.createElement)(Pt,{title:(0,f.__)("Global shortcuts"),categoryName:"global"}),(0,l.createElement)(Pt,{title:(0,f.__)("Selection shortcuts"),categoryName:"selection"}),(0,l.createElement)(Pt,{title:(0,f.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,f.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,f.__)("Forward-slash")}]}),(0,l.createElement)(Wt,{title:(0,f.__)("Text formatting"),shortcuts:At})):null}const{Fill:Lt,Slot:Ot}=(0,k.createSlotFill)("EditWidgetsToolsMoreMenuGroup");Lt.Slot=e=>{let{fillProps:t}=e;return(0,l.createElement)(Ot,{fillProps:t},(e=>!(0,x.isEmpty)(e)&&e))};var Rt=Lt;function Mt(){const[e,t]=(0,l.useState)(!1),r=()=>t(!e);(0,rt.useShortcut)("core/edit-widgets/keyboard-shortcuts",r);const n=(0,ne.useViewportMatch)("medium");return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(oe,null,(e=>(0,l.createElement)(l.Fragment,null,n&&(0,l.createElement)(k.MenuGroup,{label:(0,f._x)("View","noun")},(0,l.createElement)(p.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"fixedToolbar",label:(0,f.__)("Top toolbar"),info:(0,f.__)("Access all block and document tools in a single place"),messageActivated:(0,f.__)("Top toolbar activated"),messageDeactivated:(0,f.__)("Top toolbar deactivated")})),(0,l.createElement)(k.MenuGroup,{label:(0,f.__)("Tools")},(0,l.createElement)(k.MenuItem,{onClick:()=>{t(!0)},shortcut:yt.displayShortcut.access("h")},(0,f.__)("Keyboard shortcuts")),(0,l.createElement)(p.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"welcomeGuide",label:(0,f.__)("Welcome Guide")}),(0,l.createElement)(k.MenuItem,{role:"menuitem",icon:It,href:(0,f.__)("https://wordpress.org/support/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,f.__)("Help"),(0,l.createElement)(k.VisuallyHidden,{as:"span"},(0,f.__)("(opens in a new tab)"))),(0,l.createElement)(Rt.Slot,{fillProps:{onClose:e}})),(0,l.createElement)(k.MenuGroup,{label:(0,f.__)("Preferences")},(0,l.createElement)(p.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"keepCaretInsideBlock",label:(0,f.__)("Contain text cursor inside block"),info:(0,f.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,f.__)("Contain text cursor inside block activated"),messageDeactivated:(0,f.__)("Contain text cursor inside block deactivated")}),(0,l.createElement)(p.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"themeStyles",info:(0,f.__)("Make the editor look like your theme."),label:(0,f.__)("Use theme styles")}),n&&(0,l.createElement)(p.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"showBlockBreadcrumbs",label:(0,f.__)("Display block breadcrumbs"),info:(0,f.__)("Shows block breadcrumbs at the bottom of the editor."),messageActivated:(0,f.__)("Display block breadcrumbs activated"),messageDeactivated:(0,f.__)("Display block breadcrumbs deactivated")}))))),(0,l.createElement)(Tt,{isModalActive:e,toggleModal:r}))}var Dt=function(){const e=(0,ne.useViewportMatch)("medium"),t=(0,l.useRef)(),r=at(),n=(0,d.useSelect)((e=>e(He).getIsWidgetAreaOpen(r)),[r]),{isInserterOpen:i,isListViewOpen:a}=(0,d.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:r}=e(He);return{isInserterOpen:t(),isListViewOpen:r()}}),[]),{setIsWidgetAreaOpen:o,setIsInserterOpened:s,setIsListViewOpened:c}=(0,d.useDispatch)(He),{selectBlock:u}=(0,d.useDispatch)(se.store),m=(0,l.useCallback)((()=>c(!a)),[c,a]);return(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"edit-widgets-header"},(0,l.createElement)("div",{className:"edit-widgets-header__navigable-toolbar-wrapper"},e&&(0,l.createElement)("h1",{className:"edit-widgets-header__title"},(0,f.__)("Widgets")),!e&&(0,l.createElement)(k.VisuallyHidden,{as:"h1",className:"edit-widgets-header__title"},(0,f.__)("Widgets")),(0,l.createElement)(se.NavigableToolbar,{className:"edit-widgets-header-toolbar","aria-label":(0,f.__)("Document tools")},(0,l.createElement)(k.ToolbarItem,{ref:t,as:k.Button,className:"edit-widgets-header-toolbar__inserter-toggle",variant:"primary",isPressed:i,onMouseDown:e=>{e.preventDefault()},onClick:()=>{i?s(!1):(n||(u(r),o(r,!0)),window.requestAnimationFrame((()=>s(!0))))},icon:wt,label:(0,f._x)("Toggle block inserter","Generic label for block inserter button")}),e&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(kt,null),(0,l.createElement)(St,null),(0,l.createElement)(k.ToolbarItem,{as:k.Button,className:"edit-widgets-header-toolbar__list-view-toggle",icon:ft,isPressed:a,label:(0,f.__)("List View"),onClick:m})))),(0,l.createElement)("div",{className:"edit-widgets-header__actions"},(0,l.createElement)(bt,null),(0,l.createElement)(Z.Slot,{scope:"core/edit-widgets"}),(0,l.createElement)(Mt,null))))};var Vt=function(){const{removeNotice:e}=(0,d.useDispatch)(b.store),{notices:t}=(0,d.useSelect)((e=>({notices:e(b.store).getNotices()})),[]),r=(0,x.filter)(t,{isDismissible:!0,type:"default"}),n=(0,x.filter)(t,{isDismissible:!1,type:"default"}),i=(0,x.filter)(t,{type:"snackbar"});return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(k.NoticeList,{notices:n,className:"edit-widgets-notices__pinned"}),(0,l.createElement)(k.NoticeList,{notices:r,className:"edit-widgets-notices__dismissible",onRemove:e}),(0,l.createElement)(k.SnackbarList,{notices:i,className:"edit-widgets-notices__snackbar",onRemove:e}))};function Ft(e){let{blockEditorSettings:t}=e;const r=(0,d.useSelect)((e=>!!e(p.store).get("core/edit-widgets","themeStyles")),[]),n=(0,l.useMemo)((()=>r?t.styles:[]),[t,r]);return(0,l.createElement)("div",{className:"edit-widgets-block-editor"},(0,l.createElement)(Vt,null),(0,l.createElement)(se.BlockTools,null,(0,l.createElement)(it,null),(0,l.createElement)(se.__unstableEditorStyles,{styles:n}),(0,l.createElement)(se.BlockSelectionClearer,null,(0,l.createElement)(se.WritingFlow,null,(0,l.createElement)(se.ObserveTyping,null,(0,l.createElement)(se.BlockList,{className:"edit-widgets-main-block-list"}))))))}var Gt=(0,l.createElement)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(S.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));var zt=()=>{const e=(0,d.useSelect)((e=>{var t;const{getEntityRecord:r}=e(m.store),n=r(de,me,"widget-areas");return null==n||null===(t=n.blocks[0])||void 0===t?void 0:t.clientId}),[]);return(0,d.useSelect)((t=>{const{getBlockRootClientId:r,getBlockSelectionEnd:n,getBlockOrder:i,getBlockIndex:a}=t(se.store),o=t(He).__experimentalGetInsertionPoint();if(o.rootClientId)return o;const s=n()||e,l=r(s);return s&&""===l?{rootClientId:s,insertionIndex:i(s).length}:{rootClientId:l,insertionIndex:a(s)+1}}),[e])};function Ht(){const e=(0,ne.useViewportMatch)("medium","<"),{rootClientId:t,insertionIndex:r}=zt(),{setIsInserterOpened:n}=(0,d.useDispatch)(He),i=(0,l.useCallback)((()=>n(!1)),[n]),a=e?"div":k.VisuallyHidden,[o,s]=(0,ne.__experimentalUseDialog)({onClose:i,focusOnMount:null}),c=(0,l.useRef)();return(0,l.useEffect)((()=>{c.current.focusSearch()}),[]),(0,l.createElement)("div",E({ref:o},s,{className:"edit-widgets-layout__inserter-panel"}),(0,l.createElement)(a,{className:"edit-widgets-layout__inserter-panel-header"},(0,l.createElement)(k.Button,{icon:Gt,onClick:i,label:(0,f.__)("Close block inserter")})),(0,l.createElement)("div",{className:"edit-widgets-layout__inserter-panel-content"},(0,l.createElement)(se.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:e,rootClientId:t,__experimentalInsertionIndex:r,ref:c})))}function Ut(){const{setIsListViewOpened:e}=(0,d.useDispatch)(He),t=(0,ne.useFocusOnMount)("firstElement"),r=(0,ne.useFocusReturn)(),n=(0,ne.useFocusReturn)();const i=`edit-widgets-editor__list-view-panel-label-${(0,ne.useInstanceId)(Ut)}`;return(0,l.createElement)("div",{"aria-labelledby":i,className:"edit-widgets-editor__list-view-panel",onKeyDown:function(t){t.keyCode!==yt.ESCAPE||t.defaultPrevented||(t.preventDefault(),e(!1))}},(0,l.createElement)("div",{className:"edit-widgets-editor__list-view-panel-header",ref:r},(0,l.createElement)("strong",{id:i},(0,f.__)("List View")),(0,l.createElement)(k.Button,{icon:C,label:(0,f.__)("Close List View Sidebar"),onClick:()=>e(!1)})),(0,l.createElement)("div",{className:"edit-widgets-editor__list-view-panel-content",ref:(0,ne.useMergeRefs)([n,t])},(0,l.createElement)(se.__experimentalListView,{showNestedBlocks:!0,__experimentalHideContainerBlockActions:!0,__experimentalFeatures:!0,__experimentalPersistentListViewFeatures:!0})))}function jt(){const{isInserterOpen:e,isListViewOpen:t}=(0,d.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:r}=e(He);return{isInserterOpen:t(),isListViewOpen:r()}}),[]);return e?(0,l.createElement)(Ht,null):t?(0,l.createElement)(Ut,null):null}const $t={header:(0,f.__)("Widgets top bar"),body:(0,f.__)("Widgets and blocks"),sidebar:(0,f.__)("Widgets settings"),footer:(0,f.__)("Widgets footer")};var Kt=function(e){let{blockEditorSettings:t}=e;const r=(0,ne.useViewportMatch)("medium","<"),n=(0,ne.useViewportMatch)("huge",">="),{setIsInserterOpened:i,setIsListViewOpened:a,closeGeneralSidebar:o}=(0,d.useDispatch)(He),{hasBlockBreadCrumbsEnabled:s,hasSidebarEnabled:c,isInserterOpened:u,isListViewOpened:m,previousShortcut:g,nextShortcut:h}=(0,d.useSelect)((e=>({hasSidebarEnabled:!!e(H).getActiveComplementaryArea(He.name),isInserterOpened:!!e(He).isInserterOpened(),isListViewOpened:!!e(He).isListViewOpened(),hasBlockBreadCrumbsEnabled:!!e(p.store).get("core/edit-widgets","showBlockBreadcrumbs"),previousShortcut:e(rt.store).getAllShortcutKeyCombinations("core/edit-widgets/previous-region"),nextShortcut:e(rt.store).getAllShortcutKeyCombinations("core/edit-widgets/next-region")})),[]);(0,l.useEffect)((()=>{c&&!n&&(i(!1),a(!1))}),[c,n]),(0,l.useEffect)((()=>{!u&&!m||n||o()}),[u,m,n]);const _=m?(0,f.__)("List View"):(0,f.__)("Block Library"),w=m||u;return(0,l.createElement)(ie,{labels:{...$t,secondarySidebar:_},header:(0,l.createElement)(Dt,null),secondarySidebar:w&&(0,l.createElement)(jt,null),sidebar:c&&(0,l.createElement)(re.Slot,{scope:"core/edit-widgets"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)(Ft,{blockEditorSettings:t}),(0,l.createElement)(se.BlockStyles.Slot,{scope:"core/block-inspector"})),footer:s&&!r&&(0,l.createElement)("div",{className:"edit-widgets-layout__footer"},(0,l.createElement)(se.BlockBreadcrumb,{rootLabelText:(0,f.__)("Widgets")})),shortcuts:{previous:g,next:h}})};function Yt(){const e=(0,d.useSelect)((e=>{const{getEditedWidgetAreas:t}=e(He),r=t();return(null==r?void 0:r.length)>0}),[]);return(0,l.useEffect)((()=>{const t=t=>{if(e)return t.returnValue=(0,f.__)("You have unsaved changes. If you proceed, they will be lost."),t.returnValue};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e]),null}function qt(){var e;const t=(0,d.useSelect)((e=>!!e(p.store).get("core/edit-widgets","welcomeGuide")),[]),{toggle:r}=(0,d.useDispatch)(p.store),n=(0,d.useSelect)((e=>e(He).getWidgetAreas({per_page:-1})),[]);if(!t)return null;const i=null==n?void 0:n.every((e=>"wp_inactive_widgets"===e.id||e.widgets.every((e=>e.startsWith("block-"))))),a=null!==(e=null==n?void 0:n.filter((e=>"wp_inactive_widgets"!==e.id)).length)&&void 0!==e?e:0;return(0,l.createElement)(k.Guide,{className:"edit-widgets-welcome-guide",contentLabel:(0,f.__)("Welcome to block Widgets"),finishButtonText:(0,f.__)("Get started"),onFinish:()=>r("core/edit-widgets","welcomeGuide"),pages:[{image:(0,l.createElement)(Qt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,f.__)("Welcome to block Widgets")),i?(0,l.createElement)(l.Fragment,null,(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,f.sprintf)((0,f._n)("Your theme provides %s “block” area for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.","Your theme provides %s different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.",a),a))):(0,l.createElement)(l.Fragment,null,(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,f.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,l.createElement)("strong",null,(0,f.__)("Want to stick with the old widgets?"))," ",(0,l.createElement)(k.ExternalLink,{href:(0,f.__)("https://wordpress.org/plugins/classic-widgets/")},(0,f.__)("Get the Classic Widgets plugin.")))))},{image:(0,l.createElement)(Qt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,f.__)("Make each block your own")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,f.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")))},{image:(0,l.createElement)(Qt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,f.__)("Get to know the block library")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,l.createInterpolateElement)((0,f.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,l.createElement)("img",{className:"edit-widgets-welcome-guide__inserter-icon",alt:(0,f.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})))},{image:(0,l.createElement)(Qt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,f.__)("Learn how to use the block editor")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,f.__)("New to the block editor? Want to learn more about using it? "),(0,l.createElement)(k.ExternalLink,{href:(0,f.__)("https://wordpress.org/support/article/wordpress-editor/")},(0,f.__)("Here's a detailed guide."))))}]})}function Qt(e){let{nonAnimatedSrc:t,animatedSrc:r}=e;return(0,l.createElement)("picture",{className:"edit-widgets-welcome-guide__image"},(0,l.createElement)("source",{srcSet:t,media:"(prefers-reduced-motion: reduce)"}),(0,l.createElement)("img",{src:r,width:"312",height:"240",alt:""}))}var Jt=function(e){let{blockEditorSettings:t,onError:r}=e;const{createErrorNotice:n}=(0,d.useDispatch)(b.store);return(0,l.createElement)(et,{onError:r},(0,l.createElement)(ot,{blockEditorSettings:t},(0,l.createElement)(Kt,{blockEditorSettings:t}),(0,l.createElement)(_t,null),(0,l.createElement)(k.Popover.Slot,null),(0,l.createElement)(U.PluginArea,{onError:function(e){n((0,f.sprintf)((0,f.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}),(0,l.createElement)(Yt,null),(0,l.createElement)(qt,null)))};const Xt=["core/more","core/freeform","core/template-part","core/block"];function Zt(e,t){(0,l.unmountComponentAtNode)(e);const r=Zt.bind(null,e,t);(0,l.render)((0,l.createElement)(Jt,{blockEditorSettings:t,onError:r}),e)}function er(e,t){const r=document.getElementById(e),n=Zt.bind(null,r,t),i=(0,u.__experimentalGetCoreBlocks)().filter((e=>!(Xt.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));(0,d.dispatch)(p.store).setDefaults("core/edit-widgets",{fixedToolbar:!1,welcomeGuide:!0,showBlockBreadcrumbs:!0,themeStyles:!0}),(0,d.dispatch)(c.store).__experimentalReapplyBlockTypeFilters(),(0,u.registerCoreBlocks)(i),(0,g.registerLegacyWidgetBlock)(),(0,g.registerLegacyWidgetVariations)(t),tr(s),(0,g.registerWidgetGroupBlock)(),t.__experimentalFetchLinkSuggestions=(e,r)=>(0,m.__experimentalFetchLinkSuggestions)(e,r,t),(0,c.setFreeformContentHandlerName)("core/html"),(0,l.render)((0,l.createElement)(Jt,{blockEditorSettings:t,onError:n}),r)}const tr=e=>{if(!e)return;const{metadata:t,settings:r,name:n}=e;t&&(0,c.unstable__bootstrapServerSideBlockDefinitions)({[n]:t}),(0,c.registerBlockType)(n,r)}}(),(window.wp=window.wp||{}).editWidgets=n}();