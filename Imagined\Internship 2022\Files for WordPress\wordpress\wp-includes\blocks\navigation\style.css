/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-navigation {
  position: relative;
  --navigation-layout-justification-setting: flex-start;
  --navigation-layout-direction: row;
  --navigation-layout-wrap: wrap;
  --navigation-layout-justify: flex-start;
  --navigation-layout-align: center;
}
.wp-block-navigation ul {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-left: 0;
}
.wp-block-navigation ul,
.wp-block-navigation ul li {
  list-style: none;
  padding: 0;
}
.wp-block-navigation .wp-block-navigation-item {
  display: flex;
  align-items: center;
  position: relative;
}
.wp-block-navigation .wp-block-navigation-item .wp-block-navigation__submenu-container:empty {
  display: none;
}
.wp-block-navigation .wp-block-navigation-item__content {
  color: inherit;
  display: block;
  padding: 0;
}
.wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content {
  text-decoration: underline;
}
.wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content:focus, .wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content:active {
  text-decoration: underline;
}
.wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content {
  text-decoration: line-through;
}
.wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content:focus, .wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content:active {
  text-decoration: line-through;
}
.wp-block-navigation:where(:not([class*="has-text-decoration"])) a {
  text-decoration: none;
}
.wp-block-navigation:where(:not([class*="has-text-decoration"])) a:focus, .wp-block-navigation:where(:not([class*="has-text-decoration"])) a:active {
  text-decoration: none;
}
.wp-block-navigation .wp-block-navigation__submenu-icon {
  align-self: center;
  line-height: 0;
  display: inline-block;
  font-size: inherit;
  padding: 0;
  background-color: inherit;
  color: currentColor;
  border: none;
  width: 0.6em;
  height: 0.6em;
  margin-left: 0.25em;
}
.wp-block-navigation .wp-block-navigation__submenu-icon svg {
  display: inline-block;
  stroke: currentColor;
  width: inherit;
  height: inherit;
  margin-top: 0.075em;
}
.wp-block-navigation.is-vertical {
  --navigation-layout-direction: column;
  --navigation-layout-justify: initial;
  --navigation-layout-align: flex-start;
}
.wp-block-navigation.no-wrap {
  --navigation-layout-wrap: nowrap;
}
.wp-block-navigation.items-justified-center {
  --navigation-layout-justification-setting: center;
  --navigation-layout-justify: center;
}
.wp-block-navigation.items-justified-center.is-vertical {
  --navigation-layout-align: center;
}
.wp-block-navigation.items-justified-right {
  --navigation-layout-justification-setting: flex-end;
  --navigation-layout-justify: flex-end;
}
.wp-block-navigation.items-justified-right.is-vertical {
  --navigation-layout-align: flex-end;
}
.wp-block-navigation.items-justified-space-between {
  --navigation-layout-justification-setting: space-between;
  --navigation-layout-justify: space-between;
}

.wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) {
  background-color: inherit;
  color: inherit;
  position: absolute;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: normal;
  opacity: 0;
  transition: opacity 0.1s linear;
  visibility: hidden;
  width: 0;
  height: 0;
  overflow: hidden;
  left: -1px;
  top: 100%;
}
.wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) > .wp-block-navigation-item > .wp-block-navigation-item__content {
  display: flex;
  flex-grow: 1;
}
.wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) > .wp-block-navigation-item > .wp-block-navigation-item__content .wp-block-navigation__submenu-icon {
  margin-right: 0;
  margin-left: auto;
}
.wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) .wp-block-navigation-item__content {
  margin: 0;
}
@media (min-width: 782px) {
  .wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) .wp-block-navigation__submenu-container {
    left: 100%;
    top: -1px;
  }
  .wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) .wp-block-navigation__submenu-container::before {
    content: "";
    position: absolute;
    right: 100%;
    height: 100%;
    display: block;
    width: 0.5em;
    background: transparent;
  }
  .wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) .wp-block-navigation__submenu-icon {
    margin-right: 0.25em;
  }
  .wp-block-navigation .has-child :where(.wp-block-navigation__submenu-container) .wp-block-navigation__submenu-icon svg {
    transform: rotate(-90deg);
  }
}
.wp-block-navigation .has-child:where(:not(.open-on-click)):hover > .wp-block-navigation__submenu-container {
  visibility: visible;
  overflow: visible;
  opacity: 1;
  width: auto;
  height: auto;
  min-width: 200px;
}
.wp-block-navigation .has-child:where(:not(.open-on-click):not(.open-on-hover-click)):focus-within > .wp-block-navigation__submenu-container {
  visibility: visible;
  overflow: visible;
  opacity: 1;
  width: auto;
  height: auto;
  min-width: 200px;
}
.wp-block-navigation .has-child .wp-block-navigation-submenu__toggle[aria-expanded=true] ~ .wp-block-navigation__submenu-container {
  visibility: visible;
  overflow: visible;
  opacity: 1;
  width: auto;
  height: auto;
  min-width: 200px;
}

.wp-block-navigation.has-background .has-child .wp-block-navigation__submenu-container {
  left: 0;
  top: 100%;
}
@media (min-width: 782px) {
  .wp-block-navigation.has-background .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {
    left: 100%;
    top: 0;
  }
}

.wp-block-navigation-submenu {
  position: relative;
  display: flex;
}
.wp-block-navigation-submenu .wp-block-navigation__submenu-icon svg {
  stroke: currentColor;
}

button.wp-block-navigation-item__content {
  background-color: transparent;
  border: none;
  color: currentColor;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  font-style: inherit;
  font-weight: inherit;
  text-transform: inherit;
  text-align: left;
}

.wp-block-navigation-submenu__toggle {
  cursor: pointer;
}

.wp-block-navigation-item.open-on-click .wp-block-navigation-submenu__toggle {
  padding-right: 0.85em;
}
.wp-block-navigation-item.open-on-click .wp-block-navigation-submenu__toggle + .wp-block-navigation__submenu-icon {
  margin-left: -0.6em;
  pointer-events: none;
}

/**
 * Margins
 */
.wp-block-navigation__responsive-container,
.wp-block-navigation__responsive-close,
.wp-block-navigation__responsive-dialog,
.wp-block-navigation,
.wp-block-navigation .wp-block-page-list,
.wp-block-navigation__container,
.wp-block-navigation__responsive-container-content {
  gap: inherit;
}

.wp-block-navigation:where(.has-background),
.wp-block-navigation:where(.has-background) .wp-block-navigation .wp-block-page-list,
.wp-block-navigation:where(.has-background) .wp-block-navigation__container {
  gap: inherit;
}

/**
 * Paddings
 */
.wp-block-navigation:where(.has-background) .wp-block-navigation-item__content {
  padding: 0.5em 1em;
}

.wp-block-navigation :where(.wp-block-navigation__submenu-container) .wp-block-navigation-item__content {
  padding: 0.5em 1em;
}

/**
 * Justifications.
 */
.wp-block-navigation.items-justified-space-between .wp-block-page-list > .has-child:last-child .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-space-between > .wp-block-navigation__container > .has-child:last-child .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-page-list > .has-child .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container {
  left: auto;
  right: 0;
}
.wp-block-navigation.items-justified-space-between .wp-block-page-list > .has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-space-between > .wp-block-navigation__container > .has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-page-list > .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {
  left: -1px;
  right: -1px;
}
@media (min-width: 782px) {
  .wp-block-navigation.items-justified-space-between .wp-block-page-list > .has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-space-between > .wp-block-navigation__container > .has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-page-list > .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,
.wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {
    left: auto;
    right: 100%;
  }
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__submenu-container {
  background-color: #fff;
  color: #000;
  border: 1px solid rgba(0, 0, 0, 0.15);
}

.wp-block-navigation__container {
  display: flex;
  flex-wrap: var(--navigation-layout-wrap, wrap);
  flex-direction: var(--navigation-layout-direction, initial);
  justify-content: var(--navigation-layout-justify, initial);
  align-items: var(--navigation-layout-align, initial);
  list-style: none;
  margin: 0;
  padding-left: 0;
}
.wp-block-navigation__container .is-responsive {
  display: none;
}

.wp-block-navigation__container:only-child,
.wp-block-page-list:only-child {
  flex-grow: 1;
}

/**
 * Mobile menu.
 */
.wp-block-navigation__responsive-container {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.wp-block-navigation__responsive-container .wp-block-navigation__responsive-container-content {
  display: flex;
  flex-wrap: var(--navigation-layout-wrap, wrap);
  flex-direction: var(--navigation-layout-direction, initial);
  justify-content: var(--navigation-layout-justify, initial);
  align-items: var(--navigation-layout-align, initial);
}
.wp-block-navigation__responsive-container:not(.is-menu-open.is-menu-open) {
  color: inherit !important;
  background-color: inherit !important;
}
.wp-block-navigation__responsive-container.is-menu-open {
  display: flex;
  flex-direction: column;
  background-color: inherit;
  padding: 2rem;
  overflow: auto;
  z-index: 100000;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content {
  padding-top: calc(2rem + 24px);
  overflow: visible;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: var(--navigation-layout-justification-setting, inherit);
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-page-list,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container {
  justify-content: flex-start;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-icon {
  display: none;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .has-child .submenu-container,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .has-child .wp-block-navigation__submenu-container {
  opacity: 1;
  visibility: visible;
  height: auto;
  width: auto;
  overflow: initial;
  min-width: 200px;
  position: static;
  border: none;
  padding-left: 2rem;
  padding-right: 2rem;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-container,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container {
  gap: inherit;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-container {
  padding-top: var(--wp--style--block-gap, 2em);
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation-item__content {
  padding: 0;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation-item,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-page-list {
  display: flex;
  flex-direction: column;
  align-items: var(--navigation-layout-justification-setting, initial);
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation-item .wp-block-navigation__submenu-container,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation-item,
.wp-block-navigation__responsive-container.is-menu-open .wp-block-page-list {
  color: inherit !important;
  background: transparent !important;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container {
  right: auto;
  left: auto;
}
@media (min-width: 600px) {
  .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) {
    display: block;
    width: 100%;
    position: relative;
    z-index: auto;
    background-color: inherit;
  }
  .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) .wp-block-navigation__responsive-container-close {
    display: none;
  }
  .wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container {
    left: 0;
  }
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__responsive-container.is-menu-open {
  background-color: #fff;
  color: #000;
}

.wp-block-navigation__toggle_button_label {
  font-size: 1rem;
  font-weight: bold;
}

.wp-block-navigation__responsive-container-open,
.wp-block-navigation__responsive-container-close {
  vertical-align: middle;
  cursor: pointer;
  color: currentColor;
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
}
.wp-block-navigation__responsive-container-open svg,
.wp-block-navigation__responsive-container-close svg {
  fill: currentColor;
  pointer-events: none;
  display: block;
  width: 24px;
  height: 24px;
}

.wp-block-navigation__responsive-container-open {
  display: flex;
}
@media (min-width: 600px) {
  .wp-block-navigation__responsive-container-open:not(.always-shown) {
    display: none;
  }
}

.wp-block-navigation__responsive-container-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}

.wp-block-navigation__responsive-close {
  width: 100%;
}
.wp-block-navigation__responsive-close:focus {
  outline: none;
}

.is-menu-open .wp-block-navigation__responsive-close,
.is-menu-open .wp-block-navigation__responsive-dialog,
.is-menu-open .wp-block-navigation__responsive-container-content {
  box-sizing: border-box;
}

.wp-block-navigation__responsive-dialog {
  position: relative;
}

html.has-modal-open {
  overflow: hidden;
}