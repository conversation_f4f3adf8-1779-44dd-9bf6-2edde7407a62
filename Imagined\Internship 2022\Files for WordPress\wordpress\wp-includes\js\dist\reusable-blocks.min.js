/*! This file is auto-generated */
!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ReusableBlocksMenuItems:function(){return I},store:function(){return k}});var n={};e.r(n),e.d(n,{__experimentalConvertBlockToStatic:function(){return a},__experimentalConvertBlocksToReusable:function(){return u},__experimentalDeleteReusableBlock:function(){return d},__experimentalSetEditingReusableBlock:function(){return p}});var l={};e.r(l),e.d(l,{__experimentalIsEditingReusableBlock:function(){return m}});var o=window.wp.data,r=window.lodash,c=window.wp.blockEditor,s=window.wp.blocks,i=window.wp.i18n;const a=e=>t=>{let{registry:n}=t;const l=n.select(c.store).getBlock(e),o=n.select("core").getEditedEntityRecord("postType","wp_block",l.attributes.ref),i=(0,s.parse)((0,r.isFunction)(o.content)?o.content(o):o.content);n.dispatch(c.store).replaceBlocks(l.clientId,i)},u=(e,t)=>async n=>{let{registry:l,dispatch:o}=n;const r={title:t||(0,i.__)("Untitled Reusable block"),content:(0,s.serialize)(l.select(c.store).getBlocksByClientId(e)),status:"publish"},a=await l.dispatch("core").saveEntityRecord("postType","wp_block",r),u=(0,s.createBlock)("core/block",{ref:a.id});l.dispatch(c.store).replaceBlocks(e,u),o.__experimentalSetEditingReusableBlock(u.clientId,!0)},d=e=>async t=>{let{registry:n}=t;if(!n.select("core").getEditedEntityRecord("postType","wp_block",e))return;const l=n.select(c.store).getBlocks().filter((t=>(0,s.isReusableBlock)(t)&&t.attributes.ref===e)).map((e=>e.clientId));l.length&&n.dispatch(c.store).removeBlocks(l),await n.dispatch("core").deleteEntityRecord("postType","wp_block",e)};function p(e,t){return{type:"SET_EDITING_REUSABLE_BLOCK",clientId:e,isEditing:t}}var b=(0,o.combineReducers)({isEditingReusableBlock:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return"SET_EDITING_REUSABLE_BLOCK"===(null==t?void 0:t.type)?{...e,[t.clientId]:t.isEditing}:e}});function m(e,t){return e.isEditingReusableBlock[t]}const k=(0,o.createReduxStore)("core/reusable-blocks",{actions:n,reducer:b,selectors:l});(0,o.register)(k);var _=window.wp.element,w=window.wp.components,E=window.wp.primitives;var g=(0,_.createElement)(E.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,_.createElement)(E.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.1.1.1.3 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})),y=window.wp.notices,v=window.wp.coreData;function B(e){let{clientIds:t,rootClientId:n}=e;const[l,r]=(0,_.useState)(!1),[a,u]=(0,_.useState)(""),d=(0,o.useSelect)((e=>{var l;const{canUser:o}=e(v.store),{getBlocksByClientId:r,canInsertBlockType:i}=e(c.store),a=null!==(l=r(t))&&void 0!==l?l:[];return!(1===a.length&&a[0]&&(0,s.isReusableBlock)(a[0])&&!!e(v.store).getEntityRecord("postType","wp_block",a[0].attributes.ref))&&i("core/block",n)&&a.every((e=>!!e&&e.isValid&&(0,s.hasBlockSupport)(e.name,"reusable",!0)))&&!!o("create","blocks")}),[t]),{__experimentalConvertBlocksToReusable:p}=(0,o.useDispatch)(k),{createSuccessNotice:b,createErrorNotice:m}=(0,o.useDispatch)(y.store),E=(0,_.useCallback)((async function(e){try{await p(t,e),b((0,i.__)("Reusable block created."),{type:"snackbar"})}catch(e){m(e.message,{type:"snackbar"})}}),[t]);return d?(0,_.createElement)(c.BlockSettingsMenuControls,null,(e=>{let{onClose:t}=e;return(0,_.createElement)(_.Fragment,null,(0,_.createElement)(w.MenuItem,{icon:g,onClick:()=>{r(!0)}},(0,i.__)("Add to Reusable blocks")),l&&(0,_.createElement)(w.Modal,{title:(0,i.__)("Create Reusable block"),closeLabel:(0,i.__)("Close"),onRequestClose:()=>{r(!1),u("")},overlayClassName:"reusable-blocks-menu-items__convert-modal"},(0,_.createElement)("form",{onSubmit:e=>{e.preventDefault(),E(a),r(!1),u(""),t()}},(0,_.createElement)(w.TextControl,{label:(0,i.__)("Name"),value:a,onChange:u}),(0,_.createElement)(w.Flex,{className:"reusable-blocks-menu-items__convert-modal-actions",justify:"flex-end"},(0,_.createElement)(w.FlexItem,null,(0,_.createElement)(w.Button,{variant:"tertiary",onClick:()=>{r(!1),u("")}},(0,i.__)("Cancel"))),(0,_.createElement)(w.FlexItem,null,(0,_.createElement)(w.Button,{variant:"primary",type:"submit"},(0,i.__)("Save")))))))})):null}var f=window.wp.url;var R=function(e){let{clientId:t}=e;const{canRemove:n,isVisible:l}=(0,o.useSelect)((e=>{const{getBlock:n,canRemoveBlock:l}=e(c.store),{canUser:o}=e(v.store),r=n(t);return{canRemove:l(t),isVisible:!!r&&(0,s.isReusableBlock)(r)&&!!o("update","blocks",r.attributes.ref)}}),[t]),{__experimentalConvertBlockToStatic:r}=(0,o.useDispatch)(k);return l?(0,_.createElement)(c.BlockSettingsMenuControls,null,(0,_.createElement)(w.MenuItem,{href:(0,f.addQueryArgs)("edit.php",{post_type:"wp_block"})},(0,i.__)("Manage Reusable blocks")),n&&(0,_.createElement)(w.MenuItem,{onClick:()=>r(t)},(0,i.__)("Convert to regular blocks"))):null};var I=(0,o.withSelect)((e=>{const{getSelectedBlockClientIds:t}=e(c.store);return{clientIds:t()}}))((function(e){let{clientIds:t,rootClientId:n}=e;return(0,_.createElement)(_.Fragment,null,(0,_.createElement)(B,{clientIds:t,rootClientId:n}),1===t.length&&(0,_.createElement)(R,{clientId:t[0]}))}));(window.wp=window.wp||{}).reusableBlocks=t}();