{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/column", "title": "Column", "category": "text", "parent": ["core/columns"], "description": "A single column within a columns block.", "textdomain": "default", "attributes": {"verticalAlignment": {"type": "string"}, "width": {"type": "string"}, "allowedBlocks": {"type": "array"}, "templateLock": {"type": ["string", "boolean"], "enum": ["all", "insert", false]}}, "supports": {"anchor": true, "reusable": false, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"blockGap": true, "padding": true, "__experimentalDefaultControls": {"padding": true}}, "__experimentalLayout": true}}