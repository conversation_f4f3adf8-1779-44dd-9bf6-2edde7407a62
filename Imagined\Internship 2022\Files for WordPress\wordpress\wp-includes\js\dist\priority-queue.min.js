/*! This file is auto-generated */
!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{createQueue:function(){return o}});var n="undefined"==typeof window?e=>{setTimeout((()=>e(Date.now())),0)}:window.requestIdleCallback||window.requestAnimationFrame;const o=()=>{let e=[],t=new WeakMap,o=!1;const r=i=>{const u="number"==typeof i?()=>!1:()=>i.timeRemaining()>0;do{if(0===e.length)return void(o=!1);const n=e.shift();t.get(n)(),t.delete(n)}while(u());n(r)};return{add:(i,u)=>{t.has(i)||e.push(i),t.set(i,u),o||(o=!0,n(r))},flush:n=>{if(!t.has(n))return!1;const o=e.indexOf(n);e.splice(o,1);const r=t.get(n);return t.delete(n),r(),!0},reset:()=>{e=[],t=new WeakMap,o=!1}}};(window.wp=window.wp||{}).priorityQueue=t}();