.wp-block-separator,
hr {
	border-bottom: var(--separator--height) solid var(--separator--border-color);
	clear: both;
	opacity: 1;

	&[style*="text-align:right"],
	&[style*="text-align: right"] {
		border-right-color: var(--separator--border-color);
	}

	&:not(.is-style-dots) {
		max-width: var(--responsive--aligndefault-width);
	}

	[data-align="full"] > &,
	[data-align="wide"] > & {
		max-width: inherit;
	}

	&.is-style-twentytwentyone-separator-thick {
		border-bottom-width: calc(3 * var(--separator--height));
	}

	&.is-style-dots {
		border-bottom: none;

		&.has-background,
		&.has-text-color {
			background-color: transparent !important;

			&:before {
				color: currentColor !important;
			}
		}

		&:before {
			color: var(--separator--border-color);
		}
	}

	.has-background &,
	[class*="background-color"] &,
	[style*="background-color"] &,
	.wp-block-cover[style*="background-image"] & {
		border-color: currentColor;
	}
}
