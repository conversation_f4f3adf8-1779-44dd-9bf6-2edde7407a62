/*! This file is auto-generated */
!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{Children:function(){return n.Children},Component:function(){return n.Component},Fragment:function(){return n.Fragment},Platform:function(){return k},RawHTML:function(){return w},StrictMode:function(){return n.StrictMode},Suspense:function(){return n.Suspense},cloneElement:function(){return n.cloneElement},concatChildren:function(){return m},createContext:function(){return n.createContext},createElement:function(){return n.createElement},createInterpolateElement:function(){return p},createPortal:function(){return y.createPortal},createRef:function(){return n.createRef},findDOMNode:function(){return y.findDOMNode},forwardRef:function(){return n.forwardRef},isEmptyElement:function(){return b},isValidElement:function(){return n.isValidElement},lazy:function(){return n.lazy},memo:function(){return n.memo},render:function(){return y.render},renderToString:function(){return $},switchChildrenNodeName:function(){return g},unmountComponentAtNode:function(){return y.unmountComponentAtNode},useCallback:function(){return n.useCallback},useContext:function(){return n.useContext},useDebugValue:function(){return n.useDebugValue},useEffect:function(){return n.useEffect},useImperativeHandle:function(){return n.useImperativeHandle},useLayoutEffect:function(){return n.useLayoutEffect},useMemo:function(){return n.useMemo},useReducer:function(){return n.useReducer},useRef:function(){return n.useRef},useState:function(){return n.useState}});var n=window.React;let r,o,i,a;const s=/<(\/)?(\w+)\s*(\/)?>/g;function u(e,t,n,r,o){return{element:e,tokenStart:t,tokenLength:n,prevOffset:r,leadingTextStart:o,children:[]}}const l=e=>{const t="object"==typeof e,r=t&&Object.values(e);return t&&r.length&&r.every((e=>(0,n.isValidElement)(e)))};function c(e){const t=function(){const e=s.exec(r);if(null===e)return["no-more-tokens"];const t=e.index,[n,o,i,a]=e,u=n.length;if(a)return["self-closed",i,t,u];if(o)return["closer",i,t,u];return["opener",i,t,u]}(),[l,c,p,h]=t,m=a.length,g=p>o?o:null;if(!e[c])return f(),!1;switch(l){case"no-more-tokens":if(0!==m){const{leadingTextStart:e,tokenStart:t}=a.pop();i.push(r.substr(e,t))}return f(),!1;case"self-closed":return 0===m?(null!==g&&i.push(r.substr(g,p-g)),i.push(e[c]),o=p+h,!0):(d(u(e[c],p,h)),o=p+h,!0);case"opener":return a.push(u(e[c],p,h,p+h,g)),o=p+h,!0;case"closer":if(1===m)return function(e){const{element:t,leadingTextStart:o,prevOffset:s,tokenStart:u,children:l}=a.pop(),c=e?r.substr(s,e-s):r.substr(s);c&&l.push(c);null!==o&&i.push(r.substr(o,u-o));i.push((0,n.cloneElement)(t,null,...l))}(p),o=p+h,!0;const t=a.pop(),s=r.substr(t.prevOffset,p-t.prevOffset);t.children.push(s),t.prevOffset=p+h;const l=u(t.element,t.tokenStart,t.tokenLength,p+h);return l.children=t.children,d(l),o=p+h,!0;default:return f(),!1}}function f(){const e=r.length-o;0!==e&&i.push(r.substr(o,e))}function d(e){const{element:t,tokenStart:o,tokenLength:i,prevOffset:s,children:u}=e,l=a[a.length-1],c=r.substr(l.prevOffset,o-l.prevOffset);c&&l.children.push(c),l.children.push((0,n.cloneElement)(t,null,...u)),l.prevOffset=s||o+i}var p=(e,t)=>{if(r=e,o=0,i=[],a=[],s.lastIndex=0,!l(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are WPElements");do{}while(c(t));return(0,n.createElement)(n.Fragment,null,...i)},h=window.lodash;function m(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(((e,t,r)=>(n.Children.forEach(t,((t,o)=>{t&&"string"!=typeof t&&(t=(0,n.cloneElement)(t,{key:[r,o].join()})),e.push(t)})),e)),[])}function g(e,t){return e&&n.Children.map(e,((e,r)=>{if((0,h.isString)(e))return(0,n.createElement)(t,{key:r},e);const{children:o,...i}=e.props;return(0,n.createElement)(t,{key:r,...i},o)}))}var y=window.ReactDOM;const b=e=>!(0,h.isNumber)(e)&&((0,h.isString)(e)||(0,h.isArray)(e)?!e.length:!e);var k={OS:"web",select:e=>"web"in e?e.web:e.default,isWeb:!0},v=window.wp.escapeHtml;function w(e){let{children:t,...r}=e,o="";return n.Children.toArray(t).forEach((e=>{"string"==typeof e&&""!==e.trim()&&(o+=e)})),(0,n.createElement)("div",{dangerouslySetInnerHTML:{__html:o},...r})}const{Provider:S,Consumer:x}=(0,n.createContext)(void 0),C=(0,n.forwardRef)((()=>null)),O=new Set(["string","boolean","number"]),E=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),M=new Set(["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"]),R=new Set(["autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"]),T=new Set(["animation","animationIterationCount","baselineShift","borderImageOutset","borderImageSlice","borderImageWidth","columnCount","cx","cy","fillOpacity","flexGrow","flexShrink","floodOpacity","fontWeight","gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart","lineHeight","opacity","order","orphans","r","rx","ry","shapeImageThreshold","stopOpacity","strokeDasharray","strokeDashoffset","strokeMiterlimit","strokeOpacity","strokeWidth","tabSize","widows","x","y","zIndex","zoom"]);function A(e,t){return t.some((t=>0===e.indexOf(t)))}function L(e){return"key"===e||"children"===e}function I(e,t){return"style"===e?function(e){if(!(0,h.isPlainObject)(e))return e;let t;for(const n in e){const r=e[n];if(null==r)continue;t?t+=";":t="";t+=D(n)+":"+W(n,r)}return t}(t):t}const P=["accentHeight","alignmentBaseline","arabicForm","baselineShift","capHeight","clipPath","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","dominantBaseline","enableBackground","fillOpacity","fillRule","floodColor","floodOpacity","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","horizAdvX","horizOriginX","imageRendering","letterSpacing","lightingColor","markerEnd","markerMid","markerStart","overlinePosition","overlineThickness","paintOrder","panose1","pointerEvents","renderingIntent","shapeRendering","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","textAnchor","textDecoration","textRendering","underlinePosition","underlineThickness","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","vHanging","vIdeographic","vMathematical","vectorEffect","vertAdvY","vertOriginX","vertOriginY","wordSpacing","writingMode","xmlnsXlink","xHeight"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),H=["allowReorder","attributeName","attributeType","autoReverse","baseFrequency","baseProfile","calcMode","clipPathUnits","contentScriptType","contentStyleType","diffuseConstant","edgeMode","externalResourcesRequired","filterRes","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","suppressContentEditableWarning","suppressHydrationWarning","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),j=["xlink:actuate","xlink:arcrole","xlink:href","xlink:role","xlink:show","xlink:title","xlink:type","xml:base","xml:lang","xml:space","xmlns:xlink"].reduce(((e,t)=>(e[t.replace(":","").toLowerCase()]=t,e)),{});function z(e){switch(e){case"htmlFor":return"for";case"className":return"class"}const t=e.toLowerCase();return H[t]?H[t]:P[t]?(0,h.kebabCase)(P[t]):j[t]?j[t]:t}function D(e){return(0,h.startsWith)(e,"--")?e:A(e,["ms","O","Moz","Webkit"])?"-"+(0,h.kebabCase)(e):(0,h.kebabCase)(e)}function W(e,t){return"number"!=typeof t||0===t||T.has(e)?t:t+"px"}function N(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null==e||!1===e)return"";if(Array.isArray(e))return U(e,t,r);switch(typeof e){case"string":return(0,v.escapeHTML)(e);case"number":return e.toString()}const{type:o,props:i}=e;switch(o){case n.StrictMode:case n.Fragment:return U(i.children,t,r);case w:const{children:e,...o}=i;return _((0,h.isEmpty)(o)?null:"div",{...o,dangerouslySetInnerHTML:{__html:e}},t,r)}switch(typeof o){case"string":return _(o,i,t,r);case"function":return o.prototype&&"function"==typeof o.prototype.render?F(o,i,t,r):N(o(i,r),t,r)}switch(o&&o.$$typeof){case S.$$typeof:return U(i.children,i.value,r);case x.$$typeof:return N(i.children(t||o._currentValue),t,r);case C.$$typeof:return N(o.render(i),t,r)}return""}function _(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o="";if("textarea"===e&&t.hasOwnProperty("value")?(o=U(t.value,n,r),t=(0,h.omit)(t,"value")):t.dangerouslySetInnerHTML&&"string"==typeof t.dangerouslySetInnerHTML.__html?o=t.dangerouslySetInnerHTML.__html:void 0!==t.children&&(o=U(t.children,n,r)),!e)return o;const i=V(t);return E.has(e)?"<"+e+i+"/>":"<"+e+i+">"+o+"</"+e+">"}function F(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=new e(t,r);"function"==typeof o.getChildContext&&Object.assign(r,o.getChildContext());const i=N(o.render(),n,r);return i}function U(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="";e=(0,h.castArray)(e);for(let o=0;o<e.length;o++){r+=N(e[o],t,n)}return r}function V(e){let t="";for(const n in e){const r=z(n);if(!(0,v.isValidAttributeName)(r))continue;let o=I(n,e[n]);if(!O.has(typeof o))continue;if(L(n))continue;const i=M.has(r);if(i&&!1===o)continue;const a=i||A(n,["data-","aria-"])||R.has(r);("boolean"!=typeof o||a)&&(t+=" "+r,i||("string"==typeof o&&(o=(0,v.escapeAttribute)(o)),t+='="'+o+'"'))}return t}var $=N;(window.wp=window.wp||{}).element=t}();