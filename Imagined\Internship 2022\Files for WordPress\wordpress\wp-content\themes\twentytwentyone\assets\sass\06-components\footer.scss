// Footer
.site-footer {
	padding-top: 0;
	padding-bottom: calc(1.7 * var(--global--spacing-vertical));
	@extend %responsive-alignwide-width;

	// Increase the top vertical spacing when there is no widget area.
	.no-widgets & {
		margin-top: calc(6 * var(--global--spacing-vertical));
	}

	@include media(mobile-only) {

		.no-widgets & {
			margin-top: calc(3 * var(--global--spacing-vertical));
		}
	}
}

// Footer Branding
.site-footer > .site-info {
	padding-top: var(--global--spacing-vertical);
	color: var(--footer--color-text);
	font-family: var(--footer--font-family);
	font-size: var(--footer--font-size);
	line-height: var(--global--line-height-body);
	border-top: 3px solid var(--global--color-border);

	.site-name {
		text-transform: var(--branding--title--text-transform);
		font-size: var(--branding--title--font-size);
	}

	.privacy-policy,
	.powered-by {
		margin-top: calc(0.5 * var(--global--spacing-vertical));
	}

	@include media(desktop) {
		display: flex;
		align-items: center;

		.site-name {
			margin-right: calc(0.5 * var(--global--spacing-vertical));
		}

		.privacy-policy,
		.powered-by {
			margin-top: initial;
			margin-left: auto;
		}

		.privacy-policy + .powered-by {
			margin-left: calc(0.5 * var(--global--spacing-vertical));
		}
	}

	a {
		color: var(--footer--color-link);

		&:link,
		&:visited,
		&:active {
			color: var(--footer--color-link);
		}

		&:hover {
			color: var(--footer--color-link-hover);
		}

		&:focus {
			color: var(--footer--color-link-hover);

			.is-dark-theme & {
				color: var(--wp--style--color--link, var(--global--color-background));
			}

			// Change colors when the body background is white.
			.has-background-white & {
				color: var(--wp--style--color--link, var(--global--color-white));
			}
		}
	}
}
