/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-query-pagination > .wp-block-query-pagination-next,
.wp-block-query-pagination > .wp-block-query-pagination-previous,
.wp-block-query-pagination > .wp-block-query-pagination-numbers {
  margin-right: 0.5em;
  margin-bottom: 0.5em;
}
.wp-block-query-pagination > .wp-block-query-pagination-next:last-child,
.wp-block-query-pagination > .wp-block-query-pagination-previous:last-child,
.wp-block-query-pagination > .wp-block-query-pagination-numbers:last-child {
  margin-right: 0;
}
.wp-block-query-pagination .wp-block-query-pagination-previous-arrow {
  margin-left: 1ch;
  display: inline-block;
}
.wp-block-query-pagination .wp-block-query-pagination-previous-arrow:not(.is-arrow-chevron) {
  transform: scaleX(-1);;
}
.wp-block-query-pagination .wp-block-query-pagination-next-arrow {
  margin-right: 1ch;
  display: inline-block;
}
.wp-block-query-pagination .wp-block-query-pagination-next-arrow:not(.is-arrow-chevron) {
  transform: scaleX(-1);;
}
.wp-block-query-pagination.aligncenter {
  justify-content: center;
}