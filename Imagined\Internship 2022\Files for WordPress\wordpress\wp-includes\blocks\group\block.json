{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/group", "title": "Group", "category": "design", "description": "Gather blocks in a layout container.", "keywords": ["container", "wrapper", "row", "section"], "textdomain": "default", "attributes": {"tagName": {"type": "string", "default": "div"}, "templateLock": {"type": ["string", "boolean"], "enum": ["all", "insert", false]}}, "supports": {"align": ["wide", "full"], "anchor": true, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": ["top", "bottom"], "padding": true, "blockGap": true, "__experimentalDefaultControls": {"padding": true, "blockGap": true}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalLayout": true}, "editorStyle": "wp-block-group-editor", "style": "wp-block-group"}