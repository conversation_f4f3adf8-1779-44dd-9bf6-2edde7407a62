{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/query-pagination", "title": "Pagination", "category": "theme", "parent": ["core/query"], "description": "Displays a paginated navigation to next/previous set of posts, when applicable.", "textdomain": "default", "attributes": {"paginationArrow": {"type": "string", "default": "none"}}, "usesContext": ["queryId", "query"], "providesContext": {"paginationArrow": "paginationArrow"}, "supports": {"align": true, "reusable": false, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "__experimentalLayout": {"allowSwitching": false, "allowInheriting": false, "default": {"type": "flex"}}}, "editorStyle": "wp-block-query-pagination-editor", "style": "wp-block-query-pagination"}