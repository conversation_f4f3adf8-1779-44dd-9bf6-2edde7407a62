{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/social-links", "title": "Social Icons", "category": "widgets", "description": "Display icons linking to your social media profiles or sites.", "keywords": ["links"], "textdomain": "default", "attributes": {"iconColor": {"type": "string"}, "customIconColor": {"type": "string"}, "iconColorValue": {"type": "string"}, "iconBackgroundColor": {"type": "string"}, "customIconBackgroundColor": {"type": "string"}, "iconBackgroundColorValue": {"type": "string"}, "openInNewTab": {"type": "boolean", "default": false}, "showLabels": {"type": "boolean", "default": false}, "size": {"type": "string"}}, "providesContext": {"openInNewTab": "openInNewTab", "showLabels": "showLabels", "iconColorValue": "iconColorValue", "iconBackgroundColorValue": "iconBackgroundColorValue"}, "supports": {"align": ["left", "center", "right"], "anchor": true, "__experimentalExposeControlsToChildren": true, "__experimentalLayout": {"allowSwitching": false, "allowInheriting": false, "allowVerticalAlignment": false, "default": {"type": "flex"}}, "spacing": {"blockGap": ["horizontal", "vertical"], "margin": ["top", "bottom"], "units": ["px", "em", "rem", "vh", "vw"], "__experimentalDefaultControls": {"blockGap": true}}}, "styles": [{"name": "default", "label": "<PERSON><PERSON><PERSON>", "isDefault": true}, {"name": "logos-only", "label": "Logos Only"}, {"name": "pill-shape", "label": "<PERSON><PERSON>"}], "editorStyle": "wp-block-social-links-editor", "style": "wp-block-social-links"}