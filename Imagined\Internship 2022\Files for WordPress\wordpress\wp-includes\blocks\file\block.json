{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/file", "title": "File", "category": "media", "description": "Add a link to a downloadable file.", "keywords": ["document", "pdf", "download"], "textdomain": "default", "attributes": {"id": {"type": "number"}, "href": {"type": "string"}, "fileId": {"type": "string", "source": "attribute", "selector": "a:not([download])", "attribute": "id"}, "fileName": {"type": "string", "source": "html", "selector": "a:not([download])"}, "textLinkHref": {"type": "string", "source": "attribute", "selector": "a:not([download])", "attribute": "href"}, "textLinkTarget": {"type": "string", "source": "attribute", "selector": "a:not([download])", "attribute": "target"}, "showDownloadButton": {"type": "boolean", "default": true}, "downloadButtonText": {"type": "string", "source": "html", "selector": "a[download]"}, "displayPreview": {"type": "boolean"}, "previewHeight": {"type": "number", "default": 600}}, "supports": {"anchor": true, "align": true}, "viewScript": "file:./view.min.js", "editorStyle": "wp-block-file-editor", "style": "wp-block-file"}