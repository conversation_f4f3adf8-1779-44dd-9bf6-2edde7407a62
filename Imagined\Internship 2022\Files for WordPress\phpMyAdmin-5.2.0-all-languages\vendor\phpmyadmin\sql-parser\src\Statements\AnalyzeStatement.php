<?php
/**
 * `ANALYZE` statement.
 */

declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON><PERSON><PERSON>\SqlParser\Statements;

use Php<PERSON>yAdmin\SqlParser\Components\Expression;
use Php<PERSON>yAdmin\SqlParser\Statement;

/**
 * `ANALYZE` statement.
 *
 * ANALYZE [NO_WRITE_TO_BINLOG | LOCAL] TABLE
 *  tbl_name [, tbl_name] ...
 */
class AnalyzeStatement extends Statement
{
    /**
     * Options of this statement.
     *
     * @var array
     */
    public static $OPTIONS = [
        'TABLE' => 1,

        'NO_WRITE_TO_BINLOG' => 2,
        'LOCAL' => 3,
    ];

    /**
     * Analyzed tables.
     *
     * @var Expression[]
     */
    public $tables;
}
