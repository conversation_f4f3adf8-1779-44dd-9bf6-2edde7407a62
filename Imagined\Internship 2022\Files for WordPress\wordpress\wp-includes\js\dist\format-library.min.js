/*! This file is auto-generated */
!function(){"use strict";var e={r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t);var n=window.wp.richText,r=window.wp.element,o=window.wp.i18n,a=window.wp.blockEditor,l=window.wp.primitives;var i=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.<PERSON>,{d:"M14.7 11.3c1-.6 1.5-1.6 1.5-3 0-2.3-1.3-3.4-4-3.4H7v14h5.8c1.4 0 2.5-.3 3.3-1 .8-.7 1.2-1.7 1.2-2.9.1-1.9-.8-3.1-2.6-3.7zm-5.1-4h2.3c.6 0 1.1.1 1.4.4.3.3.5.7.5 1.2s-.2 1-.5 1.2c-.3.3-.8.4-1.4.4H9.6V7.3zm4.6 9c-.4.3-1 .4-1.7.4H9.6v-3.9h2.9c.7 0 1.3.2 1.7.5.4.3.6.8.6 1.5s-.2 1.2-.6 1.5z"}));const c="core/bold",s=(0,o.__)("Bold"),u={name:c,title:s,tagName:"strong",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:u}=e;function m(){l((0,n.toggleFormat)(o,{type:c,title:s}))}return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.RichTextShortcut,{type:"primary",character:"b",onUse:m}),(0,r.createElement)(a.RichTextToolbarButton,{name:"bold",icon:i,title:s,onClick:function(){l((0,n.toggleFormat)(o,{type:c})),u()},isActive:t,shortcutType:"primary",shortcutCharacter:"b"}),(0,r.createElement)(a.__unstableRichTextInputEvent,{inputType:"formatBold",onInput:m}))}};var m=(0,r.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(l.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.1.1.1.3 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"}));const h="core/code",p=(0,o.__)("Inline code"),g={name:h,title:p,tagName:"code",className:null,__unstableInputRule(e){const{start:t,text:r}=e;if("`"!==r.slice(t-1,t))return e;const o=r.slice(0,t-1).lastIndexOf("`");if(-1===o)return e;const a=o,l=t-2;return a===l?e:(e=(0,n.remove)(e,a,a+1),e=(0,n.remove)(e,l,l+1),e=(0,n.applyFormat)(e,{type:h},a,l))},edit(e){let{value:t,onChange:o,onFocus:l,isActive:i}=e;return(0,r.createElement)(a.RichTextToolbarButton,{icon:m,title:p,onClick:function(){o((0,n.toggleFormat)(t,{type:h,title:p})),l()},isActive:i,role:"menuitemcheckbox"})}};var d=window.wp.components;var v=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,r.createElement)(l.Path,{d:"M6.734 16.106l2.176-2.38-1.093-1.028-3.846 4.158 3.846 4.157 1.093-1.027-2.176-2.38h2.811c1.125 0 2.25.03 3.374 0 1.428-.001 3.362-.25 4.963-1.277 1.66-1.065 2.868-2.906 2.868-5.859 0-2.479-1.327-4.896-3.65-5.93-1.82-.813-3.044-.8-4.806-.788l-.567.002v1.5c.184 0 .368 0 .553-.002 1.82-.007 2.704-.014 4.21.657 1.854.827 2.76 2.657 2.76 4.561 0 2.472-.973 3.824-2.178 4.596-1.258.807-2.864 1.04-4.163 1.04h-.02c-1.115.03-2.229 0-3.344 0H6.734z"}));const w=["image"],f="core/image",b=(0,o.__)("Inline image"),y={name:f,title:b,keywords:[(0,o.__)("photo"),(0,o.__)("media")],object:!0,tagName:"img",className:null,attributes:{className:"class",style:"style",url:"src",alt:"alt"},edit:function(e){let{value:t,onChange:o,onFocus:l,isObjectActive:i,activeObjectAttributes:c,contentRef:s}=e;const[u,m]=(0,r.useState)(!1);function h(){m(!1)}return(0,r.createElement)(a.MediaUploadCheck,null,(0,r.createElement)(a.RichTextToolbarButton,{icon:(0,r.createElement)(d.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(d.Path,{d:"M4 18.5h16V17H4v1.5zM16 13v1.5h4V13h-4zM5.1 15h7.8c.6 0 1.1-.5 1.1-1.1V6.1c0-.6-.5-1.1-1.1-1.1H5.1C4.5 5 4 5.5 4 6.1v7.8c0 .6.5 1.1 1.1 1.1zm.4-8.5h7V10l-1-1c-.3-.3-.8-.3-1 0l-1.6 1.5-1.2-.7c-.3-.2-.6-.2-.9 0l-1.3 1V6.5zm0 6.1l1.8-1.3 1.3.8c.3.2.7.2.9-.1l1.5-1.4 1.5 1.4v1.5h-7v-.9z"})),title:b,onClick:function(){m(!0)},isActive:i}),u&&(0,r.createElement)(a.MediaUpload,{allowedTypes:w,onSelect:e=>{let{id:r,url:a,alt:i,width:c}=e;h(),o((0,n.insertObject)(t,{type:f,attributes:{className:`wp-image-${r}`,style:`width: ${Math.min(c,150)}px;`,url:a,alt:i}})),l()},onClose:h,render:e=>{let{open:t}=e;return t(),null}}),i&&(0,r.createElement)(k,{value:t,onChange:o,activeObjectAttributes:c,contentRef:s}))}};function k(e){let{value:t,onChange:a,activeObjectAttributes:l,contentRef:i}=e;const{style:c}=l,[s,u]=(0,r.useState)(null==c?void 0:c.replace(/\D/g,"")),m=(0,n.useAnchorRef)({ref:i,value:t,settings:y});return(0,r.createElement)(d.Popover,{position:"bottom center",focusOnMount:!1,anchorRef:m,className:"block-editor-format-toolbar__image-popover"},(0,r.createElement)("form",{className:"block-editor-format-toolbar__image-container-content",onSubmit:e=>{const n=t.replacements.slice();n[t.start]={type:f,attributes:{...l,style:s?`width: ${s}px;`:""}},a({...t,replacements:n}),e.preventDefault()}},(0,r.createElement)(d.TextControl,{className:"block-editor-format-toolbar__image-container-value",type:"number",label:(0,o.__)("Width"),value:s,min:1,onChange:e=>u(e)}),(0,r.createElement)(d.Button,{icon:v,label:(0,o.__)("Apply"),type:"submit"})))}var E=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M12.5 5L10 19h1.9l2.5-14z"}));const C="core/italic",_=(0,o.__)("Italic"),x={name:C,title:_,tagName:"em",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:i}=e;function c(){l((0,n.toggleFormat)(o,{type:C,title:_}))}return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.RichTextShortcut,{type:"primary",character:"i",onUse:c}),(0,r.createElement)(a.RichTextToolbarButton,{name:"italic",icon:E,title:_,onClick:function(){l((0,n.toggleFormat)(o,{type:C})),i()},isActive:t,shortcutType:"primary",shortcutCharacter:"i"}),(0,r.createElement)(a.__unstableRichTextInputEvent,{inputType:"formatItalic",onInput:c}))}};var T=window.wp.url,S=window.wp.htmlEntities;var F=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M15.6 7.3h-.7l1.6-3.5-.9-.4-3.9 8.5H9v1.5h2l-1.3 2.8H8.4c-2 0-3.7-1.7-3.7-3.7s1.7-3.7 3.7-3.7H10V7.3H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H9l-1.4 3.2.9.4 5.7-12.5h1.4c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.9 0 5.2-2.3 5.2-5.2 0-2.9-2.4-5.2-5.2-5.2z"}));var R=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M15.6 7.2H14v1.5h1.6c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.8 0 5.2-2.3 5.2-5.2 0-2.9-2.3-5.2-5.2-5.2zM4.7 12.4c0-2 1.7-3.7 3.7-3.7H10V7.2H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H10v-1.5H8.4c-2 0-3.7-1.7-3.7-3.7zm4.6.9h5.3v-1.5H9.3v1.5z"})),A=window.wp.a11y,N=window.wp.data,P=window.lodash;function V(e){if(!e)return!1;const t=e.trim();if(!t)return!1;if(/^\S+:/.test(t)){const e=(0,T.getProtocol)(t);if(!(0,T.isValidProtocol)(e))return!1;if((0,P.startsWith)(e,"http")&&!/^https?:\/\/[^\/\s]/i.test(t))return!1;const n=(0,T.getAuthority)(t);if(!(0,T.isValidAuthority)(n))return!1;const r=(0,T.getPath)(t);if(r&&!(0,T.isValidPath)(r))return!1;const o=(0,T.getQueryString)(t);if(o&&!(0,T.isValidQueryString)(o))return!1;const a=(0,T.getFragment)(t);if(a&&!(0,T.isValidFragment)(a))return!1}return!((0,P.startsWith)(t,"#")&&!(0,T.isValidFragment)(t))}function B(e,t,n,r,o){let a=t;const l={forwards:1,backwards:-1}[o]||1,i=-1*l;for(;e[a]&&e[a][r]===n;)a+=l;return a+=i,a}const M=(0,P.partialRight)(B,"backwards"),z=(0,P.partialRight)(B,"forwards"),H=new WeakMap;let L=-1;function I(e){return`link-control-instance-${e}`}var j=function(e){if(e)return H.has(e)?I(H.get(e)):(L+=1,H.set(e,L),I(L))};var O=(0,d.withSpokenMessages)((function(e){let{isActive:t,activeAttributes:l,addingLink:i,value:c,onChange:s,speak:u,stopAddingLink:m,contentRef:h}=e;const p=function(e,t){let r=e.start,o=e.end;if(t){const t=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.start,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.end;const o={start:null,end:null},{formats:a}=e;let l,i;if(null==a||!a.length)return o;const c=a.slice(),s=(0,P.find)(c[n],{type:t.type}),u=(0,P.find)(c[r],{type:t.type}),m=(0,P.find)(c[r-1],{type:t.type});if(s)l=s,i=n;else if(u)l=u,i=r;else{if(!m)return o;l=m,i=r-1}const h=c[i].indexOf(l),p=[c,i,l,h];return n=M(...p),r=z(...p),n=n<0?0:n,{start:n,end:r}}(e,{type:"core/link"});r=t.start,o=t.end+1}return(0,n.slice)(e,r,o)}(c,t),g=p.text,[v,w]=(0,r.useState)(),{createPageEntity:f,userCanCreatePages:b}=(0,N.useSelect)((e=>{const{getSettings:t}=e(a.store),n=t();return{createPageEntity:n.__experimentalCreatePageEntity,userCanCreatePages:n.__experimentalUserCanCreatePages}}),[]),y={url:l.url,type:l.type,id:l.id,opensInNewTab:"_blank"===l.target,title:g,...v},k=(0,n.useAnchorRef)({ref:h,value:c,settings:W}),E=j(k),C=(0,r.useRef)(!!i&&"firstElement");return(0,r.createElement)(d.Popover,{anchorRef:k,focusOnMount:C.current,onClose:m,position:"bottom center"},(0,r.createElement)(a.__experimentalLinkControl,{key:E,value:y,onChange:function(e){e={...v,...e};const r=y.opensInNewTab!==e.opensInNewTab&&y.url===e.url,a=r&&void 0===e.url;if(w(a?e:void 0),a)return;const l=(0,T.prependHTTP)(e.url),i=function(e){let{url:t,type:n,id:r,opensInNewWindow:o}=e;const a={type:"core/link",attributes:{url:t}};return n&&(a.attributes.type=n),r&&(a.attributes.id=r),o&&(a.attributes.target="_blank",a.attributes.rel="noreferrer noopener"),a}({url:l,type:e.type,id:void 0!==e.id&&null!==e.id?String(e.id):void 0,opensInNewWindow:e.opensInNewTab}),h=e.title||l;if((0,n.isCollapsed)(c)&&!t){const e=(0,n.applyFormat)((0,n.create)({text:h}),i,0,h.length);s((0,n.insert)(c,e))}else{let e;h===g?e=(0,n.applyFormat)(c,i):(e=(0,n.create)({text:h}),e=(0,n.applyFormat)(e,i,0,h.length),e=(0,n.replace)(c,g,e)),e.start=e.end,e.activeFormats=[],s(e)}r||m(),V(l)?u(t?(0,o.__)("Link edited."):(0,o.__)("Link inserted."),"assertive"):u((0,o.__)("Warning: the link has been inserted but may have errors. Please test it."),"assertive")},onRemove:function(){const e=(0,n.removeFormat)(c,"core/link");s(e),m(),u((0,o.__)("Link removed."),"assertive")},forceIsEditingLink:i,hasRichPreviews:!0,createSuggestion:f&&async function(e){const t=await f({title:e,status:"draft"});return{id:t.id,type:t.type,title:t.title.rendered,url:t.link,kind:"post-type"}},withCreateSuggestion:b,createSuggestionButtonText:function(e){return(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("Create Page: <mark>%s</mark>"),e),{mark:(0,r.createElement)("mark",null)})},hasTextControl:!0}))}));const U="core/link",G=(0,o.__)("Link");const W={name:U,title:G,tagName:"a",className:null,attributes:{url:"href",type:"data-type",id:"data-id",target:"target"},__unstablePasteRule(e,t){let{html:r,plainText:o}=t;if((0,n.isCollapsed)(e))return e;const a=(r||o).replace(/<[^>]+>/g,"").trim();return(0,T.isURL)(a)?(window.console.log("Created link:\n\n",a),(0,n.applyFormat)(e,{type:U,attributes:{url:(0,S.decodeEntities)(a)}})):e},edit:function(e){let{isActive:t,activeAttributes:l,value:i,onChange:c,onFocus:s,contentRef:u}=e;const[m,h]=(0,r.useState)(!1);function p(){const e=(0,n.getTextContent)((0,n.slice)(i));e&&(0,T.isURL)(e)&&V(e)?c((0,n.applyFormat)(i,{type:U,attributes:{url:e}})):e&&(0,T.isEmail)(e)?c((0,n.applyFormat)(i,{type:U,attributes:{url:`mailto:${e}`}})):h(!0)}function g(){c((0,n.removeFormat)(i,U)),(0,A.speak)((0,o.__)("Link removed."),"assertive")}return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.RichTextShortcut,{type:"primary",character:"k",onUse:p}),(0,r.createElement)(a.RichTextShortcut,{type:"primaryShift",character:"k",onUse:g}),t&&(0,r.createElement)(a.RichTextToolbarButton,{name:"link",icon:F,title:(0,o.__)("Unlink"),onClick:g,isActive:t,shortcutType:"primaryShift",shortcutCharacter:"k"}),!t&&(0,r.createElement)(a.RichTextToolbarButton,{name:"link",icon:R,title:G,onClick:p,isActive:t,shortcutType:"primary",shortcutCharacter:"k"}),(m||t)&&(0,r.createElement)(O,{addingLink:m,stopAddingLink:function(){h(!1),s()},isActive:t,activeAttributes:l,value:i,onChange:c,contentRef:u}))}};var $=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M9.1 9v-.5c0-.6.2-1.1.7-1.4.5-.3 1.2-.5 2-.5.7 0 1.4.1 2.1.3.7.2 1.4.5 2.1.9l.2-1.9c-.6-.3-1.2-.5-1.9-.7-.8-.1-1.6-.2-2.4-.2-1.5 0-2.7.3-3.6 1-.8.7-1.2 1.5-1.2 2.6V9h2zM20 12H4v1h8.3c.3.1.6.2.8.3.5.2.9.5 1.1.8.3.3.4.7.4 1.2 0 .7-.2 1.1-.8 1.5-.5.3-1.2.5-2.1.5-.8 0-1.6-.1-2.4-.3-.8-.2-1.5-.5-2.2-.8L7 18.1c.5.2 1.2.4 2 .6.8.2 1.6.3 2.4.3 1.7 0 3-.3 3.9-1 .9-.7 1.3-1.6 1.3-2.8 0-.9-.2-1.7-.7-2.2H20v-1z"}));const D="core/strikethrough",Q=(0,o.__)("Strikethrough"),K={name:D,title:Q,tagName:"s",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:i}=e;return(0,r.createElement)(a.RichTextToolbarButton,{icon:$,title:Q,onClick:function(){l((0,n.toggleFormat)(o,{type:D,title:Q})),i()},isActive:t,role:"menuitemcheckbox"})}},q="core/underline",J=(0,o.__)("Underline"),X={name:q,title:J,tagName:"span",className:null,attributes:{style:"style"},edit(e){let{value:t,onChange:o}=e;const l=()=>{o((0,n.toggleFormat)(t,{type:q,attributes:{style:"text-decoration: underline;"},title:J}))};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.RichTextShortcut,{type:"primary",character:"u",onUse:l}),(0,r.createElement)(a.__unstableRichTextInputEvent,{inputType:"formatUnderline",onInput:l}))}};var Y=function(e){let{icon:t,size:n=24,...o}=e;return(0,r.cloneElement)(t,{width:n,height:n,...o})};var Z=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M12.9 6h-2l-4 11h1.9l1.1-3h4.2l1.1 3h1.9L12.9 6zm-2.5 6.5l1.5-4.9 1.7 4.9h-3.2z"}));function ee(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.split(";").reduce(((e,t)=>{if(t){const[n,r]=t.split(":");"color"===n&&(e.color=r),"background-color"===n&&r!==ae&&(e.backgroundColor=r)}return e}),{})}function te(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;return e.split(" ").reduce(((e,n)=>{if(n.startsWith("has-")&&n.endsWith("-color")){const r=n.replace(/^has-/,"").replace(/-color$/,""),o=(0,a.getColorObjectByAttributeValues)(t,r);e.color=o.color}return e}),{})}function ne(e,t,r){const o=(0,n.getActiveFormat)(e,t);return o?{...ee(o.attributes.style),...te(o.attributes.class,r)}:{}}function re(e){let{name:t,property:o,value:l,onChange:i}=e;const c=(0,N.useSelect)((e=>{const{getSettings:t}=e(a.store);return(0,P.get)(t(),["colors"],[])}),[]),s=(0,r.useCallback)((e=>{i(function(e,t,r,o){const{color:l,backgroundColor:i}={...ne(e,t,r),...o};if(!l&&!i)return(0,n.removeFormat)(e,t);const c=[],s=[],u={};if(i?c.push(["background-color",i].join(":")):c.push(["background-color",ae].join(":")),l){const e=(0,a.getColorObjectByColorValue)(r,l);e?s.push((0,a.getColorClassName)("color",e.slug)):c.push(["color",l].join(":"))}return c.length&&(u.style=c.join(";")),s.length&&(u.class=s.join(" ")),(0,n.applyFormat)(e,{type:t,attributes:u})}(l,t,c,{[o]:e}))}),[c,i,o]),u=(0,r.useMemo)((()=>ne(l,t,c)),[t,l,c]);return(0,r.createElement)(a.ColorPalette,{value:u[o],onChange:s})}function oe(e){let{name:t,value:l,onChange:i,onClose:c,contentRef:s}=e;const u=(0,a.useCachedTruthy)((0,n.useAnchorRef)({ref:s,value:l,settings:ue}));return(0,r.createElement)(d.Popover,{onClose:c,className:"components-inline-color-popover",anchorRef:u},(0,r.createElement)(d.TabPanel,{tabs:[{name:"color",title:(0,o.__)("Text")},{name:"backgroundColor",title:(0,o.__)("Background")}]},(e=>(0,r.createElement)(re,{name:t,property:e.name,value:l,onChange:i}))))}const ae="rgba(0, 0, 0, 0)",le="core/text-color",ie=(0,o.__)("Highlight"),ce=[];function se(e,t){const{ownerDocument:n}=e,{defaultView:r}=n,o=r.getComputedStyle(e).getPropertyValue(t);return"background-color"===t&&o===ae&&e.parentElement?se(e.parentElement,t):o}const ue={name:le,title:ie,tagName:"mark",className:"has-inline-color",attributes:{style:"style",class:"class"},__unstableFilterAttributeValue(e,t){if("style"!==e)return t;if(t&&t.includes("background-color"))return t;const n=["background-color",ae].join(":");return t?[n,t].join(";"):n},edit:function(e){let{value:t,onChange:o,isActive:l,activeAttributes:i,contentRef:c}=e;const s=(0,a.useSetting)("color.custom"),u=(0,a.useSetting)("color.palette")||ce,[m,h]=(0,r.useState)(!1),p=(0,r.useCallback)((()=>h(!0)),[h]),g=(0,r.useCallback)((()=>h(!1)),[h]),d=(0,r.useMemo)((()=>function(e,t){let{color:n,backgroundColor:r}=t;if(n||r)return{color:n||se(e,"color"),backgroundColor:r===ae?se(e,"background-color"):r}}(c.current,ne(t,le,u))),[t,u]),v=!(0,P.isEmpty)(u)||!s;return v||l?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.RichTextToolbarButton,{className:"format-library-text-color-button",isActive:l,icon:(0,r.createElement)(Y,{icon:Z,style:d}),title:ie,onClick:v?p:()=>o((0,n.removeFormat)(t,le)),role:"menuitemcheckbox"}),m&&(0,r.createElement)(oe,{name:le,onClose:g,activeAttributes:i,value:t,onChange:o,contentRef:c})):null}};var me=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M16.9 18.3l.8-1.2c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.1-.3-.4-.5-.6-.7-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.2 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3L15 19.4h4.3v-1.2h-2.4zM14.1 7.2h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"}));const he="core/subscript",pe=(0,o.__)("Subscript"),ge={name:he,title:pe,tagName:"sub",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:i}=e;return(0,r.createElement)(a.RichTextToolbarButton,{icon:me,title:pe,onClick:function(){l((0,n.toggleFormat)(o,{type:he,title:pe})),i()},isActive:t,role:"menuitemcheckbox"})}};var de=(0,r.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(l.Path,{d:"M16.9 10.3l.8-1.3c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.2-.2-.4-.4-.7-.6-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.1 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3l-1.8 2.8h4.3v-1.2h-2.2zm-2.8-3.1h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"}));const ve="core/superscript",we=(0,o.__)("Superscript"),fe={name:ve,title:we,tagName:"sup",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:i}=e;return(0,r.createElement)(a.RichTextToolbarButton,{icon:de,title:we,onClick:function(){l((0,n.toggleFormat)(o,{type:ve,title:we})),i()},isActive:t,role:"menuitemcheckbox"})}};var be=(0,r.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(l.Path,{d:"M19 6.5H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7c0-1.1-.9-2-2-2zm.5 9c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-7c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v7zM8 12.8h8v-1.5H8v1.5z"}));const ye="core/keyboard",ke=(0,o.__)("Keyboard input");[u,g,y,x,W,K,X,ue,ge,fe,{name:ye,title:ke,tagName:"kbd",className:null,edit(e){let{isActive:t,value:o,onChange:l,onFocus:i}=e;return(0,r.createElement)(a.RichTextToolbarButton,{icon:be,title:ke,onClick:function(){l((0,n.toggleFormat)(o,{type:ye,title:ke})),i()},isActive:t,role:"menuitemcheckbox"})}}].forEach((e=>{let{name:t,...r}=e;return(0,n.registerFormatType)(t,r)})),(window.wp=window.wp||{}).formatLibrary=t}();