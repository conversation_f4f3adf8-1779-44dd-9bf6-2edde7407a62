/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.block-editor-block-list__block[data-type="core/more"] {
  max-width: 100%;
  text-align: center;
  margin-top: 28px;
  margin-bottom: 28px;
}

.wp-block-more {
  display: block;
  text-align: center;
  white-space: nowrap;
}
.wp-block-more input[type=text] {
  position: relative;
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: #757575;
  border: none;
  box-shadow: none;
  white-space: nowrap;
  text-align: center;
  margin: 0;
  border-radius: 4px;
  background: #fff;
  padding: 6px 8px;
  height: 24px;
  max-width: 100%;
}
.wp-block-more input[type=text]:focus {
  box-shadow: none;
}
.wp-block-more::before {
  content: "";
  position: absolute;
  top: calc(50%);
  right: 0;
  left: 0;
  border-top: 3px dashed #ccc;
}