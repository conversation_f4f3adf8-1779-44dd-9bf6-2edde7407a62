/*! This file is auto-generated */
!function(){"use strict";var t={d:function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{__unstableStripHTML:function(){return Q},computeCaretRect:function(){return E},documentHasSelection:function(){return A},documentHasTextSelection:function(){return C},documentHasUncollapsedSelection:function(){return S},focus:function(){return at},getFilesFromDataTransfer:function(){return ct},getOffsetParent:function(){return x},getPhrasingContentSchema:function(){return nt},getRectangleFromRange:function(){return y},getScrollContainer:function(){return R},insertAfter:function(){return X},isEmpty:function(){return Z},isEntirelySelected:function(){return D},isFormElement:function(){return M},isHorizontalEdge:function(){return B},isNumberInput:function(){return v},isPhrasingContent:function(){return rt},isRTL:function(){return H},isTextContent:function(){return ot},isTextField:function(){return T},isVerticalEdge:function(){return j},placeCaretAtHorizontalEdge:function(){return q},placeCaretAtVerticalEdge:function(){return k},remove:function(){return W},removeInvalidHTML:function(){return ut},replace:function(){return G},replaceTag:function(){return $},safeHTML:function(){return K},unwrap:function(){return Y},wrap:function(){return J}});var n={};t.r(n),t.d(n,{find:function(){return c}});var r={};function o(t){return[t?'[tabindex]:not([tabindex^="-"])':"[tabindex]","a[href]","button:not([disabled])",'input:not([type="hidden"]):not([disabled])',"select:not([disabled])","textarea:not([disabled])",'iframe:not([tabindex^="-"])',"object","embed","area[href]","[contenteditable]:not([contenteditable=false])"].join(",")}function i(t){return t.offsetWidth>0||t.offsetHeight>0||t.getClientRects().length>0}function u(t){const e=t.closest("map[name]");if(!e)return!1;const n=t.ownerDocument.querySelector('img[usemap="#'+e.name+'"]');return!!n&&i(n)}function c(t){let{sequential:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=t.querySelectorAll(o(e));return Array.from(n).filter((t=>{if(!i(t))return!1;const{nodeName:e}=t;return"AREA"!==e||u(t)}))}t.r(r),t.d(r,{find:function(){return p},findNext:function(){return b},findPrevious:function(){return g},isTabbableIndex:function(){return l}});var a=window.lodash;function s(t){const e=t.getAttribute("tabindex");return null===e?0:parseInt(e,10)}function l(t){return-1!==s(t)}function f(t,e){return{element:t,index:e}}function d(t){return t.element}function m(t,e){const n=s(t.element),r=s(e.element);return n===r?t.index-e.index:n-r}function h(t){return t.filter(l).map(f).sort(m).map(d).reduce(function(){const t={};return function(e,n){const{nodeName:r,type:o,checked:i,name:u}=n;if("INPUT"!==r||"radio"!==o||!u)return e.concat(n);const c=t.hasOwnProperty(u);if(!i&&c)return e;if(c){const n=t[u];e=(0,a.without)(e,n)}return t[u]=n,e.concat(n)}}(),[])}function p(t){return h(c(t))}function g(t){const e=c(t.ownerDocument.body),n=e.indexOf(t);if(-1!==n)return e.length=n,(0,a.last)(h(e))}function b(t){const e=c(t.ownerDocument.body),n=e.indexOf(t),r=e.slice(n+1);return(0,a.first)(h(r))}function N(t,e){0}function y(t){if(!t.collapsed){const e=Array.from(t.getClientRects());if(1===e.length)return e[0];const n=e.filter((t=>{let{width:e}=t;return e>1}));if(0===n.length)return t.getBoundingClientRect();if(1===n.length)return n[0];let{top:r,bottom:o,left:i,right:u}=n[0];for(const{top:t,bottom:e,left:c,right:a}of n)t<r&&(r=t),e>o&&(o=e),c<i&&(i=c),a>u&&(u=a);return new window.DOMRect(i,r,u-i,o-r)}const{startContainer:e}=t,{ownerDocument:n}=e;if("BR"===e.nodeName){const{parentNode:r}=e;N();const o=Array.from(r.childNodes).indexOf(e);N(),(t=n.createRange()).setStart(r,o),t.setEnd(r,o)}let r=t.getClientRects()[0];if(!r){N();const e=n.createTextNode("​");(t=t.cloneRange()).insertNode(e),r=t.getClientRects()[0],N(e.parentNode),e.parentNode.removeChild(e)}return r}function E(t){const e=t.getSelection();N();const n=e.rangeCount?e.getRangeAt(0):null;return n?y(n):null}function C(t){N(t.defaultView);const e=t.defaultView.getSelection();N();const n=e.rangeCount?e.getRangeAt(0):null;return!!n&&!n.collapsed}function w(t){return!!t&&"INPUT"===t.nodeName}function T(t){return w(t)&&t.type&&!["button","checkbox","hidden","file","radio","image","range","reset","submit","number"].includes(t.type)||"TEXTAREA"===t.nodeName||"true"===t.contentEditable}function v(t){return w(t)&&"number"===t.type&&!!t.valueAsNumber}function S(t){return C(t)||!!t.activeElement&&function(t){if(!T(t)&&!v(t))return!1;try{const{selectionStart:e,selectionEnd:n}=t;return null!==e&&e!==n}catch(t){return!1}}(t.activeElement)}function A(t){return!!t.activeElement&&(T(t.activeElement)||v(t.activeElement)||C(t))}function O(t){return N(t.ownerDocument.defaultView),t.ownerDocument.defaultView.getComputedStyle(t)}function R(t){if(t){if(t.scrollHeight>t.clientHeight){const{overflowY:e}=O(t);if(/(auto|scroll)/.test(e))return t}return R(t.parentNode)}}function x(t){let e;for(;(e=t.parentNode)&&e.nodeType!==e.ELEMENT_NODE;);return e?"static"!==O(e).position?e:e.offsetParent:null}function P(t){return"INPUT"===t.tagName||"TEXTAREA"===t.tagName}function D(t){if(P(t))return 0===t.selectionStart&&t.value.length===t.selectionEnd;if(!t.isContentEditable)return!0;const{ownerDocument:e}=t,{defaultView:n}=e;N();const r=n.getSelection();N();const o=r.rangeCount?r.getRangeAt(0):null;if(!o)return!0;const{startContainer:i,endContainer:u,startOffset:c,endOffset:a}=o;if(i===t&&u===t&&0===c&&a===t.childNodes.length)return!0;t.lastChild;N();const s=u.nodeType===u.TEXT_NODE?u.data.length:u.childNodes.length;return L(i,t,"firstChild")&&L(u,t,"lastChild")&&0===c&&a===s}function L(t,e,n){let r=e;do{if(t===r)return!0;r=r[n]}while(r);return!1}function M(t){const{tagName:e}=t;return P(t)||"BUTTON"===e||"SELECT"===e}function H(t){return"rtl"===O(t).direction}function I(t){const e=Array.from(t.getClientRects());if(!e.length)return;const n=Math.min(...e.map((t=>{let{top:e}=t;return e})));return Math.max(...e.map((t=>{let{bottom:e}=t;return e})))-n}function _(t){const{anchorNode:e,focusNode:n,anchorOffset:r,focusOffset:o}=t;N(),N();const i=e.compareDocumentPosition(n);return!(i&e.DOCUMENT_POSITION_PRECEDING)&&(!!(i&e.DOCUMENT_POSITION_FOLLOWING)||(0!==i||r<=o))}function F(t,e,n,r){const o=r.style.zIndex,i=r.style.position,{position:u="static"}=O(r);"static"===u&&(r.style.position="relative"),r.style.zIndex="10000";const c=function(t,e,n){if(t.caretRangeFromPoint)return t.caretRangeFromPoint(e,n);if(!t.caretPositionFromPoint)return null;const r=t.caretPositionFromPoint(e,n);if(!r)return null;const o=t.createRange();return o.setStart(r.offsetNode,r.offset),o.collapse(!0),o}(t,e,n);return r.style.zIndex=o,r.style.position=i,c}function V(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(P(t)&&"number"==typeof t.selectionStart)return t.selectionStart===t.selectionEnd&&(e?0===t.selectionStart:t.value.length===t.selectionStart);if(!t.isContentEditable)return!0;const{ownerDocument:r}=t,{defaultView:o}=r;N();const i=o.getSelection();if(!i||!i.rangeCount)return!1;const u=i.getRangeAt(0),c=u.cloneRange(),a=_(i),s=i.isCollapsed;s||c.collapse(!a);const l=y(c),f=y(u);if(!l||!f)return!1;const d=I(u);if(!s&&d&&d>l.height&&a===e)return!1;const m=H(t)?!e:e,h=t.getBoundingClientRect(),p=m?h.left+1:h.right-1,g=e?h.top+1:h.bottom-1,b=F(r,p,g,t);if(!b)return!1;const E=y(b);if(!E)return!1;const C=e?"top":"bottom",w=m?"left":"right",T=E[C]-f[C],v=E[w]-l[w],S=Math.abs(T)<=1,A=Math.abs(v)<=1;return n?S:S&&A}function B(t,e){return V(t,e)}function j(t,e){return V(t,e,!0)}function z(t,e,n){const{ownerDocument:r}=t,o=H(t)?!e:e,i=t.getBoundingClientRect();void 0===n&&(n=e?i.right-1:i.left+1);return F(r,n,o?i.bottom-1:i.top+1,t)}function U(t,e,n){if(!t)return;if(t.focus(),P(t)){if("number"!=typeof t.selectionStart)return;return void(e?(t.selectionStart=t.value.length,t.selectionEnd=t.value.length):(t.selectionStart=0,t.selectionEnd=0))}if(!t.isContentEditable)return;let r=z(t,e,n);if(!(r&&r.startContainer&&t.contains(r.startContainer)||(t.scrollIntoView(e),r=r=z(t,e,n),r&&r.startContainer&&t.contains(r.startContainer))))return;const{ownerDocument:o}=t,{defaultView:i}=o;N();const u=i.getSelection();N(),u.removeAllRanges(),u.addRange(r)}function q(t,e){return U(t,e,void 0)}function k(t,e,n){return U(t,e,null==n?void 0:n.left)}function X(t,e){N(e.parentNode),e.parentNode.insertBefore(t,e.nextSibling)}function W(t){N(t.parentNode),t.parentNode.removeChild(t)}function G(t,e){N(t.parentNode),X(e,t.parentNode),W(t)}function Y(t){const e=t.parentNode;for(N();t.firstChild;)e.insertBefore(t.firstChild,t);e.removeChild(t)}function $(t,e){const n=t.ownerDocument.createElement(e);for(;t.firstChild;)n.appendChild(t.firstChild);return N(t.parentNode),t.parentNode.replaceChild(n,t),n}function J(t,e){N(e.parentNode),e.parentNode.insertBefore(t,e),t.appendChild(e)}function K(t){const{body:e}=document.implementation.createHTMLDocument("");e.innerHTML=t;const n=e.getElementsByTagName("*");let r=n.length;for(;r--;){const t=n[r];if("SCRIPT"===t.tagName)W(t);else{let e=t.attributes.length;for(;e--;){const{name:n}=t.attributes[e];n.startsWith("on")&&t.removeAttribute(n)}}}return e.innerHTML}function Q(t){t=K(t);const e=document.implementation.createHTMLDocument("");return e.body.innerHTML=t,e.body.textContent||""}function Z(t){switch(t.nodeType){case t.TEXT_NODE:return/^[ \f\n\r\t\v\u00a0]*$/.test(t.nodeValue||"");case t.ELEMENT_NODE:return!t.hasAttributes()&&(!t.hasChildNodes()||Array.from(t.childNodes).every(Z));default:return!0}}const tt={strong:{},em:{},s:{},del:{},ins:{},a:{attributes:["href","target","rel","id"]},code:{},abbr:{attributes:["title"]},sub:{},sup:{},br:{},small:{},q:{attributes:["cite"]},dfn:{attributes:["title"]},data:{attributes:["value"]},time:{attributes:["datetime"]},var:{},samp:{},kbd:{},i:{},b:{},u:{},mark:{},ruby:{},rt:{},rp:{},bdi:{attributes:["dir"]},bdo:{attributes:["dir"]},wbr:{},"#text":{}};(0,a.without)(Object.keys(tt),"#text","br").forEach((t=>{tt[t].children=(0,a.omit)(tt,t)}));const et={...tt,audio:{attributes:["src","preload","autoplay","mediagroup","loop","muted"]},canvas:{attributes:["width","height"]},embed:{attributes:["src","type","width","height"]},img:{attributes:["alt","src","srcset","usemap","ismap","width","height"]},object:{attributes:["data","type","name","usemap","form","width","height"]},video:{attributes:["src","poster","preload","autoplay","mediagroup","loop","muted","controls","width","height"]}};function nt(t){return"paste"!==t?et:(0,a.omit)({...et,ins:{children:et.ins.children},del:{children:et.del.children}},["u","abbr","data","time","wbr","bdi","bdo"])}function rt(t){const e=t.nodeName.toLowerCase();return nt().hasOwnProperty(e)||"span"===e}function ot(t){const e=t.nodeName.toLowerCase();return tt.hasOwnProperty(e)||"span"===e}function it(t,e,n,r){Array.from(t).forEach((t=>{var o,i;const u=t.nodeName.toLowerCase();if(n.hasOwnProperty(u)&&(!n[u].isMatch||null!==(o=(i=n[u]).isMatch)&&void 0!==o&&o.call(i,t))){if(function(t){return!!t&&t.nodeType===t.ELEMENT_NODE}(t)){const{attributes:o=[],classes:i=[],children:c,require:s=[],allowEmpty:l}=n[u];if(c&&!l&&Z(t))return void W(t);if(t.hasAttributes()&&(Array.from(t.attributes).forEach((e=>{let{name:n}=e;"class"===n||(0,a.includes)(o,n)||t.removeAttribute(n)})),t.classList&&t.classList.length)){const e=i.map((t=>"string"==typeof t?e=>e===t:t instanceof RegExp?e=>t.test(e):a.noop));Array.from(t.classList).forEach((n=>{e.some((t=>t(n)))||t.classList.remove(n)})),t.classList.length||t.removeAttribute("class")}if(t.hasChildNodes()){if("*"===c)return;if(c)s.length&&!t.querySelector(s.join(","))?(it(t.childNodes,e,n,r),Y(t)):t.parentNode&&"BODY"===t.parentNode.nodeName&&rt(t)?(it(t.childNodes,e,n,r),Array.from(t.childNodes).some((t=>!rt(t)))&&Y(t)):it(t.childNodes,e,c,r);else for(;t.firstChild;)W(t.firstChild)}}}else it(t.childNodes,e,n,r),r&&!rt(t)&&t.nextElementSibling&&X(e.createElement("br"),t),Y(t)}))}function ut(t,e,n){const r=document.implementation.createHTMLDocument("");return r.body.innerHTML=t,it(r.body.childNodes,r,e,n),r.body.innerHTML}function ct(t){const e=Array.from(t.files);return Array.from(t.items).forEach((t=>{const n=t.getAsFile();n&&!e.find((t=>{let{name:e,type:r,size:o}=t;return e===n.name&&r===n.type&&o===n.size}))&&e.push(n)})),e}const at={focusable:n,tabbable:r};(window.wp=window.wp||{}).dom=e}();