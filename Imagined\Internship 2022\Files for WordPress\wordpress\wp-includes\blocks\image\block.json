{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/image", "title": "Image", "category": "media", "usesContext": ["allowResize", "imageCrop", "fixedHeight"], "description": "Insert an image to make a visual statement.", "keywords": ["img", "photo", "picture"], "textdomain": "default", "attributes": {"align": {"type": "string"}, "url": {"type": "string", "source": "attribute", "selector": "img", "attribute": "src"}, "alt": {"type": "string", "source": "attribute", "selector": "img", "attribute": "alt", "default": ""}, "caption": {"type": "string", "source": "html", "selector": "figcaption"}, "title": {"type": "string", "source": "attribute", "selector": "img", "attribute": "title"}, "href": {"type": "string", "source": "attribute", "selector": "figure > a", "attribute": "href"}, "rel": {"type": "string", "source": "attribute", "selector": "figure > a", "attribute": "rel"}, "linkClass": {"type": "string", "source": "attribute", "selector": "figure > a", "attribute": "class"}, "id": {"type": "number"}, "width": {"type": "number"}, "height": {"type": "number"}, "sizeSlug": {"type": "string"}, "linkDestination": {"type": "string"}, "linkTarget": {"type": "string", "source": "attribute", "selector": "figure > a", "attribute": "target"}}, "supports": {"anchor": true, "color": {"__experimentalDuotone": "img", "text": false, "background": false}, "__experimentalBorder": {"radius": true, "__experimentalDefaultControls": {"radius": true}}}, "styles": [{"name": "default", "label": "<PERSON><PERSON><PERSON>", "isDefault": true}, {"name": "rounded", "label": "Rounded"}], "editorStyle": "wp-block-image-editor", "style": "wp-block-image"}