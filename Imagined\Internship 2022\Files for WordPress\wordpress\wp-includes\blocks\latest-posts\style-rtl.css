/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-latest-posts.alignleft {
  margin-right: 2em;
}
.wp-block-latest-posts.alignright {
  margin-left: 2em;
}
.wp-block-latest-posts.wp-block-latest-posts__list {
  list-style: none;
  padding-right: 0;
}
.wp-block-latest-posts.wp-block-latest-posts__list li {
  clear: both;
}
.wp-block-latest-posts.is-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
}
.wp-block-latest-posts.is-grid li {
  margin: 0 0 1.25em 1.25em;
  width: 100%;
}
@media (min-width: 600px) {
  .wp-block-latest-posts.columns-2 li {
    width: calc((100% / 2) - 1.25em + (1.25em / 2));
  }
  .wp-block-latest-posts.columns-2 li:nth-child(2n) {
    margin-left: 0;
  }
  .wp-block-latest-posts.columns-3 li {
    width: calc((100% / 3) - 1.25em + (1.25em / 3));
  }
  .wp-block-latest-posts.columns-3 li:nth-child(3n) {
    margin-left: 0;
  }
  .wp-block-latest-posts.columns-4 li {
    width: calc((100% / 4) - 1.25em + (1.25em / 4));
  }
  .wp-block-latest-posts.columns-4 li:nth-child(4n) {
    margin-left: 0;
  }
  .wp-block-latest-posts.columns-5 li {
    width: calc((100% / 5) - 1.25em + (1.25em / 5));
  }
  .wp-block-latest-posts.columns-5 li:nth-child(5n) {
    margin-left: 0;
  }
  .wp-block-latest-posts.columns-6 li {
    width: calc((100% / 6) - 1.25em + (1.25em / 6));
  }
  .wp-block-latest-posts.columns-6 li:nth-child(6n) {
    margin-left: 0;
  }
}

.wp-block-latest-posts__post-date,
.wp-block-latest-posts__post-author {
  display: block;
  font-size: 0.8125em;
}

.wp-block-latest-posts__post-excerpt {
  margin-top: 0.5em;
  margin-bottom: 1em;
}

.wp-block-latest-posts__featured-image a {
  display: inline-block;
}
.wp-block-latest-posts__featured-image img {
  height: auto;
  width: auto;
  max-width: 100%;
}
.wp-block-latest-posts__featured-image.alignleft {
  margin-right: 1em;
  float: left;
}
.wp-block-latest-posts__featured-image.alignright {
  margin-left: 1em;
  float: right;
}
.wp-block-latest-posts__featured-image.aligncenter {
  margin-bottom: 1em;
  text-align: center;
}