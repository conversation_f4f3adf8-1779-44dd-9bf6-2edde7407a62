{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/pullquote", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "text", "description": "Give special visual emphasis to a quote from your text.", "textdomain": "default", "attributes": {"value": {"type": "string", "source": "html", "selector": "blockquote", "multiline": "p", "__experimentalRole": "content"}, "citation": {"type": "string", "source": "html", "selector": "cite", "default": "", "__experimentalRole": "content"}, "textAlign": {"type": "string"}}, "supports": {"anchor": true, "align": ["left", "right", "wide", "full"], "color": {"gradients": true, "background": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true, "fontAppearance": true}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}}, "editorStyle": "wp-block-pullquote-editor", "style": "wp-block-pullquote"}