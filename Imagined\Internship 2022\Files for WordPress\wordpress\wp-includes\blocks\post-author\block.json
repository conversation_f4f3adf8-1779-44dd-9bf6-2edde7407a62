{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/post-author", "title": "Post Author", "category": "theme", "description": "Display post author details such as name, avatar, and bio.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}, "avatarSize": {"type": "number", "default": 48}, "showAvatar": {"type": "boolean", "default": true}, "showBio": {"type": "boolean"}, "byline": {"type": "string"}}, "usesContext": ["postType", "postId", "queryId"], "supports": {"html": false, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}, "color": {"gradients": true, "link": true, "__experimentalDuotone": ".wp-block-post-author__avatar img", "__experimentalDefaultControls": {"background": true, "text": true}}}, "editorStyle": "wp-block-post-author-editor", "style": "wp-block-post-author"}