/*! This file is auto-generated */
!function(){"use strict";var r={9025:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.race=t.join=t.fork=t.promise=void 0;var e=c(n(9681)),u=n(7783),o=c(n(2451));function c(r){return r&&r.__esModule?r:{default:r}}var f=t.promise=function(r,t,n,u,o){return!!e.default.promise(r)&&(r.then(t,o),!0)},a=new Map,i=t.fork=function(r,t,n){if(!e.default.fork(r))return!1;var c=Symbol("fork"),f=(0,o.default)();a.set(c,f),n(r.iterator.apply(null,r.args),(function(r){return f.dispatch(r)}),(function(r){return f.dispatch((0,u.error)(r))}));var i=f.subscribe((function(){i(),a.delete(c)}));return t(c),!0},l=t.join=function(r,t,n,u,o){if(!e.default.join(r))return!1;var c,f=a.get(r.task);return f?c=f.subscribe((function(r){c(),t(r)})):o("join error : task not found"),!0},s=t.race=function(r,t,n,u,o){if(!e.default.race(r))return!1;var c,f=!1,a=function(r,n,e){f||(f=!0,r[n]=e,t(r))},i=function(r){f||o(r)};return e.default.array(r.competitors)?(c=r.competitors.map((function(){return!1})),r.competitors.forEach((function(r,t){n(r,(function(r){return a(c,t,r)}),i)}))):function(){var t=Object.keys(r.competitors).reduce((function(r,t){return r[t]=!1,r}),{});Object.keys(r.competitors).forEach((function(e){n(r.competitors[e],(function(r){return a(t,e,r)}),i)}))}(),!0};t.default=[f,i,l,s,function(r,t){if(!e.default.subscribe(r))return!1;if(!e.default.channel(r.channel))throw new Error('the first argument of "subscribe" must be a valid channel');var n=r.channel.subscribe((function(r){n&&n(),t(r)}));return!0}]},7961:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.iterator=t.array=t.object=t.error=t.any=void 0;var e,u=n(9681),o=(e=u)&&e.__esModule?e:{default:e};var c=t.any=function(r,t,n,e){return e(r),!0},f=t.error=function(r,t,n,e,u){return!!o.default.error(r)&&(u(r.error),!0)},a=t.object=function(r,t,n,e,u){if(!o.default.all(r)||!o.default.obj(r.value))return!1;var c={},f=Object.keys(r.value),a=0,i=!1;return f.map((function(t){n(r.value[t],(function(r){return function(r,t){i||(c[r]=t,++a===f.length&&e(c))}(t,r)}),(function(r){return function(r,t){i||(i=!0,u(t))}(0,r)}))})),!0},i=t.array=function(r,t,n,e,u){if(!o.default.all(r)||!o.default.array(r.value))return!1;var c=[],f=0,a=!1;return r.value.map((function(t,o){n(t,(function(t){return function(t,n){a||(c[t]=n,++f===r.value.length&&e(c))}(o,t)}),(function(r){return function(r,t){a||(a=!0,u(t))}(0,r)}))})),!0},l=t.iterator=function(r,t,n,e,u){return!!o.default.iterator(r)&&(n(r,t,u),!0)};t.default=[f,l,i,a,c]},2165:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.cps=t.call=void 0;var e,u=n(9681),o=(e=u)&&e.__esModule?e:{default:e};var c=t.call=function(r,t,n,e,u){if(!o.default.call(r))return!1;try{t(r.func.apply(r.context,r.args))}catch(r){u(r)}return!0},f=t.cps=function(r,t,n,e,u){var c;return!!o.default.cps(r)&&((c=r.func).call.apply(c,[null].concat(function(r){if(Array.isArray(r)){for(var t=0,n=Array(r.length);t<r.length;t++)n[t]=r[t];return n}return Array.from(r)}(r.args),[function(r,n){r?u(r):t(n)}])),!0)};t.default=[c,f]},6288:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0});var e=o(n(7961)),u=o(n(9681));function o(r){return r&&r.__esModule?r:{default:r}}function c(r){if(Array.isArray(r)){for(var t=0,n=Array(r.length);t<r.length;t++)n[t]=r[t];return n}return Array.from(r)}t.default=function(){var r=arguments.length<=0||void 0===arguments[0]?[]:arguments[0],t=[].concat(c(r),c(e.default)),n=function r(n){var e=arguments.length<=1||void 0===arguments[1]?function(){}:arguments[1],o=arguments.length<=2||void 0===arguments[2]?function(){}:arguments[2],c=function(n){var u=function(r){return function(t){try{var u=r?n.throw(t):n.next(t),f=u.value;if(u.done)return e(f);c(f)}catch(r){return o(r)}}},c=function n(e){t.some((function(t){return t(e,n,r,u(!1),u(!0))}))};u(!1)()},f=u.default.iterator(n)?n:regeneratorRuntime.mark((function r(){return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,n;case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),r,this)}))();c(f,e,o)};return n}},2290:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.wrapControls=t.asyncControls=t.create=void 0;var e=n(7783);Object.keys(e).forEach((function(r){"default"!==r&&Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})}));var u=f(n(6288)),o=f(n(9025)),c=f(n(2165));function f(r){return r&&r.__esModule?r:{default:r}}t.create=u.default,t.asyncControls=o.default,t.wrapControls=c.default},2451:function(r,t){Object.defineProperty(t,"__esModule",{value:!0});t.default=function(){var r=[];return{subscribe:function(t){return r.push(t),function(){r=r.filter((function(r){return r!==t}))}},dispatch:function(t){r.slice().forEach((function(r){return r(t)}))}}}},7783:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createChannel=t.subscribe=t.cps=t.apply=t.call=t.invoke=t.delay=t.race=t.join=t.fork=t.error=t.all=void 0;var e,u=n(9851),o=(e=u)&&e.__esModule?e:{default:e};t.all=function(r){return{type:o.default.all,value:r}},t.error=function(r){return{type:o.default.error,error:r}},t.fork=function(r){for(var t=arguments.length,n=Array(t>1?t-1:0),e=1;e<t;e++)n[e-1]=arguments[e];return{type:o.default.fork,iterator:r,args:n}},t.join=function(r){return{type:o.default.join,task:r}},t.race=function(r){return{type:o.default.race,competitors:r}},t.delay=function(r){return new Promise((function(t){setTimeout((function(){return t(!0)}),r)}))},t.invoke=function(r){for(var t=arguments.length,n=Array(t>1?t-1:0),e=1;e<t;e++)n[e-1]=arguments[e];return{type:o.default.call,func:r,context:null,args:n}},t.call=function(r,t){for(var n=arguments.length,e=Array(n>2?n-2:0),u=2;u<n;u++)e[u-2]=arguments[u];return{type:o.default.call,func:r,context:t,args:e}},t.apply=function(r,t,n){return{type:o.default.call,func:r,context:t,args:n}},t.cps=function(r){for(var t=arguments.length,n=Array(t>1?t-1:0),e=1;e<t;e++)n[e-1]=arguments[e];return{type:o.default.cps,func:r,args:n}},t.subscribe=function(r){return{type:o.default.subscribe,channel:r}},t.createChannel=function(r){var t=[];return r((function(r){return t.forEach((function(t){return t(r)}))})),{subscribe:function(r){return t.push(r),function(){return t.splice(t.indexOf(r),1)}}}}},9681:function(r,t,n){Object.defineProperty(t,"__esModule",{value:!0});var e,u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol?"symbol":typeof r},o=n(9851),c=(e=o)&&e.__esModule?e:{default:e};var f={obj:function(r){return"object"===(void 0===r?"undefined":u(r))&&!!r},all:function(r){return f.obj(r)&&r.type===c.default.all},error:function(r){return f.obj(r)&&r.type===c.default.error},array:Array.isArray,func:function(r){return"function"==typeof r},promise:function(r){return r&&f.func(r.then)},iterator:function(r){return r&&f.func(r.next)&&f.func(r.throw)},fork:function(r){return f.obj(r)&&r.type===c.default.fork},join:function(r){return f.obj(r)&&r.type===c.default.join},race:function(r){return f.obj(r)&&r.type===c.default.race},call:function(r){return f.obj(r)&&r.type===c.default.call},cps:function(r){return f.obj(r)&&r.type===c.default.cps},subscribe:function(r){return f.obj(r)&&r.type===c.default.subscribe},channel:function(r){return f.obj(r)&&f.func(r.subscribe)}};t.default=f},9851:function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var n={all:Symbol("all"),error:Symbol("error"),fork:Symbol("fork"),join:Symbol("join"),race:Symbol("race"),call:Symbol("call"),cps:Symbol("cps"),subscribe:Symbol("subscribe")};t.default=n}},t={};function n(e){var u=t[e];if(void 0!==u)return u.exports;var o=t[e]={exports:{}};return r[e](o,o.exports,n),o.exports}n.d=function(r,t){for(var e in t)n.o(t,e)&&!n.o(r,e)&&Object.defineProperty(r,e,{enumerable:!0,get:t[e]})},n.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)};var e={};!function(){function r(r){return!!r&&"function"==typeof r[Symbol.iterator]&&"function"==typeof r.next}n.d(e,{default:function(){return i}});var t=n(2290),u=window.lodash;function o(r){return!!r&&("object"==typeof r||"function"==typeof r)&&"function"==typeof r.then}function c(r){return(0,u.isPlainObject)(r)&&(0,u.isString)(r.type)}function f(r,t){return c(r)&&r.type===t}function a(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;const e=(0,u.map)(r,((r,t)=>(n,e,u,c,a)=>{if(!f(n,t))return!1;const i=r(n);return o(i)?i.then(c,a):c(i),!0})),a=(r,t)=>!!c(r)&&(n(r),t(),!0);e.push(a);const i=(0,t.create)(e);return r=>new Promise(((t,e)=>i(r,(r=>{c(r)&&n(r),t(r)}),e)))}function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n=>{const e=a(t,n.dispatch);return t=>n=>r(n)?e(n):t(n)}}}(),(window.wp=window.wp||{}).reduxRoutine=e.default}();