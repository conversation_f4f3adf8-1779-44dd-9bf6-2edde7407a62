.wp-block-navigation {

	.wp-block-navigation__container {
		background: var(--global--color-background);
		padding: 0;
	}

	.wp-block-navigation-link {

		.wp-block-navigation-link__content {
			padding: var(--primary-nav--padding);
		}

		.wp-block-navigation-link__label {
			font-family: var(--primary-nav--font-family);
			font-size: var(--primary-nav--font-size);
			font-weight: var(--primary-nav--font-weight);
		}
	}

	.has-child {

		.wp-block-navigation__container {
			box-shadow: var(--global--elevation);
		}
	}

	&:not(.has-text-color) {

		.wp-block-navigation-link {

			> a {

				&:hover,
				&:focus {
					color: var(--primary-nav--color-link-hover);
				}
			}
		}

		.wp-block-navigation-link__content {
			color: currentColor;
		}
	}
}
