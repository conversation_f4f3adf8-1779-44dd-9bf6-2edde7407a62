{"name": "webmozart/assert", "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "autoload-dev": {"psr-4": {"Webmozart\\Assert\\Tests\\": "tests/", "Webmozart\\Assert\\Bin\\": "bin/src"}}}