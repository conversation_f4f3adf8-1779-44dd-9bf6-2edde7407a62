{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/post-comments", "title": "Post Comments (deprecated)", "category": "theme", "description": "This block is deprecated. Please use the Comments Query Loop block instead.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}}, "usesContext": ["postId", "postType"], "supports": {"html": false, "align": ["wide", "full"], "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "inserter": false}, "style": ["wp-block-post-comments", "wp-block-buttons", "wp-block-button"], "editorStyle": "wp-block-post-comments-editor"}