# Contributing to phpMyAdmin

As an open-source project, phpMyAdmin welcomes contributions of many forms.

## Bug reporting

We appreciate your effort to improve phpMyAdmin by submitting a bug report. Before doing so, please check the following things:

1. Check whether the bug you face **hasn't been already reported**. Duplicate reports take us time, that we could be used to fix other bugs or make improvements.

2. Specify the phpMyAdmin, server, PHP, MySQL and browser information that may be helpful to fix the problem, especially exact **version numbers**.

3. If you got some error, please **describe what happened** and add an error message. Reports like "I get an error when I clicked on some link" are useless.

4. Provide easy steps to reproduce and if possible include your table structure (``SHOW CREATE TABLE `tbl_name`;``); if your problem implies specific data, attach a small export file for sample rows.

5. **Security problems** should not be reported here. See [our security page](https://www.phpmyadmin.net/security/).

Thanks for your help!

Please report [bugs on GitHub][1].

[1]: https://github.com/phpmyadmin/phpmyadmin/issues/new

## Patches submission

Patches are welcome as [pull requests on GitHub][2].  Please include a
Signed-off-by tag in the commit message (you can do this by passing [`--signoff`][4] parameter to Git).

When creating the commit on GitHub or using some other tool which does not have
direct support for this, it is the same as adding `Signed-off-by: Your name <<EMAIL>>`
as the last line of the commit message.

Example: `Signed-off-by: Jane Smith <<EMAIL>>`

Note that by submitting patches with the Signed-off-by tag, you are giving
permission to license the patch as GPLv2-or-later. See [the DCO file][3] for details.


[2]: https://github.com/phpmyadmin/phpmyadmin/pulls
[3]: https://github.com/phpmyadmin/phpmyadmin/blob/master/DCO
[4]: https://git-scm.com/docs/git-commit#Documentation/git-commit.txt---signoff

## Triage issues [![Open Source Helpers](https://www.codetriage.com/phpmyadmin/phpmyadmin/badges/users.svg)](https://www.codetriage.com/phpmyadmin/phpmyadmin)

You can triage issues, which may include reproducing bug reports or asking for vital information, such as version numbers or reproduction instructions. If you would like to start triaging issues, one easy way to get started is to [subscribe to phpMyAdmin on CodeTriage](https://www.codetriage.com/phpmyadmin/phpmyadmin).

## More information

You can find more information on our website:

https://www.phpmyadmin.net/contribute/
