// Responsive breakpoints mixin
@mixin add_variables( $view: frontend ) {

	@if frontend == $view {

		:root {
			@content;
		}
	}

	@if editor == $view {

		:root,
		body {
			@content;
		}
	}
}

// Button style
// - Applies button styles to blocks and elements that share them.
@mixin button-style() {
	border: var(--button--border-width) solid transparent;
	border-radius: var(--button--border-radius);
	cursor: pointer;
	font-weight: var(--button--font-weight);
	font-family: var(--button--font-family);
	font-size: var(--button--font-size);
	line-height: var(--button--line-height);
	padding: var(--button--padding-vertical) var(--button--padding-horizontal);
	text-decoration: none;

	// Standard Button Color Relationship Logic
	&:not(:hover):not(:active) {

		// Text colors
		&:not(.has-text-color) {
			color: var(--global--color-background);

			// Nested
			.has-background & {
				color: var(--local--color-background, var(--global--color-primary));

				&.has-background {
					color: var(--global--color-primary);
				}
			}
		}

		// Background-colors
		&:not(.has-background) {
			background-color: var(--global--color-primary);

			// Nested
			.has-background & {
				background-color: var(--local--color-primary, var(--global--color-primary));
			}
		}
	}

	// Hover Button color should match parent element foreground color
	&:hover,
	&:active {
		background-color: transparent;
		border-color: currentColor;
		color: inherit;
	}

	// Focus Button outline color should always match the current text color
	&:focus {
		outline-offset: -6px;
		outline: 2px dotted currentColor;
	}

	// Disabled Button colors
	&:disabled {
		background-color: var(--global--color-white-50);
		border-color: var(--global--color-white-50);
		color: var(--button--color-text-active);
	}
}

@mixin innerblock-margin-clear($container) {

	// Clear the top margin for the first-child.
	> #{$container} > *:first-child {
		margin-top: 0;
	}

	// Last child that is not the appender.
	> #{$container} > *:last-child:not(.block-list-appender) {
		margin-bottom: 0;
	}

	// When selected, the last item becomes the second last because of the appender.
	&.has-child-selected > #{$container} > *:nth-last-child(2),
	&.is-selected > #{$container} > *:nth-last-child(2) {
		margin-bottom: 0;
	}
}
