/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
.wp-block-navigation .wp-block-page-list > div,
.wp-block-navigation .wp-block-page-list {
  background-color: inherit;
}
.wp-block-navigation.items-justified-space-between .wp-block-page-list > div,
.wp-block-navigation.items-justified-space-between .wp-block-page-list {
  display: contents;
  flex: 1;
}
.wp-block-navigation.items-justified-space-between.has-child-selected .wp-block-page-list > div, .wp-block-navigation.items-justified-space-between.has-child-selected .wp-block-page-list, .wp-block-navigation.items-justified-space-between.is-selected .wp-block-page-list > div, .wp-block-navigation.items-justified-space-between.is-selected .wp-block-page-list {
  flex: inherit;
}

.wp-block-pages-list__item__link {
  pointer-events: none;
}

@media (min-width: 600px) {
  .wp-block-page-list-modal {
    max-width: 480px;
  }
}

.wp-block-page-list-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.wp-block-page-list .open-on-click:focus-within > .wp-block-navigation__submenu-container {
  visibility: visible;
  opacity: 1;
  width: auto;
  height: auto;
  min-width: 200px;
}

.wp-block-page-list .components-notice {
  margin-right: 0;
}