/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Colors
 */
/**
 * Fonts & basic variables.
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Dimensions.
 */
/**
 * Shadows.
 */
/**
 * Editor widths.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
ul.wp-block-archives {
  padding-left: 2.5em;
}

.wp-block-audio {
  margin-left: 0;
  margin-right: 0;
  position: relative;
}
.wp-block-audio.is-transient audio {
  opacity: 0.3;
}
.wp-block-audio .components-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -9px;
  margin-left: -9px;
}

.wp-block-avatar__image img {
  width: 100%;
}

.wp-block-avatar.aligncenter .components-resizable-box__container {
  margin: 0 auto;
}

.edit-post-visual-editor .block-library-block__reusable-block-container .is-root-container {
  padding-left: 0;
  padding-right: 0;
}
.edit-post-visual-editor .block-library-block__reusable-block-container .block-editor-writing-flow {
  display: block;
}
.edit-post-visual-editor .block-library-block__reusable-block-container .components-disabled .block-list-appender {
  display: none;
}

.wp-block[data-align=center] > .wp-block-button {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

.wp-block[data-align=right] > .wp-block-button {
  /*!rtl:ignore*/
  text-align: right;
}

.wp-block-button {
  position: relative;
  cursor: text;
}
.wp-block-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px var(--wp-admin-theme-color);
  outline: 2px solid transparent;
  outline-offset: -2px;
}
.wp-block-button[data-rich-text-placeholder]::after {
  opacity: 0.8;
}

.wp-block-button__inline-link {
  color: #757575;
  height: 0;
  overflow: hidden;
  max-width: 290px;
}
.wp-block-button__inline-link-input__suggestions {
  max-width: 290px;
}
@media (min-width: 782px) {
  .wp-block-button__inline-link {
    max-width: 260px;
  }
  .wp-block-button__inline-link-input__suggestions {
    max-width: 260px;
  }
}
@media (min-width: 960px) {
  .wp-block-button__inline-link {
    max-width: 290px;
  }
  .wp-block-button__inline-link-input__suggestions {
    max-width: 290px;
  }
}
.is-selected .wp-block-button__inline-link {
  height: auto;
  overflow: visible;
}

.wp-button-label__width .components-button-group {
  display: block;
}
.wp-button-label__width .components-base-control__field {
  margin-bottom: 12px;
}

div[data-type="core/button"] {
  display: table;
}

.wp-block-buttons {
  /* stylelint-disable indentation */
}
.wp-block-buttons > .wp-block {
  margin: 0;
}
.wp-block-buttons > .wp-block-button.wp-block-button.wp-block-button.wp-block-button.wp-block-button {
  margin: 0;
}
.wp-block-buttons > .block-list-appender {
  display: inline-flex;
  align-items: center;
}
.wp-block-buttons.is-vertical > .block-list-appender .block-list-appender__toggle {
  justify-content: flex-start;
}
.wp-block-buttons > .wp-block-button:focus {
  box-shadow: none;
}
.wp-block-buttons:not(.is-content-justification-space-between,
.is-content-justification-right,
.is-content-justification-left,
.is-content-justification-center) .wp-block[data-align=center] {
  /* stylelint-enable indentation */
  margin-left: auto;
  margin-right: auto;
  margin-top: 0;
  width: 100%;
}
.wp-block-buttons:not(.is-content-justification-space-between,
.is-content-justification-right,
.is-content-justification-left,
.is-content-justification-center) .wp-block[data-align=center] .wp-block-button {
  margin-bottom: 0;
}

.wp-block[data-align=center] > .wp-block-buttons {
  align-items: center;
  justify-content: center;
}

.wp-block[data-align=right] > .wp-block-buttons {
  justify-content: flex-end;
}

.wp-block-categories ul {
  padding-left: 2.5em;
}
.wp-block-categories ul ul {
  margin-top: 6px;
}

.wp-block-columns :where(.wp-block) {
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

html :where(.wp-block-column) {
  margin-top: 0;
  margin-bottom: 0;
}

.wp-block-comment-author-avatar__placeholder {
  border: currentColor 1px dashed;
  width: 100%;
  height: 100%;
  stroke: currentColor;
  stroke-dasharray: 3;
}

.block-library-comments-toolbar__popover .components-popover__content {
  min-width: 230px;
}

.wp-block[data-align=center] > .wp-block-comments-pagination {
  justify-content: center;
}

.editor-styles-wrapper .wp-block-comments-pagination {
  max-width: 100%;
}
.editor-styles-wrapper .wp-block-comments-pagination.block-editor-block-list__layout {
  margin: 0;
}

.wp-block-comments-pagination > .wp-block-comments-pagination-next,
.wp-block-comments-pagination > .wp-block-comments-pagination-previous,
.wp-block-comments-pagination > .wp-block-comments-pagination-numbers {
  margin-left: 0;
  margin-top: 0.5em;
  /*rtl:ignore*/
  margin-right: 0.5em;
  margin-bottom: 0.5em;
}
.wp-block-comments-pagination > .wp-block-comments-pagination-next:last-child,
.wp-block-comments-pagination > .wp-block-comments-pagination-previous:last-child,
.wp-block-comments-pagination > .wp-block-comments-pagination-numbers:last-child {
  /*rtl:ignore*/
  margin-right: 0;
}

.wp-block-comments-pagination-numbers a {
  text-decoration: underline;
}
.wp-block-comments-pagination-numbers .page-numbers {
  margin-right: 2px;
}
.wp-block-comments-pagination-numbers .page-numbers:last-child {
  /*rtl:ignore*/
  margin-right: 0;
}

.wp-block-comments-title.has-background {
  padding: inherit;
}

.wp-block-cover {
  /* Extra specificity needed because the reset.css applied in the editor context is overriding this rule. */
}
.editor-styles-wrapper .wp-block-cover {
  box-sizing: border-box;
}
.wp-block-cover.is-placeholder {
  min-height: auto !important;
  padding: 0 !important;
}
.wp-block-cover.is-placeholder .block-library-cover__resize-container {
  display: none;
}
.wp-block-cover.is-placeholder .components-placeholder.is-large {
  min-height: 240px;
  justify-content: flex-start;
  z-index: 1;
}
.wp-block-cover.is-placeholder .components-placeholder.is-large + .block-library-cover__resize-container {
  min-height: 240px;
  display: block;
}
.wp-block-cover.components-placeholder h2 {
  color: inherit;
}
.wp-block-cover.is-transient::before {
  background-color: #fff;
  opacity: 0.3;
}
.wp-block-cover .components-spinner {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
}
.wp-block-cover .block-editor-block-list__layout {
  width: 100%;
}
.wp-block-cover .wp-block-cover__inner-container {
  text-align: left;
  margin-left: 0;
  margin-right: 0;
}
.wp-block-cover .wp-block-cover__placeholder-background-options {
  width: 100%;
}

[data-align=left] > .wp-block-cover,
[data-align=right] > .wp-block-cover {
  max-width: 420px;
  width: 100%;
}

.block-library-cover__reset-button {
  margin-left: auto;
}

.block-library-cover__resize-container {
  position: absolute !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  min-height: 50px;
}

.block-library-cover__resize-container:not(.is-resizing) {
  height: auto !important;
}

.wp-block-cover > .components-drop-zone .components-drop-zone__content {
  opacity: 0.8 !important;
}

.block-editor-block-patterns-list__list-item .has-parallax.wp-block-cover {
  background-attachment: scroll;
}

.wp-block-embed {
  margin-left: 0;
  margin-right: 0;
  clear: both;
}
.wp-block-embed.is-loading {
  display: flex;
  justify-content: center;
}
.wp-block-embed .components-placeholder__error {
  word-break: break-word;
}
.wp-block-embed .components-placeholder__learn-more {
  margin-top: 1em;
}

.block-library-embed__interactive-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

.wp-block[data-align=left] > .wp-block-embed,
.wp-block[data-align=right] > .wp-block-embed {
  max-width: 360px;
  width: 100%;
}
.wp-block[data-align=left] > .wp-block-embed .wp-block-embed__wrapper,
.wp-block[data-align=right] > .wp-block-embed .wp-block-embed__wrapper {
  min-width: 280px;
}

.wp-block-file {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}
.wp-block[data-align=left] > .wp-block-file, .wp-block[data-align=right] > .wp-block-file {
  height: auto;
}
.wp-block-file .components-resizable-box__container {
  margin-bottom: 1em;
}
.wp-block-file .wp-block-file__preview {
  margin-bottom: 1em;
  width: 100%;
  height: 100%;
}
.wp-block-file .wp-block-file__preview-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.wp-block-file .wp-block-file__content-wrapper {
  flex-grow: 1;
}
.wp-block-file a {
  min-width: 1em;
}
.wp-block-file .wp-block-file__button-richtext-wrapper {
  display: inline-block;
  margin-left: 0.75em;
}

.wp-block-freeform.block-library-rich-text__tinymce {
  height: auto;
  /* Allow height of embed iframes to be calculated properly */
  /* Remove blue highlighting of selected images in WebKit */
  /* Image captions */
  /* WP Views */
}
.wp-block-freeform.block-library-rich-text__tinymce p,
.wp-block-freeform.block-library-rich-text__tinymce li {
  line-height: 1.8;
}
.wp-block-freeform.block-library-rich-text__tinymce ul,
.wp-block-freeform.block-library-rich-text__tinymce ol {
  padding-left: 2.5em;
  margin-left: 0;
}
.wp-block-freeform.block-library-rich-text__tinymce blockquote {
  margin: 0;
  box-shadow: inset 0 0 0 0 #ddd;
  border-left: 4px solid #000;
  padding-left: 1em;
}
.wp-block-freeform.block-library-rich-text__tinymce pre {
  white-space: pre-wrap;
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 15px;
  color: #1e1e1e;
}
.wp-block-freeform.block-library-rich-text__tinymce > *:first-child {
  margin-top: 0;
}
.wp-block-freeform.block-library-rich-text__tinymce > *:last-child {
  margin-bottom: 0;
}
.wp-block-freeform.block-library-rich-text__tinymce.mce-edit-focus {
  outline: none;
}
.wp-block-freeform.block-library-rich-text__tinymce a {
  color: var(--wp-admin-theme-color);
}
.wp-block-freeform.block-library-rich-text__tinymce:focus a[data-mce-selected] {
  padding: 0 2px;
  margin: 0 -2px;
  border-radius: 2px;
  box-shadow: 0 0 0 1px #e5f5fa;
  background: #e5f5fa;
}
.wp-block-freeform.block-library-rich-text__tinymce code {
  padding: 2px;
  border-radius: 2px;
  color: #1e1e1e;
  background: #f0f0f0;
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 14px;
}
.wp-block-freeform.block-library-rich-text__tinymce:focus code[data-mce-selected] {
  background: #ddd;
}
.wp-block-freeform.block-library-rich-text__tinymce .alignright {
  /*rtl:ignore*/
  float: right;
  /*rtl:ignore*/
  margin: 0.5em 0 0.5em 1em;
}
.wp-block-freeform.block-library-rich-text__tinymce .alignleft {
  /*rtl:ignore*/
  float: left;
  /*rtl:ignore*/
  margin: 0.5em 1em 0.5em 0;
}
.wp-block-freeform.block-library-rich-text__tinymce .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.wp-block-freeform.block-library-rich-text__tinymce .wp-more-tag {
  width: 96%;
  height: 20px;
  display: block;
  margin: 15px auto;
  outline: 0;
  cursor: default;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAADtgAAAAoBAMAAAA86gLBAAAAJFBMVEVMaXG7u7vBwcHDw8POzs68vLzGxsbMzMy+vr7AwMDQ0NDGxsYKLGzpAAAADHRSTlMA///zWf+/f///TMxNVGuqAAABwklEQVR4Ae3dMXLaQBTH4bfj8UCpx8hq0vgKvgFNemhT6Qo6gg6R+0ZahM2QLmyBJ99XWP9V5+o3jIUcLQEAAAAAAAAAAAAAAAAAAAAAAABQ8j0WL9lfTtlt18uNXAUA8O/KVtfa1tdcrOdSh9gCQAMlh1hMNbZZ1bsrsQWABsrhLRbz7z5in/32UbfUMUbkMQCAh5RfGYv82UdMdZ6HS2wjT2ILAI8r3XmM2B3WvM59vfO2xXYW2yYAENuPU8S+X/N67mKxzy225yaxBQCxLV392UdcvwV0jPVUj98ntkBWT7C7+9u2/V/vGtvXIWJ6/4rtbottWa6Ri0NUT/u72LYttrb97LHdvUXMxxrb8TO2W2TF1rYbbLG1bbGNjMi4+2Sbi1FsbbvNFlvbFtt5fDnE3d9sP1/XeIyV2Nr2U2/guZUuptNrH/dPI9eLB6SaAEBs6wPJf3/PNk9tYgsAYrv/8TFuzx/fvkFqGtrEFgDEdpcZUb7ejXy6ntrEFgDENvL6gsas4vbdyKt4DACI7TxElJv/Z7udpqFNbAFAbKduy2uU2trttM/x28UWAAAAAAAAAAAAAAAAAAAAAAAAAADgDyPwGmGTCZp7AAAAAElFTkSuQmCC);
  background-size: 1900px 20px;
  background-repeat: no-repeat;
  background-position: center;
}
.wp-block-freeform.block-library-rich-text__tinymce img::selection {
  background-color: transparent;
}
.wp-block-freeform.block-library-rich-text__tinymce div.mceTemp {
  -ms-user-select: element;
}
.wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption {
  margin: 0;
  /* dl browser reset */
  max-width: 100%;
}
.wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption a,
.wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption img {
  display: block;
}
.wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption, .wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption * {
  -webkit-user-drag: none;
}
.wp-block-freeform.block-library-rich-text__tinymce dl.wp-caption .wp-caption-dd {
  padding-top: 0.5em;
  margin: 0;
  /* browser dd reset */
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview {
  width: 99.99%;
  /* All IE need hasLayout, incl. 11 (ugh, not again!!) */
  position: relative;
  clear: both;
  margin-bottom: 16px;
  border: 1px solid transparent;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview iframe {
  display: block;
  max-width: 100%;
  background: transparent;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview .mce-shim {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview[data-mce-selected="2"] .mce-shim {
  display: none;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview .loading-placeholder {
  border: 1px dashed #ddd;
  padding: 10px;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview .wpview-error {
  border: 1px solid #ddd;
  padding: 1em 0;
  margin: 0;
  word-wrap: break-word;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview .wpview-error p {
  margin: 0;
  text-align: center;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview[data-mce-selected] .loading-placeholder, .wp-block-freeform.block-library-rich-text__tinymce .wpview[data-mce-selected] .wpview-error {
  border-color: transparent;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview .dashicons {
  display: block;
  margin: 0 auto;
  width: 32px;
  height: 32px;
  font-size: 32px;
}
.wp-block-freeform.block-library-rich-text__tinymce .wpview.wpview-type-gallery::after {
  content: "";
  display: table;
  clear: both;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery img[data-mce-selected]:focus {
  outline: none;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery a {
  cursor: default;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery {
  margin: auto -6px;
  padding: 6px 0;
  line-height: 1;
  overflow-x: hidden;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery .gallery-item {
  float: left;
  margin: 0;
  text-align: center;
  padding: 6px;
  box-sizing: border-box;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery .gallery-caption,
.wp-block-freeform.block-library-rich-text__tinymce .gallery .gallery-icon {
  margin: 0;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery .gallery-caption {
  font-size: 13px;
  margin: 4px 0;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-1 .gallery-item {
  width: 100%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-2 .gallery-item {
  width: 50%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-3 .gallery-item {
  width: 33.3333333333%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-4 .gallery-item {
  width: 25%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-5 .gallery-item {
  width: 20%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-6 .gallery-item {
  width: 16.6666666667%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-7 .gallery-item {
  width: 14.2857142857%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-8 .gallery-item {
  width: 12.5%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery-columns-9 .gallery-item {
  width: 11.1111111111%;
}
.wp-block-freeform.block-library-rich-text__tinymce .gallery img {
  max-width: 100%;
  height: auto;
  border: none;
  padding: 0;
}

div[data-type="core/freeform"]::before {
  transition: border-color 0.1s linear, box-shadow 0.1s linear;
  border: 1px solid #ddd;
  outline: 1px solid transparent;
}
@media (prefers-reduced-motion: reduce) {
  div[data-type="core/freeform"]::before {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
div[data-type="core/freeform"].is-selected::before {
  border-color: #1e1e1e;
}
div[data-type="core/freeform"] .block-editor-block-contextual-toolbar + div {
  margin-top: 0;
  padding-top: 0;
}
div[data-type="core/freeform"].is-selected .block-library-rich-text__tinymce::after {
  content: "";
  display: table;
  clear: both;
}

.mce-toolbar-grp .mce-btn.mce-active button,
.mce-toolbar-grp .mce-btn.mce-active:hover button,
.mce-toolbar-grp .mce-btn.mce-active i,
.mce-toolbar-grp .mce-btn.mce-active:hover i {
  color: #1e1e1e;
}
.mce-toolbar-grp .mce-rtl .mce-flow-layout-item.mce-last {
  margin-right: 0;
  margin-left: 8px;
}
.mce-toolbar-grp .mce-btn i {
  font-style: normal;
}

.block-library-classic__toolbar {
  display: none;
  width: auto;
  margin: 0;
  position: sticky;
  z-index: 31;
  top: 0;
  border: 1px solid #ddd;
  border-bottom: none;
  border-radius: 2px;
  margin-bottom: 8px;
  padding: 0;
}
div[data-type="core/freeform"].is-selected .block-library-classic__toolbar {
  display: block;
  border-color: #1e1e1e;
}
.block-library-classic__toolbar .mce-tinymce {
  box-shadow: none;
}
@media (min-width: 600px) {
  .block-library-classic__toolbar {
    padding: 0;
  }
}
.block-library-classic__toolbar:empty {
  display: block;
  background: #f5f5f5;
  border-bottom: 1px solid #e2e4e7;
}
.block-library-classic__toolbar:empty::before {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  content: attr(data-placeholder);
  color: #555d66;
  line-height: 37px;
  padding: 14px;
}
.block-library-classic__toolbar div.mce-toolbar-grp {
  border-bottom: 1px solid #1e1e1e;
}
.block-library-classic__toolbar .mce-tinymce-inline,
.block-library-classic__toolbar .mce-tinymce-inline > div,
.block-library-classic__toolbar div.mce-toolbar-grp,
.block-library-classic__toolbar div.mce-toolbar-grp > div,
.block-library-classic__toolbar .mce-menubar,
.block-library-classic__toolbar .mce-menubar > div {
  height: auto !important;
  width: 100% !important;
}
.block-library-classic__toolbar .mce-container-body.mce-abs-layout {
  overflow: visible;
}
.block-library-classic__toolbar .mce-menubar,
.block-library-classic__toolbar div.mce-toolbar-grp {
  position: static;
}
.block-library-classic__toolbar .mce-toolbar-grp > div {
  padding: 1px 3px;
}
.block-library-classic__toolbar .mce-toolbar-grp .mce-toolbar:not(:first-child) {
  display: none;
}
.block-library-classic__toolbar.has-advanced-toolbar .mce-toolbar-grp .mce-toolbar {
  display: block;
}

figure.wp-block-gallery {
  display: block;
}
figure.wp-block-gallery.has-nested-images .components-drop-zone {
  display: none;
  pointer-events: none;
}
figure.wp-block-gallery > .blocks-gallery-caption {
  flex: 0 0 100%;
}
figure.wp-block-gallery > .blocks-gallery-media-placeholder-wrapper {
  flex-basis: 100%;
}
figure.wp-block-gallery .wp-block-image .components-notice.is-error {
  display: block;
}
figure.wp-block-gallery .wp-block-image .components-notice__content {
  margin: 4px 0;
}
figure.wp-block-gallery .wp-block-image .components-notice__dismiss {
  position: absolute;
  top: 0;
  right: 5px;
}
figure.wp-block-gallery .block-editor-media-placeholder.is-appender .components-placeholder__label {
  display: none;
}
figure.wp-block-gallery .block-editor-media-placeholder.is-appender .block-editor-media-placeholder__button {
  margin-bottom: 0;
}
figure.wp-block-gallery .block-editor-media-placeholder {
  margin: 0;
}
figure.wp-block-gallery .block-editor-media-placeholder .components-placeholder__label {
  display: flex;
}
figure.wp-block-gallery .block-editor-media-placeholder figcaption {
  z-index: 2;
}
figure.wp-block-gallery .components-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -9px;
  margin-left: -9px;
}

/**
 * Gallery inspector controls settings.
 */
.gallery-settings-buttons .components-button:first-child {
  margin-right: 8px;
}

.gallery-image-sizes .components-base-control__label {
  display: block;
  margin-bottom: 4px;
}
.gallery-image-sizes .gallery-image-sizes__loading {
  display: flex;
  align-items: center;
  color: #757575;
  font-size: 12px;
}
.gallery-image-sizes .components-spinner {
  margin: 0 8px 0 4px;
}

/**
 * Deprecated css past this point. This can be removed once all galleries are migrated
 * to V2.
 */
.blocks-gallery-item figure:not(.is-selected):focus,
.blocks-gallery-item img:focus {
  outline: none;
}
.blocks-gallery-item figure.is-selected::before {
  box-shadow: 0 0 0 1px #fff inset, 0 0 0 3px var(--wp-admin-theme-color) inset;
  content: "";
  outline: 2px solid transparent;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}
.blocks-gallery-item figure.is-transient img {
  opacity: 0.3;
}
.blocks-gallery-item .is-selected .block-library-gallery-item__inline-menu {
  display: inline-flex;
}
.blocks-gallery-item .block-editor-media-placeholder {
  margin: 0;
  height: 100%;
}
.blocks-gallery-item .block-editor-media-placeholder .components-placeholder__label {
  display: flex;
}

.block-library-gallery-item__inline-menu {
  display: none;
  position: absolute;
  top: -2px;
  margin: 8px;
  z-index: 20;
  transition: box-shadow 0.2s ease-out;
  border-radius: 2px;
  background: #fff;
  border: 1px solid #1e1e1e;
}
@media (prefers-reduced-motion: reduce) {
  .block-library-gallery-item__inline-menu {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
.block-library-gallery-item__inline-menu:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
@media (min-width: 600px) {
  .columns-7 .block-library-gallery-item__inline-menu, .columns-8 .block-library-gallery-item__inline-menu {
    padding: 2px;
  }
}
.block-library-gallery-item__inline-menu .components-button.has-icon:not(:focus) {
  border: none;
  box-shadow: none;
}
@media (min-width: 600px) {
  .columns-7 .block-library-gallery-item__inline-menu .components-button.has-icon, .columns-8 .block-library-gallery-item__inline-menu .components-button.has-icon {
    padding: 0;
    width: inherit;
    height: inherit;
  }
}
.block-library-gallery-item__inline-menu.is-left {
  left: -2px;
}
.block-library-gallery-item__inline-menu.is-right {
  right: -2px;
}

.wp-block-gallery ul.blocks-gallery-grid {
  padding: 0;
  margin: 0;
}

@media (min-width: 600px) {
  .wp-block-update-gallery-modal {
    max-width: 480px;
  }
}

.wp-block-update-gallery-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/**
 * Group: All Alignment Settings
 */
.wp-block-group .block-editor-block-list__insertion-point {
  left: 0;
  right: 0;
}

[data-type="core/group"].is-selected .block-list-appender {
  margin-left: 0;
  margin-right: 0;
}
[data-type="core/group"].is-selected .has-background .block-list-appender {
  margin-top: 18px;
  margin-bottom: 18px;
}

.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child {
  gap: inherit;
  pointer-events: none;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child,
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-default-block-appender__content,
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-inserter {
  display: inherit;
  width: 100%;
  flex-direction: inherit;
  flex: 1;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child::after {
  content: "";
  display: flex;
  border: 1px dashed currentColor;
  opacity: 0.4;
  border-radius: 2px;
  flex: 1;
  pointer-events: none;
  min-height: 48px;
}
.is-layout-flex.block-editor-block-list__block .block-list-appender:only-child .block-editor-inserter {
  pointer-events: all;
}

.block-library-html__edit .block-library-html__preview-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.block-library-html__edit .block-editor-plain-text {
  font-family: Menlo, Consolas, monaco, monospace !important;
  color: #1e1e1e !important;
  background: #fff !important;
  padding: 12px !important;
  border: 1px solid #1e1e1e !important;
  box-shadow: none !important;
  border-radius: 2px !important;
  max-height: 250px;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  font-size: 16px !important;
}
@media (min-width: 600px) {
  .block-library-html__edit .block-editor-plain-text {
    font-size: 13px !important;
  }
}
.block-library-html__edit .block-editor-plain-text:focus {
  border-color: var(--wp-admin-theme-color) !important;
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color) !important;
  outline: 2px solid transparent !important;
}

figure.wp-block-image:not(.wp-block) {
  margin: 0;
}

.wp-block-image {
  position: relative;
}
.wp-block-image .is-applying img, .wp-block-image.is-transient img {
  opacity: 0.3;
}
.wp-block-image figcaption img {
  display: inline;
}
.wp-block-image .components-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -9px;
  margin-left: -9px;
}
.wp-block-image:not(.is-style-rounded) > div:not(.components-placeholder) {
  border-radius: inherit;
}

.wp-block-image .components-resizable-box__container {
  display: inline-block;
}
.wp-block-image .components-resizable-box__container img {
  display: block;
  width: inherit;
  height: inherit;
}

.block-editor-block-list__block[data-type="core/image"] .block-editor-block-toolbar .block-editor-url-input__button-modal {
  position: absolute;
  left: 0;
  right: 0;
  margin: -1px 0;
}
@media (min-width: 600px) {
  .block-editor-block-list__block[data-type="core/image"] .block-editor-block-toolbar .block-editor-url-input__button-modal {
    margin: -1px;
  }
}

[data-align=wide] > .wp-block-image img,
[data-align=full] > .wp-block-image img {
  height: auto;
  width: 100%;
}

.wp-block[data-align=left] > .wp-block-image,
.wp-block[data-align=center] > .wp-block-image,
.wp-block[data-align=right] > .wp-block-image {
  display: table;
}
.wp-block[data-align=left] > .wp-block-image > figcaption,
.wp-block[data-align=center] > .wp-block-image > figcaption,
.wp-block[data-align=right] > .wp-block-image > figcaption {
  display: table-caption;
  caption-side: bottom;
}

.wp-block[data-align=left] > .wp-block-image {
  margin-right: 1em;
  margin-left: 0;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.wp-block[data-align=right] > .wp-block-image {
  margin-left: 1em;
  margin-right: 0;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.wp-block[data-align=center] > .wp-block-image {
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.wp-block-image__crop-area {
  position: relative;
  max-width: 100%;
  width: 100%;
}

.wp-block-image__crop-icon {
  padding: 0 8px;
  min-width: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wp-block-image__crop-icon svg {
  fill: currentColor;
}

.wp-block-image__zoom .components-popover__content {
  overflow: visible;
  min-width: 260px;
}
.wp-block-image__zoom .components-range-control {
  flex: 1;
}
.wp-block-image__zoom .components-base-control__field {
  display: flex;
  margin-bottom: 0;
  flex-direction: column;
  align-items: flex-start;
}

.wp-block-image__aspect-ratio {
  height: 46px;
  margin-bottom: -8px;
  display: flex;
  align-items: center;
}
.wp-block-image__aspect-ratio .components-button {
  width: 36px;
  padding-left: 0;
  padding-right: 0;
}

.wp-block-latest-posts {
  padding-left: 2.5em;
}
.wp-block-latest-posts.is-grid {
  padding-left: 0;
}

.wp-block-latest-posts li a > div {
  display: inline;
}

.edit-post-visual-editor .wp-block-latest-posts.is-grid li {
  margin-bottom: 20px;
}

.editor-latest-posts-image-alignment-control .components-base-control__label {
  display: block;
}
.editor-latest-posts-image-alignment-control .components-toolbar {
  border-radius: 2px;
}

.wp-block-media-text__media {
  position: relative;
}
.wp-block-media-text__media.is-transient img {
  opacity: 0.3;
}
.wp-block-media-text__media .components-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -9px;
  margin-left: -9px;
}

.wp-block-media-text .__resizable_base__ {
  grid-column: 1/span 2;
  grid-row: 2;
}

.wp-block-media-text .editor-media-container__resizer {
  width: 100% !important;
}

.wp-block-media-text.is-image-fill .editor-media-container__resizer {
  height: 100% !important;
}

.wp-block-media-text > .block-editor-block-list__layout > .block-editor-block-list__block {
  max-width: unset;
}

.block-editor-block-list__block[data-type="core/more"] {
  max-width: 100%;
  text-align: center;
  margin-top: 28px;
  margin-bottom: 28px;
}

.wp-block-more {
  display: block;
  text-align: center;
  white-space: nowrap;
}
.wp-block-more input[type=text] {
  position: relative;
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: #757575;
  border: none;
  box-shadow: none;
  white-space: nowrap;
  text-align: center;
  margin: 0;
  border-radius: 4px;
  background: #fff;
  padding: 6px 8px;
  height: 24px;
  max-width: 100%;
}
.wp-block-more input[type=text]:focus {
  box-shadow: none;
}
.wp-block-more::before {
  content: "";
  position: absolute;
  top: calc(50%);
  left: 0;
  right: 0;
  border-top: 3px dashed #ccc;
}

/**
 * Editor only CSS.
 */
.editor-styles-wrapper .wp-block-navigation ul {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-left: 0;
}
.editor-styles-wrapper .wp-block-navigation .wp-block-navigation-item.wp-block {
  margin: revert;
}

.wp-block-navigation-item__label {
  display: inline;
}

/**
 * Submenus.
 */
.wp-block-navigation__container.is-parent-of-selected-block {
  visibility: visible;
  opacity: 1;
  overflow: visible;
}

.wp-block-navigation__container,
.wp-block-navigation-item {
  background-color: inherit;
}

.wp-block-navigation:not(.is-selected):not(.has-child-selected) .has-child:hover > .wp-block-navigation__submenu-container {
  opacity: 0;
  visibility: hidden;
}

.has-child.is-selected > .wp-block-navigation__submenu-container, .has-child.has-child-selected > .wp-block-navigation__submenu-container {
  display: flex;
  opacity: 1;
  visibility: visible;
}

.is-dragging-components-draggable .has-child.is-dragging-within > .wp-block-navigation__submenu-container {
  opacity: 1;
  visibility: visible;
}

.is-editing > .wp-block-navigation__container {
  visibility: visible;
  opacity: 1;
  display: flex;
  flex-direction: column;
}

.is-dragging-components-draggable .wp-block-navigation-link > .wp-block-navigation__container {
  opacity: 1;
  visibility: hidden;
}
.is-dragging-components-draggable .wp-block-navigation-link > .wp-block-navigation__container .block-editor-block-draggable-chip-wrapper {
  visibility: visible;
}

.is-editing > .wp-block-navigation__submenu-container > .block-list-appender {
  display: block;
  position: static;
  width: 100%;
}
.is-editing > .wp-block-navigation__submenu-container > .block-list-appender .block-editor-button-block-appender {
  color: #fff;
  background: #1e1e1e;
  padding: 0;
  width: 24px;
  border-radius: 2px;
  margin-right: 0;
  margin-left: auto;
}

.wp-block-navigation__submenu-container .block-list-appender {
  display: none;
}

/**
 * Colors Selector component
 */
.block-library-colors-selector {
  width: auto;
}
.block-library-colors-selector .block-library-colors-selector__toggle {
  display: block;
  margin: 0 auto;
  padding: 3px;
  width: auto;
}
.block-library-colors-selector .block-library-colors-selector__icon-container {
  height: 30px;
  position: relative;
  margin: 0 auto;
  padding: 3px;
  display: flex;
  align-items: center;
  border-radius: 4px;
}
.block-library-colors-selector .block-library-colors-selector__state-selection {
  margin-left: auto;
  margin-right: auto;
  border-radius: 11px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2);
  width: 22px;
  min-width: 22px;
  height: 22px;
  min-height: 22px;
  line-height: 20px;
  padding: 2px;
}
.block-library-colors-selector .block-library-colors-selector__state-selection > svg {
  min-width: auto !important;
}
.block-library-colors-selector .block-library-colors-selector__state-selection.has-text-color > svg,
.block-library-colors-selector .block-library-colors-selector__state-selection.has-text-color > svg path {
  color: inherit;
}

.block-library-colors-selector__popover .color-palette-controller-container {
  padding: 16px;
}
.block-library-colors-selector__popover .components-base-control__label {
  height: 20px;
  line-height: 20px;
}
.block-library-colors-selector__popover .component-color-indicator {
  float: right;
  margin-top: 2px;
}
.block-library-colors-selector__popover .components-panel__body-title {
  display: none;
}

.wp-block-navigation .block-editor-button-block-appender {
  background-color: #1e1e1e;
  color: #fff;
}
.wp-block-navigation .block-editor-button-block-appender.block-editor-button-block-appender.block-editor-button-block-appender {
  padding: 0;
}

.wp-block-navigation .wp-block .wp-block .block-editor-button-block-appender {
  background-color: transparent;
  color: #1e1e1e;
}

/**
 * Setup state
 */
@keyframes loadingpulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
.components-placeholder.wp-block-navigation-placeholder {
  outline: none;
  padding: 0;
  box-shadow: none;
  background: none;
  min-height: 0;
  color: inherit;
}
.components-placeholder.wp-block-navigation-placeholder .components-placeholder__fieldset {
  font-size: inherit;
}
.components-placeholder.wp-block-navigation-placeholder .components-placeholder__fieldset .components-button {
  margin-bottom: 0;
}
.wp-block-navigation.is-selected .components-placeholder.wp-block-navigation-placeholder {
  color: #1e1e1e;
}

.wp-block-navigation-placeholder .components-spinner {
  margin-top: 0;
}

.wp-block-navigation-placeholder__preview {
  display: flex;
  align-items: center;
  min-width: 96px;
  font-size: 13px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: currentColor;
  background: transparent;
}
.wp-block-navigation.is-selected .wp-block-navigation-placeholder__preview {
  display: none;
}
.wp-block-navigation-placeholder__preview::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 1px dashed currentColor;
  opacity: 0.4;
  pointer-events: none;
  border-radius: inherit;
}
.wp-block-navigation-placeholder__preview > svg {
  fill: currentColor;
  opacity: 0.4;
}

.wp-block-navigation.is-vertical .is-small .components-placeholder__fieldset,
.wp-block-navigation.is-vertical .is-medium .components-placeholder__fieldset {
  min-height: 90px;
}

.wp-block-navigation.is-vertical .is-large .components-placeholder__fieldset {
  min-height: 132px;
}

.wp-block-navigation-placeholder__preview,
.wp-block-navigation-placeholder__controls {
  padding: 6px 8px;
  flex-direction: row;
  align-items: flex-start;
}

.wp-block-navigation-placeholder__controls {
  border-radius: 2px;
  background-color: #fff;
  box-shadow: inset 0 0 0 1px #1e1e1e;
  display: none;
  position: relative;
  z-index: 1;
  float: left;
  width: 100%;
}
.wp-block-navigation.is-selected .wp-block-navigation-placeholder__controls {
  display: flex;
}
.is-small .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions__indicator,
.is-small .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions__indicator + hr, .is-medium .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions__indicator,
.is-medium .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions__indicator + hr {
  display: none;
}
.is-small .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions, .wp-block-navigation.is-vertical .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions {
  flex-direction: column;
  align-items: flex-start;
}
.is-small .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions hr, .wp-block-navigation.is-vertical .wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__actions hr {
  display: none;
}
.wp-block-navigation-placeholder__controls .wp-block-navigation-placeholder__icon {
  margin-right: 12px;
  height: 36px;
}

.wp-block-navigation-placeholder__actions__indicator {
  display: flex;
  padding: 0 6px 0 0;
  align-items: center;
  justify-content: flex-start;
  line-height: 0;
  height: 36px;
  margin-left: 4px;
}
.wp-block-navigation-placeholder__actions__indicator svg {
  margin-right: 4px;
  fill: currentColor;
}

.wp-block-navigation .components-placeholder.is-medium .components-placeholder__fieldset {
  flex-direction: row !important;
}

.wp-block-navigation-placeholder__actions {
  display: flex;
  font-size: 13px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  gap: 6px;
  align-items: center;
  height: 100%;
}
.wp-block-navigation-placeholder__actions .components-dropdown,
.wp-block-navigation-placeholder__actions > .components-button {
  margin-right: 0;
}
.wp-block-navigation-placeholder__actions.wp-block-navigation-placeholder__actions hr {
  border: 0;
  min-height: 1px;
  min-width: 1px;
  background-color: #1e1e1e;
  margin: auto 0;
  height: 100%;
  max-height: 16px;
}

/**
 * Mobile menu.
 */
@media (min-width: 600px) {
  .wp-block-navigation__responsive-container:not(.is-menu-open) .components-button.wp-block-navigation__responsive-container-close {
    display: none;
  }
}

.wp-block-navigation__responsive-container.is-menu-open {
  position: fixed;
  top: 155px;
}
@media (min-width: 782px) {
  .wp-block-navigation__responsive-container.is-menu-open {
    top: 93px;
  }
}
@media (min-width: 782px) {
  .wp-block-navigation__responsive-container.is-menu-open {
    left: 36px;
  }
}
@media (min-width: 960px) {
  .wp-block-navigation__responsive-container.is-menu-open {
    left: 160px;
  }
}

@media (min-width: 782px) {
  .has-fixed-toolbar .wp-block-navigation__responsive-container.is-menu-open {
    top: 141px;
  }
}

.is-mobile-preview .wp-block-navigation__responsive-container.is-menu-open,
.is-tablet-preview .wp-block-navigation__responsive-container.is-menu-open {
  top: 141px;
}

.is-sidebar-opened .wp-block-navigation__responsive-container.is-menu-open {
  right: 280px;
}

.is-fullscreen-mode .wp-block-navigation__responsive-container.is-menu-open {
  left: 0;
  top: 155px;
}
@media (min-width: 782px) {
  .is-fullscreen-mode .wp-block-navigation__responsive-container.is-menu-open {
    top: 61px;
  }
}
@media (min-width: 782px) {
  .is-fullscreen-mode .has-fixed-toolbar .wp-block-navigation__responsive-container.is-menu-open {
    top: 109px;
  }
}
.is-fullscreen-mode .is-mobile-preview .wp-block-navigation__responsive-container.is-menu-open,
.is-fullscreen-mode .is-tablet-preview .wp-block-navigation__responsive-container.is-menu-open {
  top: 109px;
}

body.editor-styles-wrapper .wp-block-navigation__responsive-container.is-menu-open {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

@media (min-width: 600px) {
  .wp-block-navigation__responsive-close {
    pointer-events: none;
  }
  .wp-block-navigation__responsive-close .wp-block-navigation__responsive-container-close,
.wp-block-navigation__responsive-close .block-editor-block-list__layout * {
    pointer-events: all;
  }
}
.wp-block-navigation__responsive-close .wp-block-pages-list__item__link {
  pointer-events: none;
}

.components-button.wp-block-navigation__responsive-container-open.wp-block-navigation__responsive-container-open,
.components-button.wp-block-navigation__responsive-container-close.wp-block-navigation__responsive-container-close {
  padding: 0;
  height: auto;
  color: inherit;
}

.is-menu-open .wp-block-navigation__responsive-container-content * .block-list-appender {
  margin-top: 16px;
}

@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.wp-block-navigation .components-spinner {
  padding: 8px 12px;
}

.wp-block-navigation__unsaved-changes {
  position: relative;
}
.wp-block-navigation__unsaved-changes .components-spinner {
  position: absolute;
  top: calc(50% - 16px / 2);
  left: calc(50% - 16px / 2);
  opacity: 0;
  animation: 0.5s linear 2s normal forwards fadein;
}

@keyframes fadeouthalf {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
.wp-block-navigation__unsaved-changes-overlay.is-saving {
  opacity: 1;
  animation: 0.5s linear 2s normal forwards fadeouthalf;
}

.wp-block-navigation-delete-menu-button {
  width: 100%;
  justify-content: center;
  margin-bottom: 16px;
}

.wp-block-navigation__overlay-menu-preview {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #f0f0f0;
  padding: 0 24px;
  height: 64px;
  margin-bottom: 12px;
}
.wp-block-navigation__overlay-menu-preview.open {
  box-shadow: inset 0 0 0 1px #e0e0e0;
  outline: 1px solid transparent;
  background-color: #fff;
}

.wp-block-navigation__toolbar-menu-selector.components-toolbar-group:empty {
  display: none;
}

.wp-block-navigation-placeholder__actions hr + hr {
  display: none;
}

/**
 * Submenus.
 */
.wp-block-navigation .has-child {
  cursor: pointer;
}
.wp-block-navigation .has-child .wp-block-navigation__submenu-container {
  z-index: 28;
}
.wp-block-navigation .has-child:hover .wp-block-navigation__submenu-container {
  z-index: 29;
}
.wp-block-navigation .has-child.is-selected > .wp-block-navigation__submenu-container, .wp-block-navigation .has-child.has-child-selected > .wp-block-navigation__submenu-container {
  visibility: visible !important;
  opacity: 1 !important;
  min-width: 200px !important;
  height: auto !important;
  width: auto !important;
  overflow: visible !important;
}

/**
 * Navigation Items.
 */
.wp-block-navigation-item .wp-block-navigation-item__content {
  cursor: text;
}
.wp-block-navigation-item.is-editing, .wp-block-navigation-item.is-selected {
  min-width: 20px;
}
.wp-block-navigation-item .block-list-appender {
  margin-top: 16px;
  margin-right: auto;
  margin-bottom: 16px;
  margin-left: 16px;
}

.wp-block-navigation-link__invalid-item {
  color: #000;
}

.wp-block-navigation-link__missing_text-tooltip {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
}

/**
 * Menu item setup state. Is shown when a menu item has no URL configured.
 */
.wp-block-navigation-link__placeholder {
  position: relative;
  text-decoration: none !important;
  box-shadow: none !important;
  background-image: none !important;
}
.wp-block-navigation-link__placeholder .wp-block-navigation-link__placeholder-text span {
  --wp-underline-color: var(--wp-admin-theme-color);
  background-image: linear-gradient(45deg, transparent 20%, var(--wp-underline-color) 30%, var(--wp-underline-color) 36%, transparent 46%), linear-gradient(135deg, transparent 54%, var(--wp-underline-color) 64%, var(--wp-underline-color) 70%, transparent 80%);
  background-position: 0 100%;
  background-size: 6px 3px;
  background-repeat: repeat-x;
  padding-bottom: 0.1em;
}
.is-dark-theme .wp-block-navigation-link__placeholder .wp-block-navigation-link__placeholder-text span {
  --wp-underline-color: #fff;
}
.wp-block-navigation-link__placeholder.wp-block-navigation-item__content {
  cursor: pointer;
}

/**
* Link Control Transforms
*/
.link-control-transform {
  border-top: 1px solid #ccc;
  padding: 0 16px 8px 16px;
}

.link-control-transform__subheading {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 500;
  color: #1e1e1e;
  margin-bottom: 1.5em;
}

.link-control-transform__items {
  display: flex;
  justify-content: space-between;
}

.link-control-transform__item {
  flex-basis: 33%;
  flex-direction: column;
  gap: 8px;
  height: auto;
}

.wp-block-navigation-submenu {
  display: block;
}
.wp-block-navigation-submenu .wp-block-navigation__submenu-container {
  z-index: 28;
}
.wp-block-navigation-submenu.is-selected > .wp-block-navigation__submenu-container, .wp-block-navigation-submenu.has-child-selected > .wp-block-navigation__submenu-container {
  visibility: visible !important;
  opacity: 1 !important;
  min-width: 200px !important;
  height: auto !important;
  width: auto !important;
  position: absolute;
  left: -1px;
  top: 100%;
}
@media (min-width: 782px) {
  .wp-block-navigation-submenu.is-selected > .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container, .wp-block-navigation-submenu.has-child-selected > .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {
    left: 100%;
    top: -1px;
  }
  .wp-block-navigation-submenu.is-selected > .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container::before, .wp-block-navigation-submenu.has-child-selected > .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container::before {
    content: "";
    position: absolute;
    right: 100%;
    height: 100%;
    display: block;
    width: 0.5em;
    background: transparent;
  }
}

.block-editor-block-list__block[data-type="core/nextpage"] {
  max-width: 100%;
  text-align: center;
  margin-top: 28px;
  margin-bottom: 28px;
}

.wp-block-nextpage {
  display: block;
  text-align: center;
  white-space: nowrap;
}
.wp-block-nextpage > span {
  font-size: 13px;
  position: relative;
  text-transform: uppercase;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: #757575;
  border-radius: 4px;
  background: #fff;
  padding: 6px 8px;
  height: 24px;
}
.wp-block-nextpage::before {
  content: "";
  position: absolute;
  top: calc(50%);
  left: 0;
  right: 0;
  border-top: 3px dashed #ccc;
}

.wp-block-navigation .wp-block-page-list > div,
.wp-block-navigation .wp-block-page-list {
  background-color: inherit;
}
.wp-block-navigation.items-justified-space-between .wp-block-page-list > div,
.wp-block-navigation.items-justified-space-between .wp-block-page-list {
  display: contents;
  flex: 1;
}
.wp-block-navigation.items-justified-space-between.has-child-selected .wp-block-page-list > div, .wp-block-navigation.items-justified-space-between.has-child-selected .wp-block-page-list, .wp-block-navigation.items-justified-space-between.is-selected .wp-block-page-list > div, .wp-block-navigation.items-justified-space-between.is-selected .wp-block-page-list {
  flex: inherit;
}

.wp-block-pages-list__item__link {
  pointer-events: none;
}

@media (min-width: 600px) {
  .wp-block-page-list-modal {
    max-width: 480px;
  }
}

.wp-block-page-list-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.wp-block-page-list .open-on-click:focus-within > .wp-block-navigation__submenu-container {
  visibility: visible;
  opacity: 1;
  width: auto;
  height: auto;
  min-width: 200px;
}

.wp-block-page-list .components-notice {
  margin-left: 0;
}

.block-editor-block-list__block[data-type="core/paragraph"].has-drop-cap:focus {
  min-height: auto !important;
}

.block-editor-block-list__block[data-empty=true] [data-rich-text-placeholder] {
  opacity: 1;
}

.block-editor-block-list__block[data-empty=true] + .block-editor-block-list__block[data-empty=true] [data-rich-text-placeholder] {
  opacity: 0;
}

.wp-block-post-excerpt .wp-block-post-excerpt__excerpt.is-inline {
  display: inline-block;
}

.wp-block-pullquote.has-text-align-left p,
.wp-block-pullquote.has-text-align-right p,
.wp-block[data-align=left] > .wp-block-pullquote p,
.wp-block[data-align=right] > .wp-block-pullquote p {
  font-size: 20px;
}

.wp-block-pullquote blockquote p {
  font-size: 28px;
  line-height: 1.6;
}

.wp-block-pullquote.is-style-solid-color blockquote p {
  font-size: 32px;
}
.wp-block-pullquote.is-style-solid-color .wp-block-pullquote__citation {
  text-transform: none;
  font-style: normal;
}

.wp-block-pullquote .wp-block-pullquote__citation {
  color: inherit;
}

.wp-block-rss li a > div {
  display: inline;
}

.wp-block-rss__placeholder-form {
  display: flex;
  align-items: stretch;
}
.wp-block-rss__placeholder-form > * {
  margin-bottom: 8px;
}
@media (min-width: 782px) {
  .wp-block-rss__placeholder-form > * {
    margin-bottom: 0;
  }
}

.wp-block-rss__placeholder-input {
  display: flex;
  align-items: stretch;
  flex-grow: 1;
}
.wp-block-rss__placeholder-input .components-base-control__field {
  margin: 0;
  display: flex;
  align-items: stretch;
  flex-grow: 1;
  margin-right: 8px;
}

.wp-block[data-align=center] .wp-block-search .wp-block-search__inside-wrapper {
  margin: auto;
}

.wp-block-search .wp-block-search__button {
  height: auto;
  border-radius: initial;
  display: flex;
  align-items: center;
}
.wp-block-search__components-button-group {
  margin-top: 10px;
}

.block-editor-block-list__block[data-type="core/separator"] {
  padding-top: 0.1px;
  padding-bottom: 0.1px;
}
.block-editor-block-list__block[data-type="core/separator"].wp-block-separator.is-style-dots {
  background: none !important;
}

[data-type="core/shortcode"] .block-editor-plain-text {
  max-height: 250px;
}
[data-type="core/shortcode"].components-placeholder {
  min-height: 0;
}

.blocks-shortcode__textarea {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  padding: 6px 8px;
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.1s linear;
  border-radius: 2px;
  border: 1px solid #757575;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  font-size: 16px;
  /* Override core line-height. To be reviewed. */
  line-height: normal;
}
@media (prefers-reduced-motion: reduce) {
  .blocks-shortcode__textarea {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
@media (min-width: 600px) {
  .blocks-shortcode__textarea {
    font-size: 13px;
    /* Override core line-height. To be reviewed. */
    line-height: normal;
  }
}
.blocks-shortcode__textarea:focus {
  border-color: var(--wp-admin-theme-color);
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color);
  outline: 2px solid transparent;
}
.blocks-shortcode__textarea::-webkit-input-placeholder {
  color: rgba(30, 30, 30, 0.62);
}
.blocks-shortcode__textarea::-moz-placeholder {
  opacity: 1;
  color: rgba(30, 30, 30, 0.62);
}
.blocks-shortcode__textarea:-ms-input-placeholder {
  color: rgba(30, 30, 30, 0.62);
}

.wp-block[data-align=center] > .wp-block-site-logo,
.wp-block-site-logo.aligncenter > div {
  display: table;
  margin-left: auto;
  margin-right: auto;
}

.wp-block-site-logo a {
  pointer-events: none;
}
.wp-block-site-logo .custom-logo-link {
  cursor: inherit;
}
.wp-block-site-logo .custom-logo-link:focus {
  box-shadow: none;
}
.wp-block-site-logo .custom-logo-link.is-transient img {
  opacity: 0.3;
}
.wp-block-site-logo img {
  display: block;
  height: auto;
  max-width: 100%;
}

.wp-block-site-logo.wp-block-site-logo .components-placeholder,
.wp-block-site-logo.wp-block-site-logo .components-resizable-box__container {
  border-radius: inherit;
}
.wp-block-site-logo.wp-block-site-logo.is-default-size .components-placeholder {
  height: 120px;
  width: 120px;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: none;
  padding: 0;
  min-height: 48px;
  min-width: 48px;
  height: 100%;
  width: 100%;
  color: currentColor;
  background: transparent;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-form-file-upload {
  display: none;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-placeholder__preview {
  position: absolute;
  top: 4px;
  right: 4px;
  bottom: 4px;
  left: 4px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-drop-zone__content-text {
  display: none;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 1px dashed currentColor;
  opacity: 0.4;
  pointer-events: none;
  border-radius: inherit;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-placeholder__fieldset {
  width: auto;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-button.components-button {
  color: inherit;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  visibility: hidden;
  background: transparent;
  transition: all 0.1s linear;
}
@media (prefers-reduced-motion: reduce) {
  .wp-block-site-logo.wp-block-site-logo .components-placeholder .components-button.components-button {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-button.components-button > svg {
  color: #fff;
}
.wp-block-site-logo.wp-block-site-logo .components-placeholder .components-placeholder__illustration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  stroke: currentColor;
  stroke-dasharray: 3;
  opacity: 0.4;
}
.wp-block-site-logo.wp-block-site-logo.is-selected .components-button.components-button {
  background: var(--wp-admin-theme-color);
  border-color: var(--wp-admin-theme-color);
  border-style: solid;
  color: #fff;
  opacity: 1;
  visibility: visible;
}

.wp-block-site-tagline__placeholder {
  padding: 1em 0;
  border: 1px dashed;
}

.wp-block-site-title__placeholder {
  padding: 1em 0;
  border: 1px dashed;
}

.wp-block-social-links .wp-social-link {
  line-height: 0;
}
.wp-block-social-links .wp-social-link button {
  font-size: inherit;
  color: currentColor;
  height: auto;
  line-height: 0;
  padding: 0.25em;
}

.wp-block-social-links.is-style-pill-shape .wp-social-link button {
  padding-left: calc((2/3) * 1em);
  padding-right: calc((2/3) * 1em);
}

.wp-block-social-links.is-style-logos-only .wp-social-link button {
  padding: 0;
}

.wp-block-social-links div.block-editor-url-input {
  display: inline-block;
  margin-left: 8px;
}
.wp-block-social-links.wp-block-social-links {
  background: none;
}

.wp-social-link:hover {
  transform: none;
}

.editor-styles-wrapper .wp-block-social-links {
  padding: 0;
}

.wp-block-social-links__social-placeholder {
  display: flex;
  opacity: 0.8;
  list-style: none;
}
.wp-block-social-links__social-placeholder > .wp-social-link {
  padding-left: 0 !important;
  margin-left: 0 !important;
  padding-right: 0 !important;
  margin-right: 0 !important;
  width: 0 !important;
  visibility: hidden;
}
.wp-block-social-links__social-placeholder > .wp-block-social-links__social-placeholder-icons {
  display: flex;
}
.wp-block-social-links__social-placeholder .wp-social-link {
  padding: 0.25em;
}
.is-style-pill-shape .wp-block-social-links__social-placeholder .wp-social-link {
  padding-left: calc((2/3) * 1em);
  padding-right: calc((2/3) * 1em);
}
.is-style-logos-only .wp-block-social-links__social-placeholder .wp-social-link {
  padding: 0;
}
.wp-block-social-links__social-placeholder .wp-social-link::before {
  content: "";
  display: block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
}
.is-style-logos-only .wp-block-social-links__social-placeholder .wp-social-link::before {
  background: currentColor;
}

.wp-block-social-links .wp-block-social-links__social-prompt {
  min-height: 24px;
  list-style: none;
  order: 2;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  line-height: 24px;
  margin-top: auto;
  margin-bottom: auto;
  cursor: default;
  padding-right: 8px;
}

.wp-block[data-align=center] > .wp-block-social-links {
  justify-content: center;
}

.block-editor-block-preview__content .components-button:disabled {
  opacity: 1;
}

.wp-social-link.wp-social-link__is-incomplete {
  opacity: 0.5;
}
@media (prefers-reduced-motion: reduce) {
  .wp-social-link.wp-social-link__is-incomplete {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}

.wp-block-social-links .is-selected .wp-social-link__is-incomplete,
.wp-social-link.wp-social-link__is-incomplete:hover,
.wp-social-link.wp-social-link__is-incomplete:focus {
  opacity: 1;
}

.block-editor-block-list__block[data-type="core/spacer"]::before {
  content: "";
  display: block;
  position: absolute;
  z-index: 1;
  width: 100%;
  min-height: 8px;
  min-width: 8px;
  height: 100%;
}

.wp-block-spacer.is-hovered .block-library-spacer__resize-container,
.block-library-spacer__resize-container.has-show-handle {
  background: rgba(0, 0, 0, 0.1);
}
.is-dark-theme .wp-block-spacer.is-hovered .block-library-spacer__resize-container,
.is-dark-theme .block-library-spacer__resize-container.has-show-handle {
  background: rgba(255, 255, 255, 0.15);
}

.block-library-spacer__resize-container {
  clear: both;
}
.block-library-spacer__resize-container:not(.is-resizing) {
  height: 100% !important;
  width: 100% !important;
}
.block-library-spacer__resize-container .components-resizable-box__handle::before {
  content: none;
}
.block-library-spacer__resize-container.resize-horizontal {
  margin-bottom: 0;
}

.wp-block-table {
  margin: 0;
}
.wp-block[data-align=left] > .wp-block-table, .wp-block[data-align=right] > .wp-block-table, .wp-block[data-align=center] > .wp-block-table {
  height: auto;
}
.wp-block[data-align=left] > .wp-block-table table, .wp-block[data-align=right] > .wp-block-table table, .wp-block[data-align=center] > .wp-block-table table {
  width: auto;
}
.wp-block[data-align=left] > .wp-block-table td,
.wp-block[data-align=left] > .wp-block-table th, .wp-block[data-align=right] > .wp-block-table td,
.wp-block[data-align=right] > .wp-block-table th, .wp-block[data-align=center] > .wp-block-table td,
.wp-block[data-align=center] > .wp-block-table th {
  word-break: break-word;
}
.wp-block[data-align=center] > .wp-block-table {
  text-align: initial;
}
.wp-block[data-align=center] > .wp-block-table table {
  margin: 0 auto;
}
.wp-block-table td,
.wp-block-table th {
  border: 1px solid;
}
.wp-block-table td.is-selected,
.wp-block-table th.is-selected {
  border-color: var(--wp-admin-theme-color);
  box-shadow: inset 0 0 0 1px var(--wp-admin-theme-color);
  border-style: double;
}
.wp-block-table figcaption {
  color: #555;
  font-size: 13px;
  text-align: center;
}
.is-dark-theme .wp-block-table figcaption {
  color: rgba(255, 255, 255, 0.65);
}

.blocks-table__placeholder-form.blocks-table__placeholder-form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.blocks-table__placeholder-form.blocks-table__placeholder-form > * {
  margin-bottom: 8px;
}
@media (min-width: 782px) {
  .blocks-table__placeholder-form.blocks-table__placeholder-form {
    flex-direction: row;
    align-items: flex-end;
  }
  .blocks-table__placeholder-form.blocks-table__placeholder-form > * {
    margin-bottom: 0;
  }
}

.blocks-table__placeholder-input {
  width: 112px;
  margin-right: 8px;
  margin-bottom: 0;
}
.blocks-table__placeholder-input input {
  height: 36px;
}
.blocks-table__placeholder-input .components-base-control__field {
  margin-bottom: 0;
}

@media (min-width: 600px) {
  .block-editor-template-part__selection-modal {
    width: calc(100% - 32px);
    height: calc(100% - 120px);
  }
}
@media (min-width: 782px) {
  .block-editor-template-part__selection-modal {
    width: 750px;
  }
}
@media (min-width: 960px) {
  .block-editor-template-part__selection-modal {
    height: 70%;
  }
}

.wp-block-text-columns .block-editor-rich-text__editable:focus {
  outline: 1px solid #ddd;
}

.wp-block[data-align=center] > .wp-block-video {
  text-align: center;
}

.wp-block-video {
  position: relative;
}
.wp-block-video.is-transient video {
  opacity: 0.3;
}
.wp-block-video .components-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -9px;
  margin-left: -9px;
}

.editor-video-poster-control .components-base-control__label {
  display: block;
}
.editor-video-poster-control .components-button {
  margin-right: 8px;
}

.block-library-video-tracks-editor {
  z-index: 159990;
}

.block-library-video-tracks-editor > .components-popover__content {
  width: 360px;
}

.block-library-video-tracks-editor__track-list-track {
  display: flex;
  place-content: space-between;
  align-items: baseline;
  padding-left: 12px;
}

.block-library-video-tracks-editor__single-track-editor-label-language {
  display: flex;
  margin-top: 12px;
}
.block-library-video-tracks-editor__single-track-editor-label-language > .components-base-control {
  width: 50%;
}
.block-library-video-tracks-editor__single-track-editor-label-language > .components-base-control:first-child {
  margin-right: 16px;
}

.block-library-video-tracks-editor__single-track-editor-kind-select {
  max-width: 240px;
}

.block-library-video-tracks-editor__single-track-editor-buttons-container {
  display: flex;
  place-content: space-between;
  margin-top: 32px;
}

.block-library-video-tracks-editor__single-track-editor-edit-track-label {
  margin-top: 4px;
  margin-bottom: 12px;
  color: #757575;
  text-transform: uppercase;
  font-size: 11px;
  font-weight: 500;
  display: block;
}

.block-library-video-tracks-editor > .components-popover__content > div {
  padding: 0;
}

.block-library-video-tracks-editor__track-list .components-menu-group__label,
.block-library-video-tracks-editor__add-tracks-container .components-menu-group__label {
  padding: 0;
}

.block-library-video-tracks-editor__single-track-editor,
.block-library-video-tracks-editor__track-list,
.block-library-video-tracks-editor__add-tracks-container {
  padding: 12px;
}

.block-library-video-tracks-editor__single-track-editor .components-base-control .components-base-control__label {
  margin-bottom: 4px;
}
.block-library-video-tracks-editor__single-track-editor .components-base-control .components-base-control__field {
  margin-bottom: 12px;
}
.block-library-video-tracks-editor__single-track-editor .components-base-control .components-text-control__input {
  margin-left: 0;
}
.block-library-video-tracks-editor__single-track-editor .components-base-control .components-input-control__label {
  margin-bottom: 4px;
}

.editor-styles-wrapper ul.wp-block-post-template {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
}

.block-library-query-toolbar__popover .components-popover__content {
  min-width: 230px;
}

.wp-block-query__create-new-link {
  padding: 0 16px 16px 56px;
}

.block-library-query__pattern-selection-content .block-editor-block-patterns-list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 8px;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item {
  margin-bottom: 0;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item .block-editor-block-preview__container {
  max-height: 250px;
}

.block-editor-query-pattern__selection-modal .components-modal__content {
  overflow: hidden;
  padding: 0;
}
.block-editor-query-pattern__selection-modal .components-modal__content::before {
  margin-bottom: 0;
}

@media (min-width: 600px) {
  .block-editor-query-pattern__selection-modal {
    width: calc(100% - 32px);
    height: calc(100% - 120px);
  }
}
@media (min-width: 782px) {
  .block-editor-query-pattern__selection-modal {
    width: 750px;
  }
}
@media (min-width: 960px) {
  .block-editor-query-pattern__selection-modal {
    height: 80%;
    width: 80%;
    max-height: none;
  }
}

.wp-block[data-align=center] > .wp-block-query-pagination {
  justify-content: center;
}

.editor-styles-wrapper .wp-block-query-pagination {
  max-width: 100%;
}
.editor-styles-wrapper .wp-block-query-pagination.block-editor-block-list__layout {
  margin: 0;
}

.wp-block-query-pagination > .wp-block-query-pagination-next,
.wp-block-query-pagination > .wp-block-query-pagination-previous,
.wp-block-query-pagination > .wp-block-query-pagination-numbers {
  margin-left: 0;
  margin-top: 0.5em;
  /*rtl:ignore*/
  margin-right: 0.5em;
  margin-bottom: 0.5em;
}
.wp-block-query-pagination > .wp-block-query-pagination-next:last-child,
.wp-block-query-pagination > .wp-block-query-pagination-previous:last-child,
.wp-block-query-pagination > .wp-block-query-pagination-numbers:last-child {
  /*rtl:ignore*/
  margin-right: 0;
}

.wp-block-query-pagination-numbers a {
  text-decoration: underline;
}
.wp-block-query-pagination-numbers .page-numbers {
  margin-right: 2px;
}
.wp-block-query-pagination-numbers .page-numbers:last-child {
  /*rtl:ignore*/
  margin-right: 0;
}

.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder,
.wp-block-post-featured-image.wp-block-post-featured-image .components-resizable-box__container {
  border-radius: inherit;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder {
  justify-content: center;
  align-items: center;
  box-shadow: none;
  padding: 0;
  color: currentColor;
  background: transparent;
  min-height: 200px;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-form-file-upload,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-form-file-upload {
  display: none;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__preview,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__preview {
  position: absolute;
  top: 4px;
  right: 4px;
  bottom: 4px;
  left: 4px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder::before,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 1px dashed currentColor;
  opacity: 0.4;
  pointer-events: none;
  border-radius: inherit;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__fieldset,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__fieldset {
  width: auto;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button {
  color: inherit;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  visibility: hidden;
  background: transparent;
  transition: all 0.1s linear;
}
@media (prefers-reduced-motion: reduce) {
  .wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button {
    transition-duration: 0s;
    transition-delay: 0s;
  }
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-button.components-button > svg,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-button.components-button > svg {
  color: #fff;
}
.wp-block-post-featured-image.wp-block-post-featured-image .wp-block-post-featured-image__placeholder .components-placeholder__illustration,
.wp-block-post-featured-image.wp-block-post-featured-image .components-placeholder .components-placeholder__illustration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  stroke: currentColor;
  stroke-dasharray: 3;
  opacity: 0.4;
}
.wp-block-post-featured-image.wp-block-post-featured-image[style*=height] .components-placeholder {
  min-height: 48px;
  min-width: 48px;
  height: 100%;
  width: 100%;
}
.wp-block-post-featured-image.wp-block-post-featured-image.is-selected .components-button.components-button {
  background: var(--wp-admin-theme-color);
  border-color: var(--wp-admin-theme-color);
  border-style: solid;
  color: #fff;
  opacity: 1;
  visibility: visible;
}

div[data-type="core/post-featured-image"] img {
  max-width: 100%;
  height: auto;
  display: block;
}

.wp-block-post-comments__placeholder * {
  pointer-events: none;
}

.wp-block-post-comments-form * {
  pointer-events: none;
}
.wp-block-post-comments-form *.block-editor-warning * {
  pointer-events: auto;
}

:root .editor-styles-wrapper {
  /*
   * Our classes uses the same values we set for gradient value attributes,
   * and we can not use spacing because of WP multi site kses rule.
   */
  /* stylelint-disable function-comma-space-after */
  /* stylelint-enable function-comma-space-after */
}
:root .editor-styles-wrapper .has-very-light-gray-background-color {
  background-color: #eee;
}
:root .editor-styles-wrapper .has-very-dark-gray-background-color {
  background-color: #313131;
}
:root .editor-styles-wrapper .has-very-light-gray-color {
  color: #eee;
}
:root .editor-styles-wrapper .has-very-dark-gray-color {
  color: #313131;
}
:root .editor-styles-wrapper .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
  background: linear-gradient(135deg, #00d084 0%, #0693e3 100%);
}
:root .editor-styles-wrapper .has-purple-crush-gradient-background {
  background: linear-gradient(135deg, #34e2e4 0%, #4721fb 50%, #ab1dfe 100%);
}
:root .editor-styles-wrapper .has-hazy-dawn-gradient-background {
  background: linear-gradient(135deg, #faaca8 0%, #dad0ec 100%);
}
:root .editor-styles-wrapper .has-subdued-olive-gradient-background {
  background: linear-gradient(135deg, #fafae1 0%, #67a671 100%);
}
:root .editor-styles-wrapper .has-atomic-cream-gradient-background {
  background: linear-gradient(135deg, #fdd79a 0%, #004a59 100%);
}
:root .editor-styles-wrapper .has-nightshade-gradient-background {
  background: linear-gradient(135deg, #330968 0%, #31cdcf 100%);
}
:root .editor-styles-wrapper .has-midnight-gradient-background {
  background: linear-gradient(135deg, #020381 0%, #2874fc 100%);
}

.editor-styles-wrapper {
  --wp--preset--font-size--normal: 16px;
  --wp--preset--font-size--huge: 42px;
}

.editor-styles-wrapper .has-regular-font-size {
  font-size: 16px;
}

.editor-styles-wrapper .has-larger-font-size {
  font-size: 42px;
}

.editor-styles-wrapper .has-normal-font-size {
  font-size: var(--wp--preset--font-size--normal);
}

.editor-styles-wrapper .has-huge-font-size {
  font-size: var(--wp--preset--font-size--huge);
}

/**
 * Editor Normalization Styles
 *
 * These are only output in the editor, but styles here are NOT prefixed .editor-styles-wrapper.
 * This allows us to create normalization styles that are easily overridden by editor styles.
 */