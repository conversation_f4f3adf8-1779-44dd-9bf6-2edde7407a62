/*! This file is auto-generated */
!function(){var e={8294:function(e){
/*!
 * clipboard.js v2.0.10
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
var t;t=function(){return function(){var e={686:function(e,t,n){"use strict";n.d(t,{default:function(){return k}});var r=n(279),o=n.n(r),u=n(370),i=n.n(u),c=n(817),s=n.n(c);function a(e){try{return document.execCommand(e)}catch(e){return!1}}var l=function(e){var t=s()(e);return a("cut"),t};function f(e){var t="rtl"===document.documentElement.getAttribute("dir"),n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[t?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(r,"px"),n.setAttribute("readonly",""),n.value=e,n}var d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n="";if("string"==typeof e){var r=f(e);t.container.appendChild(r),n=s()(r),a("copy"),r.remove()}else n=s()(e),a("copy");return n};function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}var v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,n=void 0===t?"copy":t,r=e.container,o=e.target,u=e.text;if("copy"!==n&&"cut"!==n)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==p(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===n&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===n&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return u?d(u,{container:r}):o?"cut"===n?l(o):d(o,{container:r}):void 0};function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function y(e,t){return y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},y(e,t)}function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=w(e);if(t){var o=w(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return g(this,n)}}function g(e,t){return!t||"object"!==h(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function w(e){return w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},w(e)}function E(e,t){var n="data-clipboard-".concat(e);if(t.hasAttribute(n))return t.getAttribute(n)}var T=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&y(e,t)}(u,e);var t,n,r,o=b(u);function u(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),(n=o.call(this)).resolveOptions(t),n.listenClick(e),n}return t=u,n=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===h(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=i()(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,n=this.action(t)||"copy",r=v({action:n,container:this.container,target:this.target(t),text:this.text(t)});this.emit(r?"success":"error",{action:n,text:r,trigger:t,clearSelection:function(){t&&t.focus(),document.activeElement.blur(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return E("action",e)}},{key:"defaultTarget",value:function(e){var t=E("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return E("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}],r=[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return d(e,t)}},{key:"cut",value:function(e){return l(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,n=!!document.queryCommandSupported;return t.forEach((function(e){n=n&&!!document.queryCommandSupported(e)})),n}}],n&&m(t.prototype,n),r&&m(t,r),u}(o()),k=T},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}e.exports=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}},438:function(e,t,n){var r=n(828);function o(e,t,n,r,o){var i=u.apply(this,arguments);return e.addEventListener(n,i,o),{destroy:function(){e.removeEventListener(n,i,o)}}}function u(e,t,n,o){return function(n){n.delegateTarget=r(n.target,t),n.delegateTarget&&o.call(e,n)}}e.exports=function(e,t,n,r,u){return"function"==typeof e.addEventListener?o.apply(null,arguments):"function"==typeof n?o.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return o(e,t,n,r,u)})))}},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var n=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,t,n){var r=n(879),o=n(438);e.exports=function(e,t,n){if(!e&&!t&&!n)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(e))return function(e,t,n){return e.addEventListener(t,n),{destroy:function(){e.removeEventListener(t,n)}}}(e,t,n);if(r.nodeList(e))return function(e,t,n){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,n)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,n)}))}}}(e,t,n);if(r.string(e))return function(e,t,n){return o(document.body,e,t,n)}(e,t,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var n=e.hasAttribute("readonly");n||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),n||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),o=document.createRange();o.selectNodeContents(e),r.removeAllRanges(),r.addRange(o),t=r.toString()}return t}},279:function(e){function t(){}t.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function o(){r.off(e,o),t.apply(n,arguments)}return o._=t,this.on(e,o,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,o=n.length;r<o;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],o=[];if(r&&t)for(var u=0,i=r.length;u<i;u++)r[u].fn!==t&&r[u].fn._!==t&&o.push(r[u]);return o.length?n[e]=o:delete n[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}return n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n(686)}().default},e.exports=t()},7973:function(e,t,n){var r;!function(o,u,i){if(o){for(var c,s={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},a={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},l={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},f={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},d=1;d<20;++d)s[111+d]="f"+d;for(d=0;d<=9;++d)s[d+96]=d.toString();g.prototype.bind=function(e,t,n){var r=this;return e=e instanceof Array?e:[e],r._bindMultiple.call(r,e,t,n),r},g.prototype.unbind=function(e,t){return this.bind.call(this,e,(function(){}),t)},g.prototype.trigger=function(e,t){var n=this;return n._directMap[e+":"+t]&&n._directMap[e+":"+t]({},e),n},g.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},g.prototype.stopCallback=function(e,t){if((" "+t.className+" ").indexOf(" mousetrap ")>-1)return!1;if(b(t,this.target))return!1;if("composedPath"in e&&"function"==typeof e.composedPath){var n=e.composedPath()[0];n!==e.target&&(t=n)}return"INPUT"==t.tagName||"SELECT"==t.tagName||"TEXTAREA"==t.tagName||t.isContentEditable},g.prototype.handleKey=function(){var e=this;return e._handleKey.apply(e,arguments)},g.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(s[t]=e[t]);c=null},g.init=function(){var e=g(u);for(var t in e)"_"!==t.charAt(0)&&(g[t]=function(t){return function(){return e[t].apply(e,arguments)}}(t))},g.init(),o.Mousetrap=g,e.exports&&(e.exports=g),void 0===(r=function(){return g}.call(t,n,t,e))||(e.exports=r)}function p(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n)}function v(e){if("keypress"==e.type){var t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return s[e.which]?s[e.which]:a[e.which]?a[e.which]:String.fromCharCode(e.which).toLowerCase()}function h(e){return"shift"==e||"ctrl"==e||"alt"==e||"meta"==e}function m(e,t,n){return n||(n=function(){if(!c)for(var e in c={},s)e>95&&e<112||s.hasOwnProperty(e)&&(c[s[e]]=e);return c}()[e]?"keydown":"keypress"),"keypress"==n&&t.length&&(n="keydown"),n}function y(e,t){var n,r,o,u=[];for(n=function(e){return"+"===e?["+"]:(e=e.replace(/\+{2}/g,"+plus")).split("+")}(e),o=0;o<n.length;++o)r=n[o],f[r]&&(r=f[r]),t&&"keypress"!=t&&l[r]&&(r=l[r],u.push("shift")),h(r)&&u.push(r);return{key:r,modifiers:u,action:t=m(r,u,t)}}function b(e,t){return null!==e&&e!==u&&(e===t||b(e.parentNode,t))}function g(e){var t=this;if(e=e||u,!(t instanceof g))return new g(e);t.target=e,t._callbacks={},t._directMap={};var n,r={},o=!1,i=!1,c=!1;function s(e){e=e||{};var t,n=!1;for(t in r)e[t]?n=!0:r[t]=0;n||(c=!1)}function a(e,n,o,u,i,c){var s,a,l,f,d=[],p=o.type;if(!t._callbacks[e])return[];for("keyup"==p&&h(e)&&(n=[e]),s=0;s<t._callbacks[e].length;++s)if(a=t._callbacks[e][s],(u||!a.seq||r[a.seq]==a.level)&&p==a.action&&("keypress"==p&&!o.metaKey&&!o.ctrlKey||(l=n,f=a.modifiers,l.sort().join(",")===f.sort().join(",")))){var v=!u&&a.combo==i,m=u&&a.seq==u&&a.level==c;(v||m)&&t._callbacks[e].splice(s,1),d.push(a)}return d}function l(e,n,r,o){t.stopCallback(n,n.target||n.srcElement,r,o)||!1===e(n,r)&&(function(e){e.preventDefault?e.preventDefault():e.returnValue=!1}(n),function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}(n))}function f(e){"number"!=typeof e.which&&(e.which=e.keyCode);var n=v(e);n&&("keyup"!=e.type||o!==n?t.handleKey(n,function(e){var t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}(e),e):o=!1)}function d(e,t,u,i){function a(t){return function(){c=t,++r[e],clearTimeout(n),n=setTimeout(s,1e3)}}function f(t){l(u,t,e),"keyup"!==i&&(o=v(t)),setTimeout(s,10)}r[e]=0;for(var d=0;d<t.length;++d){var p=d+1===t.length?f:a(i||y(t[d+1]).action);m(t[d],p,i,e,d)}}function m(e,n,r,o,u){t._directMap[e+":"+r]=n;var i,c=(e=e.replace(/\s+/g," ")).split(" ");c.length>1?d(e,c,n,r):(i=y(e,r),t._callbacks[i.key]=t._callbacks[i.key]||[],a(i.key,i.modifiers,{type:i.action},o,e,u),t._callbacks[i.key][o?"unshift":"push"]({callback:n,modifiers:i.modifiers,action:i.action,seq:o,level:u,combo:e}))}t._handleKey=function(e,t,n){var r,o=a(e,t,n),u={},f=0,d=!1;for(r=0;r<o.length;++r)o[r].seq&&(f=Math.max(f,o[r].level));for(r=0;r<o.length;++r)if(o[r].seq){if(o[r].level!=f)continue;d=!0,u[o[r].seq]=1,l(o[r].callback,n,o[r].combo,o[r].seq)}else d||l(o[r].callback,n,o[r].combo);var p="keypress"==n.type&&i;n.type!=c||h(e)||p||s(u),i=d&&"keydown"==n.type},t._bindMultiple=function(e,t,n){for(var r=0;r<e.length;++r)m(e[r],t,n)},p(e,"keypress",f),p(e,"keydown",f),p(e,"keyup",f)}}("undefined"!=typeof window?window:null,"undefined"!=typeof window?document:null)},5538:function(){!function(e){if(e){var t={},n=e.prototype.stopCallback;e.prototype.stopCallback=function(e,r,o,u){return!!this.paused||!t[o]&&!t[u]&&n.call(this,e,r,o)},e.prototype.bindGlobal=function(e,n,r){if(this.bind(e,n,r),e instanceof Array)for(var o=0;o<e.length;o++)t[e[o]]=!0;else t[e]=!0},e.init()}}("undefined"!=typeof Mousetrap?Mousetrap:void 0)},235:function(e,t,n){var r=n(9196),o={display:"block",opacity:0,position:"absolute",top:0,left:0,height:"100%",width:"100%",overflow:"hidden",pointerEvents:"none",zIndex:-1},u=function(e){var t=e.onResize,n=r.useRef();return function(e,t){var n=function(){return e.current&&e.current.contentDocument&&e.current.contentDocument.defaultView};function o(){t();var e=n();e&&e.addEventListener("resize",t)}r.useEffect((function(){return n()?o():e.current&&e.current.addEventListener&&e.current.addEventListener("load",o),function(){var e=n();e&&"function"==typeof e.removeEventListener&&e.removeEventListener("resize",t)}}),[])}(n,(function(){return t(n)})),r.createElement("iframe",{style:o,src:"about:blank",ref:n,"aria-hidden":!0,tabIndex:-1,frameBorder:0})},i=function(e){return{width:null!=e?e.offsetWidth:null,height:null!=e?e.offsetHeight:null}};e.exports=function(e){void 0===e&&(e=i);var t=r.useState(e(null)),n=t[0],o=t[1],c=r.useCallback((function(t){return o(e(t.current))}),[e]);return[r.useMemo((function(){return r.createElement(u,{onResize:c})}),[c]),n]}},9196:function(e){"use strict";e.exports=window.React}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var u=t[r]={exports:{}};return e[r].call(u.exports,u,u.exports,n),u.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){"use strict";n.r(r),n.d(r,{__experimentalUseDialog:function(){return N},__experimentalUseDisabled:function(){return U},__experimentalUseDragging:function(){return z},__experimentalUseDropZone:function(){return fe},__experimentalUseFixedWindowList:function(){return pe},__experimentalUseFocusOutside:function(){return P},compose:function(){return o},createHigherOrderComponent:function(){return t},ifCondition:function(){return i},pure:function(){return a},useAsyncList:function(){return oe},useConstrainedTabbing:function(){return L},useCopyOnClick:function(){return x},useCopyToClipboard:function(){return O},useDebounce:function(){return se},useFocusOnMount:function(){return R},useFocusReturn:function(){return M},useFocusableIframe:function(){return de},useInstanceId:function(){return y},useIsomorphicLayoutEffect:function(){return q},useKeyboardShortcut:function(){return H},useMediaQuery:function(){return W},useMergeRefs:function(){return I},usePrevious:function(){return $},useReducedMotion:function(){return G},useRefEffect:function(){return k},useResizeObserver:function(){return te},useThrottle:function(){return ae},useViewportMatch:function(){return J},useWarnOnChange:function(){return ue},withGlobalEvents:function(){return v},withInstanceId:function(){return b},withSafeTimeout:function(){return g},withState:function(){return w}});var e=window.lodash;var t=function(t,n){return r=>{const o=t(r),u=r.displayName||r.name||"Component";return o.displayName=`${(0,e.upperFirst)((0,e.camelCase)(n))}(${u})`,o}},o=e.flowRight,u=window.wp.element;var i=e=>t((t=>n=>e(n)?(0,u.createElement)(t,n):null),"ifCondition"),c=window.wp.isShallowEqual,s=n.n(c);var a=t((e=>e.prototype instanceof u.Component?class extends e{shouldComponentUpdate(e,t){return!s()(e,this.props)||!s()(t,this.state)}}:class extends u.Component{shouldComponentUpdate(e){return!s()(e,this.props)}render(){return(0,u.createElement)(e,this.props)}}),"pure");function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}var f=window.wp.deprecated,d=n.n(f);const p=new class{constructor(){this.listeners={},this.handleEvent=this.handleEvent.bind(this)}add(e,t){this.listeners[e]||(window.addEventListener(e,this.handleEvent),this.listeners[e]=[]),this.listeners[e].push(t)}remove(t,n){this.listeners[t]=(0,e.without)(this.listeners[t],n),this.listeners[t].length||(window.removeEventListener(t,this.handleEvent),delete this.listeners[t])}handleEvent(t){(0,e.forEach)(this.listeners[t.type],(e=>{e.handleEvent(t)}))}};function v(n){return d()("wp.compose.withGlobalEvents",{since:"5.7",alternative:"useEffect"}),t((t=>{class r extends u.Component{constructor(e){super(e),this.handleEvent=this.handleEvent.bind(this),this.handleRef=this.handleRef.bind(this)}componentDidMount(){(0,e.forEach)(n,((e,t)=>{p.add(t,this)}))}componentWillUnmount(){(0,e.forEach)(n,((e,t)=>{p.remove(t,this)}))}handleEvent(e){const t=n[e.type];"function"==typeof this.wrappedRef[t]&&this.wrappedRef[t](e)}handleRef(e){this.wrappedRef=e,this.props.forwardedRef&&this.props.forwardedRef(e)}render(){return(0,u.createElement)(t,l({},this.props.ownProps,{ref:this.handleRef}))}}return(0,u.forwardRef)(((e,t)=>(0,u.createElement)(r,{ownProps:e,forwardedRef:t})))}),"withGlobalEvents")}const h=new WeakMap;function m(e){const t=h.get(e)||0;return h.set(e,t+1),t}function y(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(0,u.useMemo)((()=>{if(n)return n;const r=m(e);return t?`${t}-${r}`:r}),[e])}var b=t((e=>t=>{const n=y(e);return(0,u.createElement)(e,l({},t,{instanceId:n}))}),"withInstanceId");var g=t((t=>class extends u.Component{constructor(e){super(e),this.timeouts=[],this.setTimeout=this.setTimeout.bind(this),this.clearTimeout=this.clearTimeout.bind(this)}componentWillUnmount(){this.timeouts.forEach(clearTimeout)}setTimeout(e,t){const n=setTimeout((()=>{e(),this.clearTimeout(n)}),t);return this.timeouts.push(n),n}clearTimeout(t){clearTimeout(t),this.timeouts=(0,e.without)(this.timeouts,t)}render(){const e={...this.props,setTimeout:this.setTimeout,clearTimeout:this.clearTimeout};return(0,u.createElement)(t,e)}}),"withSafeTimeout");function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return d()("wp.compose.withState",{since:"5.8",alternative:"wp.element.useState"}),t((t=>class extends u.Component{constructor(t){super(t),this.setState=this.setState.bind(this),this.state=e}render(){return(0,u.createElement)(t,l({},this.props,this.state,{setState:this.setState}))}}),"withState")}var E=window.wp.keycodes,T=window.wp.dom;function k(e,t){const n=(0,u.useRef)();return(0,u.useCallback)((t=>{t?n.current=e(t):n.current&&n.current()}),t)}var L=function(){return k((e=>{let t;function n(n){const{keyCode:r,shiftKey:o,target:u}=n;if(r!==E.TAB)return;const i=o?"findPrevious":"findNext",c=T.focus.tabbable[i](u)||null;if(e.contains(c))return;const s=o?"append":"prepend",{ownerDocument:a}=e,l=a.createElement("div");l.tabIndex=-1,e[s](l),l.focus(),t=setTimeout((()=>e.removeChild(l)))}return e.addEventListener("keydown",n),()=>{e.removeEventListener("keydown",n),clearTimeout(t)}}),[])},C=n(8294),S=n.n(C);function x(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:4e3;d()("wp.compose.useCopyOnClick",{since:"5.8",alternative:"wp.compose.useCopyToClipboard"});const r=(0,u.useRef)(),[o,i]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{let o;if(e.current)return r.current=new(S())(e.current,{text:()=>"function"==typeof t?t():t}),r.current.on("success",(e=>{let{clearSelection:t,trigger:r}=e;t(),r&&r.focus(),n&&(i(!0),clearTimeout(o),o=setTimeout((()=>i(!1)),n))})),()=>{r.current&&r.current.destroy(),clearTimeout(o)}}),[t,n,i]),o}function D(e){const t=(0,u.useRef)(e);return t.current=e,t}function O(e,t){const n=D(e),r=D(t);return k((e=>{const t=new(S())(e,{text:()=>"function"==typeof n.current?n.current():n.current||""});return t.on("success",(t=>{let{clearSelection:n}=t;n(),e.focus(),r.current&&r.current()})),()=>{t.destroy()}}),[])}function R(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"firstElement";const t=(0,u.useRef)(e);return(0,u.useEffect)((()=>{t.current=e}),[e]),(0,u.useCallback)((e=>{var n,r;if(!e||!1===t.current)return;if(e.contains(null!==(n=null===(r=e.ownerDocument)||void 0===r?void 0:r.activeElement)&&void 0!==n?n:null))return;let o=e;if("firstElement"===t.current){const t=T.focus.tabbable.find(e)[0];t&&(o=t)}o.focus()}),[])}var M=function(e){const t=(0,u.useRef)(null),n=(0,u.useRef)(null),r=(0,u.useRef)(e);return(0,u.useEffect)((()=>{r.current=e}),[e]),(0,u.useCallback)((e=>{if(e){if(t.current=e,n.current)return;n.current=e.ownerDocument.activeElement}else if(n.current){var o,u,i;const e=null===(o=t.current)||void 0===o?void 0:o.contains(null===(u=t.current)||void 0===u?void 0:u.ownerDocument.activeElement);if(null!==(i=t.current)&&void 0!==i&&i.isConnected&&!e)return;var c;if(r.current)r.current();else null===(c=n.current)||void 0===c||c.focus()}}),[])};const _=["button","submit"];function P(t){const n=(0,u.useRef)(t);(0,u.useEffect)((()=>{n.current=t}),[t]);const r=(0,u.useRef)(!1),o=(0,u.useRef)(),i=(0,u.useCallback)((()=>{clearTimeout(o.current)}),[]);(0,u.useEffect)((()=>()=>i()),[]),(0,u.useEffect)((()=>{t||i()}),[t,i]);const c=(0,u.useCallback)((t=>{const{type:n,target:o}=t;(0,e.includes)(["mouseup","touchend"],n)?r.current=!1:function(t){if(!(t instanceof window.HTMLElement))return!1;switch(t.nodeName){case"A":case"BUTTON":return!0;case"INPUT":return(0,e.includes)(_,t.type)}return!1}(o)&&(r.current=!0)}),[]),s=(0,u.useCallback)((e=>{e.persist(),r.current||(o.current=setTimeout((()=>{document.hasFocus()?"function"==typeof n.current&&n.current(e):e.preventDefault()}),0))}),[]);return{onFocus:i,onMouseDown:c,onMouseUp:c,onTouchStart:c,onTouchEnd:c,onBlur:s}}function A(e,t){"function"==typeof e?e(t):e&&e.hasOwnProperty("current")&&(e.current=t)}function I(e){const t=(0,u.useRef)(),n=(0,u.useRef)(!1),r=(0,u.useRef)([]),o=(0,u.useRef)(e);return o.current=e,(0,u.useLayoutEffect)((()=>{!1===n.current&&e.forEach(((e,n)=>{const o=r.current[n];e!==o&&(A(o,null),A(e,t.current))})),r.current=e}),e),(0,u.useLayoutEffect)((()=>{n.current=!1})),(0,u.useCallback)((e=>{A(t,e),n.current=!0;const u=e?o.current:r.current;for(const t of u)A(t,e)}),[])}var N=function(e){const t=(0,u.useRef)();(0,u.useEffect)((()=>{t.current=e}),Object.values(e));const n=L(),r=R(e.focusOnMount),o=M(),i=P((e=>{var n,r;null!==(n=t.current)&&void 0!==n&&n.__unstableOnClose?t.current.__unstableOnClose("focus-outside",e):null!==(r=t.current)&&void 0!==r&&r.onClose&&t.current.onClose()})),c=(0,u.useCallback)((e=>{e&&e.addEventListener("keydown",(e=>{var n;e.keyCode===E.ESCAPE&&!e.defaultPrevented&&null!==(n=t.current)&&void 0!==n&&n.onClose&&(e.preventDefault(),t.current.onClose())}))}),[]);return[I([!1!==e.focusOnMount?n:null,!1!==e.focusOnMount?o:null,!1!==e.focusOnMount?r:null,c]),{...i,tabIndex:"-1"}]};const j=["BUTTON","FIELDSET","INPUT","OPTGROUP","OPTION","SELECT","TEXTAREA"];function U(){const t=(0,u.useRef)(null),n=()=>{t.current&&T.focus.focusable.find(t.current).forEach((t=>{(0,e.includes)(j,t.nodeName)&&t.setAttribute("disabled",""),"A"===t.nodeName&&t.setAttribute("tabindex","-1");const n=t.getAttribute("tabindex");null!==n&&"-1"!==n&&t.removeAttribute("tabindex"),t.hasAttribute("contenteditable")&&t.setAttribute("contenteditable","false")}))},r=(0,u.useCallback)((0,e.debounce)(n,void 0,{leading:!0}),[]);return(0,u.useLayoutEffect)((()=>{let e;return n(),t.current&&(e=new window.MutationObserver(r),e.observe(t.current,{childList:!0,attributes:!0,subtree:!0})),()=>{e&&e.disconnect(),r.cancel()}}),[]),t}var q="undefined"!=typeof window?u.useLayoutEffect:u.useEffect;function z(e){let{onDragStart:t,onDragMove:n,onDragEnd:r}=e;const[o,i]=(0,u.useState)(!1),c=(0,u.useRef)({onDragStart:t,onDragMove:n,onDragEnd:r});q((()=>{c.current.onDragStart=t,c.current.onDragMove=n,c.current.onDragEnd=r}),[t,n,r]);const s=(0,u.useCallback)((e=>c.current.onDragMove&&c.current.onDragMove(e)),[]),a=(0,u.useCallback)((e=>{c.current.onDragEnd&&c.current.onDragEnd(e),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",a),i(!1)}),[]),l=(0,u.useCallback)((e=>{c.current.onDragStart&&c.current.onDragStart(e),document.addEventListener("mousemove",s),document.addEventListener("mouseup",a),i(!0)}),[]);return(0,u.useEffect)((()=>()=>{o&&(document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",a))}),[o]),{startDrag:l,endDrag:a,isDragging:o}}var K=n(7973),F=n.n(K);n(5538);function V(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;const{platform:n}=t.navigator;return-1!==n.indexOf("Mac")||(0,e.includes)(["iPad","iPhone"],n)}var H=function(t,n){let{bindGlobal:r=!1,eventName:o="keydown",isDisabled:i=!1,target:c}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=(0,u.useRef)(n);(0,u.useEffect)((()=>{s.current=n}),[n]),(0,u.useEffect)((()=>{if(i)return;const n=new(F())(c&&c.current?c.current:document);return(0,e.castArray)(t).forEach((e=>{const t=e.split("+"),u=new Set(t.filter((e=>e.length>1))),i=u.has("alt"),c=u.has("shift");if(V()&&(1===u.size&&i||2===u.size&&i&&c))throw new Error(`Cannot bind ${e}. Alt and Shift+Alt modifiers are reserved for character input.`);n[r?"bindGlobal":"bind"](e,(function(){return s.current(...arguments)}),o)})),()=>{n.reset()}}),[t,r,o,c,i])};function W(e){const[t,n]=(0,u.useState)((()=>!(!e||"undefined"==typeof window||!window.matchMedia(e).matches)));return(0,u.useEffect)((()=>{if(!e)return;const t=()=>n(window.matchMedia(e).matches);t();const r=window.matchMedia(e);return r.addListener(t),()=>{r.removeListener(t)}}),[e]),!!e&&t}function $(e){const t=(0,u.useRef)();return(0,u.useEffect)((()=>{t.current=e}),[e]),t.current}var G=()=>W("(prefers-reduced-motion: reduce)");const B={huge:1440,wide:1280,large:960,medium:782,small:600,mobile:480},Z={">=":"min-width","<":"max-width"},Q={">=":(e,t)=>t>=e,"<":(e,t)=>t<e},X=(0,u.createContext)(null),Y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:">=";const n=(0,u.useContext)(X),r=!n&&`(${Z[t]}: ${B[e]}px)`,o=W(r||void 0);return n?Q[t](B[e],n):o};Y.__experimentalWidthProvider=X.Provider;var J=Y,ee=n(235),te=n.n(ee)(),ne=window.wp.priorityQueue;function re(e,t){const n=[];for(let r=0;r<e.length;r++){const o=e[r];if(!t.includes(o))break;n.push(o)}return n}var oe=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{step:1};const{step:n=1}=t,[r,o]=(0,u.useState)([]);return(0,u.useEffect)((()=>{let t=re(e,r);t.length<n&&(t=t.concat(e.slice(t.length,n))),o(t);let u=t.length;const i=(0,ne.createQueue)(),c=()=>{e.length<=u||(o((t=>[...t,...e.slice(u,u+n)])),u+=n,i.add({},c))};return i.add({},c),()=>i.reset()}),[e]),r};var ue=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Change detection";const n=$(e);Object.entries(null!=n?n:[]).forEach((n=>{let[r,o]=n;o!==e[r]&&console.warn(`${t}: ${r} key changed:`,o,e[r])}))},ie=n(9196);function ce(e,t){var n=(0,ie.useState)((function(){return{inputs:t,result:e()}}))[0],r=(0,ie.useRef)(!0),o=(0,ie.useRef)(n),u=r.current||Boolean(t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,o.current.inputs))?o.current:{inputs:t,result:e()};return(0,ie.useEffect)((function(){r.current=!1,o.current=u}),[u]),u.result}function se(t,n,r){const o=ce((()=>(0,e.debounce)(t,n,r)),[t,n,r]);return(0,u.useEffect)((()=>()=>o.cancel()),[o]),o}function ae(t,n,r){const o=ce((()=>(0,e.throttle)(t,n,r)),[t,n,r]);return(0,u.useEffect)((()=>()=>o.cancel()),[o]),o}function le(e){const t=(0,u.useRef)();return t.current=e,t}function fe(e){let{isDisabled:t,onDrop:n,onDragStart:r,onDragEnter:o,onDragLeave:u,onDragEnd:i,onDragOver:c}=e;const s=le(n),a=le(r),l=le(o),f=le(u),d=le(i),p=le(c);return k((e=>{if(t)return;let n=!1;const{ownerDocument:r}=e;function o(e){n||(n=!0,r.removeEventListener("dragenter",o),r.addEventListener("dragend",h),r.addEventListener("mousemove",h),a.current&&a.current(e))}function u(t){t.preventDefault(),e.contains(t.relatedTarget)||l.current&&l.current(t)}function i(e){!e.defaultPrevented&&p.current&&p.current(e),e.preventDefault()}function c(t){(function(t){const{defaultView:n}=r;if(!(t&&n&&t instanceof n.HTMLElement&&e.contains(t)))return!1;let o=t;do{if(o.dataset.isDropZone)return o===e}while(o=o.parentElement);return!1})(t.relatedTarget)||f.current&&f.current(t)}function v(e){e.defaultPrevented||(e.preventDefault(),e.dataTransfer&&e.dataTransfer.files.length,s.current&&s.current(e),h(e))}function h(e){n&&(n=!1,r.addEventListener("dragenter",o),r.removeEventListener("dragend",h),r.removeEventListener("mousemove",h),d.current&&d.current(e))}return e.dataset.isDropZone="true",e.addEventListener("drop",v),e.addEventListener("dragenter",u),e.addEventListener("dragover",i),e.addEventListener("dragleave",c),r.addEventListener("dragenter",o),()=>{s.current=null,a.current=null,l.current=null,f.current=null,d.current=null,p.current=null,delete e.dataset.isDropZone,e.removeEventListener("drop",v),e.removeEventListener("dragenter",u),e.removeEventListener("dragover",i),e.removeEventListener("dragleave",c),r.removeEventListener("dragend",h),r.removeEventListener("mousemove",h),r.addEventListener("dragenter",o)}}),[t])}function de(){return k((e=>{const{ownerDocument:t}=e;if(!t)return;const{defaultView:n}=t;if(n)return n.addEventListener("blur",r),()=>{n.removeEventListener("blur",r)};function r(){t&&t.activeElement===e&&e.focus()}}),[])}function pe(t,n,r,o){var i,c;const s=null!==(i=null==o?void 0:o.initWindowSize)&&void 0!==i?i:30,a=null===(c=null==o?void 0:o.useWindowing)||void 0===c||c,[l,f]=(0,u.useState)({visibleItems:s,start:0,end:s,itemInView:e=>e>=0&&e<=s});return(0,u.useLayoutEffect)((()=>{var u,i,c,s;if(!a)return;const l=(0,T.getScrollContainer)(t.current),d=e=>{var t;if(!l)return;const u=Math.ceil(l.clientHeight/n),i=e?u:null!==(t=null==o?void 0:o.windowOverscan)&&void 0!==t?t:u,c=Math.floor(l.scrollTop/n),s=Math.max(0,c-i),a=Math.min(r-1,c+u+i);f((e=>{const t={visibleItems:u,start:s,end:a,itemInView:e=>s<=e&&e<=a};return e.start!==t.start||e.end!==t.end||e.visibleItems!==t.visibleItems?t:e}))};d(!0);const p=(0,e.debounce)((()=>{d()}),16);return null==l||l.addEventListener("scroll",p),null==l||null===(u=l.ownerDocument)||void 0===u||null===(i=u.defaultView)||void 0===i||i.addEventListener("resize",p),null==l||null===(c=l.ownerDocument)||void 0===c||null===(s=c.defaultView)||void 0===s||s.addEventListener("resize",p),()=>{var e,t;null==l||l.removeEventListener("scroll",p),null==l||null===(e=l.ownerDocument)||void 0===e||null===(t=e.defaultView)||void 0===t||t.removeEventListener("resize",p)}}),[n,t,r]),(0,u.useLayoutEffect)((()=>{var e,o;if(!a)return;const u=(0,T.getScrollContainer)(t.current),i=e=>{switch(e.keyCode){case E.HOME:return null==u?void 0:u.scrollTo({top:0});case E.END:return null==u?void 0:u.scrollTo({top:r*n});case E.PAGEUP:return null==u?void 0:u.scrollTo({top:u.scrollTop-l.visibleItems*n});case E.PAGEDOWN:return null==u?void 0:u.scrollTo({top:u.scrollTop+l.visibleItems*n})}};return null==u||null===(e=u.ownerDocument)||void 0===e||null===(o=e.defaultView)||void 0===o||o.addEventListener("keydown",i),()=>{var e,t;null==u||null===(e=u.ownerDocument)||void 0===e||null===(t=e.defaultView)||void 0===t||t.removeEventListener("keydown",i)}}),[r,n,t,l.visibleItems]),[l,f]}}(),(window.wp=window.wp||{}).compose=r}();