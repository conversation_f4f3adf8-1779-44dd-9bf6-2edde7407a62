/*! This file is auto-generated */
!function(){var e={4403:function(e,t){var n;
/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var l={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var r=a.apply(null,n);r&&e.push(r)}}else if("object"===o)if(n.toString===Object.prototype.toString)for(var s in n)l.call(n,s)&&n[s]&&e.push(s);else e.push(n.toString())}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()}},t={};function n(l){var a=t[l];if(void 0!==a)return a.exports;var o=t[l]={exports:{}};return e[l](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};!function(){"use strict";n.r(l),n.d(l,{PluginBlockSettingsMenuItem:function(){return ma},PluginDocumentSettingPanel:function(){return Nl},PluginMoreMenuItem:function(){return pa},PluginPostPublishPanel:function(){return Kl},PluginPostStatusInfo:function(){return sl},PluginPrePublishPanel:function(){return Zl},PluginSidebar:function(){return Al},PluginSidebarMoreMenuItem:function(){return ga},__experimentalFullscreenModeClose:function(){return wn},__experimentalMainDashboardButton:function(){return Fn},initializeEditor:function(){return ha},reinitializeEditor:function(){return Ea},store:function(){return Dt}});var e={};n.r(e),n.d(e,{disableComplementaryArea:function(){return H},enableComplementaryArea:function(){return z},pinItem:function(){return $},setFeatureDefaults:function(){return K},setFeatureValue:function(){return j},toggleFeature:function(){return q},unpinItem:function(){return W}});var t={};n.r(t),n.d(t,{getActiveComplementaryArea:function(){return X},isFeatureActive:function(){return Y},isItemPinned:function(){return Q}});var a={};n.r(a),n.d(a,{__experimentalSetPreviewDeviceType:function(){return We},__unstableCreateTemplate:function(){return Qe},__unstableSwitchToTemplateMode:function(){return Xe},closeGeneralSidebar:function(){return Te},closeModal:function(){return Ce},closePublishSidebar:function(){return Ie},hideBlockTypes:function(){return Ue},initializeMetaBoxes:function(){return Ze},metaBoxUpdatesFailure:function(){return $e},metaBoxUpdatesSuccess:function(){return He},openGeneralSidebar:function(){return Pe},openModal:function(){return xe},openPublishSidebar:function(){return Be},removeEditorPanel:function(){return Le},requestMetaBoxUpdates:function(){return ze},setAvailableMetaBoxesPerLocation:function(){return Ge},setIsEditingTemplate:function(){return Ke},setIsInserterOpened:function(){return qe},setIsListViewOpened:function(){return je},showBlockTypes:function(){return Ve},switchEditorMode:function(){return De},toggleEditorPanelEnabled:function(){return Ne},toggleEditorPanelOpened:function(){return Ae},toggleFeature:function(){return Oe},togglePinnedPluginItem:function(){return Re},togglePublishSidebar:function(){return Me},updatePreferredStyleVariations:function(){return Fe}});var o={};n.r(o),n.d(o,{__experimentalGetInsertionPoint:function(){return It},__experimentalGetPreviewDeviceType:function(){return Ct},areMetaBoxesInitialized:function(){return At},getActiveGeneralSidebarName:function(){return dt},getActiveMetaBoxLocations:function(){return yt},getAllMetaBoxes:function(){return Pt},getEditedPostTemplate:function(){return Lt},getEditorMode:function(){return st},getHiddenBlockTypes:function(){return pt},getMetaBoxesPerLocation:function(){return kt},getPreference:function(){return mt},getPreferences:function(){return ut},hasMetaBoxes:function(){return Tt},isEditingTemplate:function(){return Nt},isEditorPanelEnabled:function(){return ht},isEditorPanelOpened:function(){return _t},isEditorPanelRemoved:function(){return Et},isEditorSidebarOpened:function(){return it},isFeatureActive:function(){return ft},isInserterOpened:function(){return Bt},isListViewOpened:function(){return Mt},isMetaBoxLocationActive:function(){return St},isMetaBoxLocationVisible:function(){return wt},isModalActive:function(){return bt},isPluginItemPinned:function(){return vt},isPluginSidebarOpened:function(){return ct},isPublishSidebarOpened:function(){return gt},isSavingMetaBoxes:function(){return xt}});var r=window.wp.element,s=window.wp.blocks,i=window.wp.blockLibrary,c=window.wp.data,d=window.wp.hooks,u=window.wp.preferences,m=window.wp.mediaUtils;function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},p.apply(this,arguments)}(0,d.addFilter)("editor.MediaUpload","core/edit-post/replace-media-upload",(()=>m.MediaUpload));var g=window.lodash,E=window.wp.components,h=window.wp.blockEditor,_=window.wp.i18n,b=window.wp.compose;const f=(0,b.compose)((0,c.withSelect)(((e,t)=>{if((0,s.hasBlockSupport)(t.name,"multiple",!0))return{};const n=e(h.store).getBlocks(),l=(0,g.find)(n,(e=>{let{name:n}=e;return t.name===n}));return{originalBlockClientId:l&&l.clientId!==t.clientId&&l.clientId}})),(0,c.withDispatch)(((e,t)=>{let{originalBlockClientId:n}=t;return{selectFirst:()=>e(h.store).selectBlock(n)}}))),v=(0,b.createHigherOrderComponent)((e=>f((t=>{let{originalBlockClientId:n,selectFirst:l,...a}=t;if(!n)return(0,r.createElement)(e,a);const o=(0,s.getBlockType)(a.name),i=function(e){const t=(0,s.findTransform)((0,s.getBlockTransforms)("to",e),(e=>{let{type:t,blocks:n}=e;return"block"===t&&1===n.length}));if(!t)return null;return(0,s.getBlockType)(t.blocks[0])}(a.name);return[(0,r.createElement)("div",{key:"invalid-preview",style:{minHeight:"60px"}},(0,r.createElement)(e,p({key:"block-edit"},a))),(0,r.createElement)(h.Warning,{key:"multiple-use-warning",actions:[(0,r.createElement)(E.Button,{key:"find-original",variant:"secondary",onClick:l},(0,_.__)("Find original")),(0,r.createElement)(E.Button,{key:"remove",variant:"secondary",onClick:()=>a.onReplace([])},(0,_.__)("Remove")),i&&(0,r.createElement)(E.Button,{key:"transform",variant:"secondary",onClick:()=>a.onReplace((0,s.createBlock)(i.name,a.attributes))},(0,_.__)("Transform into:")," ",i.title)]},(0,r.createElement)("strong",null,null==o?void 0:o.title,": "),(0,_.__)("This block can only be used once."))]}))),"withMultipleValidation");(0,d.addFilter)("editor.BlockEdit","core/edit-post/validate-multiple-use/with-multiple-validation",v);var y=window.wp.primitives;var w=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M18.2 17c0 .7-.6 1.2-1.2 1.2H7c-.7 0-1.2-.6-1.2-1.2V7c0-.7.6-1.2 1.2-1.2h3.2V4.2H7C5.5 4.2 4.2 5.5 4.2 7v10c0 1.5 1.2 2.8 2.8 2.8h10c1.5 0 2.8-1.2 2.8-2.8v-3.6h-1.5V17zM14.9 3v1.5h3.7l-6.4 6.4 1.1 1.1 6.4-6.4v3.7h1.5V3h-6.3z"})),S=window.wp.plugins,k=window.wp.url,P=window.wp.notices,T=window.wp.editor;function x(){const{createNotice:e}=(0,c.useDispatch)(P.store),t=(0,c.useSelect)((e=>()=>e(T.store).getEditedPostAttribute("content")),[]);const n=(0,b.useCopyToClipboard)(t,(function(){e("info",(0,_.__)("All content copied."),{isDismissible:!0,type:"snackbar"})}));return(0,r.createElement)(E.MenuItem,{ref:n},(0,_.__)("Copy all content"))}var C=window.wp.keycodes;const B=(0,c.combineReducers)({isSaving:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REQUEST_META_BOX_UPDATES":return!0;case"META_BOX_UPDATES_SUCCESS":case"META_BOX_UPDATES_FAILURE":return!1;default:return e}},locations:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return"SET_META_BOXES_PER_LOCATIONS"===t.type?t.metaBoxesPerLocation:e},initialized:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;return"META_BOXES_INITIALIZED"===t.type||e}});var I=(0,c.combineReducers)({activeModal:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e},metaBoxes:B,publishSidebarActive:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_PUBLISH_SIDEBAR":return!0;case"CLOSE_PUBLISH_SIDEBAR":return!1;case"TOGGLE_PUBLISH_SIDEBAR":return!e}return e},removedPanels:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;if("REMOVE_PANEL"===t.type)if(!(0,g.includes)(e,t.panelName))return[...e,t.panelName];return e},deviceType:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Desktop",t=arguments.length>1?arguments[1]:void 0;return"SET_PREVIEW_DEVICE_TYPE"===t.type?t.deviceType:e},blockInserterPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},listViewPanel:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},isEditingTemplate:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;return"SET_IS_EDITING_TEMPLATE"===t.type?t.value:e}}),M=window.wp.apiFetch,N=n.n(M),A=n(4403),L=n.n(A);var O=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"}));var D=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"}));var R=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})),F=window.wp.viewport;var V=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),U=window.wp.deprecated,G=n.n(U);const z=(e,t)=>n=>{let{registry:l}=n;t&&l.dispatch(u.store).set(e,"complementaryArea",t)},H=e=>t=>{let{registry:n}=t;n.dispatch(u.store).set(e,"complementaryArea",null)},$=(e,t)=>n=>{let{registry:l}=n;if(!t)return;const a=l.select(u.store).get(e,"pinnedItems");!0!==(null==a?void 0:a[t])&&l.dispatch(u.store).set(e,"pinnedItems",{...a,[t]:!0})},W=(e,t)=>n=>{let{registry:l}=n;if(!t)return;const a=l.select(u.store).get(e,"pinnedItems");l.dispatch(u.store).set(e,"pinnedItems",{...a,[t]:!1})};function q(e,t){return function(n){let{registry:l}=n;G()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),l.dispatch(u.store).toggle(e,t)}}function j(e,t,n){return function(l){let{registry:a}=l;G()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),a.dispatch(u.store).set(e,t,!!n)}}function K(e,t){return function(n){let{registry:l}=n;G()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),l.dispatch(u.store).setDefaults(e,t)}}const X=(0,c.createRegistrySelector)((e=>(t,n)=>e(u.store).get(n,"complementaryArea"))),Q=(0,c.createRegistrySelector)((e=>(t,n,l)=>{var a;const o=e(u.store).get(n,"pinnedItems");return null===(a=null==o?void 0:o[l])||void 0===a||a})),Y=(0,c.createRegistrySelector)((e=>(t,n,l)=>(G()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(u.store).get(n,l)))),Z=(0,c.createReduxStore)("core/interface",{reducer:()=>{},actions:e,selectors:t});(0,c.register)(Z);var J=(0,S.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`})));var ee=J((function(e){let{as:t=E.Button,scope:n,identifier:l,icon:a,selectedIcon:o,...s}=e;const i=t,d=(0,c.useSelect)((e=>e(Z).getActiveComplementaryArea(n)===l),[l]),{enableComplementaryArea:u,disableComplementaryArea:m}=(0,c.useDispatch)(Z);return(0,r.createElement)(i,p({icon:o&&d?o:a,onClick:()=>{d?m(n):u(n,l)}},(0,g.omit)(s,["name"])))}));var te=e=>{let{smallScreenTitle:t,children:n,className:l,toggleButtonProps:a}=e;const o=(0,r.createElement)(ee,p({icon:V},a));return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"components-panel__header interface-complementary-area-header__small"},t&&(0,r.createElement)("span",{className:"interface-complementary-area-header__small-title"},t),o),(0,r.createElement)("div",{className:L()("components-panel__header","interface-complementary-area-header",l),tabIndex:-1},n,o))};function ne(e){let{name:t,as:n=E.Button,onClick:l,...a}=e;return(0,r.createElement)(E.Fill,{name:t},(e=>{let{onClick:t}=e;return(0,r.createElement)(n,p({onClick:l||t?function(){(l||g.noop)(...arguments),(t||g.noop)(...arguments)}:void 0},a))}))}ne.Slot=function(e){let{name:t,as:n=E.ButtonGroup,fillProps:l={},bubblesVirtually:a,...o}=e;return(0,r.createElement)(E.Slot,{name:t,bubblesVirtually:a,fillProps:l},(e=>{if((0,g.isEmpty)(r.Children.toArray(e)))return null;const t=[];r.Children.forEach(e,(e=>{let{props:{__unstableExplicitMenuItem:n,__unstableTarget:l}}=e;l&&n&&t.push(l)}));const l=r.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&t.includes(e.props.__unstableTarget)?null:e));return(0,r.createElement)(n,o,l)}))};var le=ne;const ae=e=>(0,r.createElement)(E.MenuItem,(0,g.omit)(e,["__unstableExplicitMenuItem","__unstableTarget"]));function oe(e){let{scope:t,target:n,__unstableExplicitMenuItem:l,...a}=e;return(0,r.createElement)(ee,p({as:e=>(0,r.createElement)(le,p({__unstableExplicitMenuItem:l,__unstableTarget:`${t}/${n}`,as:ae,name:`${t}/plugin-more-menu`},e)),role:"menuitemcheckbox",selectedIcon:O,name:n,scope:t},a))}function re(e){let{scope:t,...n}=e;return(0,r.createElement)(E.Fill,p({name:`PinnedItems/${t}`},n))}re.Slot=function(e){let{scope:t,className:n,...l}=e;return(0,r.createElement)(E.Slot,p({name:`PinnedItems/${t}`},l),(e=>!(0,g.isEmpty)(e)&&(0,r.createElement)("div",{className:L()(n,"interface-pinned-items")},e)))};var se=re;function ie(e){let{scope:t,children:n,className:l}=e;return(0,r.createElement)(E.Fill,{name:`ComplementaryArea/${t}`},(0,r.createElement)("div",{className:l},n))}const ce=J((function(e){let{children:t,className:n,closeLabel:l=(0,_.__)("Close plugin"),identifier:a,header:o,headerClassName:s,icon:i,isPinnable:d=!0,panelClassName:u,scope:m,name:p,smallScreenTitle:g,title:h,toggleShortcut:b,isActiveByDefault:f,showIconLabels:v=!1}=e;const{isActive:y,isPinned:w,activeArea:S,isSmall:k,isLarge:P}=(0,c.useSelect)((e=>{const{getActiveComplementaryArea:t,isItemPinned:n}=e(Z),l=t(m);return{isActive:l===a,isPinned:n(m,a),activeArea:l,isSmall:e(F.store).isViewportMatch("< medium"),isLarge:e(F.store).isViewportMatch("large")}}),[a,m]);!function(e,t,n,l,a){const o=(0,r.useRef)(!1),s=(0,r.useRef)(!1),{enableComplementaryArea:i,disableComplementaryArea:d}=(0,c.useDispatch)(Z);(0,r.useEffect)((()=>{l&&a&&!o.current?(d(e),s.current=!0):s.current&&!a&&o.current?(s.current=!1,i(e,t)):s.current&&n&&n!==t&&(s.current=!1),a!==o.current&&(o.current=a)}),[l,a,e,t,n])}(m,a,S,y,k);const{enableComplementaryArea:T,disableComplementaryArea:x,pinItem:C,unpinItem:B}=(0,c.useDispatch)(Z);return(0,r.useEffect)((()=>{f&&void 0===S&&!k&&T(m,a)}),[S,f,m,a,k]),(0,r.createElement)(r.Fragment,null,d&&(0,r.createElement)(se,{scope:m},w&&(0,r.createElement)(ee,{scope:m,identifier:a,isPressed:y&&(!v||P),"aria-expanded":y,label:h,icon:v?O:i,showTooltip:!v,variant:v?"tertiary":void 0})),p&&d&&(0,r.createElement)(oe,{target:p,scope:m,icon:i},h),y&&(0,r.createElement)(ie,{className:L()("interface-complementary-area",n),scope:m},(0,r.createElement)(te,{className:s,closeLabel:l,onClose:()=>x(m),smallScreenTitle:g,toggleButtonProps:{label:l,shortcut:b,scope:m,identifier:a}},o||(0,r.createElement)(r.Fragment,null,(0,r.createElement)("strong",null,h),d&&(0,r.createElement)(E.Button,{className:"interface-complementary-area__pin-unpin-item",icon:w?D:R,label:w?(0,_.__)("Unpin from toolbar"):(0,_.__)("Pin to toolbar"),onClick:()=>(w?B:C)(m,a),isPressed:w,"aria-expanded":w}))),(0,r.createElement)(E.Panel,{className:u},t)))}));ce.Slot=function(e){let{scope:t,...n}=e;return(0,r.createElement)(E.Slot,p({name:`ComplementaryArea/${t}`},n))};var de=ce;var ue=e=>{let{isActive:t}=e;return(0,r.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,r.useEffect)((()=>(t?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{t&&document.body.classList.remove("is-fullscreen-mode")})),[t]),null};var me=(0,r.forwardRef)((function(e,t){let{footer:n,header:l,sidebar:a,secondarySidebar:o,notices:s,content:i,drawer:c,actions:d,labels:u,className:m,shortcuts:g}=e;const h=(0,E.__unstableUseNavigateRegions)(g);!function(e){(0,r.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const f={...{drawer:(0,_.__)("Drawer"),header:(0,_.__)("Header"),body:(0,_.__)("Content"),secondarySidebar:(0,_.__)("Block Library"),sidebar:(0,_.__)("Settings"),actions:(0,_.__)("Publish"),footer:(0,_.__)("Footer")},...u};return(0,r.createElement)("div",p({},h,{ref:(0,b.useMergeRefs)([t,h.ref]),className:L()(m,"interface-interface-skeleton",h.className,!!n&&"has-footer")}),!!c&&(0,r.createElement)("div",{className:"interface-interface-skeleton__drawer",role:"region","aria-label":f.drawer,tabIndex:"-1"},c),(0,r.createElement)("div",{className:"interface-interface-skeleton__editor"},!!l&&(0,r.createElement)("div",{className:"interface-interface-skeleton__header",role:"region","aria-label":f.header,tabIndex:"-1"},l),(0,r.createElement)("div",{className:"interface-interface-skeleton__body"},!!o&&(0,r.createElement)("div",{className:"interface-interface-skeleton__secondary-sidebar",role:"region","aria-label":f.secondarySidebar,tabIndex:"-1"},o),!!s&&(0,r.createElement)("div",{className:"interface-interface-skeleton__notices"},s),(0,r.createElement)("div",{className:"interface-interface-skeleton__content",role:"region","aria-label":f.body,tabIndex:"-1"},i),!!a&&(0,r.createElement)("div",{className:"interface-interface-skeleton__sidebar",role:"region","aria-label":f.sidebar,tabIndex:"-1"},a),!!d&&(0,r.createElement)("div",{className:"interface-interface-skeleton__actions",role:"region","aria-label":f.actions,tabIndex:"-1"},d))),!!n&&(0,r.createElement)("div",{className:"interface-interface-skeleton__footer",role:"region","aria-label":f.footer,tabIndex:"-1"},n))}));var pe=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function ge(e){let{as:t=E.DropdownMenu,className:n,label:l=(0,_.__)("Options"),popoverProps:a,toggleProps:o,children:s}=e;return(0,r.createElement)(t,{className:L()("interface-more-menu-dropdown",n),icon:pe,label:l,popoverProps:{position:"bottom left",...a,className:L()("interface-more-menu-dropdown__content",null==a?void 0:a.className)},toggleProps:{tooltipPosition:"bottom",...o}},(e=>s(e)))}function Ee(e){let{closeModal:t,children:n}=e;return(0,r.createElement)(E.Modal,{className:"interface-preferences-modal",title:(0,_.__)("Preferences"),closeLabel:(0,_.__)("Close"),onRequestClose:t},n)}var he=function(e){let{icon:t,size:n=24,...l}=e;return(0,r.cloneElement)(t,{width:n,height:n,...l})};var _e=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"}));var be=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"}));const fe="preferences-menu";function ve(e){let{sections:t}=e;const n=(0,b.useViewportMatch)("medium"),[l,a]=(0,r.useState)(fe),{tabs:o,sectionsContentMap:s}=(0,r.useMemo)((()=>{let e={tabs:[],sectionsContentMap:{}};return t.length&&(e=t.reduce(((e,t)=>{let{name:n,tabLabel:l,content:a}=t;return e.tabs.push({name:n,title:l}),e.sectionsContentMap[n]=a,e}),{tabs:[],sectionsContentMap:{}})),e}),[t]),i=(0,r.useCallback)((e=>s[e.name]||null),[s]);let c;return c=n?(0,r.createElement)(E.TabPanel,{className:"interface-preferences__tabs",tabs:o,initialTabName:l!==fe?l:void 0,onSelect:a,orientation:"vertical"},i):(0,r.createElement)(E.__experimentalNavigatorProvider,{initialPath:"/",className:"interface-preferences__provider"},(0,r.createElement)(E.__experimentalNavigatorScreen,{path:"/"},(0,r.createElement)(E.Card,{isBorderless:!0,size:"small"},(0,r.createElement)(E.CardBody,null,(0,r.createElement)(E.__experimentalItemGroup,null,o.map((e=>(0,r.createElement)(E.__experimentalNavigatorButton,{key:e.name,path:e.name,as:E.__experimentalItem,isAction:!0},(0,r.createElement)(E.__experimentalHStack,{justify:"space-between"},(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(E.__experimentalTruncate,null,e.title)),(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(he,{icon:(0,_.isRTL)()?_e:be})))))))))),t.length&&t.map((e=>(0,r.createElement)(E.__experimentalNavigatorScreen,{key:`${e.name}-menu`,path:e.name},(0,r.createElement)(E.Card,{isBorderless:!0,size:"large"},(0,r.createElement)(E.CardHeader,{isBorderless:!1,justify:"left",size:"small",gap:"6"},(0,r.createElement)(E.__experimentalNavigatorBackButton,{icon:(0,_.isRTL)()?be:_e,"aria-label":(0,_.__)("Navigate to the previous view")}),(0,r.createElement)(E.__experimentalText,{size:"16"},e.tabLabel)),(0,r.createElement)(E.CardBody,null,e.content)))))),c}var ye=e=>{let{description:t,title:n,children:l}=e;return(0,r.createElement)("fieldset",{className:"interface-preferences-modal__section"},(0,r.createElement)("legend",null,(0,r.createElement)("h2",{className:"interface-preferences-modal__section-title"},n),t&&(0,r.createElement)("p",{className:"interface-preferences-modal__section-description"},t)),l)};var we=function(e){let{help:t,label:n,isChecked:l,onChange:a,children:o}=e;return(0,r.createElement)("div",{className:"interface-preferences-modal__option"},(0,r.createElement)(E.ToggleControl,{help:t,label:n,checked:l,onChange:a}),o)},Se=window.wp.a11y,ke=window.wp.coreData;const Pe=e=>t=>{let{registry:n}=t;return n.dispatch(Z).enableComplementaryArea(Dt.name,e)},Te=()=>e=>{let{registry:t}=e;return t.dispatch(Z).disableComplementaryArea(Dt.name)};function xe(e){return{type:"OPEN_MODAL",name:e}}function Ce(){return{type:"CLOSE_MODAL"}}function Be(){return{type:"OPEN_PUBLISH_SIDEBAR"}}function Ie(){return{type:"CLOSE_PUBLISH_SIDEBAR"}}function Me(){return{type:"TOGGLE_PUBLISH_SIDEBAR"}}const Ne=e=>t=>{var n;let{registry:l}=t;const a=null!==(n=l.select(u.store).get("core/edit-post","inactivePanels"))&&void 0!==n?n:[];let o;o=!(null==a||!a.includes(e))?a.filter((t=>t!==e)):[...a,e],l.dispatch(u.store).set("core/edit-post","inactivePanels",o)},Ae=e=>t=>{var n;let{registry:l}=t;const a=null!==(n=l.select(u.store).get("core/edit-post","openPanels"))&&void 0!==n?n:[];let o;o=!(null==a||!a.includes(e))?a.filter((t=>t!==e)):[...a,e],l.dispatch(u.store).set("core/edit-post","openPanels",o)};function Le(e){return{type:"REMOVE_PANEL",panelName:e}}const Oe=e=>t=>{let{registry:n}=t;return n.dispatch(u.store).toggle("core/edit-post",e)},De=e=>t=>{let{registry:n}=t;n.dispatch(u.store).set("core/edit-post","editorMode",e),"visual"!==e&&n.dispatch(h.store).clearSelectedBlock();const l="visual"===e?(0,_.__)("Visual editor selected"):(0,_.__)("Code editor selected");(0,Se.speak)(l,"assertive")},Re=e=>t=>{let{registry:n}=t;const l=n.select(Z).isItemPinned("core/edit-post",e);n.dispatch(Z)[l?"unpinItem":"pinItem"]("core/edit-post",e)},Fe=(e,t)=>n=>{var l;let{registry:a}=n;if(!e)return;const o=null!==(l=a.select(u.store).get("core/edit-post","preferredStyleVariations"))&&void 0!==l?l:{};if(t)a.dispatch(u.store).set("core/edit-post","preferredStyleVariations",{...o,[e]:t});else{const t={...o};delete t[e],a.dispatch(u.store).set("core/edit-post","preferredStyleVariations",t)}},Ve=e=>t=>{var n;let{registry:l}=t;const a=null!==(n=l.select(u.store).get("core/edit-post","hiddenBlockTypes"))&&void 0!==n?n:[],o=(0,g.without)(a,...(0,g.castArray)(e));l.dispatch(u.store).set("core/edit-post","hiddenBlockTypes",o)},Ue=e=>t=>{var n;let{registry:l}=t;const a=null!==(n=l.select(u.store).get("core/edit-post","hiddenBlockTypes"))&&void 0!==n?n:[],o=new Set([...a,...(0,g.castArray)(e)]);l.dispatch(u.store).set("core/edit-post","hiddenBlockTypes",[...o])},Ge=e=>t=>{let{dispatch:n}=t;return n({type:"SET_META_BOXES_PER_LOCATIONS",metaBoxesPerLocation:e})},ze=()=>async e=>{let{registry:t,select:n,dispatch:l}=e;l({type:"REQUEST_META_BOX_UPDATES"}),window.tinyMCE&&window.tinyMCE.triggerSave();const a=t.select(T.store).getCurrentPost(),o=[!!a.comment_status&&["comment_status",a.comment_status],!!a.ping_status&&["ping_status",a.ping_status],!!a.sticky&&["sticky",a.sticky],!!a.author&&["post_author",a.author]].filter(Boolean),r=[new window.FormData(document.querySelector(".metabox-base-form")),...n.getActiveMetaBoxLocations().map((e=>new window.FormData((e=>document.querySelector(`.edit-post-meta-boxes-area.is-${e} .metabox-location-${e}`)||document.querySelector("#metaboxes .metabox-location-"+e))(e))))],s=(0,g.reduce)(r,((e,t)=>{for(const[n,l]of t)e.append(n,l);return e}),new window.FormData);o.forEach((e=>{let[t,n]=e;return s.append(t,n)}));try{await N()({url:window._wpMetaBoxUrl,method:"POST",body:s,parse:!1}),l.metaBoxUpdatesSuccess()}catch{l.metaBoxUpdatesFailure()}};function He(){return{type:"META_BOX_UPDATES_SUCCESS"}}function $e(){return{type:"META_BOX_UPDATES_FAILURE"}}function We(e){return{type:"SET_PREVIEW_DEVICE_TYPE",deviceType:e}}function qe(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function je(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}function Ke(e){return{type:"SET_IS_EDITING_TEMPLATE",value:e}}const Xe=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t=>{let{registry:n,select:l,dispatch:a}=t;a(Ke(!0));if(!l.isFeatureActive("welcomeGuideTemplate")){const t=e?(0,_.__)("Custom template created. You're in template mode now."):(0,_.__)("Editing template. Changes made here affect all posts and pages that use the template.");n.dispatch(P.store).createSuccessNotice(t,{type:"snackbar"})}}},Qe=e=>async t=>{let{registry:n}=t;const l=await n.dispatch(ke.store).saveEntityRecord("postType","wp_template",e),a=n.select(T.store).getCurrentPost();n.dispatch(ke.store).editEntityRecord("postType",a.type,a.id,{template:l.slug})};let Ye=!1;const Ze=()=>e=>{let{registry:t,select:n,dispatch:l}=e;if(!t.select(T.store).__unstableIsEditorReady())return;if(Ye)return;const a=t.select(T.store).getCurrentPostType();window.postboxes.page!==a&&window.postboxes.add_postbox_toggles(a),Ye=!0;let o=t.select(T.store).isSavingPost(),r=t.select(T.store).isAutosavingPost();const s=n.hasMetaBoxes();t.subscribe((async()=>{const e=t.select(T.store).isSavingPost(),n=t.select(T.store).isAutosavingPost(),a=s&&o&&!e&&!r;o=e,r=n,a&&await l.requestMetaBoxUpdates()})),l({type:"META_BOXES_INITIALIZED"})};var Je,et;function tt(e){return[e]}function nt(){var e={clear:function(){e.head=null}};return e}function lt(e,t,n){var l;if(e.length!==t.length)return!1;for(l=n;l<e.length;l++)if(e[l]!==t[l])return!1;return!0}function at(e,t){var n,l;function a(){n=et?new WeakMap:nt()}function o(){var n,a,o,r,s,i=arguments.length;for(r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];for(s=t.apply(null,r),(n=l(s)).isUniqueByDependants||(n.lastDependants&&!lt(s,n.lastDependants,0)&&n.clear(),n.lastDependants=s),a=n.head;a;){if(lt(a.args,r,1))return a!==n.head&&(a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=n.head,a.prev=null,n.head.prev=a,n.head=a),a.val;a=a.next}return a={val:e.apply(null,r)},r[0]=null,a.args=r,n.head&&(n.head.prev=a,a.next=n.head),n.head=a,a.val}return t||(t=tt),l=et?function(e){var t,l,a,o,r,s=n,i=!0;for(t=0;t<e.length;t++){if(l=e[t],!(r=l)||"object"!=typeof r){i=!1;break}s.has(l)?s=s.get(l):(a=new WeakMap,s.set(l,a),s=a)}return s.has(Je)||((o=nt()).isUniqueByDependants=i,s.set(Je,o)),s.get(Je)}:function(){return n},o.getDependants=t,o.clear=a,a(),o}Je={},et="undefined"!=typeof WeakMap;const ot=[],rt={},st=(0,c.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(u.store).get("core/edit-post","editorMode"))&&void 0!==t?t:"visual"})),it=(0,c.createRegistrySelector)((e=>()=>{const t=e(Z).getActiveComplementaryArea("core/edit-post");return(0,g.includes)(["edit-post/document","edit-post/block"],t)})),ct=(0,c.createRegistrySelector)((e=>()=>{const t=e(Z).getActiveComplementaryArea("core/edit-post");return!!t&&!(0,g.includes)(["edit-post/document","edit-post/block"],t)})),dt=(0,c.createRegistrySelector)((e=>()=>e(Z).getActiveComplementaryArea("core/edit-post")));const ut=(0,c.createRegistrySelector)((e=>()=>{G()("select( 'core/edit-post' ).getPreferences",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const t=["hiddenBlockTypes","editorMode","preferredStyleVariations"].reduce(((t,n)=>({...t,[n]:e(u.store).get("core/edit-post",n)})),{}),n=function(e,t){var n;const l=null==e?void 0:e.reduce(((e,t)=>({...e,[t]:{enabled:!1}})),{}),a=null==t?void 0:t.reduce(((e,t)=>{const n=null==e?void 0:e[t];return{...e,[t]:{...n,opened:!0}}}),null!=l?l:{});return null!==(n=null!=a?a:l)&&void 0!==n?n:rt}(e(u.store).get("core/edit-post","inactivePanels"),e(u.store).get("core/edit-post","openPanels"));return{...t,panels:n}}));function mt(e,t,n){G()("select( 'core/edit-post' ).getPreference",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const l=ut(e)[t];return void 0===l?n:l}const pt=(0,c.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(u.store).get("core/edit-post","hiddenBlockTypes"))&&void 0!==t?t:ot}));function gt(e){return e.publishSidebarActive}function Et(e,t){return(0,g.includes)(e.removedPanels,t)}const ht=(0,c.createRegistrySelector)((e=>(t,n)=>{const l=e(u.store).get("core/edit-post","inactivePanels");return!(Et(t,n)||null!=l&&l.includes(n))})),_t=(0,c.createRegistrySelector)((e=>(t,n)=>{const l=e(u.store).get("core/edit-post","openPanels");return!(null==l||!l.includes(n))}));function bt(e,t){return e.activeModal===t}const ft=(0,c.createRegistrySelector)((e=>(t,n)=>!!e(u.store).get("core/edit-post",n))),vt=(0,c.createRegistrySelector)((e=>(t,n)=>e(Z).isItemPinned("core/edit-post",n))),yt=at((e=>Object.keys(e.metaBoxes.locations).filter((t=>St(e,t)))),(e=>[e.metaBoxes.locations]));function wt(e,t){return St(e,t)&&(0,g.some)(kt(e,t),(t=>{let{id:n}=t;return ht(e,`meta-box-${n}`)}))}function St(e,t){const n=kt(e,t);return!!n&&0!==n.length}function kt(e,t){return e.metaBoxes.locations[t]}const Pt=at((e=>(0,g.flatten)((0,g.values)(e.metaBoxes.locations))),(e=>[e.metaBoxes.locations]));function Tt(e){return yt(e).length>0}function xt(e){return e.metaBoxes.isSaving}function Ct(e){return e.deviceType}function Bt(e){return!!e.blockInserterPanel}function It(e){const{rootClientId:t,insertionIndex:n,filterValue:l}=e.blockInserterPanel;return{rootClientId:t,insertionIndex:n,filterValue:l}}function Mt(e){return e.listViewPanel}function Nt(e){return e.isEditingTemplate}function At(e){return e.metaBoxes.initialized}const Lt=(0,c.createRegistrySelector)((e=>()=>{const t=e(T.store).getEditedPostAttribute("template");if(t){var n;const l=null===(n=e(ke.store).getEntityRecords("postType","wp_template",{per_page:-1}))||void 0===n?void 0:n.find((e=>e.slug===t));return l?e(ke.store).getEditedEntityRecord("postType","wp_template",l.id):l}const l=e(T.store).getCurrentPost();return l.link?e(ke.store).__experimentalGetTemplateForLink(l.link):null})),Ot="core/edit-post",Dt=(0,c.createReduxStore)(Ot,{reducer:I,actions:a,selectors:o});(0,c.register)(Dt);var Rt=(0,c.withDispatch)((e=>{const{openModal:t}=e(Dt);return{openModal:t}}))((function(e){let{openModal:t}=e;return(0,r.createElement)(E.MenuItem,{onClick:()=>{t("edit-post/keyboard-shortcut-help")},shortcut:C.displayShortcut.access("h")},(0,_.__)("Keyboard shortcuts"))}));const{Fill:Ft,Slot:Vt}=(0,E.createSlotFill)("ToolsMoreMenuGroup");Ft.Slot=e=>{let{fillProps:t}=e;return(0,r.createElement)(Vt,{fillProps:t},(e=>!(0,g.isEmpty)(e)&&(0,r.createElement)(E.MenuGroup,{label:(0,_.__)("Tools")},e)))};var Ut=Ft;function Gt(){const e=(0,c.useSelect)((e=>e(Dt).isEditingTemplate()),[]);return(0,r.createElement)(u.PreferenceToggleMenuItem,{scope:"core/edit-post",name:e?"welcomeGuideTemplate":"welcomeGuide",label:(0,_.__)("Welcome Guide")})}(0,S.registerPlugin)("edit-post",{render:()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Ut,null,(e=>{let{onClose:t}=e;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(E.MenuItem,{role:"menuitem",href:(0,k.addQueryArgs)("edit.php",{post_type:"wp_block"})},(0,_.__)("Manage Reusable blocks")),(0,r.createElement)(Rt,{onSelect:t}),(0,r.createElement)(Gt,null),(0,r.createElement)(x,null),(0,r.createElement)(E.MenuItem,{role:"menuitem",icon:w,href:(0,_.__)("https://wordpress.org/support/article/wordpress-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,_.__)("Help"),(0,r.createElement)(E.VisuallyHidden,{as:"span"},(0,_.__)("(opens in a new tab)"))))})))});var zt=window.wp.keyboardShortcuts;var Ht=(0,b.compose)((0,c.withSelect)((e=>({isRichEditingEnabled:e(T.store).getEditorSettings().richEditingEnabled}))),(0,c.withDispatch)((e=>({onExit(){e(Dt).switchEditorMode("visual")}}))))((function(e){let{onExit:t,isRichEditingEnabled:n}=e;return(0,r.createElement)("div",{className:"edit-post-text-editor"},n&&(0,r.createElement)("div",{className:"edit-post-text-editor__toolbar"},(0,r.createElement)("h2",null,(0,_.__)("Editing code")),(0,r.createElement)(E.Button,{variant:"tertiary",onClick:t,shortcut:C.displayShortcut.secondary("m")},(0,_.__)("Exit code editor")),(0,r.createElement)(T.TextEditorGlobalKeyboardShortcuts,null)),(0,r.createElement)("div",{className:"edit-post-text-editor__body"},(0,r.createElement)(T.PostTitle,null),(0,r.createElement)(T.PostTextEditor,null)))}));var $t=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M20 10.8H6.7l4.1-4.5-1.1-1.1-5.8 6.3 5.8 5.8 1.1-1.1-4-3.9H20z"}));var Wt=function(e){let{onClick:t=g.noop,small:n=!1}=e;const{shortcut:l,areAdvancedSettingsOpened:a}=(0,c.useSelect)((e=>({shortcut:e(zt.store).getShortcutRepresentation("core/edit-post/toggle-sidebar"),areAdvancedSettingsOpened:"edit-post/block"===e(Dt).getActiveGeneralSidebarName()})),[]),{openGeneralSidebar:o,closeGeneralSidebar:s}=(0,c.useDispatch)(Dt),i=a?(0,_.__)("Hide more settings"):(0,_.__)("Show more settings");return(0,r.createElement)(E.MenuItem,{onClick:()=>{a?(s(),(0,Se.speak)((0,_.__)("Block settings closed"))):(o("edit-post/block"),(0,Se.speak)((0,_.__)("Additional settings are now available in the Editor block settings sidebar"))),t()},shortcut:l},!n&&i)};function qt(e){let{children:t,contentRef:n,shouldIframe:l,styles:a,assets:o,style:s}=e;const i=(0,h.__unstableUseMouseMoveTypingReset)();return l?(0,r.createElement)(h.__unstableIframe,{head:(0,r.createElement)(h.__unstableEditorStyles,{styles:a}),assets:o,ref:i,contentRef:n,style:{width:"100%",height:"100%",display:"block"},name:"editor-canvas"},t):(0,r.createElement)(r.Fragment,null,(0,r.createElement)(h.__unstableEditorStyles,{styles:a}),(0,r.createElement)(h.WritingFlow,{ref:n,className:"editor-styles-wrapper",style:{flex:"1",...s},tabIndex:-1},t))}function jt(e){let{styles:t}=e;const{deviceType:n,isTemplateMode:l,wrapperBlockName:a,wrapperUniqueId:o}=(0,c.useSelect)((e=>{const{isEditingTemplate:t,__experimentalGetPreviewDeviceType:n}=e(Dt),{getCurrentPostId:l,getCurrentPostType:a}=e(T.store),o=t();let r;return"wp_block"===a()?r="core/block":o||(r="core/post-content"),{deviceType:n(),isTemplateMode:o,wrapperBlockName:r,wrapperUniqueId:l()}}),[]),s=(0,c.useSelect)((e=>e(Dt).hasMetaBoxes()),[]),{themeSupportsLayout:i,assets:d}=(0,c.useSelect)((e=>{const t=e(h.store).getSettings();return{themeSupportsLayout:t.supportsLayout,assets:t.__unstableResolvedAssets}}),[]),{clearSelectedBlock:u}=(0,c.useDispatch)(h.store),{setIsEditingTemplate:m}=(0,c.useDispatch)(Dt),p={height:"100%",width:"100%",margin:0,display:"flex",flexFlow:"column",background:"white"},g={...p,borderRadius:"2px 2px 0 0",border:"1px solid #ddd",borderBottom:0},f=(0,h.__experimentalUseResizeCanvas)(n,l),v=(0,h.useSetting)("layout"),y="is-"+n.toLowerCase()+"-preview";let w,S=l?g:p;f&&(S=f),s||f||l||(w="40vh");const k=(0,r.useRef)(),P=(0,b.useMergeRefs)([k,(0,h.__unstableUseClipboardHandler)(),(0,h.__unstableUseTypewriter)(),(0,h.__unstableUseTypingObserver)(),(0,h.__unstableUseBlockSelectionClearer)()]),x=(0,h.__unstableUseBlockSelectionClearer)(),[,C]=(0,h.__experimentalUseNoRecursiveRenders)(o,a),B=(0,r.useMemo)((()=>l?{type:"default"}:i?v:void 0),[l,i,v]);return(0,r.createElement)(h.BlockTools,{__unstableContentRef:k,className:L()("edit-post-visual-editor",{"is-template-mode":l})},(0,r.createElement)(T.VisualEditorGlobalKeyboardShortcuts,null),(0,r.createElement)(E.__unstableMotion.div,{className:"edit-post-visual-editor__content-area",animate:{padding:l?"48px 48px 0":"0"},ref:x},l&&(0,r.createElement)(E.Button,{className:"edit-post-visual-editor__exit-template-mode",icon:$t,onClick:()=>{u(),m(!1)}},(0,_.__)("Back")),(0,r.createElement)(E.__unstableMotion.div,{animate:S,initial:p,className:y},(0,r.createElement)(qt,{shouldIframe:l||"Tablet"===n||"Mobile"===n,contentRef:P,styles:t,assets:d,style:{paddingBottom:w}},i&&!l&&(0,r.createElement)(h.__experimentalLayoutStyle,{selector:".edit-post-visual-editor__post-title-wrapper, .block-editor-block-list__layout.is-root-container",layout:v}),!l&&(0,r.createElement)("div",{className:"edit-post-visual-editor__post-title-wrapper",contentEditable:!1},(0,r.createElement)(T.PostTitle,null)),(0,r.createElement)(C,null,(0,r.createElement)(h.BlockList,{className:l?"wp-site-blocks":void 0,__experimentalLayout:B}))))),(0,r.createElement)(h.__unstableBlockSettingsMenuFirstItem,null,(e=>{let{onClose:t}=e;return(0,r.createElement)(Wt,{onClick:t})})))}var Kt=function(){const{getBlockSelectionStart:e}=(0,c.useSelect)(h.store),{getEditorMode:t,isEditorSidebarOpened:n,isListViewOpened:l}=(0,c.useSelect)(Dt),a=(0,c.useSelect)((e=>{const{richEditingEnabled:t,codeEditingEnabled:n}=e(T.store).getEditorSettings();return!t||!n}),[]),{switchEditorMode:o,openGeneralSidebar:s,closeGeneralSidebar:i,toggleFeature:d,setIsListViewOpened:u}=(0,c.useDispatch)(Dt),{registerShortcut:m}=(0,c.useDispatch)(zt.store);return(0,r.useEffect)((()=>{m({name:"core/edit-post/toggle-mode",category:"global",description:(0,_.__)("Switch between visual editor and code editor."),keyCombination:{modifier:"secondary",character:"m"}}),m({name:"core/edit-post/toggle-fullscreen",category:"global",description:(0,_.__)("Toggle fullscreen mode."),keyCombination:{modifier:"secondary",character:"f"}}),m({name:"core/edit-post/toggle-list-view",category:"global",description:(0,_.__)("Open the block list view."),keyCombination:{modifier:"access",character:"o"}}),m({name:"core/edit-post/toggle-sidebar",category:"global",description:(0,_.__)("Show or hide the settings sidebar."),keyCombination:{modifier:"primaryShift",character:","}}),m({name:"core/edit-post/next-region",category:"global",description:(0,_.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),m({name:"core/edit-post/previous-region",category:"global",description:(0,_.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"}]}),m({name:"core/edit-post/keyboard-shortcuts",category:"main",description:(0,_.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}})}),[]),(0,zt.useShortcut)("core/edit-post/toggle-mode",(()=>{o("visual"===t()?"text":"visual")}),{isDisabled:a}),(0,zt.useShortcut)("core/edit-post/toggle-fullscreen",(()=>{d("fullscreenMode")})),(0,zt.useShortcut)("core/edit-post/toggle-sidebar",(t=>{if(t.preventDefault(),n())i();else{const t=e()?"edit-post/block":"edit-post/document";s(t)}})),(0,zt.useShortcut)("core/edit-post/toggle-list-view",(()=>u(!l()))),null};const Xt=[{keyCombination:{modifier:"primary",character:"b"},description:(0,_.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,_.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,_.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,_.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,_.__)("Insert a link to a post or page")},{keyCombination:{modifier:"primary",character:"u"},description:(0,_.__)("Underline the selected text.")}];function Qt(e){let{keyCombination:t,forceAriaLabel:n}=e;const l=t.modifier?C.displayShortcutList[t.modifier](t.character):t.character,a=t.modifier?C.shortcutAriaLabel[t.modifier](t.character):t.character;return(0,r.createElement)("kbd",{className:"edit-post-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":n||a},(0,g.castArray)(l).map(((e,t)=>"+"===e?(0,r.createElement)(r.Fragment,{key:t},e):(0,r.createElement)("kbd",{key:t,className:"edit-post-keyboard-shortcut-help-modal__shortcut-key"},e))))}var Yt=function(e){let{description:t,keyCombination:n,aliases:l=[],ariaLabel:a}=e;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"edit-post-keyboard-shortcut-help-modal__shortcut-description"},t),(0,r.createElement)("div",{className:"edit-post-keyboard-shortcut-help-modal__shortcut-term"},(0,r.createElement)(Qt,{keyCombination:n,forceAriaLabel:a}),l.map(((e,t)=>(0,r.createElement)(Qt,{keyCombination:e,forceAriaLabel:a,key:t})))))};var Zt=function(e){let{name:t}=e;const{keyCombination:n,description:l,aliases:a}=(0,c.useSelect)((e=>{const{getShortcutKeyCombination:n,getShortcutDescription:l,getShortcutAliases:a}=e(zt.store);return{keyCombination:n(t),aliases:a(t),description:l(t)}}),[t]);return n?(0,r.createElement)(Yt,{keyCombination:n,description:l,aliases:a}):null};const Jt="edit-post/keyboard-shortcut-help",en=e=>{let{shortcuts:t}=e;return(0,r.createElement)("ul",{className:"edit-post-keyboard-shortcut-help-modal__shortcut-list",role:"list"},t.map(((e,t)=>(0,r.createElement)("li",{className:"edit-post-keyboard-shortcut-help-modal__shortcut",key:t},(0,g.isString)(e)?(0,r.createElement)(Zt,{name:e}):(0,r.createElement)(Yt,e)))))},tn=e=>{let{title:t,shortcuts:n,className:l}=e;return(0,r.createElement)("section",{className:L()("edit-post-keyboard-shortcut-help-modal__section",l)},!!t&&(0,r.createElement)("h2",{className:"edit-post-keyboard-shortcut-help-modal__section-title"},t),(0,r.createElement)(en,{shortcuts:n}))},nn=e=>{let{title:t,categoryName:n,additionalShortcuts:l=[]}=e;const a=(0,c.useSelect)((e=>e(zt.store).getCategoryShortcuts(n)),[n]);return(0,r.createElement)(tn,{title:t,shortcuts:a.concat(l)})};var ln=(0,b.compose)([(0,c.withSelect)((e=>({isModalActive:e(Dt).isModalActive(Jt)}))),(0,c.withDispatch)(((e,t)=>{let{isModalActive:n}=t;const{openModal:l,closeModal:a}=e(Dt);return{toggleModal:()=>n?a():l(Jt)}}))])((function(e){let{isModalActive:t,toggleModal:n}=e;return(0,zt.useShortcut)("core/edit-post/keyboard-shortcuts",n),t?(0,r.createElement)(E.Modal,{className:"edit-post-keyboard-shortcut-help-modal",title:(0,_.__)("Keyboard shortcuts"),closeLabel:(0,_.__)("Close"),onRequestClose:n},(0,r.createElement)(tn,{className:"edit-post-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/edit-post/keyboard-shortcuts"]}),(0,r.createElement)(nn,{title:(0,_.__)("Global shortcuts"),categoryName:"global"}),(0,r.createElement)(nn,{title:(0,_.__)("Selection shortcuts"),categoryName:"selection"}),(0,r.createElement)(nn,{title:(0,_.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,_.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,_.__)("Forward-slash")}]}),(0,r.createElement)(tn,{title:(0,_.__)("Text formatting"),shortcuts:Xt})):null}));function an(e){let{willEnable:t}=e;const[n,l]=(0,r.useState)(!1);return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("p",{className:"edit-post-preferences-modal__custom-fields-confirmation-message"},(0,_.__)("A page reload is required for this change. Make sure your content is saved before reloading.")),(0,r.createElement)(E.Button,{className:"edit-post-preferences-modal__custom-fields-confirmation-button",variant:"secondary",isBusy:n,disabled:n,onClick:()=>{l(!0),document.getElementById("toggle-custom-fields-form").submit()}},t?(0,_.__)("Enable & Reload"):(0,_.__)("Disable & Reload")))}var on=(0,c.withSelect)((e=>({areCustomFieldsEnabled:!!e(T.store).getEditorSettings().enableCustomFields})))((function(e){let{label:t,areCustomFieldsEnabled:n}=e;const[l,a]=(0,r.useState)(n);return(0,r.createElement)(we,{label:t,isChecked:l,onChange:a},l!==n&&(0,r.createElement)(an,{willEnable:l}))})),rn=(0,b.compose)((0,c.withSelect)(((e,t)=>{let{panelName:n}=t;const{isEditorPanelEnabled:l,isEditorPanelRemoved:a}=e(Dt);return{isRemoved:a(n),isChecked:l(n)}})),(0,b.ifCondition)((e=>{let{isRemoved:t}=e;return!t})),(0,c.withDispatch)(((e,t)=>{let{panelName:n}=t;return{onChange:()=>e(Dt).toggleEditorPanelEnabled(n)}})))(we);const{Fill:sn,Slot:cn}=(0,E.createSlotFill)("EnablePluginDocumentSettingPanelOption"),dn=e=>{let{label:t,panelName:n}=e;return(0,r.createElement)(sn,null,(0,r.createElement)(rn,{label:t,panelName:n}))};dn.Slot=cn;var un=dn,mn=(0,b.compose)((0,c.withSelect)((e=>({isChecked:e(T.store).isPublishSidebarEnabled()}))),(0,c.withDispatch)((e=>{const{enablePublishSidebar:t,disablePublishSidebar:n}=e(T.store);return{onChange:e=>e?t():n()}})),(0,F.ifViewportMatches)("medium"))(we),pn=(0,b.compose)((0,c.withSelect)(((e,t)=>{let{featureName:n}=t;const{isFeatureActive:l}=e(Dt);return{isChecked:l(n)}})),(0,c.withDispatch)(((e,t)=>{let{featureName:n}=t;return{onChange:()=>e(Dt).toggleFeature(n)}})))(we);var gn=(0,c.withSelect)((e=>{const{getEditorSettings:t}=e(T.store),{getAllMetaBoxes:n}=e(Dt);return{areCustomFieldsRegistered:void 0!==t().enableCustomFields,metaBoxes:n()}}))((function(e){let{areCustomFieldsRegistered:t,metaBoxes:n,...l}=e;const a=(0,g.filter)(n,(e=>{let{id:t}=e;return"postcustom"!==t}));return t||0!==a.length?(0,r.createElement)(ye,l,t&&(0,r.createElement)(on,{label:(0,_.__)("Custom fields")}),(0,g.map)(a,(e=>{let{id:t,title:n}=e;return(0,r.createElement)(rn,{key:t,label:n,panelName:`meta-box-${t}`})}))):null}));var En=function(e){let{blockTypes:t,value:n,onItemChange:l}=e;return(0,r.createElement)("ul",{className:"edit-post-block-manager__checklist"},t.map((e=>(0,r.createElement)("li",{key:e.name,className:"edit-post-block-manager__checklist-item"},(0,r.createElement)(E.CheckboxControl,{label:(0,r.createElement)(r.Fragment,null,e.title,(0,r.createElement)(h.BlockIcon,{icon:e.icon})),checked:n.includes(e.name),onChange:(0,g.partial)(l,e.name)})))))};var hn=function e(t){let{title:n,blockTypes:l}=t;const a=(0,b.useInstanceId)(e),{defaultAllowedBlockTypes:o,hiddenBlockTypes:s}=(0,c.useSelect)((e=>{const{getEditorSettings:t}=e(T.store),{getHiddenBlockTypes:n}=e(Dt);return{defaultAllowedBlockTypes:t().defaultAllowedBlockTypes,hiddenBlockTypes:n()}}),[]),i=(0,r.useMemo)((()=>!0===o?l:l.filter((e=>{let{name:t}=e;return(0,g.includes)(o||[],t)}))),[o,l]),{showBlockTypes:d,hideBlockTypes:u}=(0,c.useDispatch)(Dt),m=(0,r.useCallback)(((e,t)=>{t?d(e):u(e)}),[]),p=(0,r.useCallback)((e=>{const t=(0,g.map)(l,"name");e?d(t):u(t)}),[l]);if(!i.length)return null;const h=(0,g.without)((0,g.map)(i,"name"),...s),_="edit-post-block-manager__category-title-"+a,f=h.length===i.length;let v;return v=f?"true":h.length>0?"mixed":"false",(0,r.createElement)("div",{role:"group","aria-labelledby":_,className:"edit-post-block-manager__category"},(0,r.createElement)(E.CheckboxControl,{checked:f,onChange:p,className:"edit-post-block-manager__category-title","aria-checked":v,label:(0,r.createElement)("span",{id:_},n)}),(0,r.createElement)(En,{blockTypes:i,value:h,onItemChange:m}))};var _n=(0,c.withSelect)((e=>{const{getBlockTypes:t,getCategories:n,hasBlockSupport:l,isMatchingSearchTerm:a}=e(s.store),{getHiddenBlockTypes:o}=e(Dt),r=o(),i=(0,g.isArray)(r)&&r.length;return{blockTypes:t(),categories:n(),hasBlockSupport:l,isMatchingSearchTerm:a,numberOfHiddenBlocks:i}}))((function(e){let{blockTypes:t,categories:n,hasBlockSupport:l,isMatchingSearchTerm:a,numberOfHiddenBlocks:o}=e;const s=(0,b.useDebounce)(Se.speak,500),[i,c]=(0,r.useState)("");return t=t.filter((e=>l(e,"inserter",!0)&&(!i||a(e,i))&&(!e.parent||(0,g.includes)(e.parent,"core/post-content")))),(0,r.useEffect)((()=>{if(!i)return;const e=t.length,n=(0,_.sprintf)((0,_._n)("%d result found.","%d results found.",e),e);s(n)}),[t.length,i,s]),(0,r.createElement)("div",{className:"edit-post-block-manager__content"},!!o&&(0,r.createElement)("div",{className:"edit-post-block-manager__disabled-blocks-count"},(0,_.sprintf)((0,_._n)("%d block is hidden.","%d blocks are hidden.",o),o)),(0,r.createElement)(E.SearchControl,{label:(0,_.__)("Search for a block"),placeholder:(0,_.__)("Search for a block"),value:i,onChange:e=>c(e),className:"edit-post-block-manager__search"}),(0,r.createElement)("div",{tabIndex:"0",role:"region","aria-label":(0,_.__)("Available block types"),className:"edit-post-block-manager__results"},0===t.length&&(0,r.createElement)("p",{className:"edit-post-block-manager__no-results"},(0,_.__)("No blocks found.")),n.map((e=>(0,r.createElement)(hn,{key:e.slug,title:e.title,blockTypes:(0,g.filter)(t,{category:e.slug})}))),(0,r.createElement)(hn,{title:(0,_.__)("Uncategorized"),blockTypes:(0,g.filter)(t,(e=>{let{category:t}=e;return!t}))})))}));function bn(){const e=(0,b.useViewportMatch)("medium"),{closeModal:t}=(0,c.useDispatch)(Dt),{isModalActive:n,isViewable:l}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(T.store),{getPostType:n}=e(ke.store),l=n(t("type"));return{isModalActive:e(Dt).isModalActive("edit-post/preferences"),isViewable:(0,g.get)(l,["viewable"],!1)}}),[]),a=(0,c.useSelect)((t=>{const{getEditorSettings:n}=t(T.store),{getEditorMode:l,isFeatureActive:a}=t(Dt),o=l(),r=n().richEditingEnabled;return!a("reducedUI")&&e&&r&&"visual"===o}),[e]),o=(0,r.useMemo)((()=>[{name:"general",tabLabel:(0,_.__)("General"),content:(0,r.createElement)(r.Fragment,null,e&&(0,r.createElement)(ye,{title:(0,_.__)("Publishing"),description:(0,_.__)("Change options related to publishing.")},(0,r.createElement)(mn,{help:(0,_.__)("Review settings, such as visibility and tags."),label:(0,_.__)("Include pre-publish checklist")})),(0,r.createElement)(ye,{title:(0,_.__)("Appearance"),description:(0,_.__)("Customize options related to the block editor interface and editing flow.")},(0,r.createElement)(pn,{featureName:"reducedUI",help:(0,_.__)("Compacts options and outlines in the toolbar."),label:(0,_.__)("Reduce the interface")}),(0,r.createElement)(pn,{featureName:"focusMode",help:(0,_.__)("Highlights the current block and fades other content."),label:(0,_.__)("Spotlight mode")}),(0,r.createElement)(pn,{featureName:"showIconLabels",help:(0,_.__)("Shows text instead of icons."),label:(0,_.__)("Display button labels")}),(0,r.createElement)(pn,{featureName:"themeStyles",help:(0,_.__)("Make the editor look like your theme."),label:(0,_.__)("Use theme styles")}),a&&(0,r.createElement)(pn,{featureName:"showBlockBreadcrumbs",help:(0,_.__)("Shows block breadcrumbs at the bottom of the editor."),label:(0,_.__)("Display block breadcrumbs")})))},{name:"blocks",tabLabel:(0,_.__)("Blocks"),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)(ye,{title:(0,_.__)("Block interactions"),description:(0,_.__)("Customize how you interact with blocks in the block library and editing canvas.")},(0,r.createElement)(pn,{featureName:"mostUsedBlocks",help:(0,_.__)("Places the most frequent blocks in the block library."),label:(0,_.__)("Show most used blocks")}),(0,r.createElement)(pn,{featureName:"keepCaretInsideBlock",help:(0,_.__)("Aids screen readers by stopping text caret from leaving blocks."),label:(0,_.__)("Contain text cursor inside block")})),(0,r.createElement)(ye,{title:(0,_.__)("Visible blocks"),description:(0,_.__)("Disable blocks that you don't want to appear in the inserter. They can always be toggled back on later.")},(0,r.createElement)(_n,null)))},{name:"panels",tabLabel:(0,_.__)("Panels"),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)(ye,{title:(0,_.__)("Document settings"),description:(0,_.__)("Choose what displays in the panel.")},(0,r.createElement)(un.Slot,null),l&&(0,r.createElement)(rn,{label:(0,_.__)("Permalink"),panelName:"post-link"}),l&&(0,r.createElement)(rn,{label:(0,_.__)("Template"),panelName:"template"}),(0,r.createElement)(T.PostTaxonomies,{taxonomyWrapper:(e,t)=>(0,r.createElement)(rn,{label:(0,g.get)(t,["labels","menu_name"]),panelName:`taxonomy-panel-${t.slug}`})}),(0,r.createElement)(T.PostFeaturedImageCheck,null,(0,r.createElement)(rn,{label:(0,_.__)("Featured image"),panelName:"featured-image"})),(0,r.createElement)(T.PostExcerptCheck,null,(0,r.createElement)(rn,{label:(0,_.__)("Excerpt"),panelName:"post-excerpt"})),(0,r.createElement)(T.PostTypeSupportCheck,{supportKeys:["comments","trackbacks"]},(0,r.createElement)(rn,{label:(0,_.__)("Discussion"),panelName:"discussion-panel"})),(0,r.createElement)(T.PageAttributesCheck,null,(0,r.createElement)(rn,{label:(0,_.__)("Page attributes"),panelName:"page-attributes"}))),(0,r.createElement)(gn,{title:(0,_.__)("Additional"),description:(0,_.__)("Add extra areas to the editor.")}))}]),[l,e,a]);return n?(0,r.createElement)(Ee,{closeModal:t},(0,r.createElement)(ve,{sections:o})):null}class fn extends r.Component{constructor(){super(...arguments),this.state={historyId:null}}componentDidUpdate(e){const{postId:t,postStatus:n,postType:l,isSavingPost:a}=this.props,{historyId:o}=this.state;"trash"!==n||a?t===e.postId&&t===o||"auto-draft"===n||!t||this.setBrowserURL(t):this.setTrashURL(t,l)}setTrashURL(e,t){window.location.href=function(e,t){return(0,k.addQueryArgs)("edit.php",{trashed:1,post_type:t,ids:e})}(e,t)}setBrowserURL(e){window.history.replaceState({id:e},"Post "+e,function(e){return(0,k.addQueryArgs)("post.php",{post:e,action:"edit"})}(e)),this.setState((()=>({historyId:e})))}render(){return null}}var vn=(0,c.withSelect)((e=>{const{getCurrentPost:t,isSavingPost:n}=e(T.store),l=t();let{id:a,status:o,type:r}=l;return["wp_template","wp_template_part"].includes(r)&&(a=l.wp_id),{postId:a,postStatus:o,postType:r,isSavingPost:n()}}))(fn);var yn=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,r.createElement)(y.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"}));var wn=function(e){let{showTooltip:t,icon:n,href:l}=e;const{isActive:a,isRequestingSiteIcon:o,postType:s,siteIconUrl:i}=(0,c.useSelect)((e=>{const{getCurrentPostType:t}=e(T.store),{isFeatureActive:n}=e(Dt),{getEntityRecord:l,getPostType:a,isResolving:o}=e(ke.store),r=l("root","__unstableBase",void 0)||{};return{isActive:n("fullscreenMode"),isRequestingSiteIcon:o("getEntityRecord",["root","__unstableBase",void 0]),postType:a(t()),siteIconUrl:r.site_icon_url}}),[]),d=(0,b.useReducedMotion)();if(!a||!s)return null;let u=(0,r.createElement)(E.Icon,{size:"36px",icon:yn});const m={expand:{scale:1.25,transition:{type:"tween",duration:"0.3"}}};i&&(u=(0,r.createElement)(E.__unstableMotion.img,{variants:!d&&m,alt:(0,_.__)("Site Icon"),className:"edit-post-fullscreen-mode-close_site-icon",src:i})),o&&(u=null),n&&(u=(0,r.createElement)(E.Icon,{size:"36px",icon:n}));const p=L()({"edit-post-fullscreen-mode-close":!0,"has-icon":i});return(0,r.createElement)(E.__unstableMotion.div,{whileHover:"expand"},(0,r.createElement)(E.Button,{className:p,href:null!=l?l:(0,k.addQueryArgs)("edit.php",{post_type:s.slug}),label:(0,g.get)(s,["labels","view_items"],(0,_.__)("Back")),showTooltip:t},u))};var Sn=(0,r.createElement)(y.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(y.Path,{d:"M13.8 5.2H3v1.5h10.8V5.2zm-3.6 12v1.5H21v-1.5H10.2zm7.2-6H6.6v1.5h10.8v-1.5z"}));var kn=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));const Pn=e=>{e.preventDefault()};var Tn=function(){const e=(0,r.useRef)(),{setIsInserterOpened:t,setIsListViewOpened:n}=(0,c.useDispatch)(Dt),{isInserterEnabled:l,isInserterOpened:a,isTextModeEnabled:o,showIconLabels:s,isListViewOpen:i,listViewShortcut:d}=(0,c.useSelect)((e=>{const{hasInserterItems:t,getBlockRootClientId:n,getBlockSelectionEnd:l}=e(h.store),{getEditorSettings:a}=e(T.store),{getEditorMode:o,isFeatureActive:r,isListViewOpened:s}=e(Dt),{getShortcutRepresentation:i}=e(zt.store);return{isInserterEnabled:"visual"===o()&&a().richEditingEnabled&&t(n(l())),isInserterOpened:e(Dt).isInserterOpened(),isTextModeEnabled:"text"===o(),showIconLabels:r("showIconLabels"),isListViewOpen:s(),listViewShortcut:i("core/edit-post/toggle-list-view")}}),[]),u=(0,b.useViewportMatch)("medium"),m=(0,b.useViewportMatch)("wide"),p=(0,_.__)("Document tools"),g=(0,r.useCallback)((()=>n(!i)),[n,i]),f=(0,r.createElement)(r.Fragment,null,(0,r.createElement)(E.ToolbarItem,{as:T.TableOfContents,hasOutlineItemsDisabled:o,repositionDropdown:s&&!m,showTooltip:!s,variant:s?"tertiary":void 0}),(0,r.createElement)(E.ToolbarItem,{as:E.Button,className:"edit-post-header-toolbar__list-view-toggle",icon:Sn,disabled:o,isPressed:i,label:(0,_.__)("List View"),onClick:g,shortcut:d,showTooltip:!s})),v=(0,r.useCallback)((()=>{a?e.current.focus():t(!0)}),[a,t]);return(0,r.createElement)(h.NavigableToolbar,{className:"edit-post-header-toolbar","aria-label":p},(0,r.createElement)("div",{className:"edit-post-header-toolbar__left"},(0,r.createElement)(E.ToolbarItem,{ref:e,as:E.Button,className:"edit-post-header-toolbar__inserter-toggle",variant:"primary",isPressed:a,onMouseDown:Pn,onClick:v,disabled:!l,icon:kn,label:(0,_._x)("Toggle block inserter","Generic label for block inserter button"),showTooltip:!s},s&&(a?(0,_.__)("Close"):(0,_.__)("Add"))),(m||!s)&&(0,r.createElement)(r.Fragment,null,u&&(0,r.createElement)(E.ToolbarItem,{as:h.ToolSelector,showTooltip:!s,variant:s?"tertiary":void 0,disabled:o}),(0,r.createElement)(E.ToolbarItem,{as:T.EditorHistoryUndo,showTooltip:!s,variant:s?"tertiary":void 0}),(0,r.createElement)(E.ToolbarItem,{as:T.EditorHistoryRedo,showTooltip:!s,variant:s?"tertiary":void 0}),f)))};const xn=[{value:"visual",label:(0,_.__)("Visual editor")},{value:"text",label:(0,_.__)("Code editor")}];var Cn=function(){const{shortcut:e,isRichEditingEnabled:t,isCodeEditingEnabled:n,isEditingTemplate:l,mode:a}=(0,c.useSelect)((e=>({shortcut:e(zt.store).getShortcutRepresentation("core/edit-post/toggle-mode"),isRichEditingEnabled:e(T.store).getEditorSettings().richEditingEnabled,isCodeEditingEnabled:e(T.store).getEditorSettings().codeEditingEnabled,isEditingTemplate:e(Dt).isEditingTemplate(),mode:e(Dt).getEditorMode()})),[]),{switchEditorMode:o}=(0,c.useDispatch)(Dt);if(l)return null;if(!t||!n)return null;const s=xn.map((t=>t.value!==a?{...t,shortcut:e}:t));return(0,r.createElement)(E.MenuGroup,{label:(0,_.__)("Editor")},(0,r.createElement)(E.MenuItemsChoice,{choices:s,value:a,onSelect:o}))};function Bn(){const{openModal:e}=(0,c.useDispatch)(Dt);return(0,r.createElement)(E.MenuItem,{onClick:()=>{e("edit-post/preferences")}},(0,_.__)("Preferences"))}var In=function(){return(0,b.useViewportMatch)("medium")?(0,r.createElement)(E.MenuGroup,{label:(0,_._x)("View","noun")},(0,r.createElement)(u.PreferenceToggleMenuItem,{scope:"core/edit-post",name:"fixedToolbar",label:(0,_.__)("Top toolbar"),info:(0,_.__)("Access all block and document tools in a single place"),messageActivated:(0,_.__)("Top toolbar activated"),messageDeactivated:(0,_.__)("Top toolbar deactivated")}),(0,r.createElement)(u.PreferenceToggleMenuItem,{scope:"core/edit-post",name:"focusMode",label:(0,_.__)("Spotlight mode"),info:(0,_.__)("Focus on one block at a time"),messageActivated:(0,_.__)("Spotlight mode activated"),messageDeactivated:(0,_.__)("Spotlight mode deactivated")}),(0,r.createElement)(u.PreferenceToggleMenuItem,{scope:"core/edit-post",name:"fullscreenMode",label:(0,_.__)("Fullscreen mode"),info:(0,_.__)("Work without distraction"),messageActivated:(0,_.__)("Fullscreen mode activated"),messageDeactivated:(0,_.__)("Fullscreen mode deactivated"),shortcut:C.displayShortcut.secondary("f")})):null};var Mn=e=>{let{showIconLabels:t}=e;const n=(0,b.useViewportMatch)("large");return(0,r.createElement)(ge,{toggleProps:{showTooltip:!t,...t&&{variant:"tertiary"}}},(e=>{let{onClose:l}=e;return(0,r.createElement)(r.Fragment,null,t&&!n&&(0,r.createElement)(se.Slot,{className:t&&"show-icon-labels",scope:"core/edit-post"}),(0,r.createElement)(In,null),(0,r.createElement)(Cn,null),(0,r.createElement)(le.Slot,{name:"core/edit-post/plugin-more-menu",label:(0,_.__)("Plugins"),as:E.MenuGroup,fillProps:{onClick:l}}),(0,r.createElement)(Ut.Slot,{fillProps:{onClose:l}}),(0,r.createElement)(E.MenuGroup,null,(0,r.createElement)(Bn,null)))}))};var Nn=(0,b.compose)((0,c.withSelect)((e=>({hasPublishAction:(0,g.get)(e(T.store).getCurrentPost(),["_links","wp:action-publish"],!1),isBeingScheduled:e(T.store).isEditedPostBeingScheduled(),isPending:e(T.store).isCurrentPostPending(),isPublished:e(T.store).isCurrentPostPublished(),isPublishSidebarEnabled:e(T.store).isPublishSidebarEnabled(),isPublishSidebarOpened:e(Dt).isPublishSidebarOpened(),isScheduled:e(T.store).isCurrentPostScheduled()}))),(0,c.withDispatch)((e=>{const{togglePublishSidebar:t}=e(Dt);return{togglePublishSidebar:t}})))((function(e){let{forceIsDirty:t,forceIsSaving:n,hasPublishAction:l,isBeingScheduled:a,isPending:o,isPublished:s,isPublishSidebarEnabled:i,isPublishSidebarOpened:c,isScheduled:d,togglePublishSidebar:u,setEntitiesSavedStatesCallback:m}=e;const p="toggle",g="button",E=(0,b.useViewportMatch)("medium","<");let h;return h=s||d&&a||o&&!l&&!E?g:E||i?p:g,(0,r.createElement)(T.PostPublishButton,{forceIsDirty:t,forceIsSaving:n,isOpen:c,isToggle:h===p,onToggle:u,setEntitiesSavedStatesCallback:m})}));function An(){const{hasActiveMetaboxes:e,isPostSaveable:t,isSaving:n,deviceType:l}=(0,c.useSelect)((e=>({hasActiveMetaboxes:e(Dt).hasMetaBoxes(),isSaving:e(Dt).isSavingMetaBoxes(),isPostSaveable:e(T.store).isEditedPostSaveable(),deviceType:e(Dt).__experimentalGetPreviewDeviceType()})),[]),{__experimentalSetPreviewDeviceType:a}=(0,c.useDispatch)(Dt);return(0,r.createElement)(h.__experimentalPreviewOptions,{isEnabled:t,className:"edit-post-post-preview-dropdown",deviceType:l,setDeviceType:a},(0,r.createElement)(E.MenuGroup,null,(0,r.createElement)("div",{className:"edit-post-header-preview__grouping-external"},(0,r.createElement)(T.PostPreviewButton,{className:"edit-post-header-preview__button-external",role:"menuitem",forceIsAutosaveable:e,forcePreviewLink:n?null:void 0,textContent:(0,r.createElement)(r.Fragment,null,(0,_.__)("Preview in new tab"),(0,r.createElement)(E.Icon,{icon:w}))}))))}const Ln="__experimentalMainDashboardButton",{Fill:On,Slot:Dn}=(0,E.createSlotFill)(Ln),Rn=On;Rn.Slot=e=>{let{children:t}=e;const n=(0,E.__experimentalUseSlot)(Ln);return Boolean(n.fills&&n.fills.length)?(0,r.createElement)(Dn,{bubblesVirtually:!0}):t};var Fn=Rn;var Vn=(0,r.createElement)(y.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(y.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"}));function Un(){const{clearSelectedBlock:e}=(0,c.useDispatch)(h.store),{setIsEditingTemplate:t}=(0,c.useDispatch)(Dt),{getEditorSettings:n}=(0,c.useSelect)(T.store),{updateEditorSettings:l,editPost:a}=(0,c.useDispatch)(T.store),{deleteEntityRecord:o}=(0,c.useDispatch)(ke.store),{template:s}=(0,c.useSelect)((e=>{const{isEditingTemplate:t,getEditedPostTemplate:n}=e(Dt);return{template:t()?n():null}}),[]),[i,d]=(0,r.useState)(!1);if(!s||!s.wp_id)return null;let u=s.slug;null!=s&&s.title&&(u=s.title);return(0,r.createElement)(E.MenuGroup,{className:"edit-post-template-top-area__second-menu-group"},(0,r.createElement)(r.Fragment,null,(0,r.createElement)(E.MenuItem,{className:"edit-post-template-top-area__delete-template-button",isDestructive:!0,variant:"secondary","aria-label":(0,_.__)("Delete template"),onClick:()=>{d(!0)}},(0,_.__)("Delete template")),(0,r.createElement)(E.__experimentalConfirmDialog,{isOpen:i,onConfirm:()=>{e(),t(!1),d(!1),a({template:""});const r=n(),i=(0,g.pickBy)(r.availableTemplates,((e,t)=>t!==s.slug));l({...r,availableTemplates:i}),o("postType","wp_template",s.id,{throwOnError:!0})},onCancel:()=>{d(!1)}},(0,_.sprintf)((0,_.__)("Are you sure you want to delete the %s template? It may be used by other pages or posts."),u))))}function Gn(){const{template:e}=(0,c.useSelect)((e=>{const{getEditedPostTemplate:t}=e(Dt);return{template:t()}}),[]),{editEntityRecord:t}=(0,c.useDispatch)(ke.store),{getEditorSettings:n}=(0,c.useSelect)(T.store),{updateEditorSettings:l}=(0,c.useDispatch)(T.store);if(!e.is_custom||e.has_theme_file)return null;let a=(0,_.__)("Default");return null!=e&&e.title?a=e.title:e&&(a=e.slug),(0,r.createElement)(E.TextControl,{label:(0,_.__)("Title"),value:a,help:(0,_.__)('Give the template a title that indicates its purpose, e.g. "Full Width".'),onChange:a=>{const o=n(),r=(0,g.mapValues)(o.availableTemplates,((t,n)=>n!==e.slug?t:a));l({...o,availableTemplates:r}),t("postType","wp_template",e.id,{title:a})}})}function zn(){const{description:e,title:t}=(0,c.useSelect)((e=>{const{getEditedPostTemplate:t}=e(Dt);return{title:t().title,description:t().description}}),[]);return e?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(E.__experimentalHeading,{level:4,weight:600},t),(0,r.createElement)(E.__experimentalText,{className:"edit-post-template-details__description",size:"body",as:"p",style:{marginTop:"12px"}},e)):null}var Hn=function(){const{template:e,isEditing:t,title:n}=(0,c.useSelect)((e=>{const{isEditingTemplate:t,getEditedPostTemplate:n}=e(Dt),{getEditedPostAttribute:l}=e(T.store),a=t();return{template:a?n():null,isEditing:a,title:l("title")?l("title"):(0,_.__)("Untitled")}}),[]),{clearSelectedBlock:l}=(0,c.useDispatch)(h.store),{setIsEditingTemplate:a}=(0,c.useDispatch)(Dt);if(!t||!e)return null;let o=(0,_.__)("Default");null!=e&&e.title?o=e.title:e&&(o=e.slug);const s=!!(e.custom||e.wp_id||e.description);return(0,r.createElement)("div",{className:"edit-post-template-top-area"},(0,r.createElement)(E.Button,{className:"edit-post-template-post-title",isLink:!0,showTooltip:!0,label:(0,_.sprintf)((0,_.__)("Edit %s"),n),onClick:()=>{l(),a(!1)}},n),s?(0,r.createElement)(E.Dropdown,{position:"bottom center",contentClassName:"edit-post-template-top-area__popover",renderToggle:e=>{let{onToggle:t}=e;return(0,r.createElement)(E.Button,{className:"edit-post-template-title",isLink:!0,icon:Vn,showTooltip:!0,onClick:t,label:(0,_.__)("Template Options")},o)},renderContent:()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Gn,null),(0,r.createElement)(zn,null),(0,r.createElement)(Un,null))}):(0,r.createElement)(E.__experimentalText,{className:"edit-post-template-title",size:"body",style:{lineHeight:"24px"}},o))};var $n=function(e){let{setEntitiesSavedStatesCallback:t}=e;const{hasActiveMetaboxes:n,isPublishSidebarOpened:l,isSaving:a,showIconLabels:o,hasReducedUI:s}=(0,c.useSelect)((e=>({hasActiveMetaboxes:e(Dt).hasMetaBoxes(),isPublishSidebarOpened:e(Dt).isPublishSidebarOpened(),isSaving:e(Dt).isSavingMetaBoxes(),showIconLabels:e(Dt).isFeatureActive("showIconLabels"),hasReducedUI:e(Dt).isFeatureActive("reducedUI")})),[]),i=(0,b.useViewportMatch)("large"),d=L()("edit-post-header",{"has-reduced-ui":s});return(0,r.createElement)("div",{className:d},(0,r.createElement)(Fn.Slot,null,(0,r.createElement)(wn,{showTooltip:!0})),(0,r.createElement)("div",{className:"edit-post-header__toolbar"},(0,r.createElement)(Tn,null),(0,r.createElement)(Hn,null)),(0,r.createElement)("div",{className:"edit-post-header__settings"},!l&&(0,r.createElement)(T.PostSavedState,{forceIsDirty:n,forceIsSaving:a,showIconLabels:o}),(0,r.createElement)(An,null),(0,r.createElement)(T.PostPreviewButton,{forceIsAutosaveable:n,forcePreviewLink:a?null:void 0}),(0,r.createElement)(Nn,{forceIsDirty:n,forceIsSaving:a,setEntitiesSavedStatesCallback:t}),(i||!o)&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)(se.Slot,{scope:"core/edit-post"}),(0,r.createElement)(Mn,{showIconLabels:o})),o&&!i&&(0,r.createElement)(Mn,{showIconLabels:o})))};var Wn=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));function qn(){const{insertionPoint:e,showMostUsedBlocks:t}=(0,c.useSelect)((e=>{const{isFeatureActive:t,__experimentalGetInsertionPoint:n}=e(Dt);return{insertionPoint:n(),showMostUsedBlocks:t("mostUsedBlocks")}}),[]),{setIsInserterOpened:n}=(0,c.useDispatch)(Dt),l=(0,b.useViewportMatch)("medium","<"),a=l?"div":E.VisuallyHidden,[o,s]=(0,b.__experimentalUseDialog)({onClose:()=>n(!1),focusOnMount:null}),i=(0,r.useRef)();return(0,r.useEffect)((()=>{i.current.focusSearch()}),[]),(0,r.createElement)("div",p({ref:o},s,{className:"edit-post-editor__inserter-panel"}),(0,r.createElement)(a,{className:"edit-post-editor__inserter-panel-header"},(0,r.createElement)(E.Button,{icon:Wn,label:(0,_.__)("Close block inserter"),onClick:()=>n(!1)})),(0,r.createElement)("div",{className:"edit-post-editor__inserter-panel-content"},(0,r.createElement)(h.__experimentalLibrary,{showMostUsedBlocks:t,showInserterHelpPanel:!0,shouldFocusBlock:l,rootClientId:e.rootClientId,__experimentalInsertionIndex:e.insertionIndex,__experimentalFilterValue:e.filterValue,ref:i})))}function jn(){const{setIsListViewOpened:e}=(0,c.useDispatch)(Dt),t=(0,b.useFocusOnMount)("firstElement"),n=(0,b.useFocusReturn)(),l=(0,b.useFocusReturn)();const a=`edit-post-editor__list-view-panel-label-${(0,b.useInstanceId)(jn)}`;return(0,r.createElement)("div",{"aria-labelledby":a,className:"edit-post-editor__list-view-panel",onKeyDown:function(t){t.keyCode!==C.ESCAPE||t.defaultPrevented||(t.preventDefault(),e(!1))}},(0,r.createElement)("div",{className:"edit-post-editor__list-view-panel-header",ref:n},(0,r.createElement)("strong",{id:a},(0,_.__)("List View")),(0,r.createElement)(E.Button,{icon:V,label:(0,_.__)("Close List View Sidebar"),onClick:()=>e(!1)})),(0,r.createElement)("div",{className:"edit-post-editor__list-view-panel-content",ref:(0,b.useMergeRefs)([l,t])},(0,r.createElement)(h.__experimentalListView,{showNestedBlocks:!0,__experimentalFeatures:!0,__experimentalPersistentListViewFeatures:!0})))}var Kn=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"}));var Xn=e=>{let{sidebarName:t}=e;const{openGeneralSidebar:n}=(0,c.useDispatch)(Dt),l=()=>n("edit-post/document"),{documentLabel:a,isTemplateMode:o}=(0,c.useSelect)((e=>({documentLabel:e(T.store).getPostTypeLabel()||(0,_._x)("Document","noun"),isTemplateMode:e(Dt).isEditingTemplate()})),[]),[s,i]="edit-post/document"===t?[(0,_.sprintf)((0,_.__)("%s (selected)"),a),"is-active"]:[a,""],[d,u]="edit-post/block"===t?[(0,_.__)("Block (selected)"),"is-active"]:[(0,_.__)("Block"),""],[m,p]="edit-post/document"===t?[(0,_.__)("Template (selected)"),"is-active"]:[(0,_.__)("Template"),""];return(0,r.createElement)("ul",null,!o&&(0,r.createElement)("li",null,(0,r.createElement)(E.Button,{onClick:l,className:`edit-post-sidebar__panel-tab ${i}`,"aria-label":s,"data-label":a},a)),o&&(0,r.createElement)("li",null,(0,r.createElement)(E.Button,{onClick:l,className:`edit-post-sidebar__panel-tab ${p}`,"aria-label":m,"data-label":(0,_.__)("Template")},(0,_.__)("Template"))),(0,r.createElement)("li",null,(0,r.createElement)(E.Button,{onClick:()=>n("edit-post/block"),className:`edit-post-sidebar__panel-tab ${u}`,"aria-label":d,"data-label":(0,_.__)("Block")},(0,_.__)("Block"))))};var Qn=function(){return(0,r.createElement)(T.PostVisibilityCheck,{render:e=>{let{canEdit:t}=e;return(0,r.createElement)(E.PanelRow,{className:"edit-post-post-visibility"},(0,r.createElement)("span",null,(0,_.__)("Visibility")),!t&&(0,r.createElement)("span",null,(0,r.createElement)(T.PostVisibilityLabel,null)),t&&(0,r.createElement)(E.Dropdown,{position:"bottom left",contentClassName:"edit-post-post-visibility__dialog",renderToggle:e=>{let{isOpen:t,onToggle:n}=e;return(0,r.createElement)(E.Button,{"aria-expanded":t,className:"edit-post-post-visibility__toggle",onClick:n,variant:"tertiary"},(0,r.createElement)(T.PostVisibilityLabel,null))},renderContent:()=>(0,r.createElement)(T.PostVisibility,null)}))}})};function Yn(){return(0,r.createElement)(T.PostTrashCheck,null,(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostTrash,null)))}var Zn=function(){const e=(0,r.useRef)();return(0,r.createElement)(T.PostScheduleCheck,null,(0,r.createElement)(E.PanelRow,{className:"edit-post-post-schedule",ref:e},(0,r.createElement)("span",null,(0,_.__)("Publish")),(0,r.createElement)(E.Dropdown,{popoverProps:{anchorRef:e.current},position:"bottom left",contentClassName:"edit-post-post-schedule__dialog",renderToggle:e=>{let{onToggle:t,isOpen:n}=e;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(E.Button,{className:"edit-post-post-schedule__toggle",onClick:t,"aria-expanded":n,variant:"tertiary"},(0,r.createElement)(T.PostScheduleLabel,null)))},renderContent:()=>(0,r.createElement)(T.PostSchedule,null)})))};var Jn=function(){return(0,r.createElement)(T.PostStickyCheck,null,(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostSticky,null)))};var el=function(){return(0,r.createElement)(T.PostAuthorCheck,null,(0,r.createElement)(E.PanelRow,{className:"edit-post-post-author"},(0,r.createElement)(T.PostAuthor,null)))};var tl=function(){return(0,r.createElement)(T.PostSlugCheck,null,(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostSlug,null)))};var nl=function(){return(0,r.createElement)(T.PostFormatCheck,null,(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostFormat,null)))};var ll=function(){return(0,r.createElement)(T.PostPendingStatusCheck,null,(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostPendingStatus,null)))};const{Fill:al,Slot:ol}=(0,E.createSlotFill)("PluginPostStatusInfo"),rl=e=>{let{children:t,className:n}=e;return(0,r.createElement)(al,null,(0,r.createElement)(E.PanelRow,{className:n},t))};rl.Slot=ol;var sl=rl;const il="post-status";var cl=(0,b.compose)([(0,c.withSelect)((e=>{const{isEditorPanelRemoved:t,isEditorPanelOpened:n}=e(Dt);return{isRemoved:t(il),isOpened:n(il)}})),(0,b.ifCondition)((e=>{let{isRemoved:t}=e;return!t})),(0,c.withDispatch)((e=>({onTogglePanel:()=>e(Dt).toggleEditorPanelOpened(il)})))])((function(e){let{isOpened:t,onTogglePanel:n}=e;return(0,r.createElement)(E.PanelBody,{className:"edit-post-post-status",title:(0,_.__)("Status & visibility"),opened:t,onToggle:n},(0,r.createElement)(sl.Slot,null,(e=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Qn,null),(0,r.createElement)(Zn,null),(0,r.createElement)(nl,null),(0,r.createElement)(Jn,null),(0,r.createElement)(ll,null),(0,r.createElement)(tl,null),(0,r.createElement)(el,null),e,(0,r.createElement)(Yn,null)))))}));var dl=function(){return(0,r.createElement)(T.PostLastRevisionCheck,null,(0,r.createElement)(E.PanelBody,{className:"edit-post-last-revision__panel"},(0,r.createElement)(T.PostLastRevision,null)))};var ul=(0,b.compose)((0,c.withSelect)(((e,t)=>{const n=(0,g.get)(t.taxonomy,["slug"]),l=n?`taxonomy-panel-${n}`:"";return{panelName:l,isEnabled:!!n&&e(Dt).isEditorPanelEnabled(l),isOpened:!!n&&e(Dt).isEditorPanelOpened(l)}})),(0,c.withDispatch)(((e,t)=>({onTogglePanel:()=>{e(Dt).toggleEditorPanelOpened(t.panelName)}}))))((function(e){let{isEnabled:t,taxonomy:n,isOpened:l,onTogglePanel:a,children:o}=e;if(!t)return null;const s=(0,g.get)(n,["labels","menu_name"]);return s?(0,r.createElement)(E.PanelBody,{title:s,opened:l,onToggle:a},o):null}));var ml=function(){return(0,r.createElement)(T.PostTaxonomiesCheck,null,(0,r.createElement)(T.PostTaxonomies,{taxonomyWrapper:(e,t)=>(0,r.createElement)(ul,{taxonomy:t},e)}))};const pl="featured-image";const gl=(0,c.withSelect)((e=>{const{getEditedPostAttribute:t}=e(T.store),{getPostType:n}=e(ke.store),{isEditorPanelEnabled:l,isEditorPanelOpened:a}=e(Dt);return{postType:n(t("type")),isEnabled:l(pl),isOpened:a(pl)}})),El=(0,c.withDispatch)((e=>{const{toggleEditorPanelOpened:t}=e(Dt);return{onTogglePanel:(0,g.partial)(t,pl)}}));var hl=(0,b.compose)(gl,El)((function(e){let{isEnabled:t,isOpened:n,postType:l,onTogglePanel:a}=e;return t?(0,r.createElement)(T.PostFeaturedImageCheck,null,(0,r.createElement)(E.PanelBody,{title:(0,g.get)(l,["labels","featured_image"],(0,_.__)("Featured image")),opened:n,onToggle:a},(0,r.createElement)(T.PostFeaturedImage,null))):null}));const _l="post-excerpt";var bl=(0,b.compose)([(0,c.withSelect)((e=>({isEnabled:e(Dt).isEditorPanelEnabled(_l),isOpened:e(Dt).isEditorPanelOpened(_l)}))),(0,c.withDispatch)((e=>({onTogglePanel:()=>e(Dt).toggleEditorPanelOpened(_l)})))])((function(e){let{isEnabled:t,isOpened:n,onTogglePanel:l}=e;return t?(0,r.createElement)(T.PostExcerptCheck,null,(0,r.createElement)(E.PanelBody,{title:(0,_.__)("Excerpt"),opened:n,onToggle:l},(0,r.createElement)(T.PostExcerpt,null))):null}));const fl="post-link";var vl=(0,b.compose)([(0,c.withSelect)((e=>{const{isPermalinkEditable:t,getCurrentPost:n,isCurrentPostPublished:l,getPermalinkParts:a,getEditedPostAttribute:o,getEditedPostSlug:r}=e(T.store),{isEditorPanelEnabled:s,isEditorPanelOpened:i}=e(Dt),{getPostType:c}=e(ke.store),{link:d}=n(),u=c(o("type")),m=a();return{postLink:d,isEditable:t(),isPublished:l(),isOpened:i(fl),isEnabled:s(fl),isViewable:(0,g.get)(u,["viewable"],!1),postSlug:(0,k.safeDecodeURIComponent)(r()),postTypeLabel:(0,g.get)(u,["labels","view_item"]),hasPermalinkParts:!!m,permalinkPrefix:null==m?void 0:m.prefix,permalinkSuffix:null==m?void 0:m.suffix}})),(0,b.ifCondition)((e=>{let{isEnabled:t,postLink:n,isViewable:l,hasPermalinkParts:a}=e;return t&&n&&l&&a})),(0,c.withDispatch)((e=>{const{toggleEditorPanelOpened:t}=e(Dt),{editPost:n}=e(T.store);return{onTogglePanel:()=>t(fl),editPermalink:e=>{n({slug:e})}}}))])((function(e){let{isOpened:t,onTogglePanel:n,isEditable:l,postLink:a,permalinkPrefix:o,permalinkSuffix:s,editPermalink:i,postSlug:c,postTypeLabel:d}=e;const[u,m]=(0,r.useState)(!1);let p,g,h;return l&&(p=o&&(0,r.createElement)("span",{className:"edit-post-post-link__link-prefix"},o),g=c&&(0,r.createElement)("span",{className:"edit-post-post-link__link-post-name"},c),h=s&&(0,r.createElement)("span",{className:"edit-post-post-link__link-suffix"},s)),(0,r.createElement)(E.PanelBody,{title:(0,_.__)("Permalink"),opened:t,onToggle:n},l&&(0,r.createElement)("div",{className:"editor-post-link"},(0,r.createElement)(E.TextControl,{label:(0,_.__)("URL Slug"),value:u?"":c,autoComplete:"off",spellCheck:"false",onChange:e=>{i(e),e?u&&m(!1):u||m(!0)},onBlur:e=>{i((0,k.cleanForSlug)(e.target.value)),u&&m(!1)}}),(0,r.createElement)("p",null,(0,_.__)("The last part of the URL.")," ",(0,r.createElement)(E.ExternalLink,{href:(0,_.__)("https://wordpress.org/support/article/settings-sidebar/#permalink")},(0,_.__)("Read about permalinks")))),(0,r.createElement)("h3",{className:"edit-post-post-link__preview-label"},d||(0,_.__)("View post")),(0,r.createElement)("div",{className:"edit-post-post-link__preview-link-container"},(0,r.createElement)(E.ExternalLink,{className:"edit-post-post-link__link",href:a,target:"_blank"},l?(0,r.createElement)(r.Fragment,null,p,g,h):a)))}));const yl="discussion-panel";var wl=(0,b.compose)([(0,c.withSelect)((e=>({isEnabled:e(Dt).isEditorPanelEnabled(yl),isOpened:e(Dt).isEditorPanelOpened(yl)}))),(0,c.withDispatch)((e=>({onTogglePanel:()=>e(Dt).toggleEditorPanelOpened(yl)})))])((function(e){let{isEnabled:t,isOpened:n,onTogglePanel:l}=e;return t?(0,r.createElement)(T.PostTypeSupportCheck,{supportKeys:["comments","trackbacks"]},(0,r.createElement)(E.PanelBody,{title:(0,_.__)("Discussion"),opened:n,onToggle:l},(0,r.createElement)(T.PostTypeSupportCheck,{supportKeys:"comments"},(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostComments,null))),(0,r.createElement)(T.PostTypeSupportCheck,{supportKeys:"trackbacks"},(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PostPingbacks,null))))):null}));const Sl="page-attributes";var kl=function(){const{isEnabled:e,isOpened:t,postType:n}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(T.store),{isEditorPanelEnabled:n,isEditorPanelOpened:l}=e(Dt),{getPostType:a}=e(ke.store);return{isEnabled:n(Sl),isOpened:l(Sl),postType:a(t("type"))}}),[]),{toggleEditorPanelOpened:l}=(0,c.useDispatch)(Dt);if(!e||!n)return null;const a=(0,g.partial)(l,Sl);return(0,r.createElement)(T.PageAttributesCheck,null,(0,r.createElement)(E.PanelBody,{title:(0,g.get)(n,["labels","attributes"],(0,_.__)("Page attributes")),opened:t,onToggle:a},(0,r.createElement)(T.PageAttributesParent,null),(0,r.createElement)(E.PanelRow,null,(0,r.createElement)(T.PageAttributesOrder,null))))};var Pl=function(e){let{location:t}=e;const n=(0,r.useRef)(null),l=(0,r.useRef)(null);(0,r.useEffect)((()=>(l.current=document.querySelector(".metabox-location-"+t),l.current&&n.current.appendChild(l.current),()=>{l.current&&document.querySelector("#metaboxes").appendChild(l.current)})),[t]);const a=(0,c.useSelect)((e=>e(Dt).isSavingMetaBoxes()),[]),o=L()("edit-post-meta-boxes-area",`is-${t}`,{"is-loading":a});return(0,r.createElement)("div",{className:o},a&&(0,r.createElement)(E.Spinner,null),(0,r.createElement)("div",{className:"edit-post-meta-boxes-area__container",ref:n}),(0,r.createElement)("div",{className:"edit-post-meta-boxes-area__clear"}))};class Tl extends r.Component{componentDidMount(){this.updateDOM()}componentDidUpdate(e){this.props.isVisible!==e.isVisible&&this.updateDOM()}updateDOM(){const{id:e,isVisible:t}=this.props,n=document.getElementById(e);n&&(t?n.classList.remove("is-hidden"):n.classList.add("is-hidden"))}render(){return null}}var xl=(0,c.withSelect)(((e,t)=>{let{id:n}=t;return{isVisible:e(Dt).isEditorPanelEnabled(`meta-box-${n}`)}}))(Tl);function Cl(e){let{location:t}=e;const n=(0,c.useRegistry)(),{metaBoxes:l,areMetaBoxesInitialized:a,isEditorReady:o}=(0,c.useSelect)((e=>{const{__unstableIsEditorReady:n}=e(T.store),{getMetaBoxesPerLocation:l,areMetaBoxesInitialized:a}=e(Dt);return{metaBoxes:l(t),areMetaBoxesInitialized:a(),isEditorReady:n()}}),[t]);return(0,r.useEffect)((()=>{o&&!a&&n.dispatch(Dt).initializeMetaBoxes()}),[o,a]),a?(0,r.createElement)(r.Fragment,null,(0,g.map)(l,(e=>{let{id:t}=e;return(0,r.createElement)(xl,{key:t,id:t})})),(0,r.createElement)(Pl,{location:t})):null}window.wp.warning;const{Fill:Bl,Slot:Il}=(0,E.createSlotFill)("PluginDocumentSettingPanel"),Ml=(0,b.compose)((0,S.withPluginContext)(((e,t)=>(void 0===t.name&&"undefined"!=typeof process&&process.env,{icon:t.icon||e.icon,panelName:`${e.name}/${t.name}`}))),(0,c.withSelect)(((e,t)=>{let{panelName:n}=t;return{opened:e(Dt).isEditorPanelOpened(n),isEnabled:e(Dt).isEditorPanelEnabled(n)}})),(0,c.withDispatch)(((e,t)=>{let{panelName:n}=t;return{onToggle:()=>e(Dt).toggleEditorPanelOpened(n)}})))((e=>{let{isEnabled:t,panelName:n,opened:l,onToggle:a,className:o,title:s,icon:i,children:c}=e;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(un,{label:s,panelName:n}),(0,r.createElement)(Bl,null,t&&(0,r.createElement)(E.PanelBody,{className:o,title:s,icon:i,opened:l,onToggle:a},c)))}));Ml.Slot=Il;var Nl=Ml;function Al(e){let{className:t,...n}=e;const{postTitle:l,shortcut:a,showIconLabels:o}=(0,c.useSelect)((e=>({postTitle:e(T.store).getEditedPostAttribute("title"),shortcut:e(zt.store).getShortcutRepresentation("core/edit-post/toggle-sidebar"),showIconLabels:e(Dt).isFeatureActive("showIconLabels")})),[]);return(0,r.createElement)(de,p({panelClassName:t,className:"edit-post-sidebar",smallScreenTitle:l||(0,_.__)("(no title)"),scope:"core/edit-post",toggleShortcut:a,showIconLabels:o},n))}var Ll=function(e){let{isPostsPage:t}=e;const[n,l]=(0,r.useState)(!1),[a,o]=(0,r.useState)(!1),[i,d]=(0,r.useState)(""),{template:u,supportsTemplateMode:m,defaultTemplate:p}=(0,c.useSelect)((e=>{var t,n;const{getCurrentPostType:l,getEditorSettings:a}=e(T.store),{getPostType:o}=e(ke.store),{getEditedPostTemplate:r}=e(Dt),s=null!==(t=null===(n=o(l()))||void 0===n?void 0:n.viewable)&&void 0!==t&&t,i=a().supportsTemplateMode&&s;return{template:i&&r(),supportsTemplateMode:i,defaultTemplate:a().defaultBlockTemplate}}),[]),{__unstableCreateTemplate:h,__unstableSwitchToTemplateMode:b}=(0,c.useDispatch)(Dt);if(!m)return null;const f=(0,_.__)("Custom Template");return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"edit-post-template__actions"},!!u&&(0,r.createElement)(E.Button,{variant:"link",onClick:()=>b()},(0,_.__)("Edit")),!t&&(0,r.createElement)(E.Button,{variant:"link",onClick:()=>l(!0)},(0,_._x)("New","action"))),n&&(0,r.createElement)(E.Modal,{title:(0,_.__)("Create custom template"),closeLabel:(0,_.__)("Close"),onRequestClose:()=>{l(!1),d("")},overlayClassName:"edit-post-template__modal"},(0,r.createElement)("form",{onSubmit:async function(e){if(e.preventDefault(),a)return;o(!0);const t=null!=p?p:(0,s.serialize)([(0,s.createBlock)("core/group",{tagName:"header",layout:{inherit:!0}},[(0,s.createBlock)("core/site-title"),(0,s.createBlock)("core/site-tagline")]),(0,s.createBlock)("core/separator"),(0,s.createBlock)("core/group",{tagName:"main"},[(0,s.createBlock)("core/group",{layout:{inherit:!0}},[(0,s.createBlock)("core/post-title")]),(0,s.createBlock)("core/post-content",{layout:{inherit:!0}})])]);await h({slug:"wp-custom-template-"+(0,g.kebabCase)(i||f),content:t,title:i||f}),o(!1),l(!1),b(!0)}},(0,r.createElement)(E.Flex,{align:"flex-start",gap:8},(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(E.TextControl,{label:(0,_.__)("Name"),value:i,onChange:d,placeholder:f,disabled:a,help:(0,_.__)('Describe the purpose of the template, e.g. "Full Width". Custom templates can be applied to any post or page.')}))),(0,r.createElement)(E.Flex,{className:"edit-post-template__modal-actions",justify:"flex-end",expanded:!1},(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(E.Button,{variant:"tertiary",onClick:()=>{l(!1),d("")}},(0,_.__)("Cancel"))),(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(E.Button,{variant:"primary",type:"submit",isBusy:a,"aria-disabled":a},(0,_.__)("Create")))))))};const Ol="template";var Dl=function(){const{isEnabled:e,isOpened:t,isPostsPage:n,selectedTemplate:l,availableTemplates:a,fetchedTemplates:o,isViewable:s,template:i,supportsTemplateMode:d,canUserCreate:u}=(0,c.useSelect)((e=>{var t,n;const{isEditorPanelEnabled:l,isEditorPanelOpened:a,getEditedPostTemplate:o}=e(Dt),{getEditedPostAttribute:r,getEditorSettings:s,getCurrentPostId:i,getCurrentPostType:c}=e(T.store),{getPostType:d,getEntityRecord:u,getEntityRecords:m,canUser:p}=e(ke.store),g=i(),E=c(),h=u("root","site"),_=null!==(t=null===(n=d(E))||void 0===n?void 0:n.viewable)&&void 0!==t&&t,b=e(T.store).getEditorSettings().supportsTemplateMode&&_,f=m("postType","wp_template",{post_type:E,per_page:-1});return{isEnabled:l(Ol),isOpened:a(Ol),isPostsPage:g===(null==h?void 0:h.page_for_posts),selectedTemplate:r("template"),availableTemplates:s().availableTemplates,fetchedTemplates:f,template:b&&o(),isViewable:_,supportsTemplateMode:b,canUserCreate:p("create","templates")}}),[]),m=(0,r.useMemo)((()=>({...a,...(0,g.fromPairs)((null!=o?o:[]).map((e=>{let{slug:t,title:n}=e;return[t,n.rendered]})))})),[a,o]),{toggleEditorPanelOpened:p}=(0,c.useDispatch)(Dt),{editPost:h}=(0,c.useDispatch)(T.store);if(!e||!s||(0,g.isEmpty)(a)&&(!d||!u))return null;const b=(0,g.partial)(p,Ol);let f=(0,_.__)("Template");var v;return i&&(f=(0,_.sprintf)((0,_.__)("Template: %s"),null!==(v=null==i?void 0:i.title)&&void 0!==v?v:i.slug)),(0,r.createElement)(E.PanelBody,{title:f,opened:t,onToggle:b},n?(0,r.createElement)(E.Notice,{className:"edit-post-template__notice",status:"warning",isDismissible:!1},(0,_.__)("The posts page template cannot be changed.")):(0,r.createElement)(E.SelectControl,{hideLabelFromVision:!0,label:(0,_.__)("Template:"),value:Object.keys(m).includes(l)?l:"",onChange:e=>{h({template:e||""})},options:(0,g.map)(m,((e,t)=>({value:t,label:e})))}),u&&(0,r.createElement)(Ll,{isPostsPage:n}))};var Rl=(0,r.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(y.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}));var Fl=function(){const e=(0,c.useSelect)((e=>{const{getEditedPostTemplate:t}=e(Dt);return t()}),[]);return e?(0,r.createElement)(E.PanelBody,null,(0,r.createElement)(E.Flex,{align:"flex-start",gap:"3"},(0,r.createElement)(E.FlexItem,null,(0,r.createElement)(he,{icon:Rl})),(0,r.createElement)(E.FlexBlock,null,(0,r.createElement)("h2",{className:"edit-post-template-summary__title"},(null==e?void 0:e.title)||(null==e?void 0:e.slug)),(0,r.createElement)("p",null,null==e?void 0:e.description)))):null};const Vl=r.Platform.select({web:!0,native:!1});var Ul=()=>{const{sidebarName:e,keyboardShortcut:t,isTemplateMode:n}=(0,c.useSelect)((e=>{let t=e(Z).getActiveComplementaryArea(Dt.name);["edit-post/document","edit-post/block"].includes(t)||(e(h.store).getBlockSelectionStart()&&(t="edit-post/block"),t="edit-post/document");return{sidebarName:t,keyboardShortcut:e(zt.store).getShortcutRepresentation("core/edit-post/toggle-sidebar"),isTemplateMode:e(Dt).isEditingTemplate()}}),[]);return(0,r.createElement)(Al,{identifier:e,header:(0,r.createElement)(Xn,{sidebarName:e}),closeLabel:(0,_.__)("Close settings"),headerClassName:"edit-post-sidebar__panel-tabs",title:(0,_.__)("Settings"),toggleShortcut:t,icon:Kn,isActiveByDefault:Vl},!n&&"edit-post/document"===e&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)(cl,null),(0,r.createElement)(Dl,null),(0,r.createElement)(Nl.Slot,null),(0,r.createElement)(dl,null),(0,r.createElement)(vl,null),(0,r.createElement)(ml,null),(0,r.createElement)(hl,null),(0,r.createElement)(bl,null),(0,r.createElement)(wl,null),(0,r.createElement)(kl,null),(0,r.createElement)(Cl,{location:"side"})),n&&"edit-post/document"===e&&(0,r.createElement)(Fl,null),"edit-post/block"===e&&(0,r.createElement)(h.BlockInspector,null))};function Gl(e){let{nonAnimatedSrc:t,animatedSrc:n}=e;return(0,r.createElement)("picture",{className:"edit-post-welcome-guide__image"},(0,r.createElement)("source",{srcSet:t,media:"(prefers-reduced-motion: reduce)"}),(0,r.createElement)("img",{src:n,width:"312",height:"240",alt:""}))}function zl(){const{toggleFeature:e}=(0,c.useDispatch)(Dt);return(0,r.createElement)(E.Guide,{className:"edit-post-welcome-guide",contentLabel:(0,_.__)("Welcome to the block editor"),finishButtonText:(0,_.__)("Get started"),onFinish:()=>e("welcomeGuide"),pages:[{image:(0,r.createElement)(Gl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("h1",{className:"edit-post-welcome-guide__heading"},(0,_.__)("Welcome to the block editor")),(0,r.createElement)("p",{className:"edit-post-welcome-guide__text"},(0,_.__)("In the WordPress editor, each paragraph, image, or video is presented as a distinct “block” of content.")))},{image:(0,r.createElement)(Gl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("h1",{className:"edit-post-welcome-guide__heading"},(0,_.__)("Make each block your own")),(0,r.createElement)("p",{className:"edit-post-welcome-guide__text"},(0,_.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")))},{image:(0,r.createElement)(Gl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("h1",{className:"edit-post-welcome-guide__heading"},(0,_.__)("Get to know the block library")),(0,r.createElement)("p",{className:"edit-post-welcome-guide__text"},(0,r.createInterpolateElement)((0,_.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,r.createElement)("img",{alt:(0,_.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})))},{image:(0,r.createElement)(Gl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("h1",{className:"edit-post-welcome-guide__heading"},(0,_.__)("Learn how to use the block editor")),(0,r.createElement)("p",{className:"edit-post-welcome-guide__text"},(0,_.__)("New to the block editor? Want to learn more about using it? "),(0,r.createElement)(E.ExternalLink,{href:(0,_.__)("https://wordpress.org/support/article/wordpress-editor/")},(0,_.__)("Here's a detailed guide."))))}]})}function Hl(){const{toggleFeature:e}=(0,c.useDispatch)(Dt);return(0,r.createElement)(E.Guide,{className:"edit-template-welcome-guide",contentLabel:(0,_.__)("Welcome to the template editor"),finishButtonText:(0,_.__)("Get started"),onFinish:()=>e("welcomeGuideTemplate"),pages:[{image:(0,r.createElement)(Gl,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.gif"}),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("h1",{className:"edit-post-welcome-guide__heading"},(0,_.__)("Welcome to the template editor")),(0,r.createElement)("p",{className:"edit-post-welcome-guide__text"},(0,_.__)("Templates help define the layout of the site. You can customize all aspects of your posts and pages using blocks and patterns in this editor.")))}]})}function $l(){const{isActive:e,isTemplateMode:t}=(0,c.useSelect)((e=>{const{isFeatureActive:t,isEditingTemplate:n}=e(Dt),l=n();return{isActive:t(l?"welcomeGuideTemplate":"welcomeGuide"),isTemplateMode:l}}),[]);return e?t?(0,r.createElement)(Hl,null):(0,r.createElement)(zl,null):null}const{Fill:Wl,Slot:ql}=(0,E.createSlotFill)("PluginPostPublishPanel"),jl=(0,b.compose)((0,S.withPluginContext)(((e,t)=>({icon:t.icon||e.icon}))))((e=>{let{children:t,className:n,title:l,initialOpen:a=!1,icon:o}=e;return(0,r.createElement)(Wl,null,(0,r.createElement)(E.PanelBody,{className:n,initialOpen:a||!l,title:l,icon:o},t))}));jl.Slot=ql;var Kl=jl;const{Fill:Xl,Slot:Ql}=(0,E.createSlotFill)("PluginPrePublishPanel"),Yl=(0,b.compose)((0,S.withPluginContext)(((e,t)=>({icon:t.icon||e.icon}))))((e=>{let{children:t,className:n,title:l,initialOpen:a=!1,icon:o}=e;return(0,r.createElement)(Xl,null,(0,r.createElement)(E.PanelBody,{className:n,initialOpen:a||!l,title:l,icon:o},t))}));Yl.Slot=Ql;var Zl=Yl;const{Fill:Jl,Slot:ea}=(0,E.createSlotFill)("ActionsPanel");function ta(e){let{setEntitiesSavedStatesCallback:t,closeEntitiesSavedStates:n,isEntitiesSavedStatesOpen:l}=e;const{closePublishSidebar:a,togglePublishSidebar:o}=(0,c.useDispatch)(Dt),{publishSidebarOpened:s,hasActiveMetaboxes:i,isSavingMetaBoxes:d,hasNonPostEntityChanges:u}=(0,c.useSelect)((e=>({publishSidebarOpened:e(Dt).isPublishSidebarOpened(),hasActiveMetaboxes:e(Dt).hasMetaBoxes(),isSavingMetaBoxes:e(Dt).isSavingMetaBoxes(),hasNonPostEntityChanges:e(T.store).hasNonPostEntityChanges()})),[]),m=(0,r.useCallback)((()=>t(!0)),[]);let p;return p=s?(0,r.createElement)(T.PostPublishPanel,{onClose:a,forceIsDirty:i,forceIsSaving:d,PrePublishExtension:Zl.Slot,PostPublishExtension:Kl.Slot}):u?(0,r.createElement)("div",{className:"edit-post-layout__toggle-entities-saved-states-panel"},(0,r.createElement)(E.Button,{variant:"secondary",className:"edit-post-layout__toggle-entities-saved-states-panel-button",onClick:m,"aria-expanded":!1},(0,_.__)("Open save panel"))):(0,r.createElement)("div",{className:"edit-post-layout__toggle-publish-panel"},(0,r.createElement)(E.Button,{variant:"secondary",className:"edit-post-layout__toggle-publish-panel-button",onClick:o,"aria-expanded":!1},(0,_.__)("Open publish panel"))),(0,r.createElement)(r.Fragment,null,l&&(0,r.createElement)(T.EntitiesSavedStates,{close:n}),(0,r.createElement)(ea,{bubblesVirtually:!0}),!l&&p)}function na(e){let{onChoosePattern:t}=e;const{blockPatterns:n}=(0,c.useSelect)((e=>{const{__experimentalGetPatternsByBlockTypes:t}=e(h.store);return{blockPatterns:t("core/post-content")}}),[]),l=(0,b.useAsyncList)(n),{resetEditorBlocks:a}=(0,c.useDispatch)(T.store);return(0,r.createElement)(h.__experimentalBlockPatternsList,{blockPatterns:n,shownPatterns:l,onClickPattern:(e,n)=>{a(n),t()}})}const la="INITIAL",aa="PATTERN",oa="CLOSED";function ra(){const[e,t]=(0,r.useState)(la),n=(0,c.useSelect)((t=>{if(e!==la)return!1;const{__experimentalGetPatternsByBlockTypes:n}=t(h.store),{getCurrentPostType:l,getEditedPostContent:a,isEditedPostSaveable:o}=t(T.store),{isEditingTemplate:r,isFeatureActive:s}=t(Dt);return"page"===l()&&!o()&&""===a()&&!r()&&!s("welcomeGuide")&&n("core/post-content").length>=1}),[e]);return(0,r.useEffect)((()=>{n&&t(aa)}),[n]),e===la||e===oa?null:(0,r.createElement)(E.Modal,{className:"edit-post-start-page-options__modal",title:(0,_.__)("Choose a pattern"),closeLabel:(0,_.__)("Cancel"),onRequestClose:()=>{t(oa)}},(0,r.createElement)("div",{className:"edit-post-start-page-options__modal-content"},e===aa&&(0,r.createElement)(na,{onChoosePattern:()=>{t(oa)}})))}const sa={header:(0,_.__)("Editor top bar"),body:(0,_.__)("Editor content"),sidebar:(0,_.__)("Editor settings"),actions:(0,_.__)("Editor publish"),footer:(0,_.__)("Editor footer")};var ia=function(e){let{styles:t}=e;const n=(0,b.useViewportMatch)("medium","<"),l=(0,b.useViewportMatch)("huge",">="),{openGeneralSidebar:a,closeGeneralSidebar:o,setIsInserterOpened:s}=(0,c.useDispatch)(Dt),{createErrorNotice:i}=(0,c.useDispatch)(P.store),{mode:d,isFullscreenActive:u,isRichEditingEnabled:m,sidebarIsOpened:p,hasActiveMetaboxes:g,hasFixedToolbar:f,previousShortcut:v,nextShortcut:y,hasBlockSelected:w,isInserterOpened:k,isListViewOpened:x,showIconLabels:C,hasReducedUI:B,showBlockBreadcrumbs:I,isTemplateMode:M,documentLabel:N}=(0,c.useSelect)((e=>{const{getEditorSettings:t,getPostTypeLabel:n}=e(T.store),l=t(),a=n();return{isTemplateMode:e(Dt).isEditingTemplate(),hasFixedToolbar:e(Dt).isFeatureActive("fixedToolbar"),sidebarIsOpened:!(!e(Z).getActiveComplementaryArea(Dt.name)&&!e(Dt).isPublishSidebarOpened()),isFullscreenActive:e(Dt).isFeatureActive("fullscreenMode"),isInserterOpened:e(Dt).isInserterOpened(),isListViewOpened:e(Dt).isListViewOpened(),mode:e(Dt).getEditorMode(),isRichEditingEnabled:l.richEditingEnabled,hasActiveMetaboxes:e(Dt).hasMetaBoxes(),previousShortcut:e(zt.store).getAllShortcutKeyCombinations("core/edit-post/previous-region"),nextShortcut:e(zt.store).getAllShortcutKeyCombinations("core/edit-post/next-region"),showIconLabels:e(Dt).isFeatureActive("showIconLabels"),hasReducedUI:e(Dt).isFeatureActive("reducedUI"),showBlockBreadcrumbs:e(Dt).isFeatureActive("showBlockBreadcrumbs"),documentLabel:a||(0,_._x)("Document","noun")}}),[]),A=L()("edit-post-layout","is-mode-"+d,{"is-sidebar-opened":p,"has-fixed-toolbar":f,"has-metaboxes":g,"show-icon-labels":C});(0,r.useEffect)((()=>{p&&!l&&s(!1)}),[p,l]),(0,r.useEffect)((()=>{k&&!l&&o()}),[k,l]);const[O,D]=(0,r.useState)(!1),R=(0,r.useCallback)((e=>{"function"==typeof O&&O(e),D(!1)}),[O]),F=x?(0,_.__)("List View"):(0,_.__)("Block Library");return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(ue,{isActive:u}),(0,r.createElement)(vn,null),(0,r.createElement)(T.UnsavedChangesWarning,null),(0,r.createElement)(T.AutosaveMonitor,null),(0,r.createElement)(T.LocalAutosaveMonitor,null),(0,r.createElement)(Kt,null),(0,r.createElement)(T.EditorKeyboardShortcutsRegister,null),(0,r.createElement)(Ul,null),(0,r.createElement)(me,{className:A,labels:{...sa,secondarySidebar:F},header:(0,r.createElement)($n,{setEntitiesSavedStatesCallback:D}),secondarySidebar:"visual"===d&&k?(0,r.createElement)(qn,null):"visual"===d&&x?(0,r.createElement)(jn,null):null,sidebar:(!n||p)&&(0,r.createElement)(r.Fragment,null,!n&&!p&&(0,r.createElement)("div",{className:"edit-post-layout__toggle-sidebar-panel"},(0,r.createElement)(E.Button,{variant:"secondary",className:"edit-post-layout__toggle-sidebar-panel-button",onClick:()=>a(w?"edit-post/block":"edit-post/document"),"aria-expanded":!1},w?(0,_.__)("Open block settings"):(0,_.__)("Open document settings"))),(0,r.createElement)(de.Slot,{scope:"core/edit-post"})),notices:(0,r.createElement)(T.EditorSnackbars,null),content:(0,r.createElement)(r.Fragment,null,(0,r.createElement)(T.EditorNotices,null),("text"===d||!m)&&(0,r.createElement)(Ht,null),m&&"visual"===d&&(0,r.createElement)(jt,{styles:t}),!M&&(0,r.createElement)("div",{className:"edit-post-layout__metaboxes"},(0,r.createElement)(Cl,{location:"normal"}),(0,r.createElement)(Cl,{location:"advanced"})),n&&p&&(0,r.createElement)(E.ScrollLock,null),(0,r.createElement)(h.BlockStyles.Slot,{scope:"core/block-inspector"})),footer:!B&&I&&!n&&m&&"visual"===d&&(0,r.createElement)("div",{className:"edit-post-layout__footer"},(0,r.createElement)(h.BlockBreadcrumb,{rootLabelText:N})),actions:(0,r.createElement)(ta,{closeEntitiesSavedStates:R,isEntitiesSavedStatesOpen:O,setEntitiesSavedStatesCallback:D}),shortcuts:{previous:v,next:y}}),(0,r.createElement)(bn,null),(0,r.createElement)(ln,null),(0,r.createElement)($l,null),(0,r.createElement)(ra,null),(0,r.createElement)(E.Popover.Slot,null),(0,r.createElement)(S.PluginArea,{onError:function(e){i((0,_.sprintf)((0,_.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}))};function ca(e){let{postId:t}=e;return(e=>{const{hasBlockSelection:t,isEditorSidebarOpened:n}=(0,c.useSelect)((e=>({hasBlockSelection:!!e(h.store).getBlockSelectionStart(),isEditorSidebarOpened:e(Ot).isEditorSidebarOpened()})),[e]),{openGeneralSidebar:l}=(0,c.useDispatch)(Ot);(0,r.useEffect)((()=>{n&&l(t?"edit-post/block":"edit-post/document")}),[t,n])})(t),(e=>{const{newPermalink:t}=(0,c.useSelect)((e=>({newPermalink:e(T.store).getCurrentPost().link})),[e]),n=(0,r.useRef)();(0,r.useEffect)((()=>{n.current=document.querySelector("#wp-admin-bar-preview a")||document.querySelector("#wp-admin-bar-view a")}),[e]),(0,r.useEffect)((()=>{t&&n.current&&n.current.setAttribute("href",t)}),[t])})(t),null}var da=function(e){let{postId:t,postType:n,settings:l,initialEdits:a,onError:o,...i}=e;const{hasFixedToolbar:d,focusMode:m,hasReducedUI:h,hasThemeStyles:_,post:b,preferredStyleVariations:f,hiddenBlockTypes:v,blockTypes:y,keepCaretInsideBlock:w,isTemplateMode:S,template:k}=(0,c.useSelect)((e=>{var l,a;const{isFeatureActive:o,__experimentalGetPreviewDeviceType:r,isEditingTemplate:i,getEditedPostTemplate:c,getHiddenBlockTypes:d}=e(Dt),{getEntityRecord:m,getPostType:p,getEntityRecords:g}=e(ke.store),{getEditorSettings:E}=e(T.store),{getBlockTypes:h}=e(s.store);let _;if(["wp_template","wp_template_part"].includes(n)){const e=g("postType",n,{wp_id:t});_=null==e?void 0:e[0]}else _=m("postType",n,t);const b=E().supportsTemplateMode,f=null!==(l=null===(a=p(n))||void 0===a?void 0:a.viewable)&&void 0!==l&&l;return{hasFixedToolbar:o("fixedToolbar")||"Desktop"!==r(),focusMode:o("focusMode"),hasReducedUI:o("reducedUI"),hasThemeStyles:o("themeStyles"),preferredStyleVariations:e(u.store).get("core/edit-post","preferredStyleVariations"),hiddenBlockTypes:d(),blockTypes:h(),keepCaretInsideBlock:o("keepCaretInsideBlock"),isTemplateMode:i(),template:b&&f?c():null,post:_}}),[n,t]),{updatePreferredStyleVariations:P,setIsInserterOpened:x}=(0,c.useDispatch)(Dt),C=(0,r.useMemo)((()=>{const e={...l,__experimentalPreferredStyleVariations:{value:f,onChange:P},hasFixedToolbar:d,focusMode:m,hasReducedUI:h,__experimentalSetIsInserterOpened:x,keepCaretInsideBlock:w,defaultAllowedBlockTypes:l.allowedBlockTypes};if((0,g.size)(v)>0){const t=!0===l.allowedBlockTypes?(0,g.map)(y,"name"):l.allowedBlockTypes||[];e.allowedBlockTypes=(0,g.without)(t,...v)}return e}),[l,d,m,h,v,y,f,x,P,w]),B=(0,r.useMemo)((()=>{const e=[],t=[];(0,g.forEach)(l.styles,(n=>{n.__unstableType&&"theme"!==n.__unstableType?t.push(n):e.push(n)}));const n=[...l.defaultEditorStyles,...t];return _&&e.length?l.styles:n}),[l,_]);return b?(0,r.createElement)(r.StrictMode,null,(0,r.createElement)(zt.ShortcutProvider,null,(0,r.createElement)(E.SlotFillProvider,null,(0,r.createElement)(T.EditorProvider,p({settings:C,post:b,initialEdits:a,useSubRegistry:!1,__unstableTemplate:S?k:void 0},i),(0,r.createElement)(T.ErrorBoundary,{onError:o},(0,r.createElement)(ca,{postId:t}),(0,r.createElement)(ia,{styles:B})),(0,r.createElement)(T.PostLockedModal,null))))):null};const ua=(e,t)=>{return!Array.isArray(t)||(n=e,l=t,0===(0,g.difference)(n,l).length);var n,l};var ma=e=>{let{allowedBlocks:t,icon:n,label:l,onClick:a,small:o,role:s}=e;return(0,r.createElement)(h.BlockSettingsMenuControls,null,(e=>{let{selectedBlocks:i,onClose:c}=e;return ua(i,t)?(0,r.createElement)(E.MenuItem,{onClick:(0,b.compose)(a,c),icon:n,label:o?l:void 0,role:s},!o&&l):null}))},pa=(0,b.compose)((0,S.withPluginContext)(((e,t)=>{var n;return{as:null!==(n=t.as)&&void 0!==n?n:E.MenuItem,icon:t.icon||e.icon,name:"core/edit-post/plugin-more-menu"}})))(le);function ga(e){return(0,r.createElement)(oe,p({__unstableExplicitMenuItem:!0,scope:"core/edit-post"},e))}function Ea(e,t,n,l,a){(0,r.unmountComponentAtNode)(n);const o=Ea.bind(null,e,t,n,l,a);(0,r.render)((0,r.createElement)(da,{settings:l,onError:o,postId:t,postType:e,initialEdits:a,recovery:!0}),n)}function ha(e,t,n,l,a){(0,d.addFilter)("blockEditor.__unstableCanInsertBlockType","removeTemplatePartsFromInserter",((e,t)=>!(!(0,c.select)(Dt).isEditingTemplate()&&"core/template-part"===t.name)&&e));const o=document.getElementById(e),m=Ea.bind(null,t,n,o,l,a);(0,c.dispatch)(u.store).setDefaults("core/edit-post",{editorMode:"visual",fixedToolbar:!1,fullscreenMode:!0,hiddenBlockTypes:[],inactivePanels:[],isPublishSidebarEnabled:!0,openPanels:["post-status"],preferredStyleVariations:{},showBlockBreadcrumbs:!0,showIconLabels:!1,themeStyles:!0,welcomeGuide:!0,welcomeGuideTemplate:!0}),(0,c.dispatch)(s.store).__experimentalReapplyBlockTypeFilters(),(0,i.registerCoreBlocks)();"Standards"!==("CSS1Compat"===document.compatMode?"Standards":"Quirks")&&console.warn("Your browser is using Quirks Mode. \nThis can cause rendering issues such as blocks overlaying meta boxes in the editor. Quirks Mode can be triggered by PHP errors or HTML code appearing before the opening <!DOCTYPE html>. Try checking the raw page source or your site's PHP error log and resolving errors there, removing any HTML before the doctype, or disabling plugins.");-1!==window.navigator.userAgent.indexOf("iPhone")&&window.addEventListener("scroll",(e=>{const t=document.getElementsByClassName("interface-interface-skeleton__body")[0];e.target===document&&(window.scrollY>100&&(t.scrollTop=t.scrollTop+window.scrollY),document.getElementsByClassName("is-mode-visual")[0]&&window.scrollTo(0,0))})),(0,r.render)((0,r.createElement)(da,{settings:l,onError:m,postId:n,postType:t,initialEdits:a}),o)}}(),(window.wp=window.wp||{}).editPost=l}();