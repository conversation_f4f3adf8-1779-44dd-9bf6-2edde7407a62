#page_content {
  margin: 0 0.5em;
}

.data {
  margin: 0 0 12px;
}

button.mult_submit,
.checkall_box + label {
  text-decoration: none;
  color: #235a81;
  cursor: pointer;
  outline: none;
}

button.mult_submit {
  &:hover,
  &:focus {
    text-decoration: underline;
    color: #235a81;
  }
}

.checkall_box + label:hover {
  text-decoration: underline;
  color: #235a81;
}

#navbarDropdown > img {
  display: none;
}

textarea {
  &.char {
    margin: 6px;
  }

  &.charField {
    width: 95%;
  }
}

.pma-fieldset {
  margin-top: 1em;
  border-radius: 4px 4px 0 0;
  border: #aaa solid 1px;
  padding: 0.5em;
  background: #eee;
  text-shadow: 1px 1px 2px $white;
  box-shadow: 1px 1px 2px $white inset;
}

.pma-fieldset {
  .pma-fieldset {
    margin: 0.8em;
    border: 1px solid #aaa;
    background: #e8e8e8;
  }

  legend {
    float: none;
    font-weight: bold;
    color: #444;
    padding: 5px 10px;
    border-radius: 2px;
    border: 1px solid #aaa;
    background-color: $white;
    max-width: 100%;
    box-shadow: 3px 3px 15px #bbb;
    width: initial;
    font-size: 1em;
  }
}

// 3.4
.datatable {
  table-layout: fixed;
}

/* classes */
.clearfloat {
  clear: both;
}

.paddingtop {
  padding-top: 1em;
}

.separator {
  color: $white;
  text-shadow: 0 1px 0 $black;
}

div.tools {
  padding: 0.2em;

  a {
    color: #3a7ead !important;
  }

  margin-top: 0;
  margin-bottom: 0.5em;
  // avoid a thick line since this should be used under another fieldset
  border-top: 0;
  text-align: right;
  float: none;
  clear: both;
  border-radius: 0 0 4px 4px;
}

.pma-fieldset.tblFooters {
  margin-top: 0;
  margin-bottom: 0.5em;
  // avoid a thick line since this should be used under another fieldset
  border-top: 0;
  text-align: right;
  float: none;
  clear: both;
  border-radius: 0 0 4px 4px;
}

div.null_div {
  height: 20px;
  text-align: center;
  font-style: normal;
  min-width: 50px;
}

.pma-fieldset {
  .formelement {
    float: left;
    margin-right: 0.5em;
    // IE
    white-space: nowrap;
  }

  // revert for Gecko
  div[class=formelement] {
    white-space: normal;
  }
}

button.mult_submit {
  border: none;
  background-color: transparent;
}

/**
 * marks table rows/cells if the db field is in a where condition
 */
.condition {
  border-color: $black !important;
}

th.condition {
  border-width: 1px 1px 0 1px;
  border-style: solid;
}

td.condition {
  border-width: 0 1px 0 1px;
  border-style: solid;
}

tr:last-child td.condition {
  border-width: 0 1px 1px 1px;
}

/* rtl:begin:remove */
.before-condition {
  // for first th which must have right border set (ltr only)
  border-right: 1px solid $black;
}

/* rtl:end:remove */

/**
 * cells with the value NULL
 */
td.null {
  font-style: italic;
  color: #7d7d7d !important;
}

table {
  .valueHeader,
  .value {
    text-align: right;
    white-space: normal;
  }
}

.value {
  font-family: $font-family-monospace;
}

img.lightbulb {
  cursor: pointer;
}

.pdflayout {
  overflow: hidden;
  clip: inherit;
  background-color: $white;
  display: none;
  border: 1px solid $black;
  position: relative;
}

.pdflayout_table {
  background: #d3dce3;
  color: $black;
  overflow: hidden;
  clip: inherit;
  z-index: 2;
  display: inline;
  visibility: inherit;
  cursor: move;
  position: absolute;
  font-size: 80%;
  border: 1px dashed $black;
}

// Doc links in SQL
.cm-sql-doc {
  text-decoration: none;
  border-bottom: 1px dotted $black;
  color: inherit !important;
}

// no extra space in table cells
td .icon {
  image-rendering: pixelated;
  margin: 0;
}

.selectallarrow {
  margin-right: 0.3em;
  margin-left: 0.6em;
}

.with-selected {
  margin-left: 2em;
}

// message boxes: error, confirmation
#pma_errors,
#pma_demo,
#pma_footer {
  position: relative;
  padding: 0 0.5em;
}

.confirmation {
  color: $black;
  background-color: pink;
}

// end messageboxes

.new_central_col {
  width: 100%;
}

.tblcomment {
  font-size: 70%;
  font-weight: normal;
  color: #009;
}

.tblHeaders {
  font-weight: bold;
  color: $black;
  background: #d3dce3;
}

div.tools,
.tblFooters {
  font-weight: normal;
  color: $black;
  background: #d3dce3;
}

.tblHeaders a {
  &:link,
  &:active,
  &:visited {
    color: #00f;
  }
}

div.tools a {
  &:link,
  &:visited,
  &:active {
    color: #00f;
  }
}

.tblFooters a {
  &:link,
  &:active,
  &:visited {
    color: #00f;
  }
}

.tblHeaders a:hover,
div.tools a:hover,
.tblFooters a:hover {
  color: #f00;
}

.error {
  border: 1px solid maroon !important;
  margin-left: 2px;
  padding: 1px 2px;
  color: $black;
  background: pink;
}

// disabled text
.disabled {
  color: #666;

  a {
    &:link,
    &:active,
    &:visited {
      color: #666;
    }

    &:hover {
      color: #666;
      text-decoration: none;
    }
  }
}

tr.disabled td,
td.disabled {
  background-color: #f3f3f3;
  color: #aaa;
}

.pre_wrap {
  white-space: pre-wrap;
}

.pre_wrap br {
  display: none;
}

/**
 * login form
 */
body#loginform {
  margin: 1em 0 0 0;
  text-align: center;

  h1,
  a.logo {
    display: block;
    text-align: center;
  }

  div.container {
    text-align: left;
    width: 30em;
    margin: 0 auto;
  }
}

div.container.modal_form {
  margin: 0 auto;
  width: 30em;
  text-align: center;
  background: $white;
  z-index: 999;
}

div#modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: $white;
  z-index: 900;
}

label.col-3.d-flex.align-items-center {
  font-weight: bolder;
}

.commented_column {
  border-bottom: 1px dashed $black;
}

.column_attribute {
  font-size: 70%;
}

.column_name {
  font-size: 80%;
  margin: 5px 2px;
}

.central_columns_navigation {
  padding: 1.5% 0 !important;
}

.message_errors_found {
  margin-top: 20px;
}

.repl_gui_skip_err_cnt {
  width: 30px;
}

.color_gray {
  color: gray;
}

.max_height_400 {
  max-height: 400px;
}

li.last.database {
  // Avoid having the last item of the tree hidden behind the scroll bar
  margin-bottom: 15px !important;
}

// zoom search
div#dataDisplay {
  input,
  select {
    margin: 0;
    margin-right: 0.5em;
  }

  th {
    line-height: 2em;
  }
}

img.calendar {
  border: none;
}

form.clock {
  text-align: center;
}

// table stats
div#tablestatistics table {
  float: left;
  margin-bottom: 0.5em;
  margin-right: 1.5em;
  margin-top: 0.5em;
  min-width: 16em;
}

// end table stats

// Heading
#topmenucontainer {
  padding-right: 1em;
  width: 100%;
}

#page_nav_icons {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99;
  padding: $breadcrumb-navbar-padding-y $breadcrumb-padding-x;
}

#page_settings_icon {
  cursor: pointer;
  display: none;
}

#page_settings_modal,
#pma_navigation_settings {
  display: none;
}

#textSQLDUMP {
  width: 95%;
  height: 95%;
  font-family: Consolas, "Courier New", Courier, monospace;
  font-size: 110%;
}

#TooltipContainer {
  position: absolute;
  z-index: 99;
  width: 20em;
  height: auto;
  overflow: visible;
  visibility: hidden;
  background-color: #ffc;
  color: #060;
  border: 0.1em solid $black;
  padding: 0.5em;
}

// user privileges
#fieldset_add_user_login {
  div.item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid silver;
    padding-bottom: 0.3em;
    margin-bottom: 0.3em;
  }

  label {
    float: left;
    display: block;
    width: 10em;
    max-width: 100%;
    text-align: right;
    padding-right: 0.5em;
    margin-bottom: 0;
  }

  input {
    width: 12em;
    clear: right;
    max-width: 100%;
  }

  span.options {
    float: left;
    display: block;
    width: 12em;
    max-width: 100%;
    padding-right: 0.5em;

    #select_pred_username,
    #select_pred_hostname,
    #select_pred_password {
      width: 100%;
      max-width: 100%;
    }

    input {
      width: auto;
    }
  }
}

#fieldset_user_priv div.item {
  float: left;
  width: 9em;
  max-width: 100%;

  div.item {
    float: none;
  }

  label {
    white-space: nowrap;
  }

  select {
    width: 100%;
  }
}

#fieldset_user_global_rights .pma-fieldset,
#fieldset_user_group_rights .pma-fieldset {
  float: left;
}

#fieldset_user_global_rights > legend input {
  margin-left: 2em;
}
// end user privileges

// serverstatus

.linkElem:hover {
  text-decoration: underline;
  color: #235a81;
  cursor: pointer;
}

h3#serverstatusqueries span {
  font-size: 60%;
  display: inline;
}

div#serverStatusTabs {
  margin-top: 1em;
}

caption a.top {
  float: right;
}

div#serverstatusquerieschart {
  float: left;
  width: 500px;
  height: 350px;
  margin-right: 50px;
}

div {
  &#serverstatus table {
    tbody td.descr a,
    .tblFooters a {
      white-space: nowrap;
    }
  }

  &.liveChart {
    clear: both;
    min-width: 500px;
    height: 400px;
    padding-bottom: 80px;
  }
}

div#chartVariableSettings {
  border: 1px solid #ddd;
  background-color: #e6e6e6;
  margin-left: 10px;
}

table#chartGrid {
  td {
    padding: 3px;
    margin: 0;
  }

  div.monitorChart {
    background: #ebebeb;
    border: none;
    min-width: 1px;
  }
}

div.tabLinks {
  float: left;
  padding: 5px 0;

  a,
  label {
    margin-right: 7px;
  }

  .icon {
    margin: -0.2em 0.3em 0 0;
  }
}

.popupContent {
  display: none;
  position: absolute;
  border: 1px solid #ccc;
  margin: 0;
  padding: 3px;
  background-color: $white;
  z-index: 2;
  box-shadow: 2px 2px 3px #666;
}

div {
  &#logTable {
    padding-top: 10px;
    clear: both;

    table {
      width: 100%;
    }
  }

  &#queryAnalyzerDialog {
    min-width: 700px;

    div {
      &.CodeMirror-scroll {
        height: auto;
      }

      &#queryProfiling {
        height: 300px;
      }
    }

    td.explain {
      width: 250px;
    }

    table.queryNums {
      display: none;
      border: 0;
      text-align: left;
    }
  }
}

.smallIndent {
  padding-left: 7px;
}

// end serverstatus

// profiling

div#profilingchart {
  width: 850px;
  height: 370px;
  float: left;
}

#profilingchart .jqplot-highlighter-tooltip {
  top: auto !important;
  left: 11px;
  bottom: 24px;
}

// end profiling

// table charting
#resizer {
  border: 1px solid silver;
}

// make room for the resize handle
#inner-resizer {
  padding: 10px;
}

// querybox

#togglequerybox {
  margin: 0 10px;
}

#serverstatus h3 {
  margin: 15px 0;
  font-weight: normal;
  color: #999;
  font-size: 1.7em;
}

textarea {
  &#sqlquery {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #aaa;
    padding: 5px;
    font-family: inherit;
  }

  &#sql_query_edit {
    height: 7em;
    width: 95%;
    display: block;
  }
}

// end querybox

// main page

#mysqlmaininformation,
#pmamaininformation {
  float: left;
  width: 49%;
}

#maincontainer {
  ul {
    list-style-type: disc;
    vertical-align: middle;
  }

  li {
    margin-bottom: 0.3em;
  }
}

#full_name_layer {
  position: absolute;
  padding: 2px;
  margin-top: -3px;
  z-index: 801;
  border-radius: 3px;
  border: solid 1px #888;
  background: $white;
}

#body_browse_foreigners {
  background: #f3f3f3;
  margin: 0.5em 0.5em 0 0.5em;
}

#bodythemes {
  width: 500px;
  margin: auto;
  text-align: center;

  img {
    border: 0.1em solid $black;
  }

  a:hover img {
    border: 0.1em solid red;
  }
}

#selflink {
  clear: both;
  display: block;
  margin-top: 1em;
  margin-bottom: 1em;
  width: 98%;
  margin-left: 1%;
  text-align: right;
  border-top: 0.1em solid silver;
}

#qbe_div_table_list,
#qbe_div_sql_query {
  float: left;
}

code {
  font-size: 1em;

  &.php {
    display: block;
    padding-left: 1em;
    margin-top: 0;
    margin-bottom: 0;
    max-height: 10em;
    overflow: auto;
    direction: ltr;
  }

  &.sql {
    display: block;
    padding: 1em;
    margin-top: 0;
    margin-bottom: 0;
    max-height: 10em;
    overflow: auto;
    direction: ltr;
  }
}

div.sqlvalidate {
  display: block;
  padding: 1em;
  margin-top: 0;
  margin-bottom: 0;
  max-height: 10em;
  overflow: auto;
  direction: ltr;
}

.result_query {
  div.sqlOuter {
    background: #e5e5e5;
    text-align: left;
  }
}

#PMA_slidingMessage code.sql,
div.sqlvalidate {
  background: #e5e5e5;
}

textarea#partitiondefinition {
  height: 3em;
}

// for elements that should be revealed only via js
.hide {
  display: none;
}

#list_server {
  list-style-type: none;
  padding: 0;
}

/**
  *  Progress bar styles
  */

div {
  &.upload_progress {
    width: 400px;
    margin: 3em auto;
    text-align: center;
  }

  &.upload_progress_bar_outer {
    border: 1px solid $black;
    width: 202px;
    position: relative;
    margin: 0 auto 1em;
    color: $body-color;

    div.percentage {
      position: absolute;
      top: 0;
      left: 0;
      width: 202px;
    }
  }

  &.upload_progress_bar_inner {
    background-color: #ddd;
    width: 0;
    height: 12px;
    margin: 1px;
    overflow: hidden;
    color: $black;
    position: relative;

    div.percentage {
      top: -1px;
      left: -1px;
    }
  }

  &#statustext {
    margin-top: 0.5em;
  }
}

table {
  &#serverconnection_src_remote,
  &#serverconnection_trg_remote,
  &#serverconnection_src_local,
  &#serverconnection_trg_local {
    float: left;
  }
}

/**
  *  Validation error message styles
  */
input {
  &[type=text].invalid_value,
  &[type=password].invalid_value,
  &[type=number].invalid_value,
  &[type=date].invalid_value {
    background: #fcc;
  }
}

select.invalid_value,
.invalid_value {
  background: #fcc;
}

/**
  *  Ajax notification styling
  */
.ajax_notification {
  // The notification needs to be shown on the top of the page
  top: 0;
  position: fixed;
  margin-top: 200px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  // Keep a little space on the sides of the text
  padding: 5px;
  width: 350px;
  // If this is not kept at a high z-index, the jQueryUI modal dialogs (z-index: 1000) might hide this
  z-index: 1100;
  text-align: center;
  display: inline;
  left: 0;
  right: 0;
  background-image: url("../img/ajax_clock_small.gif");
  background-repeat: no-repeat;
  background-position: 2%;
  border: 1px solid #e2b709;
  background-color: #ffe57e;
  border-radius: 5px;
  box-shadow: 0 5px 90px #888;
}

#loading_parent {
  /** Need this parent to properly center the notification division */
  position: relative;
  width: 100%;
}

#popup_background {
  display: none;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: $black;
  z-index: 1000;
  overflow: hidden;
}

#tablestructure tbody label {
  margin: 0.3rem 0;
}

#structure-action-links a {
  margin-right: 1em;
}

#addColumns input[type=radio] {
  margin: 3px 0 0;
  margin-left: 1em;
}

/**
 * Indexes
 */
#index_frm {
  .index_info {
    input[type=text],
    select {
      width: 100%;
      margin: 0;
      box-sizing: border-box;
    }

    div {
      padding: 0.2em 0;
    }

    .label {
      float: left;
      min-width: 12em;
    }
  }

  .slider {
    width: 10em;
    margin: 0.6em;
    float: left;
  }

  .add_fields {
    float: left;

    input {
      margin-left: 1em;
    }
  }

  input {
    margin: 0;
  }

  td {
    vertical-align: middle;
  }
}

table#index_columns {
  width: 100%;

  select {
    width: 85%;
    float: left;
  }
}

#move_columns_dialog {
  div {
    padding: 1em;
  }

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  li {
    background: #d3dce3;
    border: 1px solid #aaa;
    color: $black;
    font-weight: bold;
    margin: 0.4em;
    padding: 0.2em;
    border-radius: 2px;
  }
}

// config forms
.config-form {
  fieldset {
    margin-top: 0;
    padding: 0;
    clear: both;
    border-radius: 0;

    p {
      margin: 0;
      padding: 0.5em;
      background: $white;
      border-top: 0;
    }

    // form error list
    .errors {
      margin: 0 -2px 1em;
      padding: 0.5em 1.5em;
      background: #fbead9;
      border-color: #c83838;
      border-style: solid;
      border-width: 1px 0;
      list-style: none;
      font-family: $font-family-base;
      font-size: small;
    }

    // field error list
    .inline_errors {
      margin: 0.3em 0.3em 0.3em;
      margin-left: 0;
      padding: 0;
      list-style: none;
      color: #9a0000;
      font-size: small;
    }

    .doc {
      margin-left: 1em;
    }

    .disabled-notice {
      margin-left: 1em;
      font-size: 80%;
      text-transform: uppercase;
      color: #e00;
      cursor: help;
    }

    th {
      padding: 0.3em 0.3em 0.3em;
      padding-left: 0.5em;
      text-align: left;
      vertical-align: top;
      background: transparent;
      filter: none;
      border-top: 1px #d5d5d5 solid;
      border-right: none;

      small {
        display: block;
        font-weight: normal;
        font-family: $font-family-base;
        font-size: x-small;
        color: #444;
      }
    }

    td {
      padding-top: 0.3em;
      padding-bottom: 0.3em;
      vertical-align: top;
      border-top: 1px #d5d5d5 solid;
      border-right: none;
    }
  }

  legend {
    display: none;
  }

  span.checkbox {
    padding: 2px;
    display: inline-block;

    &.custom {
      padding: 1px;
      border: 1px #edec90 solid;
      background: #ffc;
    }
  }

  // customized field
  .custom {
    background: #ffc;
  }

  .field-error {
    border-color: #a11 !important;
  }

  input {
    &[type=text],
    &[type=password],
    &[type=number] {
      border: 1px #a7a6aa solid;
      height: auto;

      &:focus {
        border: 1px #6676ff solid;
        background: #f7fbff;
      }
    }
  }

  select,
  textarea {
    border: 1px #a7a6aa solid;
    height: auto;

    &:focus {
      border: 1px #6676ff solid;
      background: #f7fbff;
    }
  }

  .field-comment-mark {
    font-family: serif;
    color: #007;
    cursor: help;
    padding: 0 0.2em;
    font-weight: bold;
    font-style: italic;
  }

  .field-comment-warning {
    color: #a00;
  }

  // error list
  dd {
    margin-left: 0.5em;

    &::before {
      content: "\25B8  ";
    }
  }
}

fieldset {
  .group-header {
    th {
      background: #d5d5d5;
    }

    + tr th {
      padding-top: 0.6em;
    }
  }

  .group-field-1 th,
  .group-header-2 th {
    padding-left: 1.5em;
  }

  .group-field-2 th,
  .group-header-3 th {
    padding-left: 3em;
  }

  .group-field-3 th {
    padding-left: 4.5em;
  }

  .disabled-field {
    th {
      color: #666;
      background-color: #ddd;

      small {
        color: #666;
        background-color: #ddd;
      }
    }

    td {
      color: #666;
      background-color: #ddd;
    }
  }
}

.click-hide-message {
  cursor: pointer;
}

.prefsmanage_opts {
  margin-left: 2em;
}

#prefs_autoload {
  margin-bottom: 0.5em;
  margin-left: 0.5em;
}

input#auto_increment_opt {
  width: min-content;
}

#placeholder {
  position: relative;
  border: 1px solid #aaa;
  float: right;
  overflow: hidden;
  width: 450px;
  height: 300px;

  .button {
    position: absolute;
    cursor: pointer;
  }

  div.button {
    font-size: smaller;
    color: #999;
    background-color: #eee;
    padding: 2px;
  }
}

.wrapper {
  float: left;
  margin-bottom: 1.5em;
}

.toggleButton {
  position: relative;
  cursor: pointer;
  font-size: 0.8em;
  text-align: center;
  line-height: 1.4em;
  height: 1.55em;
  overflow: hidden;
  border-right: 0.1em solid #888;
  border-left: 0.1em solid #888;
  border-radius: 0.3em;

  table,
  td,
  img {
    padding: 0;
    position: relative;
  }

  .container {
    position: absolute;

    td,
    tr {
      background: none !important;
    }
  }

  .toggleOn {
    color: $white;
    padding: 0 1em;
    text-shadow: 0 0 0.2em $black;
  }

  .toggleOff {
    padding: 0 1em;
  }
}

.doubleFieldset {
  .pma-fieldset {
    width: 48%;
    float: left;
    padding: 0;
  }

  legend {
    margin-left: 1.5em;
  }

  div.wrap {
    padding: 1.5em;
  }
}

#table_name_col_no_outer {
  margin-top: 45px;
}

#table_name_col_no {
  position: fixed;
  top: 100px !important;
  width: 100%;
  background: $white;
}

#table_columns {
  display: block;
  overflow: auto;

  input {
    &[type=text],
    &[type=password],
    &[type=number] {
      width: 10em;
      box-sizing: border-box;
    }
  }

  select {
    width: 10em;
    box-sizing: border-box;
  }
}

#openlayersmap {
  width: 450px;
  height: 300px;
}

.placeholderDrag {
  cursor: move;
}

#left_arrow {
  left: 8px;
  top: 26px;
}

#right_arrow {
  left: 26px;
  top: 26px;
}

#up_arrow {
  left: 17px;
  top: 8px;
}

#down_arrow {
  left: 17px;
  top: 44px;
}

#zoom_in {
  left: 17px;
  top: 67px;
}

#zoom_world {
  left: 17px;
  top: 85px;
}

#zoom_out {
  left: 17px;
  top: 103px;
}

.colborder {
  cursor: col-resize;
  height: 100%;
  margin-left: -6px;
  position: absolute;
  width: 5px;
}

.colborder_active {
  border-right: 2px solid #a44;
}

.pma_table {
  th.draggable span {
    display: block;
    overflow: hidden;
  }
}

.pma_table {
  td {
    position: static;
  }

  tbody td span {
    display: block;
    overflow: hidden;

    code span {
      display: inline;
    }
  }

  th.draggable {
    span {
      margin-right: 10px;
    }
  }
}

.modal-copy input {
  display: block;
  width: 100%;
  margin-top: 1.5em;
  padding: 0.3em 0;
}

.cRsz {
  position: absolute;
}

.cCpy {
  background: #333;
  color: $white;
  font-weight: bold;
  margin: 0.1em;
  padding: 0.3em;
  position: absolute;
  text-shadow: -1px -1px $black;
  box-shadow: 0 0 0.7em $black;
  border-radius: 0.3em;
}

.cPointer {
  $height: 20px;
  $width: 10px;

  height: $height;
  width: $width;
  margin-top: $height * -0.5;
  margin-left: $width * -0.5;
  background: url("../img/col_pointer.png");
  position: absolute;
}

.tooltip {
  background: #333 !important;
  opacity: 0.8 !important;
  z-index: 9999;
  border: 1px solid $black !important;
  border-radius: 0.3em !important;
  text-shadow: -1px -1px $black !important;
  font-size: 0.8em !important;
  font-weight: bold !important;
  padding: 1px 3px !important;

  * {
    background: none !important;
    color: $white !important;
  }
}

.cDrop {
  right: 0;
  position: absolute;
  top: 0;
}

.coldrop {
  background: url("../img/col_drop.png");
  cursor: pointer;
  height: 16px;
  margin-top: 0.3em;
  width: 16px;

  &:hover {
    background-color: #999;
  }
}

.coldrop-hover {
  background-color: #999;
}

.cList {
  background: #eee;
  border: solid 1px #999;
  position: absolute;
  box-shadow: 0 0.2em 0.5em #333;
  margin-left: 75%;
  right: 0;
  max-width: 100%;
  overflow-wrap: break-word;

  .lDiv div {
    padding: 0.2em 0.5em 0.2em;
    padding-left: 0.2em;

    &:hover {
      background: #ddd;
      cursor: pointer;
    }

    input {
      cursor: pointer;
    }
  }
}

.showAllColBtn {
  border-bottom: solid 1px #999;
  border-top: solid 1px #999;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  padding: 0.35em 1em;
  text-align: center;

  &:hover {
    background: #ddd;
  }
}

.turnOffSelect {
  user-select: none;
}

.navigation {
  margin: 0.8em 0;
  border-radius: 5px;
  background: linear-gradient(#eee, #ccc);

  td {
    margin: 0;
    padding: 0;
    vertical-align: middle;
    white-space: nowrap;
  }

  input {
    &[type=submit] {
      background: none;
      border: 0;
      filter: none;
      margin: 0;
      padding: 0.8em 0.5em;
      border-radius: 0;

      &:hover {
        color: $white;
        cursor: pointer;
        text-shadow: none;
        background: linear-gradient(#333, #555);
      }
    }

    &.edit_mode_active {
      color: $white;
      cursor: pointer;
      text-shadow: none;
      background: linear-gradient(#333, #555);
    }
  }

  .btn-link {
    color: $body-color;

    &:hover {
      color: $white;
      background-image: linear-gradient(#333, #555);
      text-decoration: none;
    }
  }

  select {
    margin: 0 0.8em;
  }
}

.navigation_separator {
  color: #999;
  display: inline-block;
  font-size: 1.5em;
  text-align: center;
  height: 1.4em;
  width: 1.2em;
  text-shadow: 1px 0 $white;
}

.cEdit {
  margin: 0;
  padding: 0;
  position: absolute;

  input[type=text] {
    background: $white;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  .edit_area {
    background: $white;
    border: 1px solid #999;
    min-width: 10em;
    padding: 0.3em 0.5em;

    select,
    textarea {
      width: 97%;
    }
  }

  .cell_edit_hint {
    color: #555;
    font-size: 0.8em;
    margin: 0.3em 0.2em;
  }

  .edit_box {
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 0;
    margin: 0;
  }

  .edit_box_posting {
    background: $white url('../img/ajax_clock_small.gif') no-repeat right center;
    padding-right: 1.5em;
  }

  .edit_area_loading {
    background: $white url("../img/ajax_clock_small.gif") no-repeat center;
    height: 10em;
  }

  .goto_link {
    background: #eee;
    color: #555;
    padding: 0.2em 0.3em;
  }
}

.saving_edited_data {
  background: url('../img/ajax_clock_small.gif') no-repeat left;
  padding-left: 20px;
}

.relationalTable {
  td {
    vertical-align: top;
  }

  select {
    width: 125px;
    margin-right: 5px;
  }
}

// css for timepicker
.ui-timepicker-div {
  .ui-widget-header {
    margin-bottom: 8px;
  }

  dl {
    text-align: left;

    dt {
      height: 25px;
      margin-bottom: -25px;
    }

    dd {
      margin: 0 10px 10px 85px;
    }
  }

  td {
    font-size: 90%;
  }
}

.ui-tpicker-grid-label {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
}

.ui-timepicker-rtl {
  direction: rtl;

  dl {
    text-align: right;

    dd {
      margin: 0 65px 10px 10px;
    }
  }
}

body .ui-widget {
  font-size: 1em;
}

.ui-dialog .pma-fieldset legend a {
  color: #235a81;
}

.ui-draggable {
  z-index: 801;
}

// over-riding jqplot-yaxis class
.jqplot-yaxis {
  left: 0 !important;
  min-width: 25px;
  width: auto;
}

.jqplot-axis {
  overflow: hidden;
}

div#page_content div {
  &#tableslistcontainer {
    margin-top: 1em;

    table.data {
      border-top: 0.1px solid #eee;
    }
  }

  &.result_query {
    margin-top: 1em;
  }
}

table.show_create {
  margin-top: 1em;

  td {
    border-right: 1px solid #bbb;
  }
}

#alias_modal {
  table {
    width: 100%;
  }

  label {
    font-weight: bold;
  }
}

.ui-dialog {
  position: fixed;
}

.small_font {
  font-size: smaller;
}

// Console styles
#pma_console_container {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
}

#pma_console {
  position: relative;
  margin-left: $navigation-width;

  .templates {
    display: none;
  }

  .mid_text {
    vertical-align: middle;
  }

  .toolbar {
    position: relative;
    background: #ccc;
    border-top: solid 1px #aaa;
    cursor: n-resize;

    span {
      vertical-align: middle;
    }

    &.collapsed {
      cursor: default;

      &:not(:hover) {
        display: inline-block;
        border-top-right-radius: 3px;
        border-right: solid 1px #aaa;
      }

      > .button {
        display: none;
      }
    }
  }

  .message span {
    &.text,
    &.action {
      padding: 0 3px;
      display: inline-block;
    }
  }

  .toolbar {
    .button,
    .text {
      padding: 0 3px;
      display: inline-block;
    }
  }

  .switch_button {
    padding: 0 3px;
    display: inline-block;
  }

  .message span.action,
  .toolbar .button,
  .switch_button {
    cursor: pointer;
  }

  .message span.action:hover,
  .toolbar .button:hover,
  .switch_button:hover {
    background: #ddd;
  }

  .toolbar {
    .button.active {
      background: #ddd;
    }

    .text {
      font-weight: bold;
    }

    .button,
    .text {
      margin-right: 0.4em;
    }

    .button,
    .text {
      float: right;
    }
  }

  .content {
    overflow-x: hidden;
    overflow-y: auto;
    margin-bottom: -65px;
    border-top: solid 1px #aaa;
    background: $white;
    padding-top: 0.4em;

    &.console_dark_theme {
      background: $black;
      color: $white;

      .CodeMirror-wrap {
        background: $black;
        color: $white;
      }

      .action_content {
        color: $black;
      }

      .message {
        border-color: #373b41;
      }

      .CodeMirror-cursor {
        border-color: $white;
      }

      .cm-keyword {
        color: #de935f;
      }
    }
  }

  .message,
  .query_input {
    position: relative;
    font-family: Monaco, Consolas, monospace;
    cursor: text;
    margin: 0 10px 0.2em 1.4em;
  }

  .message {
    border-bottom: solid 1px #ccc;
    padding-bottom: 0.2em;

    &.expanded > .action_content {
      position: relative;
    }

    &::before {
      left: -0.7em;
      position: absolute;
      content: ">";
    }
  }

  .query_input {
    &::before {
      left: -0.7em;
      position: absolute;
      content: ">";
      top: -2px;
    }

    textarea {
      width: 100%;
      height: 4em;
      resize: vertical;
    }
  }

  .message {
    &:hover::before {
      color: #7cf;
      font-weight: bold;
    }

    &.expanded::before {
      content: "]";
    }

    &.welcome::before {
      display: none;
    }

    &.failed {
      &::before,
      &.expanded::before,
      &:hover::before {
        content: "=";
        color: #944;
      }
    }

    &.pending::before {
      opacity: 0.3;
    }

    &.collapsed > .query {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &.expanded > .query {
      display: block;
      white-space: pre;
      word-wrap: break-word;
    }

    .text.targetdb,
    &.collapsed .action.collapse,
    &.expanded .action.expand {
      display: none;
    }

    .action {
      &.requery,
      &.profiling,
      &.explain,
      &.bookmark {
        display: none;
      }
    }

    &.select .action {
      &.profiling,
      &.explain {
        display: inline-block;
      }
    }

    &.history .text.targetdb,
    &.successed .text.targetdb {
      display: inline-block;
    }

    &.history .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    &.bookmark .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    &.successed .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    .action_content {
      position: absolute;
      bottom: 100%;
      background: #ccc;
      border: solid 1px #aaa;
      border-top-left-radius: 3px;
    }

    &.bookmark .text.targetdb,
    .text.query_time {
      margin: 0;
      display: inline-block;
    }

    &.failed .text.query_time,
    .text.failed {
      display: none;
    }

    &.failed .text.failed {
      display: inline-block;
    }

    .text {
      background: $white;
    }

    &.collapsed {
      > .action_content {
        display: none;
      }

      &:hover > .action_content {
        display: block;
      }
    }

    .bookmark_label {
      padding: 0 4px;
      top: 0;
      background: #369;
      color: $white;
      border-radius: 3px;

      &.shared {
        background: #396;
      }
    }

    &.expanded .bookmark_label {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }
  }

  .query_input {
    position: relative;
  }

  .mid_layer {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #666;
    display: none;
    cursor: pointer;
    z-index: 200;
  }

  .card {
    position: absolute;
    width: 94%;
    height: 100%;
    min-height: 48px;
    left: 100%;
    top: 0;
    border-left: solid 1px #999;
    z-index: 300;
    transition: left 0.2s;

    &.show {
      left: 6%;
      box-shadow: -2px 1px 4px -1px #999;
    }
  }

  .button.hide,
  .message span.text.hide {
    display: none;
  }
}

#pma_bookmarks .content.add_bookmark,
#pma_console_options .content {
  padding: 4px 6px;
}

#pma_bookmarks .content.add_bookmark {
  .options {
    margin-left: 1.4em;
    padding-bottom: 0.4em;
    margin-bottom: 0.4em;
    border-bottom: solid 1px #ccc;

    button {
      margin: 0 7px;
      vertical-align: bottom;
    }
  }

  input[type=text] {
    margin: 0;
    padding: 2px 4px;
  }
}

#debug_console {
  &.grouped .ungroup_queries {
    display: inline-block;
  }

  &.ungrouped {
    .group_queries {
      display: inline-block;
    }

    .ungroup_queries,
    .sort_count {
      display: none;
    }
  }

  &.grouped .group_queries {
    display: none;
  }

  .count {
    margin-right: 8px;
  }

  .show_trace .trace,
  .show_args .args {
    display: block;
  }

  .hide_trace .trace,
  .hide_args .args,
  .show_trace .action.dbg_show_trace,
  .hide_trace .action.dbg_hide_trace {
    display: none;
  }

  .traceStep {
    &.hide_args .action.dbg_hide_args,
    &.show_args .action.dbg_show_args {
      display: none;
    }

    &::after {
      content: "";
      display: table;
      clear: both;
    }
  }

  .trace.welcome::after,
  .debug > .welcome::after {
    content: "";
    display: table;
    clear: both;
  }

  .debug_summary {
    float: left;
  }

  .trace.welcome .time,
  .traceStep .file,
  .script_name {
    float: right;
  }

  .traceStep .args pre {
    margin: 0;
  }
}

// Code mirror console style
.cm-s-pma {
  .CodeMirror-code {
    font-family: Monaco, Consolas, monospace;

    pre {
      font-family: Monaco, Consolas, monospace;
    }
  }

  .CodeMirror-measure > pre,
  .CodeMirror-code > pre,
  .CodeMirror-lines {
    padding: 0;
  }

  &.CodeMirror {
    resize: none;
    height: auto;
    width: 100%;
    min-height: initial;
    max-height: initial;
  }

  .CodeMirror-scroll {
    cursor: text;
  }
}

// PMA drop-improt style

.pma_drop_handler {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  height: 100%;
  z-index: 999;
  color: white;
  font-size: 30pt;
  text-align: center;
  padding-top: 20%;
}

.pma_sql_import_status {
  display: none;
  position: fixed;
  bottom: 0;
  right: 25px;
  width: 400px;
  border: 1px solid #999;
  background: #f3f3f3;
  border-radius: 4px;
  box-shadow: 2px 2px 5px #ccc;

  h2 {
    background-color: #bbb;
    padding: 0.1em 0.3em;
    margin-top: 0;
    margin-bottom: 0;
    color: $white;
    font-size: 1.6em;
    font-weight: normal;
    text-shadow: 0 1px 0 #777;
    box-shadow: 1px 1px 15px #999 inset;
  }
}

.pma_drop_result h2 {
  background-color: #bbb;
  padding: 0.1em 0.3em;
  margin-top: 0;
  margin-bottom: 0;
  color: $white;
  font-size: 1.6em;
  font-weight: normal;
  text-shadow: 0 1px 0 #777;
  box-shadow: 1px 1px 15px #999 inset;
}

.pma_sql_import_status {
  div {
    height: 270px;
    overflow-y: auto;
    overflow-x: hidden;
    list-style-type: none;

    li {
      padding: 8px 10px;
      border-bottom: 1px solid #bbb;
      color: rgb(148, 14, 14);
      background: white;

      .filesize {
        float: right;
      }
    }
  }

  h2 {
    .minimize {
      float: right;
      margin-right: 5px;
      padding: 0 10px;
    }

    .close {
      float: right;
      margin-right: 5px;
      padding: 0 10px;
      display: none;
    }

    .minimize:hover,
    .close:hover {
      background: rgba(155, 149, 149, 0.78);
      cursor: pointer;
    }
  }
}

.pma_drop_result h2 .close:hover {
  background: rgba(155, 149, 149, 0.78);
  cursor: pointer;
}

.pma_drop_file_status {
  color: #235a81;

  span.underline:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}

.pma_drop_result {
  position: fixed;
  top: 10%;
  left: 20%;
  width: 60%;
  background: white;
  min-height: 300px;
  z-index: 800;
  box-shadow: 0 0 15px #999;
  border-radius: 10px;
  cursor: move;

  h2 .close {
    float: right;
    margin-right: 5px;
    padding: 0 10px;
  }
}

.dependencies_box {
  background-color: white;
  border: 3px ridge black;
}

#composite_index_list {
  list-style-type: none;
  list-style-position: inside;
}

span.drag_icon {
  display: inline-block;
  background-image: url("../img/s_sortable.png");
  background-position: center center;
  background-repeat: no-repeat;
  width: 1em;
  height: 3em;
  cursor: move;
}

.topmargin {
  margin-top: 1em;
}

meter {
  &[value="1"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #e32929 5%, transparent 10%, #e32929);
  }

  &[value="2"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #f60 5%, transparent 10%, #f60);
  }

  &[value="3"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #ffd700 5%, transparent 10%, #ffd700);
  }
}

// styles for sortable tables created with tablesorter jquery plugin
th {
  &.header {
    cursor: pointer;
    color: #235a81;

    &:hover {
      text-decoration: underline;
    }

    .sorticon {
      width: 16px;
      height: 16px;
      background-repeat: no-repeat;
      background-position: right center;
      display: inline-table;
      vertical-align: middle;
      float: right;
    }
  }

  &.headerSortUp .sorticon {
    background-image: url("../img/sort-desc.svg");
  }

  &.headerSortDown {
    &:hover .sorticon {
      background-image: url("../img/sort-desc.svg");
    }

    .sorticon {
      background-image: url("../img/sort-asc.svg");
    }
  }

  &.headerSortUp:hover .sorticon {
    background-image: url("../img/sort-asc.svg");
  }
}

// end of styles of sortable tables

// styles for jQuery-ui to support rtl languages
body .ui-dialog {
  .ui-dialog-titlebar-close {
    right: 0.3em;
    left: initial;
  }

  .ui-dialog-title {
    float: left;
  }

  .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: right;
  }

  .ui-dialog-buttonpane .ui-dialog-buttonset button {
    color: $white;
    background: none;
    background-color: #6c757d !important;
    border-color: #6c757d;
  }

  .ui-dialog-buttonpane .ui-dialog-buttonset button:hover {
    background-color: #5a6268 !important;
  }
}
// end of styles for jQuery-ui to support rtl languages

/* templates/database/multi_table_query */

.multi_table_query_form {
  .query-form__tr--hide {
    display: none;
  }

  .query-form__fieldset--inline,
  .query-form__select--inline {
    display: inline;
  }

  .query-form__tr--bg-none {
    background: none;
  }

  .query-form__input--wide {
    width: 91%;
  }

  .query-form__multi-sql-query {
    float: left;
  }
}

// templates/database/designer
// side menu
#name-panel {
  overflow: hidden;
}

// Enable scrollable blocks of code
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

@media only screen and (max-width: 768px) {
  .responsivetable {
    overflow-x: auto;
  }

  body#loginform div.container {
    width: 100%;
  }

  .largescreenonly {
    display: none;
  }

  .width96 {
    width: 96% !important;
  }

  #page_nav_icons {
    display: none;
  }

  #table_name_col_no {
    top: 62px;
  }

  .tdblock tr td {
    display: block;
  }

  #table_columns {
    margin-top: 60px;

    .tablesorter {
      min-width: 100%;
    }
  }

  .doubleFieldset .pma-fieldset {
    width: 98%;
  }

  div#serverstatusquerieschart {
    width: 100%;
    height: 450px;
  }

  .ui-dialog {
    margin: 1%;
    width: 95% !important;
  }
}

#tooltip_editor {
  font-size: 12px;
  background-color: $white;
  opacity: 0.95;
  filter: alpha(opacity=95);
  padding: 5px;
}

#tooltip_font {
  font-weight: bold;
}

#selection_box {
  z-index: 1000;
  height: 205px;
  position: absolute;
  background-color: #87ceeb;
  opacity: 0.4;
  filter: alpha(opacity=40);
  pointer-events: none;
}

#filterQueryText {
  vertical-align: baseline;
}

.ui_tpicker_hour_slider,
.ui_tpicker_minute_slider,
.ui_tpicker_second_slider,
.ui_tpicker_millisec_slider,
.ui_tpicker_microsec_slider {
  margin-left: 40px;
  margin-top: 4px;
  height: 0.75rem;
}

.ui-timepicker-div dl .ui_tpicker_timezone select {
  margin-left: 50px;
}

.ui_tpicker_time_input {
  width: 100%;
}

// Extra large devices (large desktops, 1200px and up)
@include media-breakpoint-up(xl) {
  div.tools {
    text-align: left;
  }

  .pma-fieldset.tblFooters,
  .tblFooters {
    text-align: left;
  }
}

.resize-vertical {
  resize: vertical;
}
