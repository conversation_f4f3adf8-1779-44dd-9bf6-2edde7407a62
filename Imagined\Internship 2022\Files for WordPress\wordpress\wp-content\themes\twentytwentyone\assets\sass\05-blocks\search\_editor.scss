.wp-block-search {
	max-width: var(--responsive--aligndefault-width);

	.wp-block-search__label {
		font-size: var(--form--font-size);
		font-weight: var(--form--label-weight);
		margin-bottom: calc(var(--global--spacing-vertical) / 3);
	}

	&.wp-block-search__button-inside .wp-block-search__inside-wrapper,
	.wp-block-search__input {
		border: var(--form--border-width) solid var(--form--border-color);
		border-radius: var(--form--border-radius);
		font-family: var(--form--font-family);
		font-size: var(--form--font-size);
		line-height: var(--form--line-height);
		max-width: inherit;
		margin-right: calc(-1 * var(--button--border-width));
		padding: var(--form--spacing-unit);

		.is-dark-theme & {
			background: var(--global--color-white-90);
		}

		.has-background & {
			border-color: var(--local--color-primary, var(--global--color-primary)) !important;
		}
	}

	.wp-block-search__button.wp-block-search__button {
		@include button-style();
		box-shadow: none;
		margin-left: 0;

		&.has-icon {
			padding: 6px calc(0.5 * var(--button--padding-horizontal));
			display: inherit;

			svg {
				width: 40px;
				height: 40px;
			}
		}

		&:hover,
		&:active {

			.has-background & {
				background-color: var(--local--color-background, var(--global--color-background)) !important;
				color: var(--local--color-primary, var(--global--color-primary)) !important;
			}

			.has-text-color & {
				color: var(--local--color-primary, var(--global--color-primary)) !important;
			}
		}

		// Remove :focus styles in the editor
		&:focus {
			outline-offset: inherit;
			outline: inherit;
		}
	}

	&.wp-block-search__button-inside {

		.wp-block-search__inside-wrapper {
			padding: var(--form--border-width);
		}

		.wp-block-search__input {
			border: none;
		}

		&.wp-block-search__text-button,
		&.wp-block-search__icon-button {

			.wp-block-search__button {
				// Search button always needs black contrast against white form background
				&:hover {
					color: var(--global--color-dark-gray);
				}

				.is-dark-theme & {
					color: var(--global--color-dark-gray);

					&:hover {
						background-color: var(--global--color-dark-gray);
						color: var(--global--color-white);
					}
				}
			}
		}

		&.wp-block-search__text-button .wp-block-search__button {
			// Match the text button size with the icon button.
			padding: var(--button--padding-vertical) var(--button--padding-horizontal);
		}
	}
}

.wp-block[data-align="center"] > * {
	text-align: center;
}

.wp-block[data-align="center"] {

	.wp-block-search__button-only {

		.wp-block-search__inside-wrapper {
			justify-content: center;
		}
	}
}
