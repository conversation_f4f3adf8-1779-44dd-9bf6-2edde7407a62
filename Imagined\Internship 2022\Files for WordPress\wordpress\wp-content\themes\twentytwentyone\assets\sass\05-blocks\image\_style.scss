.wp-block-image {
	text-align: center;

	figcaption {
		color: var(--global--color-primary);
		font-size: var(--global--font-size-xs);
		line-height: var(--global--line-height-body);
		margin-top: calc(0.5 * var(--global--spacing-unit));
		margin-bottom: var(--global--spacing-unit);
		text-align: center;
	}

	.alignright {
		margin-left: var(--global--spacing-horizontal);
	}

	.alignleft {
		margin-right: var(--global--spacing-horizontal);
	}

	a:focus img {
		outline-offset: 2px;
	}
}

// Remove vertical margins from image block wrappers when floated
.entry-content > *[class="wp-block-image"],
.entry-content [class*="inner-container"] > *[class="wp-block-image"] {
	margin-top: 0;
	margin-bottom: 0;

	// Remove top margins from the following element when previous image block is floated
	+ * {
		margin-top: 0;
	}
}

// Block Styles
.wp-block-image.is-style-twentytwentyone-border img,
.wp-block-image.is-style-twentytwentyone-image-frame img {
	border: calc(3 * var(--separator--height)) solid var(--global--color-border);
}

.wp-block-image.is-style-twentytwentyone-image-frame img {
	padding: var(--global--spacing-unit);
}

.entry-content {

	> .wp-block-image {

		> .alignleft,
		> .alignright {
			@include media(mobile) {
				max-width: 50%;
			}
			@include media(mobile-only) {
				margin-left: 0;
				margin-right: 0;
			}
		}
	}
}
