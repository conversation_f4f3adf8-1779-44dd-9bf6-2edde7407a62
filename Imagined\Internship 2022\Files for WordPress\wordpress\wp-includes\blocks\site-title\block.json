{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "core/site-title", "title": "Site Title", "category": "theme", "description": "Displays the name of this site. Update the block, and the changes apply everywhere it’s used. This will also appear in the browser title bar and in search results.", "textdomain": "default", "attributes": {"level": {"type": "number", "default": 1}, "textAlign": {"type": "string"}, "isLink": {"type": "boolean", "default": true}, "linkTarget": {"type": "string", "default": "_self"}}, "example": {"viewportWidth": 500}, "supports": {"align": ["wide", "full"], "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "spacing": {"padding": true, "margin": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalTextTransform": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true, "lineHeight": true, "fontAppearance": true, "letterSpacing": true, "textTransform": true}}}, "editorStyle": "wp-block-site-title-editor"}