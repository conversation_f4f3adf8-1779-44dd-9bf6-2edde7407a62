{"name": "slim/psr7", "type": "library", "description": "Strict PSR-7 implementation", "keywords": ["psr7", "psr-7", "http"], "homepage": "https://www.slimframework.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joshlockhart.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://silentworks.co.uk"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://akrabat.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com"}], "require": {"php": "^7.2 || ^8.0", "fig/http-message-util": "^1.1.5", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3", "symfony/polyfill-php80": "^1.22"}, "require-dev": {"ext-json": "*", "adriansuter/php-autoload-override": "^1.2", "http-interop/http-factory-tests": "^0.9.0", "php-http/psr7-integration-tests": "dev-master", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5 || ^9.5", "squizlabs/php_codesniffer": "^3.6", "weirdan/prophecy-shim": "^1.0 || ^2.0.2"}, "provide": {"psr/http-message-implementation": "1.0", "psr/http-factory-implementation": "1.0"}, "autoload": {"psr-4": {"Slim\\Psr7\\": "src"}}, "autoload-dev": {"psr-4": {"Slim\\Tests\\Psr7\\": "tests"}}, "scripts": {"test": ["@phpunit", "@phpcs", "@phpstan"], "phpunit": "phpunit", "phpcs": "phpcs", "phpstan": "phpstan analyse src --memory-limit=-1"}, "config": {"sort-packages": true}}