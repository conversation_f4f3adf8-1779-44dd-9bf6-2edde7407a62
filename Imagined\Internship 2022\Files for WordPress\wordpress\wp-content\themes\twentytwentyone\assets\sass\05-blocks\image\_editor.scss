/* Center image block by default in the editor */

.wp-block-image,
.wp-block-image > div:not(.components-placeholder) {
	text-align: center;
}

[data-type="core/image"] .block-editor-block-list__block-edit figure.is-resized {
	margin: 0 auto;
}

/* Block Styles */

.wp-block-image.is-style-twentytwentyone-border img,
.wp-block-image.is-style-twentytwentyone-image-frame img {
	border: calc(3 * var(--separator--height)) solid var(--global--color-border);
}

.wp-block-image.is-style-twentytwentyone-image-frame img {
	padding: var(--global--spacing-unit);
}
