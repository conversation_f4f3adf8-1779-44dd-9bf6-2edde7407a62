/*! This file is auto-generated */
!function(){"use strict";var e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t);var n=window.wp.element,o=window.wp.i18n,r=window.lodash,a=window.wp.apiFetch,l=e.n(a);var i=async function(e){const t=await l()({path:"/wp/v2/types/wp_block"}),n=await l()({path:`/wp/v2/${t.rest_base}/${e}?context=edit`}),o=n.title.raw,a=n.content.raw,i=JSON.stringify({__file:"wp_block",title:o,content:a},null,2);!function(e,t,n){const o=new window.Blob([t],{type:n});if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(o,e);else{const t=document.createElement("a");t.href=URL.createObjectURL(o),t.download=e,t.style.display="none",document.body.appendChild(t),t.click(),document.body.removeChild(t)}}((0,r.kebabCase)(o)+".json",i,"application/json")},s=window.wp.components,c=window.wp.compose;var d=async function(e){const t=await function(e){const t=new window.FileReader;return new Promise((n=>{t.onload=()=>{n(t.result)},t.readAsText(e)}))}(e);let n;try{n=JSON.parse(t)}catch(e){throw new Error("Invalid JSON file")}if(!("wp_block"===n.__file&&n.title&&n.content&&(0,r.isString)(n.title)&&(0,r.isString)(n.content)))throw new Error("Invalid Reusable block JSON file");const o=await l()({path:"/wp/v2/types/wp_block"});return await l()({path:`/wp/v2/${o.rest_base}`,data:{title:n.title,content:n.content,status:"publish"},method:"POST"})};var u=(0,c.withInstanceId)((function(e){let{instanceId:t,onUpload:r}=e;const a="list-reusable-blocks-import-form-"+t,l=(0,n.useRef)(),[i,c]=(0,n.useState)(!1),[u,p]=(0,n.useState)(null),[w,m]=(0,n.useState)(null);return(0,n.createElement)("form",{className:"list-reusable-blocks-import-form",onSubmit:e=>{e.preventDefault(),w&&(c({isLoading:!0}),d(w).then((e=>{l&&(c(!1),r(e))})).catch((e=>{if(!l)return;let t;switch(e.message){case"Invalid JSON file":t=(0,o.__)("Invalid JSON file");break;case"Invalid Reusable block JSON file":t=(0,o.__)("Invalid Reusable block JSON file");break;default:t=(0,o.__)("Unknown error")}c(!1),p(t)})))},ref:l},u&&(0,n.createElement)(s.Notice,{status:"error",onRemove:()=>{p(null)}},u),(0,n.createElement)("label",{htmlFor:a,className:"list-reusable-blocks-import-form__label"},(0,o.__)("File")),(0,n.createElement)("input",{id:a,type:"file",onChange:e=>{m(e.target.files[0]),p(null)}}),(0,n.createElement)(s.Button,{type:"submit",isBusy:i,disabled:!w||i,variant:"secondary",className:"list-reusable-blocks-import-form__button"},(0,o._x)("Import","button label")))}));var p=function(e){let{onUpload:t}=e;return(0,n.createElement)(s.Dropdown,{position:"bottom right",contentClassName:"list-reusable-blocks-import-dropdown__content",renderToggle:e=>{let{isOpen:t,onToggle:r}=e;return(0,n.createElement)(s.Button,{"aria-expanded":t,onClick:r,variant:"primary"},(0,o.__)("Import from JSON"))},renderContent:e=>{let{onClose:o}=e;return(0,n.createElement)(u,{onUpload:(0,r.flow)(o,t)})}})};document.body.addEventListener("click",(e=>{e.target.classList.contains("wp-list-reusable-blocks__export")&&(e.preventDefault(),i(e.target.dataset.id))})),document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelector(".page-title-action");if(!e)return;const t=document.createElement("div");t.className="list-reusable-blocks__container",e.parentNode.insertBefore(t,e),(0,n.render)((0,n.createElement)(p,{onUpload:()=>{const e=document.createElement("div");e.className="notice notice-success is-dismissible",e.innerHTML=`<p>${(0,o.__)("Reusable block imported successfully!")}</p>`;const t=document.querySelector(".wp-header-end");t&&t.parentNode.insertBefore(e,t)}}),t)})),(window.wp=window.wp||{}).listReusableBlocks=t}();